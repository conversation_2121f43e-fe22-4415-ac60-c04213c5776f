<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>com.microteam.team</groupId>
    <artifactId>teams</artifactId>
    <version>1.0.0</version>
    <name>teams</name>
    <description>Demo project for Spring Boot</description>


    <parent>
        <groupId>com.microteam</groupId>
        <artifactId>pom</artifactId>
        <version>1.0.0</version>
    </parent>


    <dependencies>
        <dependency>
            <groupId>com.microteam</groupId>
            <artifactId>base</artifactId>
            <version>1.0.7</version>
        </dependency>
    </dependencies>


    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <configuration>
                    <skipTests>true</skipTests>
                </configuration>
            </plugin>
        </plugins>
        <finalName>team-teams-1.0.0</finalName>
    </build>

</project>
