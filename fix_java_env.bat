@echo off
chcp 65001 >nul
echo 设置Java环境变量...
set "JAVA_HOME=C:\Program Files\Java\jdk1.8.0_101"
set "PATH=%JAVA_HOME%\bin;%PATH%"
set "MAVEN_OPTS=-Dmaven.compiler.fork=true -Dmaven.compiler.executable=%JAVA_HOME%\bin\javac"

echo 验证Java环境...
java -version
javac -version

echo.
echo 开始编译项目...
cd /d "C:\Users\<USER>\Desktop\microteam\back\work\mciro-service\pom\base"
mvn clean compile -Dmaven.compiler.fork=true

pause
