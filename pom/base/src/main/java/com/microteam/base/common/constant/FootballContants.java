package com.microteam.base.common.constant;

/**
 * 整个项目的全局常量；；
 *
 * @param
 * @param
 * @return
 */
public class FootballContants {

    public static final String QUITTEAM = "quitTeam";
    public static final String NEWTEAMMEMBER = "newTeamMember";
    // 请假
    public static final String ABSENTGAME = "absentGame";
    // 报名
    public static final String ENROLLGAME = "enrollGame";
    //删除球队
    public static final String CAPTAIDELTEAM = "captainDelTeam";
    // 申请通过
    public static final String APPLIEDJOINAUDIT = "appliedJoinAudit";
    public static final String HISTORYCONTEST = "historyContest";
    public static final String LIVECONTEST = "liveContest";
    public static final String APPLYADDTEAM = "applyAddTeam";
    public static final String AUDITADDTEAM = "auditAddTeam";
    public static final String APPLYADDTEAMCHEER = "applyAddTeamCheer";
    public static final String AUDITADDTEAMCHEER = "auditAddTeamCheer";
    public static final String TEAMINVITEUSER = "teamInviteUser";
    public static final String UPDATEHARDWARESOFT = "updateHardwareSoft";
    public static final String UPLOADHARDWAREDATA = "uploadHardwareData"; //系统发的设备同步通知
    public static final String UPLOADED = "uploaded";
    public static final String UPLOADHARDWAREDATABYOWNER = "uploadHardwareDataByOwner"; //队长发的设备同步通知
    public static final String PUBLISHCONTEST = "publishContest";
    public static final String UPLOADHANDDATA = "uploadHandData";
    public static final String REFUSEINVITE = "refuseInvite"; //拒绝邀请
    public static final String NOTALLOWADDTEAM = "notAllowAddTeam";
    public static final String TEAMDELUSER = "teamDelUser";
    public static final String PRIVILEGETRANSFER = "privilegeTransfer";
    public static final String UPDATEHARDWAREDATA = "updateHardwareData";
    public static final long GAMEDURATION = 7200000;//比赛时间设定为120分钟
    public static final long GAMEHALF = 2700000;//45分钟
    public static final long GAMEREST = 0;//15分钟
    public static final String TEAMGAMESTART = "teamGameStart"; //比赛开始的通知
    public static final String TEAMGAMEEND = "teamGameEnd"; //比赛结束通知
    public static final String DATAINPUT = "dataInput"; //数据录入
    public static final long DATAINPUTTIME = 7200000;//数据录入提醒时间间隔
    public static final String FEEDBACK = "feedback"; //反馈信息
    public static final String FEEDBACKDEAL = "feedBackDeal"; //处理反馈
    public static final String UPDATENODEINFO = "updateNodeInfo"; //修改录入名
    public static final String UPDATEPOLONUMBER = "updatePoloNumber";//修改球衣号码 11
    public static final String AGENCYDELUSER = "agencyDelUser";
    public static final String APPLYADDAGENCY = "applyAddAgency";
    public static final String AUDITADDAGENCY = "auditAddAgency";
    public static final String NOTALLOWADDAGENCY = "notAllowAddAgency";
    public static final String AUDITADDAGENCYTEAM = "auditAddAgencyTeam";
    public static final String CREATETEAM = "createTeam";
    //常量数据
    public static final int MESSIMOVEDISTANCE = 8000;//梅西每场比赛跑动距离
    public static final int MESSIKICKBALLCOUNT = 70;//梅西每场比赛跑动距离
    public static final String JOIN = "join";    //主动加入球队
    public static final String INVITATION = "invitation";    //被邀请加入球队
    public static final long LONG_SHORTPASSCUTOFF = 2000; //长、短传临界值
    public static final long PASSBALLCUTOFF = 3000; //传球临界值
    public static final long ONEDAY = 86400000; //24小时
    /**
     * 环信透传信息字段定义
     * 报名信息
     */
    public static final String APPSIGNUPTEAM = "appSignUpTeam";

    /**
     * 启动设备信息
     */
    public static final String STARTEQUIPMENT = "startEquipment";


    public static final String TEAMGAMENODEEND = "teamGameNodeEnd"; //比赛手记录入结束通知

    public static final long ONEHOUR = 3600000;

}



