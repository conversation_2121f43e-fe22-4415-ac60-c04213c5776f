package com.microteam.base.common.base;

import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;
import java.util.Date;

@MappedSuperclass
@Getter
@Setter
public class BaseEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false)
    protected Long id;
    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "createTime", nullable = false)
    protected Date createTime;
    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "updateTime")
    protected Date updateTime;
    @Column(name = "deleted", nullable = false)
    protected boolean deleted;

    public BaseEntity() {
        this.createTime = new Date();
        this.updateTime = new Date();
        this.deleted = false;
    }

    public Date getCreateTime() {
        if (this.createTime == null) {
            return null;
        }
        return (Date) this.createTime.clone();
    }

    public void setCreateTime(Date createTime) {
        this.createTime = (Date) createTime.clone();
    }

    public Date getUpdateTime() {
        if (this.updateTime == null) {
            return null;
        }
        return (Date) this.updateTime.clone();
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = (Date) updateTime.clone();
    }
}
