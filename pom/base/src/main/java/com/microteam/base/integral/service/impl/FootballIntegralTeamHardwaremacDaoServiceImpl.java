package com.microteam.base.integral.service.impl;

import com.microteam.base.integral.dao.FootballIntegralTeamHardwaremacDao;
import com.microteam.base.integral.service.FootballIntegralTeamHardwaremacDaoService;
import com.microteam.base.entity.integral.FootballIntegralTeamHardwaremac;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service(value = "footballIntegralTeamHardwaremacDaoService")
public class FootballIntegralTeamHardwaremacDaoServiceImpl implements FootballIntegralTeamHardwaremacDaoService {

    @Autowired
    private FootballIntegralTeamHardwaremacDao footballIntegralTeamHardwaremacDao;

    @Override
    public List<FootballIntegralTeamHardwaremac> findByTeamId(Long teamId) {
        return footballIntegralTeamHardwaremacDao.findByTeamId(teamId);
    }

    @Override
    public List<FootballIntegralTeamHardwaremac> findByTeamIdAndMac(Long teamId, String mac) {
        return footballIntegralTeamHardwaremacDao.findByTeamIdAndMac(teamId, mac);
    }

    @Override
    public FootballIntegralTeamHardwaremac save(FootballIntegralTeamHardwaremac footballIntegralTeamHardwaremac) {
        return footballIntegralTeamHardwaremacDao.save(footballIntegralTeamHardwaremac);
    }
}
