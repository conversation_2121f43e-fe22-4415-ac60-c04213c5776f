package com.microteam.base.entity.team;


import com.microteam.base.entity.user.User;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

@Entity
@Table(name = "football_team_game_takenotes")
@Getter
@Setter
public class FootballTeamTakeNode implements Serializable {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false)
    protected Long id;
    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "create_time", nullable = false, length = 19)
    protected Date createTime;
    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "update_time", nullable = false, length = 19)
    protected Date updateTime;
    @Column(name = "deleted", nullable = false)
    protected boolean deleted;

    @ManyToOne
    @JoinColumn(name = "user_id", nullable = false)
    private User user;
    @ManyToOne
    @JoinColumn(name = "team_id", nullable = false)
    private FootballTeam footballTeam;
    @ManyToOne
    @JoinColumn(name = "node_id")
    private FootballTeamNode footballTeamNode;
    @ManyToOne
    @JoinColumn(name = "team_game_id", nullable = false)
    private FootballTeamGame footballTeamGame;
    @Column(name = "goals_for", nullable = false)
    private Long goalsfor; //进球数
    @Column(name = "assist", nullable = false)
    private Long assist;  //助攻
    @Column(name = "fault", nullable = false)
    private Long fault;//失误
    @Column(name = "shoot", nullable = false)
    private Long shoot;//射正
    @Column(name = "shoot_aside", nullable = false)
    private Long shootAside;//射偏
    @Column(name = "wave_shot", nullable = false)
    private Long waveShot;//浪射
    @Column(name = "hold_up", nullable = false)
    private Long holdUp;//抢断
    @Column(name = "excel", nullable = false)
    private Long excel;//过人
    @Column(name = "corner", nullable = false)
    private Long corner;//角球
    @Column(name = "free_kick", nullable = false)
    private Long freeKick;//任意球
    @Column(name = "penaltykick", nullable = false)
    private Long penaltyKick;//点球
    @Column(name = "redcard", nullable = false)
    private Long redCard;//红牌
    @Column(name = "yellowcard", nullable = false)
    private Long yellowCard;//黄牌
    @Column(name = "own_goal", nullable = false)
    private Long ownGoal;//乌龙球
    @Column(name = "menace")
    private Long menace;//威胁球
    @Column(name = "save")
    private Long save;//解围
    @Column(name = "foul")
    private Long foul;//犯规
    @Column(name = "offSide")
    private Long offSide;//越位
    @Column(name = "roof")
    private Long roof;//冒顶.
    @Column(name = "head")
    private Long head;//头球
    @Column(name = "stopFault")
    private Long stopFault;//停球失误
    @Column(name = "passFault")
    private Long passFault;//传球失误
    @Column(name = "defendFault")
    private Long defendFault;//防守失误
    @Column(name = "kickEmpty")
    private Long kickEmpty;//踢空
    @Column(name = "version", nullable = false)
    private Long version;

    public Long getPassFault() {
        if (passFault == null) {
            return 0L;
        }
        return passFault;
    }

    public FootballTeamTakeNode() {
        super();
    }

    public FootballTeamTakeNode(Long id, User user, FootballTeam footballTeam,
                                FootballTeamGame footballTeamGame, Long goalsfor, Long assist,
                                Long fault, Long shoot, Long shootAside, Long waveShot,
                                Long holdUp, Long excel, Long corner, Long freeKick,
                                Long penaltyKick, Long redCard, Long yellowCard, Long ownGoal, Long menace,
                                Long save, Long foul, Long offSide, Long roof, Long head, Long stopFault, Long passFault, Long defendFault, Long kickEmpty,
                                Date createTime, Date updateTime, boolean deleted, Long version) {
        super();
        this.id = id;
        this.user = user;
        this.footballTeam = footballTeam;
        this.footballTeamGame = footballTeamGame;
        this.goalsfor = goalsfor;
        this.assist = assist;
        this.fault = fault;
        this.shoot = shoot;
        this.shootAside = shootAside;
        this.waveShot = waveShot;
        this.holdUp = holdUp;
        this.excel = excel;
        this.corner = corner;
        this.freeKick = freeKick;
        this.penaltyKick = penaltyKick;
        this.redCard = redCard;
        this.yellowCard = yellowCard;
        this.ownGoal = ownGoal;
        this.menace = menace;
        this.save = save;
        this.foul = foul;
        this.offSide = offSide;
        this.roof = roof;
        this.head = head;
        this.stopFault = stopFault;
        this.passFault = passFault;
        this.defendFault = defendFault;
        this.kickEmpty = kickEmpty;
        this.createTime = createTime;
        this.updateTime = updateTime;
        this.deleted = deleted;
        this.version = version;
    }

    public FootballTeamTakeNode(Long goalsfor, Long shoot, Long holdUp, Long freeKick, Long penaltyKick, Long redCard, Long yellowCard, Long foul, Long offSide) {
        this.goalsfor = goalsfor;
        this.shoot = shoot;
        this.holdUp = holdUp;
        this.freeKick = freeKick;
        this.penaltyKick = penaltyKick;
        this.redCard = redCard;
        this.yellowCard = yellowCard;
        this.foul = foul;
        this.offSide = offSide;
    }

    public FootballTeamTakeNode(Long goalsfor,
                                Long assist,
                                Long fault,
                                Long shoot,
                                Long shootAside,
                                Long waveShot,
                                Long holdUp,
                                Long excel,
                                Long corner,
                                Long freeKick,
                                Long penaltyKick,
                                Long redCard,
                                Long yellowCard,
                                Long ownGoal) {
        this.goalsfor = goalsfor;
        this.assist = assist;
        this.fault = fault;
        this.shoot = shoot;
        this.shootAside = shootAside;
        this.waveShot = waveShot;
        this.holdUp = holdUp;
        this.excel = excel;
        this.corner = corner;
        this.freeKick = freeKick;
        this.penaltyKick = penaltyKick;
        this.redCard = redCard;
        this.yellowCard = yellowCard;
        this.ownGoal = ownGoal;
    }
}
