package com.microteam.base.integral.service;


import com.microteam.base.entity.integral.FootballIntegralTeam;

import java.util.List;

public interface FootballIntegralTeamDaoService {

    FootballIntegralTeam findByTeamId(Long teamId);

    List<FootballIntegralTeam> findByTeamIdList(List<Long> teamIdList);

//    int findRangeByTeamId(Long teamId, List<Long> teamIdList, FootballIntegralTeam integralTeam) throws Exception;
//
//    int addNewIntegral(List<FootballIntegralHistory> integralHistoryList, FootballIntegralTeam integralTeam);

    FootballIntegralTeam generalTeamData(Long teamId) throws Exception;


}
