//package com.microteam.base.agency.dao.impl;
//
//import com.microteam.base.agency.dao.FootballAgencyClassPublicationPraiseDao;
//import com.microteam.base.entity.agency.FootballAgencyClassPublicationPraise;
//import com.microteam.base.common.base.AbstractHibernateDao;
//import org.apache.log4j.Logger;
//import org.hibernate.query.Query;
//import org.springframework.stereotype.Repository;
//
//import java.util.List;
//
//@Repository("footballAgencyClassPublicationPraiseDao")
//public class FootballAgencyClassPublicationPraiseDaoImpl extends AbstractHibernateDao<FootballAgencyClassPublicationPraise> implements FootballAgencyClassPublicationPraiseDao {
//    static Logger logger = Logger.getLogger(FootballAgencyClassPublicationPraiseDaoImpl.class.getName());
//
//    public FootballAgencyClassPublicationPraiseDaoImpl() {
//        super();
//        setClazz(FootballAgencyClassPublicationPraise.class);
//    }
//
//    @Override
//    public List<FootballAgencyClassPublicationPraise> findByPublicationId(Long publicationId) {
//        String hql = "from FootballAgencyClassPublicationPraise as agencyClassPublicationPraise " +
//                "where agencyClassPublicationPraise.footballAgencyClassPublication.id = (:publicationId) " +
//                "and agencyClassPublicationPraise.isPraised=true " +
//                "and agencyClassPublicationPraise.deleted=false ";
//        Query query = getCurrentSession().createQuery(hql)
//                .setParameter("publicationId", publicationId);
//        List<FootballAgencyClassPublicationPraise> list = query.list();
//        return list;
//    }
//
//    @Override
//    public FootballAgencyClassPublicationPraise findByPublicationIdAndUserId(Long publicationId, Long userId) {
//        String hql = "from FootballAgencyClassPublicationPraise as agencyClassPublicationPraise " +
//                "where agencyClassPublicationPraise.footballAgencyClassPublication.id = (:publicationId) " +
//                "and agencyClassPublicationPraise.user.id = (:userId) " +
//                "and agencyClassPublicationPraise.deleted=false ";
//        Query query = getCurrentSession().createQuery(hql)
//                .setParameter("publicationId", publicationId)
//                .setParameter("userId", userId);
//        List<FootballAgencyClassPublicationPraise> list = query.list();
//        if (list != null && list.size() > 0) {
//            return list.get(0);
//        }
//        return null;
//    }
//
//
//}
