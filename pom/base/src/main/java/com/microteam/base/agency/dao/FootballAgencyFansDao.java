package com.microteam.base.agency.dao;

import com.microteam.base.entity.agency.FootballAgency;
import com.microteam.base.entity.agency.FootballAgencyClass;
import com.microteam.base.entity.agency.FootballAgencyFans;
import com.microteam.base.entity.user.User;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

public interface FootballAgencyFansDao extends JpaRepository<FootballAgencyFans, Long>, JpaSpecificationExecutor<FootballAgencyFans> {

    //查询机构下的所有粉丝
    @Query("from FootballAgencyFans as footballAgencyFans " +
            "left join fetch footballAgencyFans.user as user " +
            "left join fetch footballAgencyFans.footballAgencyClass as footballAgencyClass " +
            "where footballAgencyFans.footballAgency=?1 " +
            "and footballAgencyFans.deleted=false ")
    List<FootballAgencyFans> findFansByAgency(FootballAgency footballAgency);

    //判断是否是机构下的粉丝
    @Query("from FootballAgencyFans as footballAgencyFans " +
            "left join fetch footballAgencyFans.footballAgencyClass as footballAgencyClass " +
            "where footballAgencyFans.footballAgency=?1 " +
            "and footballAgencyFans.user=?2 " +
            "and footballAgencyFans.deleted=false ")
    List<FootballAgencyFans> findFansByAgencyAndUser(FootballAgency footballAgency, User user);

    //查询机构班级下粉丝数量
    @Query("select distinct new com.microteam.base.entity.agency.FootballAgencyFans(footballAgencyFans.footballAgency,footballAgencyFans.user) " +
            "from FootballAgencyFans as footballAgencyFans " +
            "left join footballAgencyFans.user as user " +
            "left join footballAgencyFans.footballAgency as footballAgency " +
            "where footballAgencyFans.footballAgency=?1 " +
            "and footballAgencyFans.footballAgencyClass=?2 " +
            "and footballAgencyFans.deleted=false ")
    List<FootballAgencyFans> findFansByAgencyAndClass(FootballAgency footballAgency, FootballAgencyClass agencyClass);

    //分页查询机构班级的粉丝申请列表
    @Query("from FootballAgencyFans as footballAgencyFans " +
            "left join fetch footballAgencyFans.user " +
            "where footballAgencyFans.footballAgency=?1 " +
            "and footballAgencyFans.footballAgencyClass=?2 " +
            "and footballAgencyFans.deleted=false " +
            "order by footballAgencyFans.isPermited asc,footballAgencyFans.createTime desc")
    List<FootballAgencyFans> findFansByAgencyAndClassForPage(FootballAgency footballAgency, FootballAgencyClass agencyClass, Pageable pageable);

    //确定用户是不是班级的粉丝
    @Query("from FootballAgencyFans as footballAgencyFans " +
            "where footballAgencyFans.footballAgency=?1 " +
            "and footballAgencyFans.footballAgencyClass=?2 " +
            "and footballAgencyFans.user=?3 " +
            "and footballAgencyFans.deleted=false ")
    FootballAgencyFans findFansByAgencyAndClassAndUser(FootballAgency footballAgency, FootballAgencyClass agencyClass, User user);

    //分页查询我关注的机构
//    List<FootballAgencyFans> findFansByUserAndCityCodeAndCountyCodeAndAgencyNameForPage(User user, int page, int pageSize, String cityCode, String countyCode, String search);

    @Query("from FootballAgencyFans as footballAgencyFans " +
            "left join fetch footballAgencyFans.footballAgency as footballAgency " +
            "left join fetch  footballAgency.groups as groups " +
            "left join fetch footballAgencyFans.footballAgencyClass as footballAgencyClass " +
            "where footballAgencyFans.user=?1 ")
    List<FootballAgencyFans> findFansByUser(User user);

    //根据id查询
    @Query("from FootballAgencyFans as footballAgencyFans " +
            "left join fetch footballAgencyFans.footballAgency as footballAgency " +
            "where footballAgencyFans.id=?1 " +
            "and footballAgencyFans.deleted=false ")
    FootballAgencyFans findFansById(long id);

    //删除机构下的粉丝
    @Query("delete from FootballAgencyFans as footballAgencyFans " +
            "where footballAgencyFans.footballAgency=?1 ")
    @Modifying
    void delFansByAgency(FootballAgency footballAgency);
} 
