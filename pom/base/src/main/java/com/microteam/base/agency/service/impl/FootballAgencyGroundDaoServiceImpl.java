package com.microteam.base.agency.service.impl;

import com.microteam.base.agency.dao.FootballAgencyGroundDao;
import com.microteam.base.agency.service.FootballAgencyGroundDaoService;
import com.microteam.base.entity.agency.FootballAgency;
import com.microteam.base.entity.agency.FootballAgencyGround;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.List;

@Service("footballAgencyGroundDaoService")
public class FootballAgencyGroundDaoServiceImpl implements FootballAgencyGroundDaoService {
    static Logger logger = Logger.getLogger(FootballAgencyGroundDaoServiceImpl.class.getName());
    @Autowired
    private FootballAgencyGroundDao dao;

    public FootballAgencyGroundDaoServiceImpl() {
        super();

    }

    //分页查询机构场地
    @Override
    public List<FootballAgencyGround> findAgencyGroundList(FootballAgency agency, int page, int pageSize) {
        if (page > 0) {
            page--;
        }
        Pageable pageable = PageRequest.of(page, pageSize);
        return dao.findAgencyGroundList(agency, pageable);
    }

    @Override
    public FootballAgencyGround findById(long id) {
        return dao.findById(id);
    }
}
