package com.microteam.base.agency.service;

import com.microteam.base.entity.agency.FootballAgencyClassCourse;
import com.microteam.base.entity.agency.FootballAgencyStudentScore;

import java.util.List;

public interface FootballAgencyStudentScoreDaoService {
    //查询课程下所有学员的分数
    List<FootballAgencyStudentScore> findScoreByCourse(FootballAgencyClassCourse agencyClassCourse);

    FootballAgencyStudentScore findScoreById(long id);
}
