//package com.microteam.base.demoManager.dao.impl;
//
//import com.microteam.base.common.base.AbstractHibernateDao;
//import com.microteam.base.demoManager.dao.MacQRCodeDao;
//import com.microteam.base.entity.demoManager.MacQRCode;
//import org.hibernate.query.Query;
//import org.springframework.stereotype.Service;
//
//import java.util.List;
//
//@Service("macQRCodeDao")
//public class MacQRCodeDaoImpl extends AbstractHibernateDao<MacQRCode> implements MacQRCodeDao {
//
//    @Override
//    public List<MacQRCode> findAllCode() {
//        String hql = "from MacQRCode as code " +
//                "where code.deleted = false";
//        Query query = getCurrentSession().createQuery(hql);
//        List<MacQRCode> list = query.list();
//        return list;
//    }
//
//    @Override
//    public MacQRCode findMacQRCode(String mac) {
//        String hql = "from MacQRCode as code where " +
//                "code.mac = :mac " +
//                "and code.deleted = false";
//        Query query = getCurrentSession().createQuery(hql)
//                .setParameter("mac", mac);
//        List<MacQRCode> list = query.list();
//        if (list != null && list.size() > 0) {
//            return list.get(0);
//        }
//        return null;
//    }
//
//    @Override
//    public MacQRCode findMacQRCodeByCode(String qrCode) {
//        String hql = "from MacQRCode as code " +
//                "where code.qrCode = :qrCode " +
//                "and code.deleted = false";
//        Query query = getCurrentSession().createQuery(hql).setParameter("qrCode", qrCode);
//        List<MacQRCode> list = query.list();
//        if (list != null && list.size() > 0) {
//            return list.get(0);
//        }
//        return null;
//    }
//
//}
