package com.microteam.base.entity.team;


import com.microteam.base.common.base.BaseEntity;
import com.microteam.base.entity.user.User;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

@Entity
@Table(name = "football_team_join_applicant")
@Getter
@Setter
public class FootballTeamJoinApplicant extends BaseEntity implements Serializable {
    @ManyToOne
    @JoinColumn(name = "teamMemberRoleId", nullable = false)
    private FootballTeamMemberRole footballTeamMemberRole;
    @ManyToOne
    @JoinColumn(name = "applicantId", nullable = false)
    private User user;
    @ManyToOne
    @JoinColumn(name = "teamId", nullable = false)
    private FootballTeam footballTeam;
    @Column(name = "applyMessage", length = 50)
    private String applyMessage;
    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "applyTime", length = 19)
    private Date applyTime;
    @Column(name = "allowedId")
    private Long allowedId;
    @Column(name = "audit", nullable = false)
    private short audit;
    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "auditTime", length = 19)
    private Date auditTime;
    @Column(name = "teamNumber")
    private Integer teamNumber;

    public Date getApplyTime() {
        if (this.applyTime == null) {
            return null;
        }
        return (Date) this.applyTime.clone();
    }

    public void setApplyTime(Date applyTime) {
        this.applyTime = (Date) applyTime.clone();
    }


    public Date getAuditTime() {
        if (this.auditTime == null) {
            return null;
        }
        return (Date) this.auditTime.clone();
    }

    public void setAuditTime(Date auditTime) {
        this.auditTime = (Date) auditTime.clone();
    }

}
