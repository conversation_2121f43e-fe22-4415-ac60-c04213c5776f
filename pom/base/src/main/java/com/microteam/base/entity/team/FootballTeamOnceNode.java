package com.microteam.base.entity.team;

import com.microteam.base.common.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

@Entity
@Table(name = "football_team_game_once_node")
@Getter
@Setter
public class FootballTeamOnceNode extends BaseEntity implements Serializable {

    @Column(name = "userId", nullable = false)
    private Long userId;
    @Column(name = "teamId", nullable = false)
    private Long teamId;
    @Column(name = "gameId", nullable = false)
    private Long gameId;
    @Column(name = "actionId", nullable = false)
    private Long actionId;
    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "time", nullable = false)
    private Date time;

    public Date getTime() {
        if (this.time == null) {
            return null;
        }
        return (Date) time.clone();
    }

    public void setTime(Date time) {
        this.time = (Date) time.clone();
    }

}