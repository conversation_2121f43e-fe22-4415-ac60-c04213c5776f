package com.microteam.base.agency.service;

import com.microteam.base.entity.agency.FootballAgencyClassEnrollment;

import java.util.List;

public interface FootballAgencyClassEnrollmentDaoService {
    //根据Id查询
    FootballAgencyClassEnrollment findById(long id);

    //查询机构下的报名情况
    List<FootballAgencyClassEnrollment> findByAgencyId(Long agencyId);

    //分页查询机构下的报名情况
    List<FootballAgencyClassEnrollment> findByAgencyIdForPage(Long agencyId, int page, int pageSize);

    List<FootballAgencyClassEnrollment> findByAgencyIdAndAgencyClassId(Long agencyId, Long agencyClassId);

    List<FootballAgencyClassEnrollment> findByAgencyIdAndAgencyClassIdForPage(Long agencyId, Long agencyClassId, int page, int pageSize);
}
