package com.microteam.base.lesson.service.impl;

import com.microteam.base.entity.lesson.RecommendCourseware;
import com.microteam.base.lesson.dao.RecommendCoursewareDao;
import com.microteam.base.lesson.service.RecommendCoursewareDaoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class RecommendCoursewareDaoServiceImpl implements RecommendCoursewareDaoService {

    @Autowired
    private RecommendCoursewareDao dao;

    @Override
    public List<RecommendCourseware> findByType(Integer type, int page, int pageSize) {
        if (page > 0) {
            page--;
        }
        Pageable pageable = PageRequest.of(page, pageSize);
        return dao.findByType(type, pageable);
    }

    @Override
    public List<RecommendCourseware> findByRecommendTypeAndCoursewareType(int recommendType, int coursewareType, int page, int pageSize) {
        if (page > 0) {
            page--;
        }
        Pageable pageable = PageRequest.of(page, pageSize);
        return dao.findByRecommendTypeAndCoursewareType(recommendType, coursewareType, pageable);
    }
}
