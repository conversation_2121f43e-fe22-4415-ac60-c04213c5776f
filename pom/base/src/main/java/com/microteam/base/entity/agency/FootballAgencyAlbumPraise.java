package com.microteam.base.entity.agency;


import com.microteam.base.common.base.BaseEntity;
import com.microteam.base.entity.user.User;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;
import java.io.Serializable;

@Entity
@Table(name = "football_agency_album_praise")
@Getter
@Setter
public class FootballAgencyAlbumPraise extends BaseEntity implements Serializable {

    @ManyToOne
    @JoinColumn(name = "albumId", nullable = false)
    private FootballAgencyAlbum footballAgencyAlbum;
    @ManyToOne
    @JoinColumn(name = "userId", nullable = false)
    private User user;
    @Column(name = "isPraised")
    private Boolean isPraised;

}
