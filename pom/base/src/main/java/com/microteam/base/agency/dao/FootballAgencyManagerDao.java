package com.microteam.base.agency.dao;

import com.microteam.base.entity.agency.FootballAgency;
import com.microteam.base.entity.agency.FootballAgencyManager;
import com.microteam.base.entity.user.User;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

public interface FootballAgencyManagerDao extends JpaRepository<FootballAgencyManager, Long>, JpaSpecificationExecutor<FootballAgencyManager> {

    //查询用户是不是机构下的管理员
    @Query("from FootballAgencyManager as agencyManager " +
            "where agencyManager.footballAgency=?1 " +
            "and agencyManager.user=?2 " +
            "and agencyManager.audit=1 " +
            "and agencyManager.deleted=false ")
    FootballAgencyManager findAgencyManagerByAgencyAndUser(FootballAgency agency, User user);

    //分页查询机构管理员
    @Query("from FootballAgencyManager as agencyManager " +
            "left join fetch agencyManager.user as user " +
            "where agencyManager.footballAgency=?1 " +
            "and agencyManager.deleted=false " +
            "order by agencyManager.createTime desc ")
    List<FootballAgencyManager> findAgencyManagerByAgencyForPage(FootballAgency agency, Pageable pageable);

    //根据Id查询
    @Query("from FootballAgencyManager as agencyManager " +
            "left join fetch agencyManager.user as user " +
            "where agencyManager.id=?1 " +
            "and agencyManager.deleted=false ")
    FootballAgencyManager findAgencyManagerById(long id);

    //根据机构查询
    @Query("from FootballAgencyManager as agencyManager " +
            "left join fetch agencyManager.user as user " +
            "where agencyManager.footballAgency=?1 " +
            "and agencyManager.deleted=false " +
            "order by agencyManager.createTime desc ")
    List<FootballAgencyManager> findAgencyManagerByAgency(FootballAgency agency);

    //分页模糊查询机构下的管理员
    @Query("from FootballAgencyManager as agencyManager " +
            "left join fetch agencyManager.user as user " +
            "where agencyManager.footballAgency=?1 " +
            "and agencyManager.managerName like ?2 " +
            "and agencyManager.audit=1 " +
            "and agencyManager.deleted=false " +
            "order by agencyManager.createTime desc ")
    List<FootballAgencyManager> findAgencyManagerByAgencyAndManagerNameForPage(FootballAgency agency, String managerName, Pageable pageable);

    @Query("from FootballAgencyManager as agencyManager " +
            "left join fetch agencyManager.user as user "+
            "where agencyManager.footballAgency=?1 " +
            "and agencyManager.managerName like ?2 " +
            "and agencyManager.audit=1 " +
            "and agencyManager.deleted=false " +
            "order by agencyManager.createTime desc ")
    List<FootballAgencyManager> findAgencyManagerByAgencyAndSearch(FootballAgency agency, String managerName);

//    List<FootballAgencyManager> findAgencyManagerByUser(User user, String cityCode, String countyCode, String search);
}
