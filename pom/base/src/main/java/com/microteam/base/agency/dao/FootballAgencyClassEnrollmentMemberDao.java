package com.microteam.base.agency.dao;

import com.microteam.base.entity.agency.FootballAgencyClassEnrollmentMember;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

public interface FootballAgencyClassEnrollmentMemberDao extends JpaRepository<FootballAgencyClassEnrollmentMember, Long>, JpaSpecificationExecutor<FootballAgencyClassEnrollmentMember> {
    //分页查询用户报名列表
    @Query("from FootballAgencyClassEnrollmentMember as agencyClassEnrollmentMember " +
            "left join fetch agencyClassEnrollmentMember.user as user " +
            "where agencyClassEnrollmentMember.footballAgencyClassEnrollment.id = (:enrollmentId) " +
            "and agencyClassEnrollmentMember.userRole = (:userRole) " +
            "and (user.nickName like (:search) or (:search) = '') " +
            "and agencyClassEnrollmentMember.deleted=false")
    List<FootballAgencyClassEnrollmentMember> findByEnrollmentIdAndRoleAndNickNameForPage(@Param("enrollmentId") Long enrollmentId, @Param("userRole") short userRole, @Param("search") String search, Pageable pageable);

    //根据Id查询
    @Query("from FootballAgencyClassEnrollmentMember as agencyClassEnrollmentMember " +
            "left join fetch agencyClassEnrollmentMember.footballAgencyClassEnrollment as footballAgencyClassEnrollment " +
            "left join fetch agencyClassEnrollmentMember.user as user " +
            "where agencyClassEnrollmentMember.id=?1 ")
    FootballAgencyClassEnrollmentMember findById(long id);

    @Query("from FootballAgencyClassEnrollmentMember as agencyClassEnrollmentMember " +
            "left join fetch agencyClassEnrollmentMember.user as user " +
            "where agencyClassEnrollmentMember.footballAgencyClassEnrollment.id = (:enrollmentId) " +
            "and agencyClassEnrollmentMember.userRole = (:userRole) " +
            "and agencyClassEnrollmentMember.deleted=false")
    List<FootballAgencyClassEnrollmentMember> findByEnrollmentIdAndRoleForPage(@Param("enrollmentId") Long enrollmentId, @Param("userRole") short userRole, Pageable pageable);

    //查询学员申请列表
    @Query("from FootballAgencyClassEnrollmentMember as agencyClassEnrollmentMember " +
            "left join fetch agencyClassEnrollmentMember.user as user " +
            "where agencyClassEnrollmentMember.footballAgencyClassEnrollment.id = (:enrollmentId) " +
            "and agencyClassEnrollmentMember.userRole=1 " +
            "and agencyClassEnrollmentMember.deleted=false ")
    List<FootballAgencyClassEnrollmentMember> findByEnrollmentId(@Param("enrollmentId") Long enrollmentId);
}
