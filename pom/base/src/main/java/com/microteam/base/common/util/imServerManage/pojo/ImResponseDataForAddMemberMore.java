package com.microteam.base.common.util.imServerManage.pojo;


import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import java.util.List;

/**
*  IM response body 里面的data pojo类；
* 
* @param 
* @param 
* @return
*/
@JsonIgnoreProperties(ignoreUnknown = true)
public class ImResponseDataForAddMemberMore implements java.io.Serializable{
/*
	"data" : {
    "newmembers" : [ "5cxhactgdj", "mh2kbjyop1" ],
    "action" : "add_member",
    "groupid" : "1411816013089"
     },
*/	
	

		/**
		 * 
		 */
		
		private List<String> newmembers;
		
		private String action;
		private String groupid;
		
		public ImResponseDataForAddMemberMore() {
		}

		public ImResponseDataForAddMemberMore(List<String> newmembers,
				String action, String groupid) {
			super();
			this.newmembers = newmembers;
			this.action = action;
			this.groupid = groupid;
		}

		public List<String> getNewmembers() {
			return newmembers;
		}

		public void setNewmembers(List<String> newmembers) {
			this.newmembers = newmembers;
		}

		public String getAction() {
			return action;
		}

		public void setAction(String action) {
			this.action = action;
		}

		public String getGroupid() {
			return groupid;
		}

		public void setGroupid(String groupid) {
			this.groupid = groupid;
		}


		
		

		


		
		
}