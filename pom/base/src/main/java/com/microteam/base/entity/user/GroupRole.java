package com.microteam.base.entity.user;


import com.microteam.base.common.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;
import java.io.Serializable;

@Entity
@Table(name = "group_role", uniqueConstraints = @UniqueConstraint(columnNames = "roleName"))
@Getter
@Setter
public class GroupRole extends BaseEntity implements Serializable {

    @Column(name = "roleName", unique = true, nullable = false, length = 50)
    private String roleName;
    @Column(name = "description")
    private String description;

}
