package com.microteam.base.integral.service.impl;

import com.microteam.base.entity.integral.FootballIntegralHistory;
import com.microteam.base.entity.integral.FootballIntegralTeam;
import com.microteam.base.entity.integral.FootballIntegralType;
import com.microteam.base.integral.dao.FootballIntegralTeamDao;
import com.microteam.base.integral.dao.FootballIntegralTypeDao;
import com.microteam.base.integral.service.FootballIntegralHistoryDaoService;
import com.microteam.base.integral.service.FootballIntegralTeamDaoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.sql.Timestamp;
import java.util.List;

@Service(value = "footballIntegralTeamDaoService")
public class FootballIntegralTeamDaoServiceImpl implements FootballIntegralTeamDaoService {

    @Autowired
    private FootballIntegralTeamDao footballIntegralTeamDao;
    @Autowired
    private FootballIntegralHistoryDaoService footballIntegralHistoryDaoService;
    @Autowired
    private FootballIntegralTypeDao footballIntegralTypeDao;

    public FootballIntegralTeam findByTeamId(Long teamId) {
        return footballIntegralTeamDao.findByTeamId(teamId);
    }

    @Override
    public List<FootballIntegralTeam> findByTeamIdList(List<Long> teamIdList) {
        return footballIntegralTeamDao.findByTeamIdList(teamIdList);
    }

//    @Override
//    public int findRangeByTeamId(Long teamId, List<Long> teamIdList, FootballIntegralTeam integralTeam) {
//        List<FootballIntegralHistory> integralHistoryList = footballIntegralHistoryDaoService.findNewByTeamId(teamId, integralTeam.getLastAdd());
//        addNewIntegral(integralHistoryList, integralTeam);
//        if (teamIdList != null && teamIdList.size() > 0) {
//            return footballIntegralTeamDao.findRangeByTeamId(teamId, teamIdList);
//        }
//        return 1;
//    }
//
//    @Override
//    public int addNewIntegral(List<FootballIntegralHistory> integralHistoryList, FootballIntegralTeam integralTeam) {
//
//        int integral = integralTeam.getIntegral();
//        if (integralHistoryList != null && !integralHistoryList.isEmpty()) {
//            Long lastAdd = integralTeam.getLastAdd();
//            for (FootballIntegralHistory integralHistory : integralHistoryList) {
//                Long integralTypeId = integralHistory.getTypeId();
//                FootballIntegralType integralType = footballIntegralTypeDao.findById(integralTypeId).orElse(null);
//                if (integralType != null) {
//                    integral += integralType.getIntegral();
//                }
//                lastAdd = integralHistory.getId();
//            }
//            integralTeam.setLastAdd(lastAdd);
//            integralTeam.setIntegral(integral);
//            footballIntegralTeamDao.save(integralTeam);
//        }
//        return integral;
//    }

    @Override
    public FootballIntegralTeam generalTeamData(Long teamId) throws Exception {
        List<FootballIntegralHistory> integralHistoryList = footballIntegralHistoryDaoService.findByTeamId(teamId);
        int integral = 0;
        Long lastAdd = null;
        for (FootballIntegralHistory integralHistory : integralHistoryList) {
            Long integralTypeId = integralHistory.getTypeId();
            FootballIntegralType integralType = footballIntegralTypeDao.findById(integralTypeId.longValue());
            integral += integralType.getIntegral();
            lastAdd = integralHistory.getId();
        }
        FootballIntegralTeam integralTeam = new FootballIntegralTeam();
        integralTeam.setTeamId(teamId);
        integralTeam.setIntegral(integral);
        integralTeam.setLastAdd(lastAdd);
        integralTeam.setDeleted(false);
        integralTeam.setCreateTime(new Timestamp(System.currentTimeMillis()));
        footballIntegralTeamDao.save(integralTeam);
        return integralTeam;
    }


}
