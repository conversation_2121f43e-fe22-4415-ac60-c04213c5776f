package com.microteam.base.grounds.service.impl;

import com.microteam.base.entity.grounds.FootballGroundDiscount;
import com.microteam.base.grounds.dao.FootballGroundDiscountDao;
import com.microteam.base.grounds.service.FootballGroundDiscountDaoService;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;


@Service("footballGroundDiscountDaoService")
public class FootballGroundDiscountDaoServiceImpl implements FootballGroundDiscountDaoService {
    static Logger logger = Logger.getLogger(FootballGroundDiscountDaoServiceImpl.class.getName());
    @Resource(name = "footballGroundDiscountDao")
    private FootballGroundDiscountDao dao;

//    @Override
//    public List<FootballGroundDiscount> findByDiscountAndCodeForPage(String discount, int page, int pageSize,
//                                                                     String cityCode, String countryCode) {
//        return dao.findByDiscountAndCodeForPage(discount, page, pageSize, cityCode, countryCode);
//    }

    @Override
    public FootballGroundDiscount findById(long id) {
        return dao.findById(id);
    }

//    @Override
//    public List<FootballGroundDiscount> findListDiscountGroundsBySearchContentAndCityOrCounty(String discountType, String cityCode,
//                                                                                              String countryCode, int page, int pageSize, String search) {
//        return dao.findListDiscountGroundsBySearchContentAndCityOrCounty(discountType, cityCode, countryCode, page, pageSize, search);
//    }

}
