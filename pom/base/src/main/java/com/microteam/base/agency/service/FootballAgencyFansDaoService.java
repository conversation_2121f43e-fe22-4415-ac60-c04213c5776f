package com.microteam.base.agency.service;

import com.microteam.base.entity.agency.FootballAgency;
import com.microteam.base.entity.agency.FootballAgencyClass;
import com.microteam.base.entity.agency.FootballAgencyFans;
import com.microteam.base.entity.user.User;

import java.util.List;

public interface FootballAgencyFansDaoService {
    //查询机构下的所有粉丝
    List<FootballAgencyFans> findFansByAgency(FootballAgency footballAgency);

    //判断是否是机构下的粉丝
    List<FootballAgencyFans> findFansByAgencyAndUser(FootballAgency footballAgency, User user);

    //查询机构班级下粉丝数量
    List<FootballAgencyFans> findFansByAgencyAndClass(FootballAgency footballAgency, FootballAgencyClass agencyClass);

    //分页查询机构班级的粉丝申请列表
    List<FootballAgencyFans> findFansByAgencyAndClassForPage(FootballAgency footballAgency, FootballAgencyClass agencyClass, int page, int pageSize);

    //确定用户是不是班级的粉丝
    FootballAgencyFans findFansByAgencyAndClassAndUser(FootballAgency footballAgency, FootballAgencyClass agencyClass, User user);

    //分页查询我关注的机构
//    List<FootballAgencyFans> findFansByUserAndCityCodeAndCountyCodeAndAgencyNameForPage(User user, int page, int pageSize, String cityCode, String countyCode, String search);

    List<FootballAgencyFans> findFansByUser(User user);

    //根据id查询
    FootballAgencyFans findFansById(long id);

    //删除机构下的粉丝
    void delFansByAgency(FootballAgency footballAgency);

}
