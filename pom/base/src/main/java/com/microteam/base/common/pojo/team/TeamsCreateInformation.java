package com.microteam.base.common.pojo.team;


import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class TeamsCreateInformation implements java.io.Serializable {

    private String teamName;
    private String teamAbbreviation;
    private String teamNatureType;
    private String country;
    private String province;
    private String city;
    private String county;
    private int maxusers;
    private short groupStyle; //0=PrivateOnlyownerInvite;1=PrivateMemberCanInvite;2=PublicJoinNeedApproval;3=PublicOpenJoin;
    private String groupRole;
    private String[] members; //群组成员；
    private int isConscribe;//是否招募
    private int isHistory;//是否是历史球队
    private boolean isCampusTeam;//是否是校园足球
    private String vsteamNumber;
    private String telephone;//联系方式
    private String contact;//联系人
    //"isConscribe":1,"teamHistory":1
    private int selfEvaluation;//球队自评 1=较弱;2=一般；3=中等,4=强,5=超强
    private String description;//球队描述
    private String slogan;//球队口号
    private int teamAttr;//球队属性。1:class(班队)，2：grade（年级队）3：department（系队）4：institute（院队）5：school（校队）6:other
    private int favorRules;//擅长赛制:1：three(3人制)，2：sevenornine（7-9人制），3：eleven（11制）,4：other(其他)
    private String teamRecord;//球队记录
    private String teamHonour;//球队荣誉
    private String playerList;//队员名单
    private String playGround;//主要活动场地
    private String playStar;//球星介绍
    private String leader;//队长
    private String coach;//教练
    private String wechatGroup;//微信群
    private long teamCreateTime;//球队创建时间
    private String schoolName;//所属学校
    private int hostColor;//主场队服 1=red；2=green;3=yellow;4=white;5=blue;6=black;7=other;
    private int guestColor;//客场队服1=red；2=green;3=yellow;4=white;5=blue;6=black;7=other;
    private String teamTime;
    private Long agencyId;

}
