package com.microteam.base.lesson.service;


import com.microteam.base.entity.lesson.FootballLessonSecondEnroll;

import java.util.List;

public interface FootballLessonSecondEnrollDaoService {
    List<FootballLessonSecondEnroll> findByLessonIdAndIsGroup(Long lessonId, Integer isGroup);

    FootballLessonSecondEnroll findByUserIdAndTeamIdAndLessonId(Long userId, Long teamId, Long lessonId);

    FootballLessonSecondEnroll findUserIdAndLessonId(Long userId,Long lessonId);

    FootballLessonSecondEnroll findByUserIdAndLessonId(Long userId,Long lessonId);

    List<FootballLessonSecondEnroll> findNotOfUser(List<Long> userList, Long teamId, Long lessonId);

    FootballLessonSecondEnroll save(FootballLessonSecondEnroll enroll);

    List<FootballLessonSecondEnroll> findByTeamIdAndLessonId(Long teamId, Long lessonId);

    List<FootballLessonSecondEnroll> findByLessonId(Long lessonId);

    int deleteByTeamId(Long teamId);
}
