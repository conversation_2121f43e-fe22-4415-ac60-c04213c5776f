package com.microteam.base.demoManager.service.impl;

import com.microteam.base.demoManager.dao.MacQRCodeDao;
import com.microteam.base.demoManager.service.MacQRCodeDaoService;
import com.microteam.base.entity.demoManager.MacQRCode;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service("macQRCodeDaoService")
public class MacQRCodeDaoServiceImpl implements MacQRCodeDaoService {

    @Resource(name = "macQRCodeDao")
    MacQRCodeDao dao;

    @Override
    public List<MacQRCode> findAllCode() {
        return dao.findAllCode();
    }

    @Override
    public MacQRCode findMacQRCodeByMac(String mac) {
        if (mac != null && !mac.equals("")) {
            return dao.findMacQRCode(mac);
        }
        return null;
    }

    @Override
    public MacQRCode findMacQRCodeByCode(String qrCode) {
        if (qrCode != null && !qrCode.equals("")) {
            return dao.findMacQRCodeByCode(qrCode);
        }
        return null;
    }

}
