package com.microteam.base.lesson.service.impl;

import com.microteam.base.entity.lesson.TrainingTools;
import com.microteam.base.lesson.dao.TrainingToolsDao;
import com.microteam.base.lesson.service.TrainingToolsDaoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service("trainingToolsDaoService")
public class TrainingToolsDaoServiceImpl implements TrainingToolsDaoService {

    @Autowired
    private TrainingToolsDao dao;

    @Override
    public List<TrainingTools> findByIdList(List<Long> idList) {
        return dao.findByIdList(idList);
    }

    //获取训所有训练工具
    @Override
    public List<TrainingTools> findAllTrainingTools() {
        return dao.findAllTrainingTools();
    }

    @Override
    public List<TrainingTools> findByCourwareId(Long courwareId) {
        return dao.findByCourwareId(courwareId);
    }

    @Override
    public List<TrainingTools> findByLessonId(Long lessonId) {
        return dao.findByLessonId(lessonId);
    }

    @Override
    public TrainingTools findByName(String name) {
        return dao.findByName(name);
    }

    @Override
    public TrainingTools findById(Long id) {
        return dao.findByIds(id);
    }
}
