/*
package com.microteam.base.common.filter.exception;

import com.microteam.base.common.mail.MailService;
import com.microteam.base.common.mail.MtAdmin;
import com.microteam.base.common.mail.MtAdminDao;
import com.microteam.base.common.pojo.MtJavaServerResponseBodyPojo;
import com.microteam.base.common.pojo.MtJavaServerResponseBodyResultPojo;
import com.microteam.base.common.util.common.CommonUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.config.annotation.EnableWebMvc;

import javax.servlet.http.HttpServletRequest;
import java.io.ByteArrayOutputStream;
import java.io.PrintStream;
import java.io.UnsupportedEncodingException;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;

@ControllerAdvice
@EnableWebMvc
public class ExceptionFilter {
    private static final Logger logger = LoggerFactory.getLogger(ExceptionFilter.class);

    @Autowired
    private MailService mailService;
    @Autowired
    private MtAdminDao mtAdminDao;
    @Value("${custom.server-ip}")
    private String serverIP;
    @Value("${custom.application-name}")
    private String applicationName;
    @Value("${custom.isMailEnable}")
    private boolean isMailEnable;

    @ExceptionHandler(value = Exception.class)
    @ResponseBody
    public MtJavaServerResponseBodyPojo doException(Exception e, HttpServletRequest request) throws UnsupportedEncodingException {
        Long createTimeEnd;
        MtJavaServerResponseBodyPojo responsePojo = (MtJavaServerResponseBodyPojo) request.getAttribute("responseBodyPojo");
        if (responsePojo == null) {
            responsePojo = new MtJavaServerResponseBodyPojo();
        }
        Long createTimeStart = Long.parseLong(request.getHeader("createTimeStart"));
        createTimeEnd = System.currentTimeMillis();
        MtJavaServerResponseBodyResultPojo resultPojo = new MtJavaServerResponseBodyResultPojo();
        resultPojo.setCode("9999");
        resultPojo.setMessage("System Exception Error.");
        String resultString = CommonUtil.changeResultToString(resultPojo);
        responsePojo.setResult(resultString);
        responsePojo.setDuration(createTimeEnd - createTimeStart);
        if (e != null) {
            logger.error("全局异常", e);
            if (isMailEnable) {
                // 发送邮件
                ByteArrayOutputStream out = new ByteArrayOutputStream();
                PrintStream pout = new PrintStream(out, false, "UTF-8");
                e.printStackTrace(pout);
                String ret = new String(out.toByteArray(), StandardCharsets.UTF_8);
                pout.close();
                try {
                    out.close();
                } catch (Exception ex) {
                    ex.printStackTrace();
                }
                ret = applicationName + '\n' + ret;
                sendMail(0, serverIP + "=====" + applicationName, ret);
            }
        }
        return responsePojo;
    }

    private void sendMail(int level, String subject, String message) {
        List<MtAdmin> mtAdminList = mtAdminDao.findAllByLevelAfter(level);
//        String[] to;
        List<String> to = new ArrayList<>();
        for (MtAdmin mtAdmin : mtAdminList) {
            to.add(mtAdmin.getEmail());
        }
        String[] toArray = to.toArray(new String[0]);
        mailService.sendSimpleMail(toArray, subject, message);
    }
}
*/
