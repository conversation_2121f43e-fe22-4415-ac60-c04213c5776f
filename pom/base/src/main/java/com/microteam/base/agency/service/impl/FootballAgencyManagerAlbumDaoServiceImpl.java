package com.microteam.base.agency.service.impl;

import com.microteam.base.agency.dao.FootballAgencyManagerAlbumDao;
import com.microteam.base.entity.agency.FootballAgencyManager;
import com.microteam.base.entity.agency.FootballAgencyManagerAlbum;
import com.microteam.base.agency.service.FootballAgencyManagerAlbumDaoService;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service("footballAgencyManagerAlbumDaoService")
public class FootballAgencyManagerAlbumDaoServiceImpl implements FootballAgencyManagerAlbumDaoService {
    static Logger logger = Logger.getLogger(FootballAgencyManagerAlbumDaoServiceImpl.class.getName());
    @Autowired
    private FootballAgencyManagerAlbumDao dao;

    public FootballAgencyManagerAlbumDaoServiceImpl() {
        super();

    }

    @Override
    public List<FootballAgencyManagerAlbum> findAlbumByManager(
            FootballAgencyManager agencyManager) {

        return dao.findAlbumByManager(agencyManager);
    }


}
