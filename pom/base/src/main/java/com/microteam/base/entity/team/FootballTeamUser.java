package com.microteam.base.entity.team;


import com.microteam.base.common.base.BaseEntity;
import com.microteam.base.entity.user.User;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

@Entity
@Table(name = "football_team_user")
@Getter
@Setter
public class FootballTeamUser extends BaseEntity implements Serializable {
    @ManyToOne
    @JoinColumn(name = "teamId", nullable = false)
    private FootballTeam footballTeam;
    @ManyToOne
    @JoinColumn(name = "teamMemberRoleId", nullable = false)
    private FootballTeamMemberRole footballTeamMemberRole;
    @ManyToOne
    @JoinColumn(name = "userId", nullable = false)
    private User user;
    @Column(name = "joinMode", nullable = false)
    private short joinMode;
    @Column(name = "joinMessage", length = 100)
    private String joinMessage;
    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "joinTime", length = 19)
    private Date joinTime;
    @Column(name = "isLeaved")
    private Boolean isLeaved;
    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "leaveTime", length = 19)
    private Date leaveTime;
    @Column(name = "leavedMessage", length = 100)
    private String leavedMessage;
    @Column(name = "kickPersonId")
    private Long kickPersonId;
    @Column(name = "kickMessage", length = 100)
    private String kickMessage;
    @Column(name = "joinedMatch")
    private String joinedMatch;
    @Column(name = "teamPosition")
    private Short teamPosition;
    @Column(name = "teamNumber")
    private Integer teamNumber;
    @Column(name = "audit", nullable = false)
    private int audit;
    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "auditTime", length = 19)
    private Date auditTime;
    @Column(name = "isFollowed")
    private Boolean isFollowed;

    public Date getJoinTime() {
        if (this.joinTime == null) {
            return null;
        }
        return (Date) this.joinTime.clone();
    }

    public void setJoinTime(Date joinTime) {
        this.joinTime = (Date) joinTime.clone();
    }

    public Date getLeaveTime() {
        if (this.leaveTime == null) {
            return null;
        }
        return (Date) this.leaveTime.clone();
    }

    public void setLeaveTime(Date leaveTime) {
        this.leaveTime = (Date) leaveTime.clone();
    }

    public Date getAuditTime() {
        if (this.auditTime == null) {
            return null;
        }
        return (Date) this.auditTime.clone();
    }

    public void setAuditTime(Date auditTime) {
        this.auditTime = (Date) auditTime.clone();
    }

}
