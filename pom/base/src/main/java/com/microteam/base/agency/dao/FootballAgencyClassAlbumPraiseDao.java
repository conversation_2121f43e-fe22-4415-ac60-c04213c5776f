package com.microteam.base.agency.dao;

import com.microteam.base.entity.agency.FootballAgencyClassAlbumPraise;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

public interface FootballAgencyClassAlbumPraiseDao extends JpaRepository<FootballAgencyClassAlbumPraise, Long>, JpaSpecificationExecutor<FootballAgencyClassAlbumPraise> {
    //查询班级相册点赞
    @Query("from FootballAgencyClassAlbumPraise as footballAgencyClassAlbumPraise " +
            "where footballAgencyClassAlbumPraise.footballAgencyClassAlbum.id = (:agencyClassAlbumId) " +
            "and footballAgencyClassAlbumPraise.isPraised=true " +
            "and footballAgencyClassAlbumPraise.deleted=false ")
    List<FootballAgencyClassAlbumPraise> findByAgencyClassAlbumId(@Param("agencyClassAlbumId") Long agencyClassAlbumId);

    //查询用户对相册的点赞
    @Query("from FootballAgencyClassAlbumPraise as footballAgencyClassAlbumPraise " +
            "where footballAgencyClassAlbumPraise.footballAgencyClassAlbum.id = (:agencyClassAlbumId) " +
            "and footballAgencyClassAlbumPraise.user.id = (:userId) " +
            "and footballAgencyClassAlbumPraise.deleted=false ")
    FootballAgencyClassAlbumPraise findByAgencyClassAlbumIdAndUserId(@Param("agencyClassAlbumId") Long agencyClassAlbumId, @Param("userId") Long userId);
}
