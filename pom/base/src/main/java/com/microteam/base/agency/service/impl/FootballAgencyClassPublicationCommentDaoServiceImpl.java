package com.microteam.base.agency.service.impl;

import com.microteam.base.agency.dao.FootballAgencyClassPublicationCommentDao;
import com.microteam.base.agency.service.FootballAgencyClassPublicationCommentDaoService;
import com.microteam.base.entity.agency.FootballAgencyClassPublicationComment;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.List;

@Service("footballAgencyClassPublicationCommentDaoService")
public class FootballAgencyClassPublicationCommentDaoServiceImpl implements FootballAgencyClassPublicationCommentDaoService {
    static Logger logger = Logger.getLogger(FootballAgencyClassPublicationCommentDaoServiceImpl.class.getName());
    @Autowired
    private FootballAgencyClassPublicationCommentDao dao;

    @Override
    public List<FootballAgencyClassPublicationComment> findByPublicationId(Long publicationId) {
        return dao.findByPublicationId(publicationId);
    }

    @Override
    public List<FootballAgencyClassPublicationComment> findByPublicationIdForPage(Long publicationId, int page, int pageSize) {
        if (page > 0) {
            page--;
        }
        Pageable pageable = PageRequest.of(page, pageSize);
        return this.dao.findByPublicationIdForPage(publicationId, pageable);
    }

}
