package com.microteam.base.entity.agency;


import com.microteam.base.common.base.BaseEntity;
import com.microteam.base.entity.user.User;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;
import java.io.Serializable;

@Entity
@Table(name = "football_agency_manager")
@Getter
@Setter
public class FootballAgencyManager extends BaseEntity implements Serializable {

    @ManyToOne
    @JoinColumn(name = "agencyId", nullable = false)
    private FootballAgency footballAgency;
    @ManyToOne
    @JoinColumn(name = "courseId")
    private FootballAgencyClassCourse footballAgencyClassCourse;
    @ManyToOne
    @JoinColumn(name = "userId", nullable = false)
    private User user;
    @ManyToOne
    @JoinColumn(name = "classId")
    private FootballAgencyClass footballAgencyClass;
    @Column(name = "managerName", length = 100)
    private String managerName;
    @Column(name = "userRole")
    private Short userRole;
    @Column(name = "audit")
    private Short audit;
    @Column(name = "headImgNetUrl", length = 250)
    private String headImgNetUrl;
    @Column(name = "headImgUrl", length = 250)
    private String headImgUrl;
    @Column(name = "headImgName", length = 50)
    private String headImgName;
    @Column(name = "headImgLength")
    private Long headImgLength;
    @Column(name = "birthDay", length = 100)
    private String birthDay;
    @Column(name = "resume", length = 65535)
    private String resume;
    @Column(name = "duty", length = 100)
    private String duty;

}
