package com.microteam.base.agency.service;

import com.microteam.base.entity.agency.FootballAgencyClass;

import java.util.List;

public interface FootballAgencyClassDaoService {
    //查询机构下的班级
    List<FootballAgencyClass> findByAgencyId(Long agencyId);

    //查询机构下的历史班级
    List<FootballAgencyClass> findHistoryByAgencyId(Long agencyId, int page, int pageSize, String searchType);

    //根据id查询班级
    FootballAgencyClass findById(long id);
}
