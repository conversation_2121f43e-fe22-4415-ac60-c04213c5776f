package com.microteam.base.lesson.service;


import com.microteam.base.common.pojo.LessonSummaryAverage;
import com.microteam.base.common.pojo.lesson.LessonData;
import com.microteam.base.common.pojo.team.FootTeamballPassballAverage;
import com.microteam.base.entity.lesson.FootballLessonStatisticsPlayer;

import java.util.List;

public interface FootballLessonStatisticsPlayerDaoService {

    FootballLessonStatisticsPlayer findByUserIdAndLessonId(Long userId, Long lessonId);

    boolean delByUserIdAndLessonId(Long userId, Long lessonId);

    List<FootballLessonStatisticsPlayer> findByLessonId(Long lessonId);

    List<FootballLessonStatisticsPlayer> findByLessonIdAndUserIdList(Long lessonId, List<Long> userIdList);

    LessonData getDataByLessonIdAndTeamId(Long lessonId, Long teamId);

    LessonData getDataByTeamId(Long teamId);

    List<FootballLessonStatisticsPlayer> findByLessonIdAndTeamId(Long lessonId, Long teamId);

    List<FootballLessonStatisticsPlayer> findByTeamId(Long teamId);

    FootballLessonStatisticsPlayer findByTeamIdAndUserIdAndLessonId(Long teamId, Long lessonId, Long userId);

    //获取训练课程传球平均数
    FootTeamballPassballAverage findAveragePassBallByUserId(Long userId);

    //获取训练课程概要平均数
    LessonSummaryAverage getLessonSummaryAverage(long userId);

    //获取传球分析
    FootballLessonStatisticsPlayer getLessonPassballAnalysis(long lessonId, long teamId, long userId);

    int deleteByTeamId(Long teamId);
}
