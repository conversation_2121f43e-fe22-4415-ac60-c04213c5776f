package com.microteam.base.agency.dao;

import com.microteam.base.entity.agency.FootballAgency;
import com.microteam.base.entity.agency.FootballAgencyEvaluate;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

public interface FootballAgencyEvaluateDao extends JpaRepository<FootballAgencyEvaluate, Long>, JpaSpecificationExecutor<FootballAgencyEvaluate> {
    //分页查询机构评价
    @Query("from FootballAgencyEvaluate as agencyEvaluate " +
            "left join fetch agencyEvaluate.user as user " +
            "where agencyEvaluate.footballAgency=?1 " +
            "and agencyEvaluate.deleted=false ")
    List<FootballAgencyEvaluate> findEvaluateByAgency(FootballAgency agency, Pageable pageable);
}
