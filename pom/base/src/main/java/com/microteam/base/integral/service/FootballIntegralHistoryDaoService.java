package com.microteam.base.integral.service;


import com.microteam.base.entity.integral.FootballIntegralHistory;

import java.util.List;

public interface FootballIntegralHistoryDaoService {

    List<FootballIntegralHistory> findAll();

    List<FootballIntegralHistory> findByTeamId(Long teamId) throws Exception;

    List<FootballIntegralHistory> findByTeamIdAndTypeId(Long teamId, Long typeId);

    List<FootballIntegralHistory> generalTeamData(Long teamId) throws Exception;

    List<FootballIntegralHistory> findNewByTeamId(Long teamId, Long lastAdd);

    FootballIntegralHistory save(FootballIntegralHistory footballIntegralHistory);
}
