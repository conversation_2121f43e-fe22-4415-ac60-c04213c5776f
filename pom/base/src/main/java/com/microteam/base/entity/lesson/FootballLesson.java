package com.microteam.base.entity.lesson;


import com.microteam.base.common.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

@Entity
@Table(name = "football_lesson")
@Getter
@Setter
public class FootballLesson extends BaseEntity implements Serializable {
    @Column(name = "lessonName", length = 100)
    private String lessonName;
    @Column(name = "userId", nullable = false)
    private Long userId;
    @Column(name = "openness", nullable = false)
    private Integer openness;
    @Column(name = "status", nullable = false)
    private Integer status;
    @Column(name = "duration")
    private Integer duration;
    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "startTime", nullable = false, length = 19)
    private Date startTime;
    @Column(name = "content", length = 500)
    private String content;
    @Column(name = "timeSlot", length = 250)
    private String timeSlot;
    @Column(name = "needHomework")
    private boolean needHomework;
    @Column(name = "seen", nullable = false)
    private boolean seen;
    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "publishTime", nullable = false, length = 19)
    private Date publishTime;
    private boolean needHandRecord;
    private boolean needTrainingTool;
    private boolean needGroupMatch;
    @Column(name = "overLook")
    private boolean ignore;

    public Date getStartTime() {
        if (this.startTime == null) {
            return null;
        }
        return (Date) startTime.clone();
    }

    public void setStartTime(Date startTime) {
        this.startTime = (Date) startTime.clone();
    }

    public Date getPublishTime() {
        if (this.publishTime == null) {
            return null;
        }
        return (Date) publishTime.clone();
    }

    public void setPublishTime(Date publishTime) {
        this.publishTime = (Date) publishTime.clone();
    }

}
