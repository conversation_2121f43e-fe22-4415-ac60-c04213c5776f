package com.microteam.base.agency.service.impl;

import com.microteam.base.agency.dao.FootballAgencyCoachResumeAlbumDao;
import com.microteam.base.entity.agency.FootballAgencyCoach;
import com.microteam.base.entity.agency.FootballAgencyCoachResumeAlbum;
import com.microteam.base.agency.service.FootballAgencyCoachResumeAlbumDaoService;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service("footballAgencyCoachResumeAlbumDaoService")
public class FootballAgencyCoachResumeAlbumDaoServiceImpl implements FootballAgencyCoachResumeAlbumDaoService {
    static Logger logger = Logger.getLogger(FootballAgencyCoachResumeAlbumDaoServiceImpl.class.getName());
    @Autowired
    private FootballAgencyCoachResumeAlbumDao dao;

    public FootballAgencyCoachResumeAlbumDaoServiceImpl() {
        super();

    }

    @Override
    public List<FootballAgencyCoachResumeAlbum> findCoachResumeAlbumList(
            FootballAgencyCoach agencyCoach) {

        return dao.findCoachResumeAlbumList(agencyCoach);
    }


}
