package com.microteam.base.agency.service;


import com.microteam.base.entity.agency.FootballAgencyClassEnrollmentMember;

import java.util.List;

public interface FootballAgencyClassEnrollmentMemberDaoService {
    //分页查询用户报名列表
    List<FootballAgencyClassEnrollmentMember> findByEnrollmentIdAndRoleAndNickNameForPage(Long enrollmentId, short userRole, int page, int pageSize, String search);

    //根据Id查询
    FootballAgencyClassEnrollmentMember findById(long id);

    //分页查询用户报名列表
    List<FootballAgencyClassEnrollmentMember> findByEnrollmentIdAndRoleForPage(Long enrollmentId, short userRole, int page, int pageSize);

    //查询学员申请列表
    List<FootballAgencyClassEnrollmentMember> findByEnrollmentId(Long enrollmentId);
}
