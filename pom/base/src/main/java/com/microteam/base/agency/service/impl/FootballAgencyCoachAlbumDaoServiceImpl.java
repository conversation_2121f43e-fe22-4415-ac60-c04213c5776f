package com.microteam.base.agency.service.impl;

import com.microteam.base.agency.dao.FootballAgencyCoachAlbumDao;
import com.microteam.base.entity.agency.FootballAgencyCoachAlbum;
import com.microteam.base.agency.service.FootballAgencyCoachAlbumDaoService;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service("footballAgencyCoachAlbumDaoService")
public class FootballAgencyCoachAlbumDaoServiceImpl implements FootballAgencyCoachAlbumDaoService {
    static Logger logger = Logger.getLogger(FootballAgencyCoachAlbumDaoServiceImpl.class.getName());
    @Autowired
    private FootballAgencyCoachAlbumDao dao;

    @Override
    public List<FootballAgencyCoachAlbum> findByAgencyCoachId(Long agencyCoachId) {
        return dao.findByAgencyCoachId(agencyCoachId);
    }


}
