package com.microteam.base.lesson.service;


import com.microteam.base.entity.lesson.FootballLessonExhibition;

import java.util.List;

public interface FootballLessonExhibitionDaoService {
    int updateLessonStatus(Long lessonId);

    FootballLessonExhibition findByLessonId(Long lessonId,Long userId);

    List<FootballLessonExhibition> findByExLessonId(Long lessonId);

    FootballLessonExhibition save(FootballLessonExhibition footballLessonExhibition);


}
