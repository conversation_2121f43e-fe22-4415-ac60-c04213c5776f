package com.microteam.base.entity.team;

import com.microteam.base.common.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.io.Serializable;

@Entity
@Table(name = "football_team_user_datatype")
@Getter
@Setter
public class FootballTeamUserDataType extends BaseEntity implements Serializable {

    @Column(name = "userId", nullable = false)
    private Long userId;
    @Column(name = "teamId", nullable = false)
    private Long teamId;
    @Column(name = "teamDataArray")
    private String teamDataArray;

}
