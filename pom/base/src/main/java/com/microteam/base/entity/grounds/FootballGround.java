package com.microteam.base.entity.grounds;


import com.microteam.base.common.base.BaseEntity;
import com.microteam.base.entity.user.Groups;
import com.microteam.base.entity.user.User;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

@Entity
@Table(name = "football_ground")
@Getter
@Setter
public class FootballGround extends BaseEntity implements Serializable {

    @ManyToOne
    @JoinColumn(name = "groundTypeId", nullable = false)
    private FootballGroundType footballGroundType;
    @ManyToOne
    @JoinColumn(name = "creator", nullable = false)
    private User user;
    @ManyToOne
    @JoinColumn(name = "groupId", nullable = false)
    private Groups groups;
    @Column(name = "groundName", nullable = false, length = 50)
    private String groundName;
    @Column(name = "groundSize", length = 50)
    private String groundSize;
    @Column(name = "credits")
    private Integer credits;
    @Column(name = "creditsRank")
    private String creditsRank;
    @Column(name = "charge", length = 100)
    private String charge;
    @Column(name = "description")
    private String description;
    @Column(name = "openTime", length = 100)
    private String openTime;
    @Column(name = "groundHeadImgNeturl", length = 250)
    private String groundHeadImgNeturl;
    @Column(name = "groundHeadImgUrl", length = 250)
    private String groundHeadImgUrl;
    @Column(name = "groundHeadImgName", length = 50)
    private String groundHeadImgName;
    @Column(name = "groundHeadImgLength")
    private Long groundHeadImgLength;
    @Column(name = "groundBackImgNeturl", length = 250)
    private String groundBackImgNeturl;
    @Column(name = "groundBackImgUrl", length = 250)
    private String groundBackImgUrl;
    @Column(name = "groundBackImgName", length = 50)
    private String groundBackImgName;
    @Column(name = "groundBackImgLength")
    private Long groundBackImgLength;
    @Column(name = "countryCode")
    private String countryCode;
    @Column(name = "provinceCode")
    private String provinceCode;
    @Column(name = "cityCode")
    private String cityCode;
    @Column(name = "countyCode")
    private String countyCode;
    @Column(name = "longitude")
    private String longitude;
    @Column(name = "latitude")
    private String latitude;
    @Column(name = "contact")
    private String contact;
    @Column(name = "telephone")
    private String telephone;
    @Column(name = "groundNumber")
    private Integer groundNumber;
    @Column(name = "location")
    private String location;
    @Column(name = "busline")
    private String busline;
    @Column(name = "audit", nullable = false)
    private short audit;
    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "auditTime")
    private Date auditTime;
    @Column(name = "discountType")
    private String discountType;
    @Column(name = "enabled", nullable = false)
    private boolean enabled;

    public Date getAuditTime() {
        if (this.auditTime == null) {
            return null;
        }
        return (Date) this.auditTime.clone();
    }

    public void setAuditTime(Date auditTime) {
        this.auditTime = (Date) auditTime.clone();
    }

}
