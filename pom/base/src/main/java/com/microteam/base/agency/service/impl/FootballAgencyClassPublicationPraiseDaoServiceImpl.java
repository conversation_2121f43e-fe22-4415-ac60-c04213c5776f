package com.microteam.base.agency.service.impl;

import com.microteam.base.agency.dao.FootballAgencyClassPublicationPraiseDao;
import com.microteam.base.entity.agency.FootballAgencyClassPublicationPraise;
import com.microteam.base.agency.service.FootballAgencyClassPublicationPraiseDaoService;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service("footballAgencyClassPublicationPraiseDaoService")
public class FootballAgencyClassPublicationPraiseDaoServiceImpl implements FootballAgencyClassPublicationPraiseDaoService {
    static Logger logger = Logger.getLogger(FootballAgencyClassPublicationPraiseDaoServiceImpl.class.getName());
    @Autowired
    private FootballAgencyClassPublicationPraiseDao dao;

    @Override
    public List<FootballAgencyClassPublicationPraise> findByPublicationId(Long publicationId){
        return dao.findByPublicationId(publicationId);
    }

    @Override
    public FootballAgencyClassPublicationPraise findByPublicationIdAndUserId(Long publicationId, Long userId) {
        return dao.findByPublicationIdAndUserId(publicationId, userId);
    }


}
