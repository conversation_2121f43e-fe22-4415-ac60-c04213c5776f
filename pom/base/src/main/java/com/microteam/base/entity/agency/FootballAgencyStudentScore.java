package com.microteam.base.entity.agency;


import com.microteam.base.common.base.BaseEntity;
import com.microteam.base.entity.user.User;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;
import java.io.Serializable;

@Entity
@Table(name = "football_agency_student_score")
@Getter
@Setter
public class FootballAgencyStudentScore extends BaseEntity implements Serializable {

    @ManyToOne
    @JoinColumn(name = "courseId", nullable = false)
    private FootballAgencyClassCourse footballAgencyClassCourse;
    @ManyToOne
    @JoinColumn(name = "scoredId", nullable = false)
    private User user;
    @ManyToOne
    @JoinColumn(name = "agencyStudentId", nullable = false)
    private FootballAgencyStudent footballAgencyStudent;
    @Column(name = "courseScore")
    private Integer courseScore;
    @Column(name = "courseComment", length = 65535)
    private String courseComment;

}
