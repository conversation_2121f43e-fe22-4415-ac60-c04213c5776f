package com.microteam.base.integral.dao;

import com.microteam.base.entity.integral.FootballIntegralTeam;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

public interface FootballIntegralTeamDao extends JpaRepository<FootballIntegralTeam, Long>, JpaSpecificationExecutor<FootballIntegralTeam> {

    @Query("from FootballIntegralTeam as integralTeam " +
            "where integralTeam.teamId = (:teamId) " +
            "and integralTeam.deleted = false " +
            "order by integralTeam.updateTime desc")
    FootballIntegralTeam findByTeamId(@Param("teamId") Long teamId);

    @Query("from FootballIntegralTeam as integralTeam " +
            "where integralTeam.teamId in (:teamIdList) " +
            "and integralTeam.deleted = false " +
            "order by integralTeam.updateTime desc")
    List<FootballIntegralTeam> findByTeamIdList(@Param("teamIdList") List<Long> teamIdList);

    @Query(value = "select rownum" +
            "from (" +
            "select *,@rownum:=@rownum+1 as rownum" +
            "from football_integral_team a,(select @rownum:=0) b" +
            "where a.teamId in (:teamIdList)" +
            "order by a.integral desc,a.updateTime" +
            ") c" +
            "where c.teamId = (:teamId)", nativeQuery = true)
    int findRangeByTeamId(@Param("teamId") Long teamId, @Param("teamIdList") List<Long> teamIdList);

    @Query("from FootballIntegralTeam as integralTeam " +
            "where integralTeam.id = (:id) " +
            "and integralTeam.deleted = false " +
            "order by integralTeam.updateTime desc")
    FootballIntegralTeam findById(@Param("id") long id);

}
