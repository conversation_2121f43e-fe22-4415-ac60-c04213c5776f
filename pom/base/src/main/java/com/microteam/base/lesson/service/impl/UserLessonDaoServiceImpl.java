package com.microteam.base.lesson.service.impl;

import com.microteam.base.entity.lesson.UserLesson;
import com.microteam.base.lesson.dao.UserLessonDao;
import com.microteam.base.lesson.service.UserLessonDaoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Calendar;
import java.util.Date;
import java.util.List;

@Service("userLessonDaoService")
public class UserLessonDaoServiceImpl implements UserLessonDaoService {


    @Autowired
    private UserLessonDao dao;

    @Override
    public UserLesson findByUserIdAndLessonId(long userId, long lessonId) {
        return dao.findByUserIdAndLessonId(userId, lessonId);
    }

    @Override
    public List<UserLesson> findByUserId(long userId) {
        return dao.findByUserId(userId);
    }

    @Override
    public List<UserLesson> findChosenByUserId(long userId) {
        return dao.findChosenByUserId(userId);
    }

    @Override
    public List<UserLesson> findChosenByUserIdForToday(Long userId) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date());
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        Date zero = calendar.getTime();
        return dao.findChosenByUserIdForToday(userId, zero);
    }
}
