package com.microteam.base.lesson.service;


import com.microteam.base.entity.lesson.FootballLesson;
import com.microteam.base.entity.lesson.FootballLessonDataType;

import java.util.List;

public interface FootballLessonDataTypeDaoService {

    List<FootballLessonDataType> findByLessonId(Long lessonId);

    List<FootballLessonDataType> findByLessonIdList(List<FootballLesson> lessonList) throws Exception;

    List<FootballLessonDataType> saveAll(List<FootballLessonDataType> list);

    List<FootballLessonDataType> findByCourwareId(Long courwareId);
}
