//package com.microteam.base.agency.dao.impl;
//
//import com.microteam.base.agency.dao.FootballAgencyClassAlbumPraiseDao;
//import com.microteam.base.entity.agency.FootballAgencyClassAlbumPraise;
//import com.microteam.base.common.base.AbstractHibernateDao;
//import org.apache.log4j.Logger;
//import org.hibernate.query.Query;
//import org.springframework.stereotype.Repository;
//
//import java.util.List;
//
//@Repository("footballAgencyClassAlbumPraiseDao")
//public class FootballAgencyClassAlbumPraiseDaoImpl extends AbstractHibernateDao<FootballAgencyClassAlbumPraise> implements FootballAgencyClassAlbumPraiseDao {
//    static Logger logger = Logger.getLogger(FootballAgencyClassAlbumPraiseDaoImpl.class.getName());
//
//    public FootballAgencyClassAlbumPraiseDaoImpl() {
//        super();
//        setClazz(FootballAgencyClassAlbumPraise.class);
//    }
//
//    //查询班级相册点赞
//    @Override
//    public List<FootballAgencyClassAlbumPraise> findByAgencyClassAlbumId(Long agencyClassAlbumId) {
//        String hql = "from FootballAgencyClassAlbumPraise as footballAgencyClassAlbumPraise " +
//                "where footballAgencyClassAlbumPraise.footballAgencyClassAlbum.id = (:agencyClassAlbumId) " +
//                "and footballAgencyClassAlbumPraise.isPraised=true " +
//                "and footballAgencyClassAlbumPraise.deleted=false ";
//        Query query = getCurrentSession().createQuery(hql)
//                .setParameter("agencyClassAlbumId", agencyClassAlbumId);
//        List<FootballAgencyClassAlbumPraise> list = query.list();
//        return list;
//    }
//
//    //查询用户对相册的点赞
//    @Override
//    public FootballAgencyClassAlbumPraise findByAgencyClassAlbumIdAndUserId(Long agencyClassAlbumId, Long userId) {
//        String hql = "from FootballAgencyClassAlbumPraise as footballAgencyClassAlbumPraise " +
//                "where footballAgencyClassAlbumPraise.footballAgencyClassAlbum.id = (:agencyClassAlbumId) " +
//                "and footballAgencyClassAlbumPraise.user.id = (:userId) " +
//                "and footballAgencyClassAlbumPraise.deleted=false ";
//        Query query = getCurrentSession().createQuery(hql)
//                .setParameter("agencyClassAlbumId", agencyClassAlbumId)
//                .setParameter("userId", userId);
//        List<FootballAgencyClassAlbumPraise> list = query.list();
//        if (list != null && list.size() > 0) {
//            return list.get(0);
//        }
//        return null;
//    }
//
//}
