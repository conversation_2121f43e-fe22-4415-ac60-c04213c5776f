package com.microteam.base.entity.match;


import com.microteam.base.common.base.BaseEntity;
import com.microteam.base.entity.team.FootballTeam;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;
import java.io.Serializable;

@Entity
@Table(name = "football_team_match_enroll")
@Getter
@Setter
public class FootballTeamMatchEnroll extends BaseEntity implements Serializable {
    @ManyToOne
    @JoinColumn(name = "teamId", nullable = false)
    private FootballTeam footballTeam;
    @ManyToOne
    @JoinColumn(name = "matchId", nullable = false)
    private FootballTeamMatch footballTeamMatch;
    @Column(name = "allowedEnroll")
    private Boolean allowedEnroll;

}
