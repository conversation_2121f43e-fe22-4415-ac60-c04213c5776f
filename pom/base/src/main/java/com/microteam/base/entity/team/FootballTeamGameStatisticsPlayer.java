package com.microteam.base.entity.team;


import com.microteam.base.common.base.BaseEntity;
import com.microteam.base.entity.user.User;
import com.microteam.base.entity.user.UserHardwarePractice;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

@Entity
@Table(name = "football_team_game_statistics_player")
@Getter
@Setter
public class FootballTeamGameStatisticsPlayer extends BaseEntity implements Serializable {

    @ManyToOne
    @JoinColumn(name = "teamId")
    private FootballTeam footballTeam;
    @ManyToOne
    @JoinColumn(name = "teamGameId")
    private FootballTeamGame footballTeamGame;
    @ManyToOne
    @JoinColumn(name = "userId")
    private User user;
    @ManyToOne
    @JoinColumn(name = "hardwarePracticeId")
    private UserHardwarePractice userHardwarePractice;
    @Column(name = "identification", length = 100)
    private String identification;
    @Column(name = "touchCounts")
    private Integer touchCounts;       //触球次数
    @Column(name = "passBallCounts")
    private Integer passBallCounts;        //传求次数
    @Column(name = "leftPassBallCounts")
    private Integer leftPassBallCounts;    //左脚传球次数
    @Column(name = "rightPassBallCounts")
    private Integer rightPassBallCounts;//右脚传球次数
    @Column(name = "oneFootPassCount")
    private Integer oneFootPassCount;    //一脚传球
    @Column(name = "twoFootPassCount")
    private Integer twoFootPassCount;    //两脚传球
    @Column(name = "longPass")
    private Integer longPass;            //长传
    @Column(name = "shortPass")
    private Integer shortPass;            //短传
    @Column(name = "carryCount")
    private Integer carryCount;            //带球次数
    @Column(name = "carryTime")
    private Integer carryTime;            //控球时间
    @Column(name = "carryDistance")
    private Integer carryDistance;        //带球距离
    @Column(name = "highMoveCount")
    private Integer highMoveCount;        //高速跑动次数
    @Column(name = "highMoveDistance")
    private Long highMoveDistance;        //高速跑动距离
    @Column(name = "highMoveCalorie", length = 65535)
    private String highMoveCalorie;        //高速跑动卡路里
    @Column(name = "midMoveCount")
    private Integer midMoveCount;        //中速跑动次数
    @Column(name = "midMoveDistance")
    private Long midMoveDistance;        //中速跑动距离
    @Column(name = "midMoveCalorie", length = 65535)
    private String midMoveCalorie;        //卡路里
    @Column(name = "lowMoveCount")
    private Integer lowMoveCount;        //低
    @Column(name = "lowMoveDistance")
    private Long lowMoveDistance;
    @Column(name = "lowMoveCalorie", length = 65535)
    private String lowMoveCalorie;
    @Column(name = "normalMoveCount")
    private Integer normalMoveCount;    //走
    @Column(name = "normalMoveDistance")
    private Long normalMoveDistance;
    @Column(name = "normalMoveCalorie", length = 65535)
    private String normalMoveCalorie;
    @Column(name = "wholeMoveDistance")
    private Long wholeMoveDistance;            //全场跑动距离
    @Column(name = "moveCalorie")
    private Long moveCalorie;                    //运动卡路里
    @Column(name = "calorieCurveData", length = 65535)
    private String calorieCurveData;            //卡路里曲线
    @Column(name = "shoots")
    private Integer shoots;                        //射门
    @Column(name = "assists")
    private Integer assists;                    //助攻
    @Column(name = "highCarryDistance")
    private Integer highCarryDistance;     //高速带球距离
    @Column(name = "midCarryDistance")
    private Integer midCarryDistance;      //中速带球距离
    @Column(name = "lowCarryDistance")
    private Integer lowCarryDistance;      //低速带球距离
    @Column(name = "normalCarryDistance")
    private Integer normalCarryDistance;   //步行带球距离
    @Column(name = "highCarryCount")
    private Integer highCarryCount;        //高速带球次数
    @Column(name = "midCarryCount")
    private Integer midCarryCount;        //中速带球次数
    @Column(name = "lowCarryCount")
    private Integer lowCarryCount;        //低速带球次数
    @Column(name = "normalCarryCount")
    private Integer normalCarryCount;    //步行带球次数
    @Column(name = "maxSprintSpeed")
    private Double maxSprintSpeed;
    @Column(name = "maxCarrySpeed")
    private Double maxCarrySpeed;
    @Column(name = "passBallError")
    private Integer passBallError;    //传球失误


    public FootballTeamGameStatisticsPlayer() {
    }


    public FootballTeamGameStatisticsPlayer(Integer touchCounts, Long wholeMoveDistance, Long moveCalorie, Integer carryDistance, Integer passBallCounts) {
        super();
        this.touchCounts = touchCounts;
        this.wholeMoveDistance = wholeMoveDistance;
        this.moveCalorie = moveCalorie;
        this.carryDistance = carryDistance;
        this.passBallCounts = passBallCounts;
    }

    public FootballTeamGameStatisticsPlayer(Long id, FootballTeam footballTeam, FootballTeamGame footballTeamGame, Integer passBallCounts, User user, Long wholeMoveDistance) {
        super();
        setId(id);
        this.footballTeam = footballTeam;
        this.footballTeamGame = footballTeamGame;
        this.passBallCounts = passBallCounts;
        this.user = user;
        this.wholeMoveDistance = wholeMoveDistance;
    }

    public FootballTeamGameStatisticsPlayer(Long id, FootballTeam footballTeam,
                                            FootballTeamGame footballTeamGame, User user,
                                            UserHardwarePractice userHardwarePractice, String identification,
                                            Integer touchCounts, Integer passBallCounts,
                                            Integer oneFootPassCount, Integer carryCount, Integer carryTime,
                                            Integer carryDistance, Integer highMoveCount,
                                            Long highMoveDistance, String highMoveCalorie,
                                            Integer midMoveCount, Long midMoveDistance, String midMoveCalorie,
                                            Integer lowMoveCount, Long lowMoveDistance, String lowMoveCalorie,
                                            Integer normalMoveCount, Long normalMoveDistance,
                                            String normalMoveCalorie, Long wholeMoveDistance, Long moveCalorie,
                                            String calorieCurveData, Integer shoots, Integer assists,
                                            Date createTime, Date updateTime, Integer highCarryDistance,
                                            Integer midCarryDistance, Integer lowCarryDistance,
                                            Integer normalCarryDistance, Integer highCarryCount, Integer midCarryCount,
                                            Integer lowCarryCount, Integer normalCarryCount, Integer passBallError, boolean deleted) {
        super();
        setId(id);
        this.footballTeam = footballTeam;
        this.footballTeamGame = footballTeamGame;
        this.user = user;
        this.userHardwarePractice = userHardwarePractice;
        this.identification = identification;
        this.touchCounts = touchCounts;
        this.passBallCounts = passBallCounts;
        this.oneFootPassCount = oneFootPassCount;
        this.carryCount = carryCount;
        this.carryTime = carryTime;
        this.carryDistance = carryDistance;
        this.highMoveCount = highMoveCount;
        this.highMoveDistance = highMoveDistance;
        this.highMoveCalorie = highMoveCalorie;
        this.midMoveCount = midMoveCount;
        this.midMoveDistance = midMoveDistance;
        this.midMoveCalorie = midMoveCalorie;
        this.lowMoveCount = lowMoveCount;
        this.lowMoveDistance = lowMoveDistance;
        this.lowMoveCalorie = lowMoveCalorie;
        this.normalMoveCount = normalMoveCount;
        this.normalMoveDistance = normalMoveDistance;
        this.normalMoveCalorie = normalMoveCalorie;
        this.wholeMoveDistance = wholeMoveDistance;
        this.moveCalorie = moveCalorie;
        this.calorieCurveData = calorieCurveData;
        this.shoots = shoots;
        this.assists = assists;
        setCreateTime(createTime);
        setUpdateTime(updateTime);
        this.highCarryDistance = highCarryDistance;
        this.midCarryDistance = midCarryDistance;
        this.lowCarryDistance = lowCarryDistance;
        this.normalCarryDistance = normalCarryDistance;
        this.highCarryCount = highCarryCount;
        this.midCarryCount = midCarryCount;
        this.lowCarryCount = lowCarryCount;
        this.normalCarryCount = normalCarryCount;
        this.passBallError = passBallError;
        setDeleted(deleted);
    }

}
