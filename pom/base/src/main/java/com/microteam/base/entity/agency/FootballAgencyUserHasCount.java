package com.microteam.base.entity.agency;


import com.microteam.base.common.base.BaseEntity;
import com.microteam.base.entity.user.User;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;
import java.io.Serializable;

@Entity
@Table(name = "football_agency_user_has_count")
@Getter
@Setter
public class FootballAgencyUserHasCount extends BaseEntity implements Serializable {

    @ManyToOne
    @JoinColumn(name = "userId", nullable = false)
    private User user;
    @Column(name = "ownerAgencyArray", length = 65535)
    private String ownerAgencyArray;
    @Column(name = "joinAgencyArray", length = 65535)
    private String joinAgencyArray;
    @Column(name = "followAgencyArray", length = 65535)
    private String followAgencyArray;
    @Column(name = "fansAgencyArray", length = 65535)
    private String fansAgencyArray;

}
