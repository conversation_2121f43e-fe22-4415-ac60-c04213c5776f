package com.microteam.base.agency.service;

import com.microteam.base.entity.agency.FootballAgencyClassPublication;

import java.util.List;

public interface FootballAgencyClassPublicationDaoService {

    //分页查询通知
    List<FootballAgencyClassPublication> findByAgencyIdAndAgencyClassIdForPage(Long agencyId, Long agencyClassId, int pageSize, int page);

    //分页查询通知
    List<FootballAgencyClassPublication> findByAgencyIdForPage(Long agencyId, int pageSize, int page);

    List<FootballAgencyClassPublication> findByAgencyIdAndJudgeForPage(Long agencyId, Long judge, int pageSize, int page) ;

    //通过Id查询
    FootballAgencyClassPublication findById(long id);
}
