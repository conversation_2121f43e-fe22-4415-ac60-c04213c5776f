package com.microteam.base.entity.grounds;


import com.microteam.base.common.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

@Entity
@Table(name = "football_ground_discount")
@Getter
@Setter
public class FootballGroundDiscount extends BaseEntity implements Serializable {

    @ManyToOne
    @JoinColumn(name = "groundId", nullable = false)
    private FootballGround footballGround;
    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "publishTime", nullable = false, length = 19)
    private Date publishTime;
    @Column(name = "countryCode")
    private String countryCode;
    @Column(name = "provinceCode")
    private String provinceCode;
    @Column(name = "cityCode")
    private String cityCode;
    @Column(name = "countyCode")
    private String countyCode;
    @Column(name = "longitude")
    private String longitude;
    @Column(name = "latitude")
    private String latitude;
    @Column(name = "discountType")
    private String discountType;
    @Column(name = "discountPeriod", length = 50)
    private String discountPeriod;
    @Column(name = "discountRemark", length = 65535)
    private String discountRemark;

    public Date getPublishTime() {
        if (this.publishTime == null) {
            return null;
        }
        return (Date) this.publishTime.clone();
    }

    public void setPublishTime(Date publishTime) {
        this.publishTime = (Date) publishTime.clone();
    }

}
