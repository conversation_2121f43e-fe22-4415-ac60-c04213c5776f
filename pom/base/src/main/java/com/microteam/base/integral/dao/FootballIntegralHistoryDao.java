package com.microteam.base.integral.dao;

import com.microteam.base.entity.integral.FootballIntegralHistory;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

public interface FootballIntegralHistoryDao extends JpaRepository<FootballIntegralHistory, Long>, JpaSpecificationExecutor<FootballIntegralHistory> {

    @Query("from FootballIntegralHistory as integralHistory " +
            "where integralHistory.teamId = (:teamId) " +
            "and integralHistory.deleted = false ")
    List<FootballIntegralHistory> findByTeamId(@Param("teamId") Long teamId);

    @Query("from FootballIntegralHistory as integralHistory " +
            "where integralHistory.teamId = (:teamId) " +
            "and integralHistory.typeId = (:typeId) " +
            "and integralHistory.deleted = false ")
    List<FootballIntegralHistory> findByTeamIdAndTypeId(@Param("teamId") Long teamId, @Param("typeId") Long typeId);

    @Query("from FootballIntegralHistory as integralHistory " +
            "where integralHistory.teamId = (:teamId) " +
            "and integralHistory.id>(:lastAdd) " +
            "and integralHistory.deleted = false ")
    List<FootballIntegralHistory> findNewByTeamId(@Param("teamId") Long teamId, @Param("lastAdd") Long lastAdd);

//    List<FootballIntegralHistory> saveList(List<FootballIntegralHistory> list);

}
