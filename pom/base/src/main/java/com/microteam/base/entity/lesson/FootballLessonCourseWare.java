package com.microteam.base.entity.lesson;

import com.microteam.base.common.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.io.Serializable;

@Entity
@Table(name = "football_lesson_courseware")
@Getter
@Setter
public class FootballLessonCourseWare extends BaseEntity implements Serializable {
    @Column(name = "lessonId", nullable = false)
    private Long lessonId;
    @Column(name = "courseId", nullable = false)
    private Long courseId;


}
