package com.microteam.base.lesson.dao;

import com.microteam.base.entity.lesson.CoursewareDataType;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

public interface CoursewareDataTypeDao extends JpaRepository<CoursewareDataType, Long>, JpaSpecificationExecutor<CoursewareDataType> {

    @Query("from CoursewareDataType as coursewareDataType " +
            "where coursewareDataType.coursewareId = (:coursewareId) " +
            "and coursewareDataType.deleted = false ")
    List<CoursewareDataType> findByCoursewareId(@Param("coursewareId") Long coursewareId);
}
