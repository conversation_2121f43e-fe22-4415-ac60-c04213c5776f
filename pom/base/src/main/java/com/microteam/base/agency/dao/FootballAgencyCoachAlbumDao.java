package com.microteam.base.agency.dao;

import com.microteam.base.entity.agency.FootballAgencyCoachAlbum;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

public interface FootballAgencyCoachAlbumDao extends JpaRepository<FootballAgencyCoachAlbum, Long>, JpaSpecificationExecutor<FootballAgencyCoachAlbum> {
    //查询机构教练图片
    @Query("from FootballAgencyCoachAlbum as agencyCoachAlbum " +
            "where agencyCoachAlbum.footballAgencyCoach.id = (:agencyCoachId) " +
            "order by agencyCoachAlbum.createTime desc ")
    List<FootballAgencyCoachAlbum> findByAgencyCoachId(@Param("agencyCoachId") Long agencyCoachId);
}
