package com.microteam.base.demoManager.common;


import com.microteam.base.entity.demoManager.MacQRCode;

import java.util.HashMap;
import java.util.Map;


public class DemoUtils {

    /**
     * @param map
     * @param macQrCode
     * @return Map<String, Object>
     * @Description: 解析二维码序列号（19位）
     * <AUTHOR>
     * @date 2018年2月6日 上午10:54:31
     */
    public static Map<String, Object> analyzeQRCode(Map<String, Object> map, MacQRCode macQrCode) {

        String qrCode = macQrCode.getQrCode();
        Integer orderCode = Integer.parseInt(qrCode.substring(qrCode.length() - 8)); //截取有关左右脚的序列号
        if (orderCode % 2 == 0) {  //偶数为右脚 反之为左脚
            map.put("hardwareType", 2);
        } else {
            map.put("hardwareType", 1);
        }
        map.put("mac", macQrCode.getMac());
        return map;
    }
    /**
     *
     * @Description: 解析二维码序列号（19位）
     * @return Map<String,Object>
     * <AUTHOR>
     * @date 2018年2月6日 上午10:54:31
     *
     */
    public static Map<String, Object> analyzeQRCode(MacQRCode macQrCode) {
        Map<String, Object> map = new HashMap<>();
        String qrCode = macQrCode.getQrCode();
        //截取有关左右脚的序列号
        Integer orderCode = Integer.parseInt(qrCode.substring(qrCode.length() - 8));
        //偶数为右脚 反之为左脚
        if (orderCode % 2 == 0) {
            map.put("hardwareType", 2);
        } else {
            map.put("hardwareType", 1);
        }
        map.put("mac", macQrCode.getMac());
        //截取有关系列序列号
        String brandName;
        Integer brandCode = Integer.parseInt(qrCode.substring(3, 5));
        switch (brandCode) {
            case 2:
                brandName = "H";
                break;
            case 3:
                brandName = "P";
                break;
            default:
                brandName = "F";
                break;
        }
        map.put("brandName", brandName);
        //截取有关鞋钉的序列号
        String release;
        Integer releaseCode = Integer.parseInt(qrCode.substring(5, 7));
        switch (releaseCode) {

            case 1:
                release = "tf";
                break;
            default:
                release = "fg";
                break;
        }
        map.put("release", release);
        return map;
    }
}
