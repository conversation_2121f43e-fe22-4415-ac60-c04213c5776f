package com.microteam.base.entity.team;

import com.microteam.base.common.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.io.Serializable;


@Entity
@Table(name = "football_team_color")
@Getter
@Setter
public class FootballTeamColor extends BaseEntity implements Serializable {

    @Column(name = "teamColorNetUrl", length = 250)
    private String teamColorNetUrl;
    @Column(name = "teamColorUrl", length = 250)
    private String teamColorUrl;
    @Column(name = "fileName", length = 250)
    private String fileName;
    @Column(name = "length")
    private Long length;

}
