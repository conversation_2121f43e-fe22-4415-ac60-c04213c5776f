//package com.microteam.base.integral.dao.impl;
//
//import com.microteam.base.common.base.AbstractHibernateDao;
//import com.microteam.base.integral.dao.FootballIntegralHistoryDao;
//import com.microteam.base.entity.integral.FootballIntegralHistory;
//import org.apache.log4j.Logger;
//import org.hibernate.query.Query;
//import org.hibernate.Session;
//import org.hibernate.Transaction;
//import org.springframework.stereotype.Service;
//
//import java.util.List;
//
//@Service(value = "footballIntegralHistoryDao")
//public class FootballIntegralHistoryDaoImpl extends AbstractHibernateDao<FootballIntegralHistory> implements FootballIntegralHistoryDao {
//    static Logger logger = Logger.getLogger(FootballIntegralHistoryDaoImpl.class.getName());
//
//    public FootballIntegralHistoryDaoImpl() {
//        super();
//        setClazz(FootballIntegralHistory.class);
//    }
//
//    @Override
//    public List<FootballIntegralHistory> findByTeamId(Long teamId) {
//        String hql = "from FootballIntegralHistory as integralHistory " +
//                "where integralHistory.teamId = (:teamId) " +
//                "and integralHistory.deleted=0";
//        Query query = getCurrentSession().createQuery(hql).setParameter("teamId", teamId);
//        return query.list();
//    }
//
//    @Override
//    public List<FootballIntegralHistory> findByTeamIdAndTypeId(Long teamId, Long typeId) {
//        String hql = "from FootballIntegralHistory as integralHistory " +
//                "where integralHistory.teamId = (:teamId) " +
//                "and integralHistory.typeId = (:typeId) " +
//                "and integralHistory.deleted=0";
//        Query query = getCurrentSession().createQuery(hql).setParameter("teamId", teamId).setParameter("typeId", typeId);
//        return query.list();
//    }
//
//    @Override
//    public List<FootballIntegralHistory> findNewByTeamId(Long teamId, Long lastAdd) {
//        String hql = "from FootballIntegralHistory as integralHistory " +
//                "where integralHistory.teamId = (:teamId) " +
//                "and integralHistory.id>(:lastAdd) " +
//                "and integralHistory.deleted=0";
//        Query query = getCurrentSession().createQuery(hql).setParameter("teamId", teamId).setParameter("lastAdd", lastAdd);
//        return query.list();
//    }
//
//    @Override
//    public List<FootballIntegralHistory> saveList(List<FootballIntegralHistory> list) {
//        Transaction tx = null;
//        Session session = getCurrentSession();
//        if (list != null) {
//            for (FootballIntegralHistory integralHistory : list) {
//                tx = session.beginTransaction();
//                session.save(integralHistory);
//                tx.commit();
//            }
//        }
//        return list;
//    }
//
//
//}
