package com.microteam.base.agency.dao;

import com.microteam.base.entity.agency.FootballAgencyCoachComment;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

public interface FootballAgencyCoachCommentDao extends JpaRepository<FootballAgencyCoachComment, Long>, JpaSpecificationExecutor<FootballAgencyCoachComment> {
    //分页查询教练评论
    @Query("from FootballAgencyCoachComment as agencyCoachComment " +
            "left join fetch agencyCoachComment.user as user " +
            "where agencyCoachComment.footballAgencyCoach.id = (:agencyCoachId) " +
            "and agencyCoachComment.deleted=false ")
    List<FootballAgencyCoachComment> findByAgencyCoachIdForPage(@Param("agencyCoachId") Long agencyCoachId, Pageable pageable);
}
