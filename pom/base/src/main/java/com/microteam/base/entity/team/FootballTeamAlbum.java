package com.microteam.base.entity.team;


import com.microteam.base.common.base.BaseEntity;
import com.microteam.base.entity.user.User;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;
import java.io.Serializable;


@Entity
@Table(name = "football_team_album")
@Getter
@Setter
public class FootballTeamAlbum extends BaseEntity implements Serializable {
    @ManyToOne
    @JoinColumn(name = "userId")
    private User user;
    @ManyToOne
    @JoinColumn(name = "teamId", nullable = false)
    private FootballTeam footballTeam;
    @Column(name = "albumName", length = 100)
    private String albumName;
    @Column(name = "albumDesc", length = 200)
    private String albumDesc;

}
