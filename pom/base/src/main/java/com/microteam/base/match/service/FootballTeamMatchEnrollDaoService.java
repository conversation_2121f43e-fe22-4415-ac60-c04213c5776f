package com.microteam.base.match.service;

import com.microteam.base.entity.match.FootballTeamMatchEnroll;

import java.util.List;

public interface FootballTeamMatchEnrollDaoService {
    //查询球队是否已经报过名了
    FootballTeamMatchEnroll findByMatchIdAndTeamId(Long matchId, Long teamId);

    //根据id查询
    FootballTeamMatchEnroll findById(long id);

    //分页查询赛事球队报名列表
    List<FootballTeamMatchEnroll> findByMatchIdAndEnrollTypeForPage(Long matchId, int page, int pageSize, int enrollType);

    //查询报名赛事的球队
    List<FootballTeamMatchEnroll> findByMatchId(Long matchId);

    //查询用户球队有没有报名
    List<FootballTeamMatchEnroll> findByMatchIdAndUserId(Long matchId, Long userId);
}
