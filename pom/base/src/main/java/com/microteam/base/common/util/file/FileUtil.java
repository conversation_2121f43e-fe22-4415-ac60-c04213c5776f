package com.microteam.base.common.util.file;

import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import java.io.*;
import java.net.URL;
import java.net.URLConnection;

@Component
public class FileUtil {

    private static Logger logger = Logger.getLogger(FileUtil.class);
    @Autowired
    @Qualifier(value = "remoteRestTemplate")
    private RestTemplate restTemplate;

    public byte[] getUserHead(String url) {
        ResponseEntity<byte[]> response = this.restTemplate.exchange(url, HttpMethod.GET, null, byte[].class);
        // 取得文件字节
        return response.getBody();
    }

    public void saveImg(byte[] contents, String filePath) {
        BufferedInputStream bis = null;
        FileOutputStream fos = null;
        BufferedOutputStream output = null;
        try {
            ByteArrayInputStream byteInputStream = new ByteArrayInputStream(contents);
            bis = new BufferedInputStream(byteInputStream);
            File file = new File(filePath);
            // 获取文件的父路径字符串
            File path = file.getParentFile();
            if (!path.exists()) {
                logger.info("文件夹不存在，创建。path={" + path.toString() + "}");
                boolean isCreated = path.mkdirs();
                if (!isCreated) {
                    logger.error("创建文件夹失败，path={" + path.toString() + "}");
                }
            }
            fos = new FileOutputStream(file);
            // 实例化OutputString 对象
            output = new BufferedOutputStream(fos);
            byte[] buffer = new byte[1024];
            int length = bis.read(buffer);
            while (length != -1) {
                output.write(buffer, 0, length);
                length = bis.read(buffer);
            }
            output.flush();
        } catch (Exception e) {
            logger.error("输出文件流时抛异常，filePath={" + filePath + "}", e);
        } finally {
            try {
                bis.close();
                fos.close();
                output.close();
            } catch (IOException | NullPointerException e0) {
                logger.error("文件处理失败，filePath={" + filePath + "}", e0);
            }
        }
    }

    public static String getImageByUrl(String imgUrl, String savePath, String fileNameUuid) {
        try {
            // 构造URL
            URL url = new URL(imgUrl);
            // 打开连接
            URLConnection con = url.openConnection();
            // 输入流
            InputStream is = con.getInputStream();
            // 1K的数据缓冲
            byte[] bs = new byte[1024];
            // 读取到的数据长度
            int len;
            File file = new File(savePath);// (String) property.get("ewmPath"));
            if (!file.exists()) {
                if (!file.mkdirs()) {
                    return "error";
                }
            }
            // 输出的文件流
            OutputStream os = new FileOutputStream(savePath + fileNameUuid);
            // 开始读取
            while ((len = is.read(bs)) != -1) {
                os.write(bs, 0, len);
            }
            // 完毕，关闭所有链接
            os.close();
            is.close();
            return "success";
        } catch (Exception e) {
            return "error";
        }
    }

}
