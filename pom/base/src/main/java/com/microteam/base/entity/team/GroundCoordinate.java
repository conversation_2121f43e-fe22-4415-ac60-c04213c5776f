package com.microteam.base.entity.team;

import com.microteam.base.common.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.io.Serializable;

@Entity
@Table(name = "ground_coordinate")
@Getter
@Setter
public class GroundCoordinate extends BaseEntity implements Serializable {

    @Column
    private Long groundId;
    @Column
    private Long teamId;
    @Column(name = "mt_order")
    private Integer order;
    @Column
    private Double longitude;
    @Column
    private Double latitude;
    @Column(name = "userId")
    private Long userId;
    @Column(name = "uuid")
    private String uuid;
    @Column(name = "course_name")
    private String courseName;
    @Column(name = "microteam")
    private boolean microteam;

    public GroundCoordinate() {

    }

    public GroundCoordinate(Long groundId, Long teamId, Integer order, Double longitude, Double latitude, Long userId, String uuid, String courseName, boolean microteam) {
        this.groundId = groundId;
        this.teamId = teamId;
        this.order = order;
        this.longitude = longitude;
        this.latitude = latitude;
        this.userId = userId;
        this.uuid = uuid;
        this.courseName = courseName;
        this.microteam = microteam;
    }

}
