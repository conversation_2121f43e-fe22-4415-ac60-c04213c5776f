package com.microteam.base.entity.team;


import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

@Entity
@Table(name = "football_team_node")
@Getter
@Setter
public class FootballTeamNode implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false)
    protected Long id;
    @Column(name = "node", nullable = false)
    private Long node;    //节点id
    @Column(name = "node_time", nullable = false)
    private Long nodeTime;  //节点比赛时间(单位分钟)
    @ManyToOne
    @JoinColumn(name = "team_game_id", nullable = false)
    private FootballTeamGame footballTeamGame;
    @ManyToOne
    @JoinColumn(name = "team_id", nullable = false)
    private FootballTeam footballTeam;
    @Column(name = "try_deleted", nullable = false)
    private boolean tryDeleted;  //是否删除
    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "create_time", nullable = false, length = 19)
    private Date createTime;
    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "update_time", nullable = false, length = 19)
    private Date updateTime;

}
