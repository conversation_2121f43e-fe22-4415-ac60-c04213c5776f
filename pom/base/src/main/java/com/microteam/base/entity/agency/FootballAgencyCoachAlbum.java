package com.microteam.base.entity.agency;


import com.microteam.base.common.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;
import java.io.Serializable;

@Entity
@Table(name = "football_agency_coach_album")
@Getter
@Setter
public class FootballAgencyCoachAlbum extends BaseEntity implements Serializable {

    @ManyToOne
    @JoinColumn(name = "agencyCoachId", nullable = false)
    private FootballAgencyCoach footballAgencyCoach;
    @Column(name = "imgNetUrl", length = 250)
    private String imgNetUrl;
    @Column(name = "imgUrl", length = 250)
    private String imgUrl;
    @Column(name = "imgName", length = 50)
    private String imgName;
    @Column(name = "imgLength")
    private Long imgLength;

}
