package com.microteam.base.common.pojo;

import com.microteam.base.common.pojo.team.KickStatePojo;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
public class DataPojo {

    // 设备信息
    private long userHardwareId;
    private long userHardwareDataId;
    private String identification;
    private String hardwareName;
    private String versionNumber;
    private String hardwareMac;

    // 比赛数据
    private long kickBallStartTime;
    private int[] kickBallData;
    private List<KickStatePojo> kickState;
    private long velocity;
    private long velocityTime;
    private Integer energyValue;
    private List<UserHardWareStepPojo> highMoveData;
    private List<UserHardWareStepPojo> midMoveData;
    private List<UserHardWareStepPojo> lowMoveData;
    private List<UserHardWareStepPojo> normalMoveData;
    private int highMoveCount;
    private int midMoveCount;
    private int lowMoveCount;
    private int normalMoveCount;
    private int exteriorData;
    private int instepKickingData;
    private int archData;
    private int tiptoeData;
    private int heelData;
    private int soleFootData;

    public void initLowMoveData() {
        this.lowMoveCount = this.lowMoveCount + this.normalMoveCount;
        this.normalMoveCount = 0;
        if (this.normalMoveData != null && !this.normalMoveData.isEmpty()) {
            this.lowMoveData.addAll(this.normalMoveData);
            this.normalMoveData = new ArrayList<>();
        }

    }
}
