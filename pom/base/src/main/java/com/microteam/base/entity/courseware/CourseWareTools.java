package com.microteam.base.entity.courseware;

import com.microteam.base.common.base.BaseEntity;
import com.microteam.base.entity.lesson.TrainingTools;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;
import java.io.Serializable;

@Entity
@Table(name = "courseware_tools")
@Getter
@Setter
public class CourseWareTools extends BaseEntity implements Serializable {
    @Column(name = "coursewareId")
    private Long coursewareId;
    @ManyToOne
    @JoinColumn(name = "toolId")
    private TrainingTools tools;
}
