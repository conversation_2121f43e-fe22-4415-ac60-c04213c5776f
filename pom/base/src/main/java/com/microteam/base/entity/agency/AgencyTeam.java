package com.microteam.base.entity.agency;

import com.microteam.base.common.base.BaseEntity;
import com.microteam.base.entity.team.FootballTeam;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Entity;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import java.io.Serializable;

@Entity
@Table(name = "agency_team")
@Getter
@Setter
public class AgencyTeam extends BaseEntity implements Serializable {

    @ManyToOne
    @JoinColumn(name = "agencyId", nullable = false)
    private FootballAgency agency;
    @ManyToOne
    @JoinColumn(name = "teamId", nullable = false)
    private FootballTeam team;

}
