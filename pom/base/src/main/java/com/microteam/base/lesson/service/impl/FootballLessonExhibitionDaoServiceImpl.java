package com.microteam.base.lesson.service.impl;


import com.microteam.base.entity.lesson.FootballCourseWare;
import com.microteam.base.entity.lesson.FootballLessonExhibition;
import com.microteam.base.lesson.dao.FootballLessonDao;
import com.microteam.base.lesson.dao.FootballLessonExhibitionDao;
import com.microteam.base.lesson.service.FootballLessonDaoService;
import com.microteam.base.lesson.service.FootballLessonExhibitionDaoService;
import org.apache.log4j.Logger;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;


@Service("footballLessonExhibitionDaoService")
public class FootballLessonExhibitionDaoServiceImpl implements FootballLessonExhibitionDaoService {
    static Logger logger = Logger.getLogger(FootballLessonExhibitionDaoServiceImpl.class.getName());

    @Autowired
    private FootballLessonExhibitionDao dao;

    @Autowired
    private FootballLessonDaoService footballLessonDaoService;
    @Override
    public int updateLessonStatus(Long lessonId) {
        return footballLessonDaoService.updateStatusById(lessonId);
    }

    @Override
    public FootballLessonExhibition findByLessonId(Long lessonId,Long userId) {
        return dao.findByLessonId(lessonId,userId);
    }

    @Override
    public List<FootballLessonExhibition> findByExLessonId(Long lessonId) {
        return dao.findByExLessonId(lessonId);
    }

    @Override
    public FootballLessonExhibition save(FootballLessonExhibition footballLessonExhibition) {
        return this.dao.save(footballLessonExhibition);
    }
}
