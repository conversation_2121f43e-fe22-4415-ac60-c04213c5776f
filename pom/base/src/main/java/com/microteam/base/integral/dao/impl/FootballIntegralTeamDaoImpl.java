//package com.microteam.base.integral.dao.impl;
//
//import com.microteam.base.common.base.AbstractHibernateDao;
//import com.microteam.base.integral.dao.FootballIntegralTeamDao;
//import com.microteam.base.entity.integral.FootballIntegralTeam;
//import org.apache.log4j.Logger;
//import org.hibernate.query.Query;
//import org.springframework.stereotype.Service;
//
//import java.util.List;
//
//@Service(value = "footballIntegralTeamDao")
//public class FootballIntegralTeamDaoImpl extends AbstractHibernateDao<FootballIntegralTeam> implements FootballIntegralTeamDao {
//
//    static Logger logger = Logger.getLogger(FootballIntegralTeamDaoImpl.class.getName());
//
//    public FootballIntegralTeamDaoImpl() {
//        super();
//        setClazz(FootballIntegralTeam.class);
//    }
//
//    @Override
//    public FootballIntegralTeam findByTeamId(Long teamId) {
//        String hql = "from FootballIntegralTeam as integralTeam " +
//                "where integralTeam.teamId=(:teamId) " +
//                "and integralTeam.deleted=0 " +
//                "order by integralTeam.updateTime desc";
//        Query query = getCurrentSession().createQuery(hql).setParameter("teamId", teamId);
//        List<FootballIntegralTeam> list = query.list();
//        if (list != null && !list.isEmpty()) {
//            return list.get(0);
//        }
//        return null;
//    }
//
//    @Override
//    public List<FootballIntegralTeam> findByTeamIdList(List<Long> teamIdList) {
//        String hql = "from FootballIntegralTeam as integralTeam " +
//                "where integralTeam.teamId in (:teamIdList) " +
//                "and integralTeam.deleted=0 " +
//                "order by integralTeam.updateTime desc";
//        Query query = getCurrentSession().createQuery(hql)
//                .setParameterList("teamIdList", teamIdList);
//        return query.list();
//    }
//
//    @Override
//    public int findRangeByTeamId(Long teamId, List<Long> teamIdList) {
//        String sql = "select rownum\n" +
//                "from (\n" +
//                "select *,@rownum\\:=@rownum+1 as rownum\n" +
//                "from football_integral_team a,(select @rownum\\:=0) b\n" +
//                "where a.teamId in (:teamIdList)\n" +
//                "order by a.integral desc,a.updateTime\n" +
//                ") c\n" +
//                "where c.teamId = (:teamId)";
//        Query query = getCurrentSession().createNativeQuery(sql)
//                .setParameterList("teamIdList", teamIdList)
//                .setParameter("teamId", teamId);
//        List list = query.list();
//        if (list != null && !list.isEmpty()) {
//            double d = (double) list.get(0);
//            return ((int) d);
//        }
//        return 0;
//    }
//
//    @Override
//    public FootballIntegralTeam findById(long id) {
//        String hql = "from FootballIntegralTeam as integralTeam " +
//                "where integralTeam.id = (:id) " +
//                "and integralTeam.deleted=0 " +
//                "order by integralTeam.updateTime desc";
//        Query query = getCurrentSession().createQuery(hql)
//                .setParameter("id", id);
//        return (FootballIntegralTeam) query.uniqueResult();
//    }
//}
