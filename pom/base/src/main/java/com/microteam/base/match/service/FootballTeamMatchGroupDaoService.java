package com.microteam.base.match.service;

import com.microteam.base.entity.match.FootballTeamMatchGroup;

import java.util.List;

public interface FootballTeamMatchGroupDaoService {
    List<FootballTeamMatchGroup> findByMatchId(Long matchId);

    //根据球队和赛事查询分组
    FootballTeamMatchGroup findByMatchIdAndTeamId(Long matchId, Long teamId);

    //查询同一分组的球队
    List<FootballTeamMatchGroup> findByContestGroupAndMatchId(Short contestGroup, Long matchId);

    int delByTeamId(Long teamId);
}
