package com.microteam.base.common.pojo.team;

import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
public class FootballContestPojo{

    private long contestTime;
    private String location;
    private int clotheColor;//主队球衣颜色:1=red；2=green;3=yellow;4=white;5=blue;6=black;7=other;
    private int guestClotheColor;//主队客场球衣颜色
    private int contestType;
    private String opponent;
    private String opponentRandomImg;    //客队随机头像
    private String countryCode;
    private String provinceCode;
    private String cityCode;
    private String countyCode;
    private String fieldLocation;
    private String attentions;
    private int opponentClothColor;//客队球衣颜色:1=red；2=green;3=yellow;4=white;5=blue;6=black;7=other;
    private int opponentGuestClothColor;//客队客场球衣颜色
    private String judge;
    private String competitionFee;
    private String judgeFee;
    private String drinkFee;
    private String longitude;
    private String latitude;
    private String contact;
    private String phone;
    private int contestRule;//擅长赛制:1:three(3人制)，2:sevenornine（7-9人制），3:eleven（11制）,4:other(其他)
    private int hostScore;//主队比分
    private int guestScore;//客队比分
    private int shoots;//射门
    private int assists;//助攻
    private Integer isOverTime; //是否有加时赛
    private int overHostScore; //加时赛主队比分
    private int overGuestScore; //加时赛客队比分
    private Integer isSpot;     //是否有点球大战
    private int spotHostScore;  //点球大战主队比分
    private int spotGuestScore; //点球大战客队比分
    private Integer isGiveUp;   //是否有球队弃权
    private String giveUpTeamId;//弃权球队id
    private List<FootballContestUserPojo> contestUserPojoList;
    private Long enrollDeadline;//报名截止时间

}
