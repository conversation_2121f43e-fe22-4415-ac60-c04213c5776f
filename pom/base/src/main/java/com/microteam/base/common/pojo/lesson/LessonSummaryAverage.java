package com.microteam.base.common.pojo.lesson;

public class LessonSummaryAverage {
	double avgWholeMoveDistance;  //跑动距离,平均值
	double avgCarryDistance;      //带球距离,平均值
    double avgPassBallCounts;     //传球次数,平均值
    double avgMaxSprintSpeed;     //最大冲刺速度,平均值
    double avgPassBallError;      //传球失误,平均值
    double avgSuccessRate;        //传球成功率,平均值
    
	public double getAvgWholeMoveDistance() {
		return avgWholeMoveDistance;
	}
	public void setAvgWholeMoveDistance(double avgWholeMoveDistance) {
		this.avgWholeMoveDistance = avgWholeMoveDistance;
	}
	public double getAvgCarryDistance() {
		return avgCarryDistance;
	}
	public void setAvgCarryDistance(double avgCarryDistance) {
		this.avgCarryDistance = avgCarryDistance;
	}
	public double getAvgPassBallCounts() {
		return avgPassBallCounts;
	}
	public void setAvgPassBallCounts(double avgPassBallCounts) {
		this.avgPassBallCounts = avgPassBallCounts;
	}
	public double getAvgMaxSprintSpeed() {
		return avgMaxSprintSpeed;
	}
	public void setAvgMaxSprintSpeed(double avgMaxSprintSpeed) {
		this.avgMaxSprintSpeed = avgMaxSprintSpeed;
	}
	public double getAvgPassBallError() {
		return avgPassBallError;
	}
	public void setAvgPassBallError(double avgPassBallError) {
		this.avgPassBallError = avgPassBallError;
	}
	public double getAvgSuccessRate() {
		return avgSuccessRate;
	}
	public void setAvgSuccessRate(double avgSuccessRate) {
		this.avgSuccessRate = avgSuccessRate;
	}

    
}
