package com.microteam.base.entity.lesson;

import com.microteam.base.common.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;
import java.io.Serializable;

@Entity
@Table(name = "user_lesson")
@Getter
@Setter
public class UserLesson extends BaseEntity implements Serializable {

    @Basic
    @Column(name = "lessonid", nullable = false)
    private long lessonId;
    @Basic
    @Column(name = "userid", nullable = false)
    private long userId;
    @Basic
    @Column(name = "status", nullable = false)
    private byte status;
    @ManyToOne
    @JoinColumn(name = "lessonId", nullable = false)
    private FootballLesson lesson;

}
