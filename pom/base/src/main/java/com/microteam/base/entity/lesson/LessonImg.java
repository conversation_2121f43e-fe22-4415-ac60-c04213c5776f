package com.microteam.base.entity.lesson;

import com.microteam.base.common.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.io.Serializable;

@Entity
@Table(name = "lesson_img")
@Getter
@Setter
public class LessonImg extends BaseEntity implements Serializable {

    @Column
    private Long lessonId;
    @Column
    private String imgUrl;
    @Column
    private String imgPath;

}
