//package com.microteam.base.agency.dao.impl;
//
//import com.microteam.base.agency.dao.FootballAgencyUserHasCountDao;
//import com.microteam.base.entity.agency.FootballAgencyUserHasCount;
//import com.microteam.base.common.base.AbstractHibernateDao;
//import com.microteam.base.entity.user.User;
//import org.apache.log4j.Logger;
//import org.hibernate.query.Query;
//import org.springframework.stereotype.Repository;
//
//import java.util.List;
//
//@Repository("footballAgencyUserHasCountDao")
//public class FootballAgencyUserHasCountDaoImpl extends
//        AbstractHibernateDao<FootballAgencyUserHasCount> implements FootballAgencyUserHasCountDao {
//    static Logger logger = Logger.getLogger(FootballAgencyUserHasCountDaoImpl.class.getName());
//
//    public FootballAgencyUserHasCountDaoImpl() {
//        super();
//
//        setClazz(FootballAgencyUserHasCount.class);
//    }
//
//    @SuppressWarnings({"unchecked"})
//    @Override
//    public FootballAgencyUserHasCount findAgencyUserHasCountByUser(User user) {
//
//        String hql = "from FootballAgencyUserHasCount as footballAgencyUserHasCount where footballAgencyUserHasCount.user=? and footballAgencyUserHasCount.deleted=false";
//        Query query = getCurrentSession().createQuery(hql)
//                .setParameter(0, user);
//        List<FootballAgencyUserHasCount> list = query.list();
//        if (list != null && list.size() > 0) {
//            return list.get(0);
//        }
//        return null;
//    }
//
//
//}
