package com.microteam.base.agency.service.impl;

import com.microteam.base.agency.dao.AgencyTeamDao;
import com.microteam.base.agency.service.AgencyTeamDaoService;
import com.microteam.base.entity.agency.AgencyTeam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
public class AgencyTeamDaoServiceImpl implements AgencyTeamDaoService {
    @Autowired
    private AgencyTeamDao dao;

    @Override
    public Integer findContByAgencyId(Long agencyId) {
        return dao.findContByAgencyId(agencyId);
    }

    @Override
    public List<AgencyTeam> findByAgencyId(Long agencyId) {
        List<AgencyTeam> list = dao.findByAgencyId(agencyId);
        if (list == null) {
            list = new ArrayList<>();
        }
        return list;
    }

    @Override
    public List<AgencyTeam> findByAgencyIdForPage(Long agencyId, int page, int pageSize) {
        if (page > 0) {
            page--;
        }
        Pageable pageable = PageRequest.of(page, pageSize);
        List<AgencyTeam> list = dao.findByAgencyIdForPage(agencyId, pageable);
        if (list == null) {
            list = new ArrayList<>();
        }
        return list;
    }

    @Override
    public AgencyTeam findByTeamId(Long teamId) {
        return dao.findByTeamId(teamId);
    }

    @Override
    public AgencyTeam save(AgencyTeam agencyTeam) {
        return dao.save(agencyTeam);
    }

    @Override
    public List<AgencyTeam> findByTeamUserId(Long userId) {
        return dao.findByTeamUserId(userId);
    }

    @Override
    public int deleteByTeamId(Long teamId) {
        return dao.deleteByTeamId(teamId);
    }
}
