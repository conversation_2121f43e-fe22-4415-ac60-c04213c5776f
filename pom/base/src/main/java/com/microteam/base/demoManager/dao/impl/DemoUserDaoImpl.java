//package com.microteam.base.demoManager.dao.impl;
//
//import com.microteam.base.common.base.AbstractHibernateDao;
//import com.microteam.base.demoManager.dao.DemoUserDao;
//import com.microteam.base.entity.demoManager.DemoUser;
//import org.apache.log4j.Logger;
//import org.hibernate.query.Query;
//import org.springframework.stereotype.Service;
//
//import java.util.List;
//
//@Service("demoUserDao")
//public class DemoUserDaoImpl extends AbstractHibernateDao<DemoUser> implements DemoUserDao {
//    static Logger logger = Logger.getLogger(DemoUserDaoImpl.class.getName());
//
//    public DemoUserDaoImpl() {
//        super();
//        setClazz(DemoUser.class);
//    }
//
//    @Override
//    public List<DemoUser> findAllUser() {
//        String hql = "from DemoUser as user " +
//                "where user.deleted = false";
//        Query query = getCurrentSession().createQuery(hql);
//        List<DemoUser> list = query.list();
//        return list;
//    }
//
//
//    @Override
//    public DemoUser findUserByUserName(String userName) {
//        String hql = "from DemoUser as user " +
//                "where user.userName = :userName " +
//                "and user.deleted = false";
//        Query query = getCurrentSession().createQuery(hql)
//                .setParameter("userName", userName);
//        List<DemoUser> list = query.list();
//        if (list != null && list.size() > 0) {
//            return list.get(0);
//        }
//        return null;
//    }
//
//}
