package com.microteam.base.entity.integral;

import com.microteam.base.common.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.io.Serializable;

@Entity
@Table(name = "football_integral_team_hardwaremac")
@Getter
@Setter
public class FootballIntegralTeamHardwaremac extends BaseEntity implements Serializable {

    @Basic
    @Column(name = "teamId")
    private Long teamId;
    @Basic
    @Column(name = "hardwareMac")
    private String hardwareMac;

}
