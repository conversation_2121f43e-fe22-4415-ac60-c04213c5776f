package com.microteam.base.agency.service;

import com.microteam.base.entity.agency.FootballAgency;
import com.microteam.base.entity.agency.FootballAgencyAlbum;

import java.util.List;

public interface FootballAgencyAlbumDaoService {
    //查询机构相册是否存在
    FootballAgencyAlbum findAlbumByAgency(FootballAgency footballAgency, String name);

    FootballAgencyAlbum findAlbumById(long id);

    //分页查询机构相册
    List<FootballAgencyAlbum> findAlbumByAgencyAndPage(FootballAgency footballAgency, int page, int pageSize);
}
