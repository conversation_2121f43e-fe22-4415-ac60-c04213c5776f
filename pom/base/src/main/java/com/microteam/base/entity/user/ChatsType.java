package com.microteam.base.entity.user;


import com.microteam.base.common.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;
import java.io.Serializable;

@Entity
@Table(name = "chats_type", uniqueConstraints = @UniqueConstraint(columnNames = "typeName"))
@Getter
@Setter
public class ChatsType extends BaseEntity implements Serializable {

    @Column(name = "typeName", unique = true, nullable = false, length = 30)
    private String typeName;
    @Column(name = "description")
    private String description;

}
