package com.microteam.base.entity.lesson;


import com.microteam.base.common.base.BaseEntity;
import com.microteam.base.entity.team.FootballTeam;
import com.microteam.base.entity.user.User;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Entity;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import java.io.Serializable;

@Entity
@Table(name = "football_lesson_enroll")
@Getter
@Setter
public class FootballLessonEnroll extends BaseEntity implements Serializable {

    @ManyToOne
    @JoinColumn(name = "userId")
    private User user;
    @ManyToOne
    @JoinColumn(name = "teamId")
    private FootballTeam team;
    @ManyToOne
    @JoinColumn(name = "lessonId")
    private FootballLesson lesson;

}
