package com.microteam.base.agency.dao;

import com.microteam.base.entity.agency.FootballAgencyAlbum;
import com.microteam.base.entity.agency.FootballAgencyAlbumComment;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

public interface FootballAgencyAlbumCommentDao extends JpaRepository<FootballAgencyAlbumComment, Long>, JpaSpecificationExecutor<FootballAgencyAlbumComment> {
    //查询相册评论
    @Query("from FootballAgencyAlbumComment as footballAgencyAlbumComment " +
            "where footballAgencyAlbumComment.footballAgencyAlbum=?1 " +
            "and footballAgencyAlbumComment.deleted=false ")
    List<FootballAgencyAlbumComment> findCommentByAlbum(FootballAgencyAlbum agencyAlbum);

    //分页查询相册评论
    @Query("from FootballAgencyAlbumComment as footballAgencyAlbumComment " +
            "left join fetch footballAgencyAlbumComment.user as user " +
            "where footballAgencyAlbumComment.footballAgencyAlbum=?1 " +
            "and footballAgencyAlbumComment.deleted=false ")
    List<FootballAgencyAlbumComment> findCommentByAlbumByPage(FootballAgencyAlbum agencyAlbum, Pageable pageable);
}
