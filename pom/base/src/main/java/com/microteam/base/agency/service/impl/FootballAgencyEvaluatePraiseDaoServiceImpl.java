package com.microteam.base.agency.service.impl;

import com.microteam.base.agency.dao.FootballAgencyEvaluatePraiseDao;
import com.microteam.base.entity.agency.FootballAgencyEvaluate;
import com.microteam.base.entity.agency.FootballAgencyEvaluatePraise;
import com.microteam.base.agency.service.FootballAgencyEvaluatePraiseDaoService;
import com.microteam.base.entity.user.User;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service("footballAgencyEvaluatePraiseDaoService")
public class FootballAgencyEvaluatePraiseDaoServiceImpl implements FootballAgencyEvaluatePraiseDaoService {
    static Logger logger = Logger.getLogger(FootballAgencyEvaluatePraiseDaoServiceImpl.class.getName());
    @Autowired
    private FootballAgencyEvaluatePraiseDao dao;

    public FootballAgencyEvaluatePraiseDaoServiceImpl() {
        super();

    }

    @Override
    public List<FootballAgencyEvaluatePraise> findPraiseByEvaluate(
            FootballAgencyEvaluate agencyEvaluate) {

        return dao.findPraiseByEvaluate(agencyEvaluate);
    }

    @Override
    public FootballAgencyEvaluatePraise findPraiseByEvaluateAndUser(
            FootballAgencyEvaluate agencyEvaluate, User user) {

        return dao.findPraiseByEvaluateAndUser(agencyEvaluate, user);
    }

}
