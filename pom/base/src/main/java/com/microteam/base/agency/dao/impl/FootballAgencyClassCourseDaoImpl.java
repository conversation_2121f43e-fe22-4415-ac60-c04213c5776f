//package com.microteam.base.agency.dao.impl;
//
//import com.microteam.base.agency.dao.FootballAgencyClassCourseDao;
//import com.microteam.base.entity.agency.FootballAgencyClassCourse;
//import com.microteam.base.common.base.AbstractHibernateDao;
//import org.apache.log4j.Logger;
//import org.hibernate.query.Query;
//import org.springframework.stereotype.Repository;
//
//import java.util.List;
//
//@Repository("footballAgencyClassCourseDao")
//public class FootballAgencyClassCourseDaoImpl extends AbstractHibernateDao<FootballAgencyClassCourse> implements FootballAgencyClassCourseDao {
//    static Logger logger = Logger.getLogger(FootballAgencyClassCourseDaoImpl.class.getName());
//
//    public FootballAgencyClassCourseDaoImpl() {
//        super();
//        setClazz(FootballAgencyClassCourse.class);
//    }
//
//    //查询指定机构班级下的课程列表
//    @Override
//    public List<FootballAgencyClassCourse> findByAgencyClassIdForPage(Long agencyClassId, int page, int pageSize) {
//        String hql = "from FootballAgencyClassCourse as agencyClassCourse " +
//                "where agencyClassCourse.footballAgencyClass.id = (:agencyClassId) " +
//                "and agencyClassCourse.deleted=false ";
//        Query query = getCurrentSession().createQuery(hql)
//                .setParameter("agencyClassId", agencyClassId);
//        query.setFirstResult((page - 1) * pageSize);
//        query.setMaxResults(pageSize);
//        List<FootballAgencyClassCourse> list = query.list();
//        return list;
//    }
//
//    @Override
//    public FootballAgencyClassCourse findById(long id) {
//        String hql = "from FootballAgencyClassCourse as agencyClassCourse " +
//                "left join fetch agencyClassCourse.footballAgencyClass as footballAgencyClass " +
//                "left join fetch footballAgencyClass.footballAgency as footballAgency " +
//                "where agencyClassCourse.id=? " +
//                "and agencyClassCourse.deleted=false ";
//        Query query = getCurrentSession().createQuery(hql).setParameter(0, id);
//        List<FootballAgencyClassCourse> list = query.list();
//        if (list != null && list.size() > 0) {
//            return list.get(0);
//        }
//        return null;
//    }
//
//
//}
