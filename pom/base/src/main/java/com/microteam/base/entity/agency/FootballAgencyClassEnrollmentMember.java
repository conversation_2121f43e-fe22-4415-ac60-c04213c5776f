package com.microteam.base.entity.agency;


import com.microteam.base.common.base.BaseEntity;
import com.microteam.base.entity.user.User;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;
import java.io.Serializable;


@Entity
@Table(name = "football_agency_class_enrollment_member")
@Getter
@Setter
public class FootballAgencyClassEnrollmentMember extends BaseEntity implements Serializable {

    @ManyToOne
    @JoinColumn(name = "userId", nullable = false)
    private User user;
    @ManyToOne
    @JoinColumn(name = "enrollMentId", nullable = false)
    private FootballAgencyClassEnrollment footballAgencyClassEnrollment;
    @Column(name = "userRole", nullable = false)
    private short userRole;
    @Column(name = "birthDay", length = 100)
    private String birthDay;
    @Column(name = "homeAddr", length = 200)
    private String homeAddr;
    @Column(name = "headImgNetUrl", length = 250)
    private String headImgNetUrl;
    @Column(name = "headImgUrl", length = 250)
    private String headImgUrl;
    @Column(name = "headImgName", length = 50)
    private String headImgName;
    @Column(name = "headImgLength")
    private Long headImgLength;
    @Column(name = "myPhone", length = 50)
    private String myPhone;
    @Column(name = "parentPhone", length = 50)
    private String parentPhone;
    @Column(name = "posInTeam")
    private Short posInTeam;
    @Column(name = "habitFoot")
    private Short habitFoot;
    @Column(name = "resume", length = 65535)
    private String resume;
    @Column(name = "duty", length = 100)
    private String duty;

}
