package com.microteam.base.agency.service.impl;

import com.microteam.base.agency.dao.FootballAgencyFansDao;
import com.microteam.base.agency.service.FootballAgencyFansDaoService;
import com.microteam.base.entity.agency.FootballAgency;
import com.microteam.base.entity.agency.FootballAgencyClass;
import com.microteam.base.entity.agency.FootballAgencyFans;
import com.microteam.base.entity.user.User;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.List;

@Service("footballAgencyFansDaoService")
public class FootballAgencyFansDaoServiceImpl implements FootballAgencyFansDaoService {
    static Logger logger = Logger.getLogger(FootballAgencyFansDaoServiceImpl.class.getName());
    @Autowired
    private FootballAgencyFansDao dao;

    public FootballAgencyFansDaoServiceImpl() {
        super();

    }

    //查询机构下的所有粉丝
    @Override
    public List<FootballAgencyFans> findFansByAgency(
            FootballAgency footballAgency) {

        return dao.findFansByAgency(footballAgency);
    }

    @Override
    public List<FootballAgencyFans> findFansByAgencyAndUser(
            FootballAgency footballAgency, User user) {

        return dao.findFansByAgencyAndUser(footballAgency, user);
    }

    //查询机构班级下粉丝数量
    @Override
    public List<FootballAgencyFans> findFansByAgencyAndClass(
            FootballAgency footballAgency, FootballAgencyClass agencyClass) {

        return dao.findFansByAgencyAndClass(footballAgency, agencyClass);
    }

    //分页查询机构班级的粉丝申请列表
    @Override
    public List<FootballAgencyFans> findFansByAgencyAndClassForPage(FootballAgency footballAgency, FootballAgencyClass agencyClass, int page, int pageSize) {
        if (page > 0) {
            page--;
        }
        Pageable pageable = PageRequest.of(page, pageSize);
        return dao.findFansByAgencyAndClassForPage(footballAgency, agencyClass, pageable);
    }

    //确定用户是不是班级的粉丝
    @Override
    public FootballAgencyFans findFansByAgencyAndClassAndUser(
            FootballAgency footballAgency, FootballAgencyClass agencyClass,
            User user) {

        return dao.findFansByAgencyAndClassAndUser(footballAgency, agencyClass, user);
    }

    //分页查询我关注的机构
//    @Override
//    public List<FootballAgencyFans> findFansByUserAndCityCodeAndCountyCodeAndAgencyNameForPage(User user, int page,
//                                                                                               int pageSize, String cityCode, String countyCode, String search) {
//
//        return dao.findFansByUserAndCityCodeAndCountyCodeAndAgencyNameForPage(user, page, pageSize, cityCode, countyCode, search);
//    }

    @Override
    public FootballAgencyFans findFansById(long id) {

        return dao.findFansById(id);
    }

    @Override
    public void delFansByAgency(FootballAgency footballAgency) {

        dao.delFansByAgency(footballAgency);

    }

    @Override
    public List<FootballAgencyFans> findFansByUser(User user) {

        return dao.findFansByUser(user);
    }

}
