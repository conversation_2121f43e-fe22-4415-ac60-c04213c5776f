package com.microteam.base.common.util.common;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONException;
import com.alibaba.fastjson.JSONObject;
import com.microteam.base.common.pojo.MtJavaServerResponseBodyPojo;
import com.microteam.base.common.pojo.MtJavaServerResponseBodyResultPojo;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Component
public class CommonUtil {
    private static Logger logger = Logger.getLogger(CommonUtil.class.getName());

    public static String changeResultToString(MtJavaServerResponseBodyResultPojo resultPojo) {
        if (resultPojo != null) {
            try {
                Map<String, Object> map1 = new HashMap<>();
                map1.put("code", resultPojo.getCode());
                map1.put("message", resultPojo.getMessage());
                JSONArray jArray1 = new JSONArray();
                jArray1.add(0, map1);
                return jArray1.toString();
            } catch (JSONException e) {
                logger.debug(e);
            }
        }
        return null;
    }

    public static void errorException(MtJavaServerResponseBodyPojo responseBodyPojo, Long createTimeStart, String methodName) {
        Long createTimeEnd;
        //创建response中result属性对象
        MtJavaServerResponseBodyResultPojo responseBodyResultPojo = new MtJavaServerResponseBodyResultPojo();
        responseBodyResultPojo.setCode("9999");
        responseBodyResultPojo.setMessage(methodName + " mvc Exception Error.");
        String resultString = changeResultToString(responseBodyResultPojo);
        responseBodyPojo.setResult(resultString);
        createTimeEnd = System.currentTimeMillis();
        responseBodyPojo.setDuration(createTimeEnd - createTimeStart);
    }

    public static void pageException(MtJavaServerResponseBodyPojo responseBodyPojo, Long createTimeStart) {
        Long createTimeEnd;
        MtJavaServerResponseBodyResultPojo responseBodyResultPojo = new MtJavaServerResponseBodyResultPojo();
        responseBodyResultPojo.setCode("9998");
        responseBodyResultPojo.setMessage("The pageSize or cursor Error.");
        String resultString = changeResultToString(responseBodyResultPojo);
        responseBodyPojo.setResult(resultString);
        createTimeEnd = System.currentTimeMillis();
        responseBodyPojo.setDuration(createTimeEnd - createTimeStart);
    }

    public static boolean checkResultFromResponsebodypojo(MtJavaServerResponseBodyPojo responsebodypojoId) throws JSONException {
        String result = responsebodypojoId.getResult();
        JSONArray jArray = JSON.parseArray(result);
        JSONObject json = jArray.getJSONObject(0);
        return json.getString("code").equals("0000");
    }



    public static MtJavaServerResponseBodyPojo setSuccess(MtJavaServerResponseBodyResultPojo resultPojo,MtJavaServerResponseBodyPojo responseBodyPojo) {
        resultPojo.setCode("0000");
        resultPojo.setMessage("success.");
        String resultString = changeResultToString(resultPojo);
        responseBodyPojo.setResult(resultString);
        return responseBodyPojo;
    }

    public static MtJavaServerResponseBodyPojo setError(String code,String message,MtJavaServerResponseBodyResultPojo resultPojo,MtJavaServerResponseBodyPojo responseBodyPojo) {
        resultPojo.setCode(code);
        resultPojo.setMessage(message);
        String resultString = changeResultToString(resultPojo);
        responseBodyPojo.setResult(resultString);
        return responseBodyPojo;
    }
}
