package com.microteam.base.entity.team;

import com.microteam.base.common.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

@Entity
@Table(name = "running_thermogram")
@Getter
@Setter
public class RunningThermogram extends BaseEntity implements Serializable {
    @Column
    private Long gameId;
    @Column
    private Long teamId;
    @Column
    private Long userId;
    @Column(columnDefinition = "MEDIUMTEXT")
    private String thermogram;
    @Column
    private Integer isChangeSides;
    @Temporal(TemporalType.TIMESTAMP)
    @Column
    private Date updateTime;
}
