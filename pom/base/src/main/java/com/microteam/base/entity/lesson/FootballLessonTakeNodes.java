package com.microteam.base.entity.lesson;


import com.microteam.base.entity.team.FootballTeam;
import com.microteam.base.entity.user.User;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

@Entity
@Table(name = "football_lesson_takenotes")
@Getter
@Setter
public class FootballLessonTakeNodes implements Serializable {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false)
    protected Long id;
    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "create_time", nullable = false, length = 19)
    protected Date createTime;
    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "update_time", nullable = false, length = 19)
    protected Date updateTime;
    @Column(name = "deleted", nullable = false)
    protected boolean deleted;

    @ManyToOne
    @JoinColumn(name = "user_id", nullable = false)
    private User user;
    @ManyToOne
    @JoinColumn(name = "team_id", nullable = false)
    private FootballTeam footballTeam;
    @ManyToOne
    @JoinColumn(name = "lesson_id", nullable = false)
    private FootballLesson lesson;
    @Column(name = "goals_for", nullable = false)
    private Long goalsfor; //进球数
    @Column(name = "assist", nullable = false)
    private Long assist;  //助攻
    @Column(name = "fault", nullable = false)
    private Long fault;//失误
    @Column(name = "shoot", nullable = false)
    private Long shoot;//射正
    @Column(name = "shoot_aside", nullable = false)
    private Long shootAside;//射偏
    @Column(name = "wave_shot", nullable = false)
    private Long waveShot;//浪射
    @Column(name = "hold_up", nullable = false)
    private Long holdUp;//抢断
    @Column(name = "excel", nullable = false)
    private Long excel;//过人
    @Column(name = "corner", nullable = false)
    private Long corner;//角球
    @Column(name = "free_kick", nullable = false)
    private Long freeKick;//任意球
    @Column(name = "penaltykick", nullable = false)
    private Long penaltyKick;//点球
    @Column(name = "redcard", nullable = false)
    private Long redCard;//红牌
    @Column(name = "yellowcard", nullable = false)
    private Long yellowCard;//黄牌
    @Column(name = "own_goal", nullable = false)
    private Long ownGoal;//乌龙球
    @Column(name = "menace")
    private Long menace;//威胁球
    @Column(name = "save")
    private Long save;//解围
    @Column(name = "foul")
    private Long foul;//犯规
    @Column(name = "offSide")
    private Long offSide;//越位
    @Column(name = "roof")
    private Long roof;//冒顶
    @Column(name = "head")
    private Long head;//头球
    @Column(name = "stopFault")
    private Long stopFault;//停球失误
    @Column(name = "passFault")
    private Long passFault;//传球失误
    @Column(name = "defendFault")
    private Long defendFault;//防守失误
    @Column(name = "kickEmpty")
    private Long kickEmpty;//踢空
    @Column(name = "version", nullable = false)
    private Long version;


    public void setGoalsfor(Long goalsfor) {
        if (goalsfor == null) {
            goalsfor = 0L;
        }
        this.goalsfor = goalsfor;
    }


    public void setAssist(Long assist) {
        if (assist == null) {
            assist = 0L;
        }
        this.assist = assist;
    }


    public void setFault(Long fault) {
        if (fault == null) {
            fault = 0L;
        }
        this.fault = fault;
    }


    public void setShoot(Long shoot) {
        if (shoot == null) {
            shoot = 0L;
        }
        this.shoot = shoot;
    }

    public void setShootAside(Long shootAside) {
        if (shootAside == null) {
            shootAside = 0L;
        }
        this.shootAside = shootAside;
    }


    public void setWaveShot(Long waveShot) {
        if (waveShot == null) {
            waveShot = 0L;
        }
        this.waveShot = waveShot;
    }


    public void setHoldUp(Long holdUp) {
        if (holdUp == null) {
            holdUp = 0L;
        }
        this.holdUp = holdUp;
    }


    public void setExcel(Long excel) {
        if (excel == null) {
            excel = 0L;
        }
        this.excel = excel;
    }


    public void setCorner(Long corner) {
        if (corner == null) {
            corner = 0L;
        }
        this.corner = corner;
    }

    public void setFreeKick(Long freeKick) {
        if (freeKick == null) {
            freeKick = 0L;
        }
        this.freeKick = freeKick;
    }

    public void setPenaltyKick(Long penaltyKick) {
        if (penaltyKick == null) {
            penaltyKick = 0L;
        }
        this.penaltyKick = penaltyKick;
    }

    public void setRedCard(Long redCard) {
        if (redCard == null) {
            redCard = 0L;
        }
        this.redCard = redCard;
    }


    public void setYellowCard(Long yellowCard) {
        if (yellowCard == null) {
            yellowCard = 0L;
        }
        this.yellowCard = yellowCard;
    }

    public void setOwnGoal(Long ownGoal) {
        if (ownGoal == null) {
            ownGoal = 0L;
        }
        this.ownGoal = ownGoal;
    }

    public void setMenace(Long menace) {
        if (menace == null) {
            menace = 0L;
        }
        this.menace = menace;
    }


    public void setSave(Long save) {
        if (save == null) {
            save = 0L;
        }
        this.save = save;
    }


    public void setFoul(Long foul) {
        if (foul == null) {
            foul = 0L;
        }
        this.foul = foul;
    }


    public void setOffSide(Long offSide) {
        if (offSide == null) {
            offSide = 0L;
        }
        this.offSide = offSide;
    }


    public void setRoof(Long roof) {
        if (roof == null) {
            roof = 0L;
        }
        this.roof = roof;
    }


    public void setHead(Long head) {
        if (head == null) {
            head = 0L;
        }
        this.head = head;
    }


    public void setStopFault(Long stopFault) {
        if (stopFault == null) {
            stopFault = 0L;
        }
        this.stopFault = stopFault;
    }


    public Long getPassFault() {
        if (passFault == null) {
            return 0L;
        }
        return passFault;
    }


    public void setPassFault(Long passFault) {
        if (passFault == null) {
            passFault = 0L;
        }
        this.passFault = passFault;
    }


    public void setDefendFault(Long defendFault) {
        if (defendFault == null) {
            defendFault = 0L;
        }
        this.defendFault = defendFault;
    }


    public void setKickEmpty(Long kickEmpty) {
        if (kickEmpty == null) {
            kickEmpty = 0L;
        }
        this.kickEmpty = kickEmpty;
    }

}
