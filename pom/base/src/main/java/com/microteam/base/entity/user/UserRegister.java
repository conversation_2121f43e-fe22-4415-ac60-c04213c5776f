package com.microteam.base.entity.user;


import com.microteam.base.common.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

@Entity
@Table(name = "user_register")
@Getter
@Setter
public class UserRegister extends BaseEntity implements Serializable {
    @ManyToOne
    @JoinColumn(name = "userId", nullable = false)
    private User user;
    @ManyToOne
    @JoinColumn(name = "deviceId")
    private DeviceInfo deviceInfo;
    @Column(name = "audit", nullable = false)
    private short audit;
    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "audiTime", nullable = false, length = 19)
    private Date audiTime;
    @Column(name = "auditorId", length = 30)
    private String auditorId;
    @Column(name = "longitude")
    private String longitude;
    @Column(name = "latitude")
    private String latitude;
    @Column(name = "registerMode", length = 30)
    private String registerMode;
    @Column(name = "clientMode", length = 30)
    private String clientMode;

    public Date getAudiTime() {
        if (this.audiTime == null) {
            return null;
        }
        return (Date) this.audiTime.clone();
    }

    public void setAudiTime(Date audiTime) {
        this.audiTime = (Date) audiTime.clone();
    }

}
