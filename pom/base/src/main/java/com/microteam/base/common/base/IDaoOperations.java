//package com.microteam.base.common.base;
//
//import java.io.Serializable;
//import java.util.List;
//
//public interface IDaoOperations<T extends Serializable> {
//
//    T findOne(final long id);
//
//    List<T> findAll();
//
//    void create(final T entity);
//
////    Boolean save(final T entity);
//
//    T save(final T entity);
//
//    //Boolean merge(final T entity);
//
//    T update(final T entity);
//
//    void delete(final T entity);
//
//    void deleteById(final long entityId);
//
//}
