package com.microteam.base.common.pojo.user;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Getter;
import lombok.Setter;


@JsonIgnoreProperties(ignoreUnknown = true)
@Getter
@Setter
public class AppUserRegisterRequestBodyPojo implements java.io.Serializable {

    private String validCode;   //register注册时，手机短信验证码；
    private String userName;    //注册或登录，用户名
    private String nickName;    //注册昵称；
    private String passWord;    //注册或登录，密码；
    private String identifierNumber;  //手机序列号或者Id，唯一识别手机的
    private String deviceName;    //设备名称，例如：iPhone OS
    private String phoneVersion;  //手机系统版本:例如 5.1.1
    private String phoneModel;    //手机型号: 例如 iPod touch
    private String appCurVersion; //应用版本号：例如 1。0
    private String longitude;     //手机APP 经度 地址；
    private String latitude;      //手机APP 纬度 地址；
    private String teamsType;     //目前球队分类：footballTeams（足球）; basketballTeams(篮球);
    private String terminalMode;  //终端方式 :1:ios;2:android; 3:web
    private String clientMode;    //1:terminal(手机终端) 2:mail(邮箱);3:wechat(微信);4:qq;5:weibo(微博);
    private String bindAccount;   //绑定的第三方唯一标识
    private String bindName;     //绑定的第三方昵称
    private String headImgNetUrl; //第三方头像网络地址
    private String deviceTokens;  //第三方透传消息token
    private String countryName;   //国家代号 （CN：中国   AU：澳大利亚   UK：英国   CAN：加拿大   USA：美国  HK：香港）

}
