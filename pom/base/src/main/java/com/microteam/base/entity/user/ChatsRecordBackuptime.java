package com.microteam.base.entity.user;


import com.microteam.base.common.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.io.Serializable;

@Entity
@Table(name = "chats_record_backuptime")
@Getter
@Setter
public class ChatsRecordBackuptime extends BaseEntity implements Serializable {

    @Column(name = "name", nullable = false, length = 30)
    private String name;
    @Column(name = "content", length = 250)
    private String content;
    @Column(name = "backupTime", nullable = false)
    private long backupTime;
    @Column(name = "enabled", nullable = false)
    private boolean enabled;
    @Column(name = "description")
    private String description;

}
