package com.microteam.base.integral.service.impl;

import com.microteam.base.integral.dao.FootballIntegralTypeDao;
import com.microteam.base.integral.service.FootballIntegralTypeDaoService;
import com.microteam.base.entity.integral.FootballIntegralType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service(value = "footballIntegralTypeDaoService")
public class FootballIntegralTypeDaoServiceImpl implements FootballIntegralTypeDaoService {

    @Autowired
    private FootballIntegralTypeDao footballIntegralTypeDao;

    @Override
    public List<FootballIntegralType> findAll() {
        return footballIntegralTypeDao.findAll();
    }
}
