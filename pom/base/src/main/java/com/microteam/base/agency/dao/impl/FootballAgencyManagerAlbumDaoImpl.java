//package com.microteam.base.agency.dao.impl;
//
//import com.microteam.base.agency.dao.FootballAgencyManagerAlbumDao;
//import com.microteam.base.entity.agency.FootballAgencyManager;
//import com.microteam.base.entity.agency.FootballAgencyManagerAlbum;
//import com.microteam.base.common.base.AbstractHibernateDao;
//import org.apache.log4j.Logger;
//import org.hibernate.query.Query;
//import org.springframework.stereotype.Repository;
//
//import java.util.List;
//
//@Repository("footballAgencyManagerAlbumDao")
//public class FootballAgencyManagerAlbumDaoImpl extends
//        AbstractHibernateDao<FootballAgencyManagerAlbum>
//        implements FootballAgencyManagerAlbumDao {
//    static Logger logger = Logger.getLogger(FootballAgencyManagerAlbumDaoImpl.class.getName());
//
//    public FootballAgencyManagerAlbumDaoImpl() {
//        super();
//        setClazz(FootballAgencyManagerAlbum.class);
//
//    }
//
//    @SuppressWarnings("unchecked")
//    @Override
//    public List<FootballAgencyManagerAlbum> findAlbumByManager(
//            FootballAgencyManager agencyManager) {
//
//        String hql = "from FootballAgencyManagerAlbum as agencyManagerAlbum where agencyManagerAlbum.footballAgencyManager=? and agencyManagerAlbum.deleted=false order by agencyManagerAlbum.createTime desc ";
//        Query query = getCurrentSession().createQuery(hql).setParameter(0, agencyManager);
//        List<FootballAgencyManagerAlbum> list = query.list();
//        return list;
//    }
//
//
//}
