//package com.microteam.base.userHelp.dao.impl;
//
//
//import com.microteam.base.common.base.AbstractHibernateDao;
//import com.microteam.base.userHelp.dao.UserHelpFeedbackDao;
//import com.microteam.base.entity.userHelp.UserHelpFeedback;
//import org.apache.log4j.Logger;
//import org.hibernate.query.Query;
//import org.springframework.stereotype.Repository;
//
//import java.util.List;
//
//@Repository("userHelpFeedbackDao")
//public class UserHelpFeedbackDaoImpl extends AbstractHibernateDao<UserHelpFeedback> implements UserHelpFeedbackDao {
//    static Logger logger = Logger.getLogger(UserHelpFeedbackDaoImpl.class.getName());
//
//    public UserHelpFeedbackDaoImpl() {
//        super();
//        setClazz(UserHelpFeedback.class);
//    }
//
//    @Override
//    public UserHelpFeedback findById(Long id) {
//        String hql = "from UserHelpFeedback as userHelpFeedback " +
//                "where userHelpFeedback.id=? " +
//                "and userHelpFeedback.deleted=false";
//        Query query = getCurrentSession().createQuery(hql).setParameter(0, id);
//        List<UserHelpFeedback> list = query.list();
//        if (list.size() == 1) {
//            UserHelpFeedback userHelpFeedback = list.get(0);
//            return userHelpFeedback;
//        }
//        return null;
//    }
//
//    @Override
//    public UserHelpFeedback findByUserIdAndMessage(Long userId, String message) {
//        String hql = "from UserHelpFeedback as userHelpFeedback " +
//                "where userHelpFeedback.user.id = (:userId) " +
//                "and userHelpFeedback.feedBackMessage = (:message) " +
//                "and userHelpFeedback.deleted=false ";
//        Query query = getCurrentSession().createQuery(hql)
//                .setParameter("userId", userId)
//                .setParameter("message", message);
//        List<UserHelpFeedback> list = query.list();
//        if (list.size() == 1) {
//            UserHelpFeedback userHelpFeedback = list.get(0);
//            return userHelpFeedback;
//        }
//        return null;
//    }
//
//
//}