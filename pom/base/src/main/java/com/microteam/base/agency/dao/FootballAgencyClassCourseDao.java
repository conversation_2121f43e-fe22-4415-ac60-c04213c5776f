package com.microteam.base.agency.dao;

import com.microteam.base.entity.agency.FootballAgencyClassCourse;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

public interface FootballAgencyClassCourseDao extends JpaRepository<FootballAgencyClassCourse, Long>, JpaSpecificationExecutor<FootballAgencyClassCourse> {
    //查询指定机构班级下的课程列表
    @Query("from FootballAgencyClassCourse as agencyClassCourse " +
            "where agencyClassCourse.footballAgencyClass.id = (:agencyClassId) " +
            "and agencyClassCourse.deleted=false ")
    List<FootballAgencyClassCourse> findByAgencyClassIdForPage(@Param("agencyClassId") Long agencyClassId, Pageable pageable);

    //根据Id
    @Query("from FootballAgencyClassCourse as agencyClassCourse " +
            "left join fetch agencyClassCourse.footballAgencyClass as footballAgencyClass " +
            "left join fetch footballAgencyClass.footballAgency as footballAgency " +
            "where agencyClassCourse.id=?1 " +
            "and agencyClassCourse.deleted=false ")
    FootballAgencyClassCourse findById(long id);
}
