package com.microteam.base.lesson.service.impl;

import com.microteam.base.entity.lesson.FootballLessonEnroll;
import com.microteam.base.lesson.dao.FootballLessonEnrollDao;
import com.microteam.base.lesson.service.FootballLessonEnrollDaoService;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.math.BigInteger;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

@Service("footballLessonEnrollDaoService")
public class FootballLessonEnrollDaoServiceImpl implements FootballLessonEnrollDaoService {

    static Logger logger = Logger.getLogger(FootballLessonEnrollDaoServiceImpl.class.getName());


    @Autowired
    private FootballLessonEnrollDao dao;

    @Override
    public List<FootballLessonEnroll> findByUserIdForDay(Long userId, Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        Date zero = calendar.getTime();
        Calendar calendar2 = Calendar.getInstance();
        calendar2.setTime(date);
        calendar2.set(Calendar.HOUR_OF_DAY, 23);
        calendar2.set(Calendar.MINUTE, 59);
        calendar2.set(Calendar.SECOND, 59);
        Date lastDay = calendar2.getTime();
        return dao.findByUserIdForDay(userId, zero, lastDay);
    }

    @Override
    public List<FootballLessonEnroll> findByTeamId(Long teamId) {
        return dao.findByTeamId(teamId);
    }

    @Override
    public List<BigInteger> findFinishedByUserIdOrderByStartTimeDescForPage(Long userId, Integer page, Integer pageSize) {
        if (page > 0) {
            page--;
        }
        Pageable pageable = PageRequest.of(page, pageSize);
        return dao.findFinishedByUserIdOrderByStartTimeDescForPage(userId, pageable);
    }

    @Override
    public List<BigInteger> findUnFinishedByUserIdOrderByStartTimeForPage(Long userId, Integer page, Integer pageSize) {
        if (page > 0) {
            page--;
        }
        Pageable pageable = PageRequest.of(page, pageSize);
        return dao.findUnFinishedByUserIdOrderByStartTimeForPage(userId, pageable);
    }

    @Override
    public List<FootballLessonEnroll> findByLessonIdAndTeamId(Long lessonId, Long teamId) {
        List<FootballLessonEnroll> list = dao.findByLessonIdAndTeamId(lessonId, teamId);
        if (list != null && list.size() > 0) {
            return list;
        }
        return new ArrayList<>();
    }

    @Override
    public List<FootballLessonEnroll> findByLessonId(Long lessonId) {
        return dao.findByLessonId(lessonId);
    }

    @Override
    public FootballLessonEnroll findByUserIdAndLessonIdAndTeamId(Long userId, Long lessonId, Long teamId) {
        return dao.findByUserIdAndLessonIdAndTeamId(userId, lessonId, teamId);
    }

    @Override
    public FootballLessonEnroll findByUserIdAndLessonId(Long userId, Long lessonId) {
        return dao.findByUserIdAndLessonId(userId, lessonId);
    }

    @Override
    public int deleteByTeamId(Long teamId) {
        return dao.deleteByTeamId(teamId);
    }

    @Override
    public FootballLessonEnroll save(FootballLessonEnroll footballLessonEnroll) {
        return dao.save(footballLessonEnroll);
    }

    @Override
    public List<FootballLessonEnroll> saveAll(List<FootballLessonEnroll> list) {
        return dao.saveAll(list);
    }
}
