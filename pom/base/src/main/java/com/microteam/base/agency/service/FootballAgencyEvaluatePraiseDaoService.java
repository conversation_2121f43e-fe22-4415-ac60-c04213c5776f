package com.microteam.base.agency.service;

import com.microteam.base.entity.agency.FootballAgencyEvaluate;
import com.microteam.base.entity.agency.FootballAgencyEvaluatePraise;
import com.microteam.base.entity.user.User;

import java.util.List;

public interface FootballAgencyEvaluatePraiseDaoService {
    //查询评论点赞
    List<FootballAgencyEvaluatePraise> findPraiseByEvaluate(FootballAgencyEvaluate agencyEvaluate);

    //查询用户对评论的点赞
    FootballAgencyEvaluatePraise findPraiseByEvaluateAndUser(FootballAgencyEvaluate agencyEvaluate, User user);
}
