package com.microteam.base.grounds.dao;


import com.microteam.base.entity.grounds.FootballGroundType;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

public interface FootballGroundTypeDao extends JpaRepository<FootballGroundType, Long>, JpaSpecificationExecutor<FootballGroundType> {

    @Query("from FootballGroundType as footballGroundsType " +
            "where footballGroundsType.typeName=(:typeName) " +
            "and footballGroundsType.deleted=false")
    FootballGroundType findByTypeName(@Param("typeName") String typeName);

}