package com.microteam.base.agency.service;

import com.microteam.base.entity.agency.FootballAgency;
import com.microteam.base.entity.agency.FootballAgencyClass;
import com.microteam.base.entity.agency.FootballAgencyClassCourse;
import com.microteam.base.entity.agency.FootballAgencyCoach;
import com.microteam.base.entity.user.User;

import java.util.List;

public interface FootballAgencyCoachDaoService {
    //查询教练
    FootballAgencyCoach findByAgencyIdAndAgencyClassIdAndAgencyClassCourseIdAndUserId(Long agencyId, Long agencyClassId, Long agencyClassCourseId, Long userId);

    //查询教练
    List<FootballAgencyCoach> findByAgencyIdAndAgencyClassIdAndUserId(Long agencyId, Long agencyClassId, Long userId);

    //查询是不是机构下的教练
    List<FootballAgencyCoach> findByAgencyIdAndUserId(Long agencyId, Long userId);

    //查询班级课程教练
    List<FootballAgencyCoach> findByAgencyIdAndAgencyClassIdAndAgencyClassCourseId(Long agencyId, Long agencyClassId, Long agencyClassCourseId);

    //查询机构下的所有教练
    List<FootballAgencyCoach> findByAgencyId(Long agencyId);

    List<FootballAgencyCoach> findByAgencyIdForPage(Long agencyId, int pageSize, int page);

    List<FootballAgencyCoach> findByAgencyIdAndSearchForPage(Long agencyId, int pageSize, int page, String coachName);

    //查询课程教练名单列表
    List<FootballAgencyCoach> findByAgencyIdAndAgencyClassIdAndAgencyClassCourseIdForPage(Long agencyId, Long agencyClassId, Long agencyClassCourseId, int pageSize, int page);

//    List<FootballAgencyCoach> findCoachByUser(User user, String cityCode, String countyCode, String search);

    FootballAgencyCoach findAllById(long id);

    FootballAgencyCoach findById(long id);

    //查询机构下的教练
    List<FootballAgencyCoach> findByAgencyIdAndAgencyClassId(Long agencyId, Long agencyClassId);

    List<FootballAgencyCoach> findByAgencyIdAndAgencyClassIdForPage(Long agencyId, Long agencyClassId, int page, int pageSize);

    List<FootballAgencyCoach> findByAgencyIdAndAgencyClassIdAndCoachNameForPage(Long agencyId, Long agencyClassId, int page, int pageSize, String coachName);

    //删除班级下的所有教练
    int delByAgencyIdAndAgencyClassId(Long agencyId, Long agencyClassId);

    //查询教练是否报名了
    List<FootballAgencyCoach> findCoachIsEnrolled(FootballAgency agency, User user);

    //分页查询机构教练报名申请
    List<FootballAgencyCoach> findCoachEnrollByAgencyForPage(FootballAgency agency, int pageSize, int page);

    //查询课程下的所有教练
    List<FootballAgencyCoach> findCoachByCourse(FootballAgencyClassCourse agencyClassCourse);

    List<FootballAgencyCoach> findCoachBySearch(FootballAgency agency, String coachName);

    List<FootballAgencyCoach> findCoachEnrollByAgency(FootballAgency agency);

    //分页模糊查询教练申请表
    List<FootballAgencyCoach> findCoachEnrollBySearch(FootballAgency agency, int pageSize, int page, String coachName);

    //删除课程下的教练
    int delCoachByAgency(FootballAgency agency, FootballAgencyClass agencyClass, FootballAgencyClassCourse agencyClassCourse);
}
