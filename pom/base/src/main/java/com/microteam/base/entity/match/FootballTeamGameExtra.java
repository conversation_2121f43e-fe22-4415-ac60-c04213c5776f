package com.microteam.base.entity.match;


import com.microteam.base.entity.team.FootballTeamGame;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

import static javax.persistence.GenerationType.IDENTITY;

@Entity
@Table(name = "football_team_game_extra")
@Getter
@Setter
public class FootballTeamGameExtra implements Serializable {
    @Id
    @GeneratedValue(strategy = IDENTITY)
    @Column(name = "id", unique = true, nullable = false)
    private Long id;
    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "createTime", nullable = false, length = 19)
    private Date createTime;
    @Column(name = "isOverTime", nullable = false)
    private Boolean isOverTime;
    @Column(name = "overHostScore", length = 3)
    private Integer overHostScore;
    @Column(name = "overGuestScore", length = 3)
    private Integer overGuestScore;
    @Column(name = "isSpot", nullable = false)
    private Boolean isSpot;
    @Column(name = "spotHostScore", length = 3)
    private Integer spotHostScore;
    @Column(name = "spotGuestScore", length = 3)
    private Integer spotGuestScore;
    @Column(name = "isGiveUp", nullable = false)
    private Boolean isGiveUp;
    @Column(name = "giveUpTeamId", length = 10)
    private String giveUpTeamId;
    @Column(name = "giveUpTeamName", length = 50)
    private String giveUpTeamName;
    @ManyToOne
    @JoinColumn(name = "teamGameId", nullable = false)
    private FootballTeamGame teamGame;

}
