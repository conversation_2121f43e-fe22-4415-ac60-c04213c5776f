package com.microteam.base.agency.service.impl;

import com.microteam.base.agency.dao.FootballAgencyCoachDao;
import com.microteam.base.agency.service.FootballAgencyCoachDaoService;
import com.microteam.base.entity.agency.FootballAgency;
import com.microteam.base.entity.agency.FootballAgencyClass;
import com.microteam.base.entity.agency.FootballAgencyClassCourse;
import com.microteam.base.entity.agency.FootballAgencyCoach;
import com.microteam.base.entity.user.User;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.List;

@Service("footballAgencyCoachDaoService")
public class FootballAgencyCoachDaoServiceImpl implements FootballAgencyCoachDaoService {
    static Logger logger = Logger.getLogger(FootballAgencyCoachDaoServiceImpl.class.getName());
    @Autowired
    private FootballAgencyCoachDao dao;

    @Override
    public FootballAgencyCoach findByAgencyIdAndAgencyClassIdAndAgencyClassCourseIdAndUserId(Long agencyId, Long agencyClassId, Long agencyClassCourseId, Long userId) {
        return dao.findByAgencyIdAndAgencyClassIdAndAgencyClassCourseIdAndUserId(agencyId, agencyClassId, agencyClassCourseId, userId);
    }

    @Override
    public List<FootballAgencyCoach> findByAgencyIdAndAgencyClassIdAndUserId(Long agencyId, Long agencyClassId, Long userId) {
        return dao.findByAgencyIdAndAgencyClassIdAndUserId(agencyId, agencyClassId, userId);
    }

    @Override
    public List<FootballAgencyCoach> findByAgencyIdAndUserId(Long agencyId, Long userId) {
        return dao.findByAgencyIdAndUserId(agencyId, userId);
    }

    @Override
    public List<FootballAgencyCoach> findByAgencyIdAndAgencyClassIdAndAgencyClassCourseId(Long agencyId, Long agencyClassId, Long agencyClassCourseId) {
        return dao.findByAgencyIdAndAgencyClassIdAndAgencyClassCourseId(agencyId, agencyClassId, agencyClassCourseId);
    }

    //查询机构下的所有教练
    @Override
    public List<FootballAgencyCoach> findByAgencyId(Long agencyId) {
        return dao.findByAgencyId(agencyId);
    }

    @Override
    public List<FootballAgencyCoach> findByAgencyIdForPage(Long agencyId, int pageSize, int page) {
        if (page > 0) {
            page--;
        }
        Pageable pageable = PageRequest.of(page, pageSize);
        return dao.findByAgencyIdForPage(agencyId, pageable);
    }

    @Override
    public List<FootballAgencyCoach> findByAgencyIdAndSearchForPage(Long agencyId, int pageSize, int page, String coachName) {
        if (coachName != null && !"".equals(coachName)) {
            coachName = '%' + coachName + '%';
        } else {
            coachName = "";
        }
        if (page > 0) {
            page--;
        }
        Pageable pageable = PageRequest.of(page, pageSize);
        return dao.findByAgencyIdAndSearchForPage(agencyId, coachName, pageable);
    }

    //查询课程教练名单列表
    @Override
    public List<FootballAgencyCoach> findByAgencyIdAndAgencyClassIdAndAgencyClassCourseIdForPage(Long agencyId, Long agencyClassId, Long agencyClassCourseId, int pageSize, int page) {
        if (page > 0) {
            page--;
        }
        Pageable pageable = PageRequest.of(page, pageSize);
        return dao.findByAgencyIdAndAgencyClassIdAndAgencyClassCourseIdForPage(agencyId, agencyClassId, agencyClassCourseId, pageable);
    }

//    @Override
//    public List<FootballAgencyCoach> findCoachByUser(User user, String cityCode, String countyCode, String search) {
//        return dao.findCoachByUser(user, cityCode, countyCode, search);
//    }

    @Override
    public FootballAgencyCoach findAllById(long id) {
        return dao.findAllById(id);
    }

    @Override
    public FootballAgencyCoach findById(long id) {
        return dao.findById(id);
    }

    @Override
    public List<FootballAgencyCoach> findByAgencyIdAndAgencyClassId(Long agencyId, Long agencyClassId) {
        return dao.findByAgencyIdAndAgencyClassId(agencyId, agencyClassId);
    }

    @Override
    public List<FootballAgencyCoach> findByAgencyIdAndAgencyClassIdForPage(Long agencyId, Long agencyClassId, int page, int pageSize) {
        if (page > 0) {
            page--;
        }
        Pageable pageable = PageRequest.of(page, pageSize);
        return dao.findByAgencyIdAndAgencyClassIdForPage(agencyId, agencyClassId, pageable);
    }

    @Override
    public List<FootballAgencyCoach> findByAgencyIdAndAgencyClassIdAndCoachNameForPage(Long agencyId, Long agencyClassId, int page, int pageSize, String coachName) {
        if (coachName != null && !"".equals(coachName)) {
            coachName = '%' + coachName + '%';
        } else {
            coachName = "";
        }
        if (page > 0) {
            page--;
        }
        Pageable pageable = PageRequest.of(page, pageSize);
        return dao.findByAgencyIdAndAgencyClassIdAndCoachNameForPage(agencyId, agencyClassId, coachName, pageable);
    }

    //删除班级下的所有教练
    @Override
    public int delByAgencyIdAndAgencyClassId(Long agencyId, Long agencyClassId) {
        return dao.delByAgencyIdAndAgencyClassId(agencyId, agencyClassId);
    }

    //查询教练是否报名了
    @Override
    public List<FootballAgencyCoach> findCoachIsEnrolled(FootballAgency agency, User user) {
        return dao.findCoachIsEnrolled(agency, user);
    }

    //分页查询机构教练报名申请
    @Override
    public List<FootballAgencyCoach> findCoachEnrollByAgencyForPage(FootballAgency agency, int pageSize, int page) {
        if (page > 0) {
            page--;
        }
        Pageable pageable = PageRequest.of(page, pageSize);
        return dao.findCoachEnrollByAgencyForPage(agency, pageable);
    }

    //查询课程下的所有教练
    @Override
    public List<FootballAgencyCoach> findCoachByCourse(FootballAgencyClassCourse agencyClassCourse) {
        return dao.findCoachByCourse(agencyClassCourse);
    }

    @Override
    public List<FootballAgencyCoach> findCoachBySearch(FootballAgency agency, String coachName) {
        if (coachName != null && !"".equals(coachName)) {
            coachName = '%' + coachName + '%';
        } else {
            coachName = "";
        }
        return dao.findCoachBySearch(agency, coachName);
    }

    @Override
    public List<FootballAgencyCoach> findCoachEnrollByAgency(FootballAgency agency) {
        return dao.findCoachEnrollByAgency(agency);
    }

    //分页模糊查询教练申请表
    @Override
    public List<FootballAgencyCoach> findCoachEnrollBySearch(FootballAgency agency, int pageSize, int page, String coachName) {
        if (coachName != null && !"".equals(coachName)) {
            coachName = '%' + coachName + '%';
        } else {
            coachName = "";
        }
        if (page > 0) {
            page--;
        }
        Pageable pageable = PageRequest.of(page, pageSize);
        return dao.findCoachEnrollBySearch(agency, coachName, pageable);
    }

    @Override
    public int delCoachByAgency(FootballAgency agency, FootballAgencyClass agencyClass, FootballAgencyClassCourse agencyClassCourse) {
        return dao.delCoachByAgency(agency, agencyClass, agencyClassCourse);
    }


}
