package com.microteam.base.agency.service.impl;

import com.microteam.base.agency.dao.FootballAgencyAlbumDao;
import com.microteam.base.agency.service.FootballAgencyAlbumDaoService;
import com.microteam.base.entity.agency.FootballAgency;
import com.microteam.base.entity.agency.FootballAgencyAlbum;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.List;

@Service("footballAgencyAlbumDaoService")
public class FootballAgencyAlbumDaoServiceImpl implements FootballAgencyAlbumDaoService {
    static Logger logger = Logger.getLogger(FootballAgencyAlbumDaoServiceImpl.class.getName());
    @Autowired
    private FootballAgencyAlbumDao dao;

    //查询机构相册是否存在
    @Override
    public FootballAgencyAlbum findAlbumByAgency(FootballAgency footballAgency, String name) {
        return dao.findAlbumByAgency(footballAgency, name);
    }

    @Override
    public List<FootballAgencyAlbum> findAlbumByAgencyAndPage(FootballAgency footballAgency, int page, int pageSize) {
        if (page > 0) {
            page--;
        }
        Pageable pageable = PageRequest.of(page, pageSize);
        return dao.findAlbumByAgencyAndPage(footballAgency, pageable);
    }

    @Override
    public FootballAgencyAlbum findAlbumById(long id) {
        return dao.findAlbumById(id);
    }


}
