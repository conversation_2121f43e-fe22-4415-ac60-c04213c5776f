//package com.microteam.base.agency.dao.impl;
//
//import com.microteam.base.agency.dao.FootballAgencyCoachAlbumDao;
//import com.microteam.base.entity.agency.FootballAgencyCoachAlbum;
//import com.microteam.base.common.base.AbstractHibernateDao;
//import org.apache.log4j.Logger;
//import org.hibernate.query.Query;
//import org.springframework.stereotype.Repository;
//
//import java.util.List;
//
//@Repository("footballAgencyCoachAlbumDao")
//public class FootballAgencyCoachAlbumDaoImpl extends AbstractHibernateDao<FootballAgencyCoachAlbum> implements FootballAgencyCoachAlbumDao {
//    static Logger logger = Logger.getLogger(FootballAgencyCoachAlbumDaoImpl.class.getName());
//
//    public FootballAgencyCoachAlbumDaoImpl() {
//        super();
//        setClazz(FootballAgencyCoachAlbum.class);
//    }
//
//    @Override
//    public List<FootballAgencyCoachAlbum> findByAgencyCoachId(Long agencyCoachId) {
//        String hql = "from FootballAgencyCoachAlbum as agencyCoachAlbum " +
//                "where agencyCoachAlbum.footballAgencyCoach.id = (:agencyCoachId) " +
//                "order by agencyCoachAlbum.createTime desc ";
//        Query query = getCurrentSession().createQuery(hql)
//                .setParameter("agencyCoachId", agencyCoachId);
//        return (List<FootballAgencyCoachAlbum>) query.list();
//    }
//
//
//}
