package com.microteam.base.common.util.jdbc;

import java.io.BufferedInputStream;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.sql.*;
import java.util.Properties;

public class JDBCUtil {

    private static String url;
    private static String username;
    private static String password;

    {
        init();
    }

    private JDBCUtil() {
    }


    public static Connection getConnection() throws SQLException {
        return DriverManager.getConnection(url, username, password);
    }

    public static void free(ResultSet rs, PreparedStatement pst, Connection conn) {
        try {
            if (rs != null) {
                rs.close();
            }
        } catch (SQLException e) {
            e.printStackTrace();
        } finally {
            try {
                if (pst != null) {
                    pst.close();
                }
            } catch (SQLException e) {
                e.printStackTrace();
            } finally {
                try {
                    if (conn != null) {
                        conn.close();
                    }
                } catch (SQLException e) {
                    e.printStackTrace();
                }
            }
        }
    }

    public static void init() {
        Properties prop = new Properties();
        String path;
        path = System.getProperty("vsteam.root");
        path = path + "/WEB-INF/classes/config/properties/persistence-mysql.properties";
        try {
            InputStream in = new BufferedInputStream(new FileInputStream(path));
            prop.load(in);
            in.close();
            url = prop.getProperty("jdbc.url");
            String driverClass = prop.getProperty("jdbc.driverClassName");
            username = prop.getProperty("jdbc.user");
            password = prop.getProperty("jdbc.pass");
            Class.forName(driverClass);
        } catch (ClassNotFoundException | IOException e) {
            e.printStackTrace();
        }
    }


}
