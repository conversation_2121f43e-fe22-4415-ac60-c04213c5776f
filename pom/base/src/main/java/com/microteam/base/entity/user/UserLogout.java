package com.microteam.base.entity.user;


import com.microteam.base.common.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

@Entity
@Table(name = "user_logout")
@Getter
@Setter
public class UserLogout extends BaseEntity implements Serializable {

    @ManyToOne
    @JoinColumn(name = "userId", nullable = false)
    private User user;
    @ManyToOne
    @JoinColumn(name = "deviceId")
    private DeviceInfo deviceInfo;
    @Column(name = "logoutMode")
    private Short logoutMode;
    @Column(name = "longitude")
    private String longitude;
    @Column(name = "latitude")
    private String latitude;
    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "logoutTime", nullable = false, length = 19)
    private Date logoutTime;

    public Date getLogoutTime() {
        if (this.logoutTime == null) {
            return null;
        }
        return (Date) this.logoutTime.clone();
    }

    public void setLogoutTime(Date logoutTime) {
        this.logoutTime = (Date) logoutTime.clone();
    }


}
