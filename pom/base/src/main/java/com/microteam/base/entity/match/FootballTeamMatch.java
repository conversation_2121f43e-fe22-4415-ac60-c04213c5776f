package com.microteam.base.entity.match;


import com.microteam.base.common.base.BaseEntity;
import com.microteam.base.entity.user.Groups;
import com.microteam.base.entity.user.User;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

@Entity
@Table(name = "football_team_match")
@Getter
@Setter
public class FootballTeamMatch extends BaseEntity implements Serializable {
    @ManyToOne
    @JoinColumn(name = "userId", nullable = false)
    private User user;
    @ManyToOne
    @JoinColumn(name = "groupsId", nullable = false)
    private Groups groups;
    @Column(name = "matchName", nullable = false, length = 100)
    private String matchName;
    @Column(name = "matchAbbreviation", length = 100)
    private String matchAbbreviation;
    @Column(name = "subMatchName", length = 100)
    private String subMatchName;
    @Column(name = "matchType")
    private Short matchType;
    @Column(name = "matchStatus")
    private Short matchStatus;
    @Column(name = "isHistory")
    private Boolean isHistory;
    @Column(name = "uniqueIdentify", length = 100)
    private String uniqueIdentify;
    @Column(name = "countryCode")
    private String countryCode;
    @Column(name = "provinceCode")
    private String provinceCode;
    @Column(name = "cityCode")
    private String cityCode;
    @Column(name = "countyCode")
    private String countyCode;
    @Column(name = "longitude")
    private String longitude;
    @Column(name = "latitude")
    private String latitude;
    @Column(name = "sponsor", length = 100)
    private String sponsor;
    @Column(name = "sponsorInfo", length = 65535)
    private String sponsorInfo;
    @Column(name = "logoImgNetUrl", length = 250)
    private String logoImgNetUrl;
    @Column(name = "logoImgUrl", length = 250)
    private String logoImgUrl;
    @Column(name = "logoImgName", length = 50)
    private String logoImgName;
    @Column(name = "logoImgLength")
    private Long logoImgLength;
    @Column(name = "detailLocation", length = 200)
    private String detailLocation;
    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "matchStartTime", length = 19)
    private Date matchStartTime;
    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "matchEndTime", length = 19)
    private Date matchEndTime;
    @Column(name = "numberRule")
    private Short numberRule;
    @Column(name = "teamCount")
    private Integer teamCount;
    @Column(name = "introduction", length = 65535)
    private String introduction;
    @Column(name = "regulations", length = 65535)
    private String regulations;
    @Column(name = "promotionRules")
    private Short promotionRules;
    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "enrollStartTime", length = 19)
    private Date enrollStartTime;
    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "enrollEndTime", length = 19)
    private Date enrollEndTime;
    @Column(name = "contact")
    private String contact;
    @Column(name = "telephone")
    private String telephone;
    @Column(name = "enrollCondition", length = 200)
    private String enrollCondition;
    @Column(name = "credits")
    private Integer credits;
    @Column(name = "creditsRank")
    private String creditsRank;
    @Column(name = "contractor", length = 100)
    private String contractor;//承办方
    @Column(name = "matchRule")
    private Short matchRule;//赛制  1:单循环+淘汰赛     2:双循环+淘汰赛    3:单循环    4:双循环
    @Column(name = "integralRule")
    private Short integralRule;//积分规则  1:净胜球优先   2:胜负关系优先
    @Column(name = "sponsorId")
    private Long sponsorId; // 主办方id
    @Column(name = "contractorId")
    private Long contractorId;//承办方id
    @Column(name = "state")
    private Integer state;//赛事状态
    @Column(name = "scheduleState")
    private Integer scheduleState; //日程设置状态
    @Column(name = "versusType")
    private Integer versusType; //赛事的对阵类型


    public Date getMatchStartTime() {
        if (this.matchStartTime == null) {
            return null;
        }
        return (Date) this.matchStartTime.clone();
    }

    public void setMatchStartTime(Date matchStartTime) {
        this.matchStartTime = (Date) matchStartTime.clone();
    }


    public Date getMatchEndTime() {
        if (this.matchEndTime == null) {
            return null;
        }
        return (Date) this.matchEndTime.clone();
    }

    public void setMatchEndTime(Date matchEndTime) {
        this.matchEndTime = (Date) matchEndTime.clone();
    }

    public Date getEnrollStartTime() {
        if (this.enrollStartTime == null) {
            return null;
        }
        return (Date) this.enrollStartTime.clone();
    }

    public void setEnrollStartTime(Date enrollStartTime) {
        this.enrollStartTime = (Date) enrollStartTime.clone();
    }


    public Date getEnrollEndTime() {
        if (this.enrollEndTime == null) {
            return null;
        }
        return (Date) this.enrollEndTime.clone();
    }

    public void setEnrollEndTime(Date enrollEndTime) {
        this.enrollEndTime = (Date) enrollEndTime.clone();
    }


}
