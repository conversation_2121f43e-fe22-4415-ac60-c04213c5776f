package com.microteam.base.entity.user;


import com.microteam.base.common.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.io.Serializable;

@Entity
@Table(name = "transparent_message_list")
@Getter
@Setter
public class TransparentMessageList extends BaseEntity implements Serializable {

    @Column(name = "messageType")
    private Integer messageType;
    @Column(name = "fromUser", length = 50)
    private String fromUser;
    @Column(name = "toUser", length = 50)
    private String toUser;
    @Column(name = "isRead")
    private Integer isRead;
    @Column(name = "sendMessage", length = 65535)
    private String sendMessage;

}
