package com.microteam.base.entity.lesson;

import com.microteam.base.common.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.io.Serializable;

@Entity
@Table(name = "football_lesson_training_tools")
@Getter
@Setter
public class FootballLessonTrainingTools extends BaseEntity implements Serializable {

    @Column(name = "lessonId")
    private long lessonId;    //课程ID
    @Column(name = "toolId", nullable = false)
    private long toolId;      //训练工具ID
    @Column(name = "courwareId")
    private  long courwareId;   //课件ID

}
