package com.microteam.base.agency.service.impl;

import com.microteam.base.agency.dao.FootballAgencySubalbumDao;
import com.microteam.base.entity.agency.FootballAgencyAlbum;
import com.microteam.base.entity.agency.FootballAgencySubalbum;
import com.microteam.base.agency.service.FootballAgencySubalbumDaoService;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service("footballAgencySubalbumDaoService")
public class FootballAgencySubalbumDaoServiceImpl implements FootballAgencySubalbumDaoService {
    static Logger logger = Logger.getLogger(FootballAgencySubalbumDaoServiceImpl.class.getName());
    @Autowired
    private FootballAgencySubalbumDao dao;

    public FootballAgencySubalbumDaoServiceImpl() {
        super();

    }

    //查询机构的图片列表
    @Override
    public List<FootballAgencySubalbum> findSubAlbumByAlbum(
            FootballAgencyAlbum agencyAlbum) {

        return dao.findSubAlbumByAlbum(agencyAlbum);
    }


}
