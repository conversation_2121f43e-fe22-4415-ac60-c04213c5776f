package com.microteam.base.common.util.common;

import java.io.Serializable;
import java.util.Comparator;
import java.util.Map;

public class ValueComparator implements Comparator, Serializable {

    private Map map;

    public ValueComparator(Map map) {
        this.map = map;
    }

    public int compare(Object keyA, Object keyB) {
        Comparable valueA = (Comparable) map.get(keyA);
        Comparable valueB = (Comparable) map.get(keyB);
        if (!map.get(keyA).equals(map.get(keyB))) {
            return valueB.compareTo(valueA);
        }
        return 1;
    }
}
