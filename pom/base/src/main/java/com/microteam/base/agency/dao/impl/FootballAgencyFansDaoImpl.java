//package com.microteam.base.agency.dao.impl;
//
//import com.microteam.base.agency.dao.FootballAgencyFansDao;
//import com.microteam.base.common.base.AbstractHibernateDao;
//import com.microteam.base.entity.agency.FootballAgency;
//import com.microteam.base.entity.agency.FootballAgencyClass;
//import com.microteam.base.entity.agency.FootballAgencyFans;
//import com.microteam.base.entity.user.User;
//import org.apache.log4j.Logger;
//import org.hibernate.Hibernate;
//import org.hibernate.query.Query;
//import org.springframework.stereotype.Repository;
//
//import java.util.ArrayList;
//import java.util.List;
//
//@Repository("footballAgencyFansDao")
//public class FootballAgencyFansDaoImpl extends
//        AbstractHibernateDao<FootballAgencyFans> implements FootballAgencyFansDao {
//    static Logger logger = Logger.getLogger(FootballAgencyFansDaoImpl.class.getName());
//
//    public FootballAgencyFansDaoImpl() {
//        super();
//
//        setClazz(FootballAgencyFans.class);
//    }
//
//    //查询机构下的所有粉丝
//    @SuppressWarnings("unchecked")
//    @Override
//    public List<FootballAgencyFans> findFansByAgency(
//            FootballAgency footballAgency) {
//
//        String hql = "from FootballAgencyFans as footballAgencyFans left join fetch footballAgencyFans.user as user left join fetch footballAgencyFans.footballAgencyClass as footballAgencyClass  where footballAgencyFans.footballAgency=? and footballAgencyFans.deleted=false ";
//        Query query = getCurrentSession().createQuery(hql).setParameter(0, footballAgency);
//        List<FootballAgencyFans> list = query.list();
//        return list;
//    }
//
//    @SuppressWarnings("unchecked")
//    @Override
//    public List<FootballAgencyFans> findFansByAgencyAndUser(
//            FootballAgency footballAgency, User user) {
//        String hql = "from FootballAgencyFans as footballAgencyFans left join fetch footballAgencyFans.footballAgencyClass as footballAgencyClass where footballAgencyFans.footballAgency=? and footballAgencyFans.user=? and footballAgencyFans.deleted=false ";
//        Query query = getCurrentSession().createQuery(hql).setParameter(0, footballAgency).setParameter(1, user);
//        List<FootballAgencyFans> list = query.list();
//        return list;
//    }
//
//    //查询机构班级下粉丝数量
//    @SuppressWarnings("unchecked")
//    @Override
//    public List<FootballAgencyFans> findFansByAgencyAndClass(
//            FootballAgency footballAgency, FootballAgencyClass agencyClass) {
//        String hql = "select distinct new com.microteam.base.entity.agency.FootballAgencyFans(footballAgencyFans.footballAgency,footballAgencyFans.user) " +
//                "from FootballAgencyFans as footballAgencyFans " +
//                "left join footballAgencyFans.user as user " +
//                "left join footballAgencyFans.footballAgency as footballAgency " +
//                "where footballAgencyFans.footballAgency=? " +
//                "and footballAgencyFans.footballAgencyClass=? " +
//                "and footballAgencyFans.deleted=false ";
//        Query query = getCurrentSession().createQuery(hql).setParameter(0, footballAgency).setParameter(1, agencyClass);
//        List<FootballAgencyFans> list = query.list();
//        return list;
//    }
//
//    //分页查询机构班级的粉丝申请列表
//    @SuppressWarnings("unchecked")
//    @Override
//    public List<FootballAgencyFans> findFansByAgencyAndClassForPage(
//            FootballAgency footballAgency, FootballAgencyClass agencyClass,
//            int page, int pageSize) {
//
//        String hql = "from FootballAgencyFans as footballAgencyFans left join fetch footballAgencyFans.user where footballAgencyFans.footballAgency=? and footballAgencyFans.footballAgencyClass=? and footballAgencyFans.deleted=false order by footballAgencyFans.isPermited asc,footballAgencyFans.createTime desc";
//        Query query = getCurrentSession().createQuery(hql).setParameter(0, footballAgency).setParameter(1, agencyClass);
//        query.setFirstResult((page - 1) * pageSize);
//        query.setMaxResults(pageSize);
//        List<FootballAgencyFans> list = query.list();
//        return list;
//    }
//
//    //确定用户是不是班级的粉丝
//    @SuppressWarnings("unchecked")
//    @Override
//    public FootballAgencyFans findFansByAgencyAndClassAndUser(
//            FootballAgency footballAgency, FootballAgencyClass agencyClass,
//            User user) {
//        String hql = "from FootballAgencyFans as footballAgencyFans   where footballAgencyFans.footballAgency=? and footballAgencyFans.footballAgencyClass=? and footballAgencyFans.user=? and footballAgencyFans.deleted=false ";
//        Query query = getCurrentSession().createQuery(hql).setParameter(0, footballAgency).setParameter(1, agencyClass).setParameter(2, user);
//        List<FootballAgencyFans> list = query.list();
//        if (list != null && list.size() > 0) {
//            return list.get(0);
//        }
//        return null;
//    }
//
//    //分页查询我关注的机构
//    @SuppressWarnings("unchecked")
////    @Override
////    public List<FootballAgencyFans> findFansByUserAndCityCodeAndCountyCodeAndAgencyNameForPage(User user, int page,
////                                                                                               int pageSize, String cityCode, String countyCode, String search) {
////
////        //模糊查询机构
////        String hql = "from FootballAgencyFans as footballAgencyFans left join fetch footballAgencyFans.footballAgency as footballAgency left join fetch  footballAgency.groups as groups left join fetch footballAgencyFans.footballAgencyClass as footballAgencyClass ";
////        Query query = null;
////        if (!"".equals(search)) {
////            if ("1000000".equals(cityCode) || "".equals(cityCode)) {
////                //查询全国的
////                hql += " where footballAgency.agencyName like ?  and footballAgency.countryCode='1000000'   and footballAgency.deleted=false  and footballAgencyFans.user=?  and footballAgencyFans.deleted=false order by footballAgencyFans.createTime desc ";
////                query = getCurrentSession().createQuery(hql)
////                        .setParameter(0, "%" + search + "%")
////                        .setParameter(1, user);
////                query.setFirstResult((page - 1) * pageSize);
////                query.setMaxResults(pageSize);
////            } else {
////                //查询直辖市、特别行政区
////                if ("110000".equals(cityCode) ||
////                        "120000".equals(cityCode) ||
////                        "310000".equals(cityCode) ||
////                        "500000".equals(cityCode) ||
////                        "710000".equals(cityCode) ||
////                        "810000".equals(cityCode) ||
////                        "820000".equals(cityCode)) {
////                    hql += " where footballAgency.agencyName like ?  and footballAgency.provinceCode=?   and footballAgency.deleted=false  and footballAgencyFans.user=?  and footballAgencyFans.deleted=false order by footballAgencyFans.createTime desc";
////                    query = getCurrentSession().createQuery(hql)
////                            .setParameter(0, "%" + search + "%")
////                            .setParameter(1, cityCode)
////                            .setParameter(2, user);
////                    query.setFirstResult((page - 1) * pageSize);
////                    query.setMaxResults(pageSize);
////                } else {
////                    //普通城市
////                    if ((countyCode != null) && (!countyCode.equals(""))) {
////                        hql += " where  footballAgency.agencyName like ? and footballAgency.cityCode=? and footballAgency.countyCode=?   and footballAgency.deleted=false  and footballAgencyFans.user=?  and footballAgencyFans.deleted=false order by footballAgencyFans.createTime desc";
////                        query = getCurrentSession().createQuery(hql)
////                                .setParameter(0, "%" + search + "%")
////                                .setParameter(1, cityCode)
////                                .setParameter(2, countyCode)
////                                .setParameter(3, user);
////                        query.setFirstResult((page - 1) * pageSize);
////                        query.setMaxResults(pageSize);
////                    } else {
////                        hql += " where footballAgency.agencyName like ?  and footballAgency.cityCode=?   and footballAgency.deleted=false  and footballAgencyFans.user=?  and footballAgencyFans.deleted=false order by footballAgencyFans.createTime desc ";
////                        query = getCurrentSession().createQuery(hql)
////                                .setParameter(0, "%" + search + "%")
////                                .setParameter(1, cityCode)
////                                .setParameter(2, user);
////                        query.setFirstResult((page - 1) * pageSize);
////                        query.setMaxResults(pageSize);
////                    }
////                    //普通城市
////                }
////
////            }
////        } else {
////            if ("1000000".equals(cityCode) || "".equals(cityCode)) {
////                //查询全国的
////                hql += " where  footballAgency.countryCode='1000000'   and footballAgency.deleted=false  and footballAgencyFans.user=?  and footballAgencyFans.deleted=false order by footballAgencyFans.createTime desc";
////                query = getCurrentSession().createQuery(hql)
////                        .setParameter(0, user);
////                query.setFirstResult((page - 1) * pageSize);
////                query.setMaxResults(pageSize);
////            } else {
////                //查询直辖市、特别行政区
////                if ("110000".equals(cityCode) ||
////                        "120000".equals(cityCode) ||
////                        "310000".equals(cityCode) ||
////                        "500000".equals(cityCode) ||
////                        "710000".equals(cityCode) ||
////                        "810000".equals(cityCode) ||
////                        "820000".equals(cityCode)) {
////                    hql += " where  footballAgency.provinceCode=?   and footballAgency.deleted=false  and footballAgencyFans.user=?  and footballAgencyFans.deleted=false order by footballAgencyFans.createTime desc";
////                    query = getCurrentSession().createQuery(hql)
////                            .setParameter(0, cityCode)
////                            .setParameter(1, user);
////                    query.setFirstResult((page - 1) * pageSize);
////                    query.setMaxResults(pageSize);
////                } else {
////                    //普通城市
////                    if ((countyCode != null) && (!countyCode.equals(""))) {
////                        hql += " where footballAgency.cityCode=?  and  footballAgency.countyCode=?   and footballAgency.deleted=false  and footballAgencyFans.user=?  and footballAgencyFans.deleted=false order by footballAgencyFans.createTime desc";
////                        query = getCurrentSession().createQuery(hql)
////                                .setParameter(0, cityCode)
////                                .setParameter(1, countyCode)
////                                .setParameter(2, user);
////                        query.setFirstResult((page - 1) * pageSize);
////                        query.setMaxResults(pageSize);
////                    } else {
////                        hql += " where  footballAgency.cityCode=?   and footballAgency.deleted=false  and footballAgencyFans.user=?  and footballAgencyFans.deleted=false order by footballAgencyFans.createTime desc";
////                        query = getCurrentSession().createQuery(hql)
////                                .setParameter(0, cityCode)
////                                .setParameter(1, user);
////                        query.setFirstResult((page - 1) * pageSize);
////                        query.setMaxResults(pageSize);
////                    }
////                    //普通城市
////                }
////
////            }
////        }//else
////        List<FootballAgencyFans> list = query.list();
////        if (list.size() > 0) {
////            List<FootballAgencyFans> listnew = new ArrayList<FootballAgencyFans>();
////            for (int i = 0; i < list.size(); i++) {
////                FootballAgencyFans agencyFans = list.get(i);
////                Hibernate.initialize(agencyFans);// 获取赖加载的集合内容；
////                Hibernate.initialize(agencyFans.getFootballAgency());// 获取赖加载的集合内容；
////                Hibernate.initialize(agencyFans.getFootballAgency().getGroups());// 获取赖加载的集合内容；
////                Hibernate.initialize(agencyFans.getFootballAgencyClass());// 获取赖加载的集合内容；
////                listnew.add(i, agencyFans);
////            }
////            return listnew;
////        }
////        return null;
////    }
//
//    @SuppressWarnings("unchecked")
//    @Override
//    public FootballAgencyFans findFansById(long id) {
//
//        String hql = "from FootballAgencyFans as footballAgencyFans left join fetch footballAgencyFans.footballAgency as footballAgency where footballAgencyFans.id=?  and footballAgencyFans.deleted=false ";
//        Query query = getCurrentSession().createQuery(hql).setParameter(0, id);
//        List<FootballAgencyFans> list = query.list();
//        if (list != null && list.size() > 0) {
//            return list.get(0);
//        }
//        return null;
//    }
//
//    @Override
//    public void delFansByAgency(FootballAgency footballAgency) {
//
//        String hql = "delete FootballAgencyFans as footballAgencyFans where footballAgencyFans.footballAgency=? ";
//        Query query = getCurrentSession().createQuery(hql).setParameter(0, footballAgency);
//        query.executeUpdate();
//    }
//
//    @SuppressWarnings("unchecked")
//    @Override
//    public List<FootballAgencyFans> findFansByUser(User user) {
//        String hql = "from FootballAgencyFans as footballAgencyFans left join fetch footballAgencyFans.footballAgency as footballAgency left join fetch  footballAgency.groups as groups left join fetch footballAgencyFans.footballAgencyClass as footballAgencyClass where footballAgencyFans.user=? ";
//        Query query = getCurrentSession().createQuery(hql).setParameter(0, user);
//        List<FootballAgencyFans> list = query.list();
//        return list;
//    }
//
//
//}
