package com.microteam.base.entity.team;


import com.microteam.base.common.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;
import java.io.Serializable;

@Entity
@Table(name = "football_team_game_new_album")
@Getter
@Setter
public class FootballTeamGameNewAlbum extends BaseEntity implements Serializable {

    @ManyToOne
    @JoinColumn(name = "gameNewId", nullable = false)
    private FootballTeamGameNew footballTeamGameNew;
    @Column(name = "imgNetUrl", length = 250)
    private String imgNetUrl;
    @Column(name = "imgUrl", length = 250)
    private String imgUrl;
    @Column(name = "imgName", length = 50)
    private String imgName;
    @Column(name = "imgLength")
    private Long imgLength;

}
