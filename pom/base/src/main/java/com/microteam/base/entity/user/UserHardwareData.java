package com.microteam.base.entity.user;


import com.microteam.base.common.base.BaseEntity;
import com.microteam.base.entity.team.FootballTeamGame;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

@Entity
@Table(name = "user_hardware_data")
@Getter
@Setter
public class UserHardwareData extends BaseEntity implements Serializable {

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "teamGameId")
    private FootballTeamGame footballTeamGame;
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "userId")
    private User user;
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "hardwareId")
    private UserHardware userHardware;
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "hardwarePracticeId")
    private UserHardwarePractice userHardwarePractice;
    @Column(name = "isTrainning")
    private Boolean isTrainning;
    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "kickBallStartTime", length = 19)
    private Date kickBallStartTime;
    @Column(name = "kickBallData", length = 65535)
    private String kickBallData;
    @Column(name = "kickBallState")
    private String kickBallState;
    @Column(name = "kickBallPart")
    private String kickBallPart;
    @Column(name = "highSpeedMoveData", length = 65535)
    private String highSpeedMoveData;//高速移动
    @Column(name = "midSpeedMoveData", length = 65535)
    private String midSpeedMoveData;//中速移动
    @Column(name = "lowSpeedMoveData", length = 65535)
    private String lowSpeedMoveData;//低速移动
    @Column(name = "normalSpeedMoveData", length = 65535)
    private String normalSpeedMoveData;//正常移动
    @Column(name = "shoots", length = 65535)
    private String shoots;
    @Column(name = "assists", length = 65535)
    private String assists;
    @Column(name = "isStartUp")
    private Integer isStartUp;
    @Column(name = "isUploadPose")
    private Integer isUploadPose;
    @Column(name = "velocity")
    private Long velocity;
    @Column(name = "velocityTime")
    private Long velocityTime;
    @Column(name = "footBall_data", length = 65535)
    private String footBallData;
    @Column(name = "highMoveCount")
    private Integer highMoveCount;
    @Column(name = "midMoveCount")
    private Integer midMoveCount;
    @Column(name = "lowMoveCount")
    private Integer lowMoveCount;
    @Column(name = "normalMoveCount")
    private Integer normalMoveCount;

    public Date getKickBallStartTime() {
        if (this.kickBallStartTime == null) {
            return null;
        }
        return (Date) this.kickBallStartTime.clone();
    }

    public void setKickBallStartTime(Date kickBallStartTime) {
        this.kickBallStartTime = (Date) kickBallStartTime.clone();
    }


}
