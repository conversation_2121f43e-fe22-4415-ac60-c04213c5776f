package com.microteam.base.telMailValiManage.service.impl;


import com.microteam.base.entity.telMailValiManage.TelMailValiInfo;
import com.microteam.base.telMailValiManage.dao.MobileMessageDao;
import com.microteam.base.telMailValiManage.service.MobileMessageDaoService;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service("mobileMessageDaoService")
public class MobileMessageDaoServiceImpl implements MobileMessageDaoService {
    static Logger logger = Logger.getLogger(MobileMessageDaoServiceImpl.class.getName());

    @Resource(name = "mobileMessageDao")
    private MobileMessageDao dao;

    //保存或更新MobileMessage--gerald
    @Override
    public TelMailValiInfo save(TelMailValiInfo mobileMessage) {
        return dao.save(mobileMessage);
    }

    //根据手机号查询MobileMessage--gerald
    @Override
    public TelMailValiInfo findByValiAccount(String phoneNumber) {
        return dao.findByValiAccount(phoneNumber);
    }

    //删除MobileMessage--gerald
    @Override
    public int delByValiAccount(String phoneNumber) {
        return dao.delByValiAccount(phoneNumber);
    }

}