//package com.microteam.base.agency.dao.impl;
//
//import com.microteam.base.agency.dao.FootballAgencyCoachCommentPraiseDao;
//import com.microteam.base.entity.agency.FootballAgencyCoachCommentPraise;
//import com.microteam.base.common.base.AbstractHibernateDao;
//import org.apache.log4j.Logger;
//import org.hibernate.query.Query;
//import org.springframework.stereotype.Repository;
//
//import java.util.List;
//
//@Repository("footballAgencyCoachCommentPraiseDao")
//public class FootballAgencyCoachCommentPraiseDaoImpl extends AbstractHibernateDao<FootballAgencyCoachCommentPraise> implements FootballAgencyCoachCommentPraiseDao {
//    static Logger logger = Logger.getLogger(FootballAgencyCoachCommentPraiseDaoImpl.class.getName());
//
//    public FootballAgencyCoachCommentPraiseDaoImpl() {
//        super();
//        setClazz(FootballAgencyCoachCommentPraise.class);
//    }
//
//    @Override
//    public List<FootballAgencyCoachCommentPraise> findByCoachCommentId(Long coachCommentId) {
//        String hql = "from FootballAgencyCoachCommentPraise as agencyCoachCommentPraise " +
//                "where agencyCoachCommentPraise.footballAgencyCoachComment.id = (:coachCommentId) " +
//                "and agencyCoachCommentPraise.isPraised=true " +
//                "and agencyCoachCommentPraise.deleted=false ";
//        Query query = getCurrentSession().createQuery(hql)
//                .setParameter("coachCommentId", coachCommentId);
//        return (List<FootballAgencyCoachCommentPraise>) query.list();
//    }
//
//    @Override
//    public FootballAgencyCoachCommentPraise findByCoachCommentIdAndUserId(Long coachCommentId, Long userId) {
//        String hql = "from FootballAgencyCoachCommentPraise as agencyCoachCommentPraise " +
//                "where agencyCoachCommentPraise.footballAgencyCoachComment.id = (:coachCommentId) " +
//                "and agencyCoachCommentPraise.user.id = (:userId) " +
//                "and agencyCoachCommentPraise.deleted=false ";
//        Query query = getCurrentSession().createQuery(hql)
//                .setParameter("coachCommentId", coachCommentId)
//                .setParameter("userId", userId);
//        List<FootballAgencyCoachCommentPraise> list = query.list();
//        if (list != null && list.size() > 0) {
//            return list.get(0);
//        }
//        return null;
//    }
//
//
//}
