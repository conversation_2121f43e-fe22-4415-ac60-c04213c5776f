package com.microteam.base.agency.dao;

import com.microteam.base.entity.agency.FootballAgencyClassPublicationPraise;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

public interface FootballAgencyClassPublicationPraiseDao extends JpaRepository<FootballAgencyClassPublicationPraise, Long>, JpaSpecificationExecutor<FootballAgencyClassPublicationPraise> {
    //查询机构点赞
    @Query("from FootballAgencyClassPublicationPraise as agencyClassPublicationPraise " +
            "where agencyClassPublicationPraise.footballAgencyClassPublication.id = (:publicationId) " +
            "and agencyClassPublicationPraise.isPraised=true " +
            "and agencyClassPublicationPraise.deleted=false ")
    List<FootballAgencyClassPublicationPraise> findByPublicationId(@Param("publicationId") Long publicationId);

    //查询用户对公告的点赞
    @Query("from FootballAgencyClassPublicationPraise as agencyClassPublicationPraise " +
            "where agencyClassPublicationPraise.footballAgencyClassPublication.id = (:publicationId) " +
            "and agencyClassPublicationPraise.user.id = (:userId) " +
            "and agencyClassPublicationPraise.deleted=false ")
    FootballAgencyClassPublicationPraise findByPublicationIdAndUserId(@Param("publicationId") Long publicationId, @Param("userId") Long userId);
}
