package com.microteam.base.entity.team;


import com.microteam.base.common.base.BaseEntity;
import com.microteam.base.entity.user.User;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;
import java.io.Serializable;

@Entity
@Table(name = "football_team_game_enroll")
@Getter
@Setter
public class FootballTeamGameEnroll extends BaseEntity implements Serializable {

    @ManyToOne
    @JoinColumn(name = "teamId", nullable = false)
    private FootballTeam footballTeam;
    @ManyToOne
    @JoinColumn(name = "teamGameId", nullable = false)
    private FootballTeamGame footballTeamGame;
    @ManyToOne
    @JoinColumn(name = "userRoleId", nullable = false)
    private FootballTeamMemberRole footballTeamMemberRole;
    @ManyToOne
    @JoinColumn(name = "userId", nullable = false)
    private User user;
    @Column(name = "position")
    private Integer position;
    @Column(name = "number")
    private Integer number;
    @Column(name = "firstPlay")
    private Boolean firstPlay;
    @Column(name = "bench")
    private Boolean bench;

}
