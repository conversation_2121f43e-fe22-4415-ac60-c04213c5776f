package com.microteam.base.entity.agency;


import com.microteam.base.common.base.BaseEntity;
import com.microteam.base.entity.user.User;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

@Entity
@Table(name = "football_agency_applicant")
@Getter
@Setter
public class FootballAgencyApplicant extends BaseEntity implements Serializable {

    @ManyToOne
    @JoinColumn(name = "agencyId", nullable = false)
    private FootballAgency footballAgency;
    @ManyToOne
    @JoinColumn(name = "applicantId", nullable = false)
    private User user;
    @Column(name = "applyMessage", length = 100)
    private String applyMessage;
    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "applyTime", length = 19)
    private Date applyTime;
    @Column(name = "allowedId")
    private Long allowedId;
    @Column(name = "audit")
    private Short audit;
    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "auditTime", length = 19)
    private Date auditTime;

    public Date getApplyTime() {
        if (this.applyTime == null) {
            return null;
        }
        return (Date) this.applyTime.clone();
    }

    public void setApplyTime(Date applyTime) {
        this.applyTime = (Date) applyTime.clone();
    }

    public Date getAuditTime() {
        if (this.auditTime == null) {
            return null;
        }
        return (Date) this.auditTime.clone();
    }

    public void setAuditTime(Date auditTime) {
        this.auditTime = (Date) auditTime.clone();
    }


}
