package com.microteam.base.entity.file;

import com.microteam.base.entity.agency.FootballAgencyStore;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 2017-7-1
 */
@Entity
@Table(name = "store_album")
@Getter
@Setter
public class StoreAlbum implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false)
    protected Long id;
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "store_id", nullable = false)
    private FootballAgencyStore footballAgencyStore;
    @Column(name = "imgNetUrl", length = 250)
    private String imgNetUrl;
    @Column(name = "imgUrl", length = 250)
    private String imgUrl;
    @Column(name = "imgName", length = 250)
    private String imgName;
    @Column(name = "imgLength", length = 250)
    private Long imgLength;
    @Column(name = "create_time")
    private Date createTime;
    @Column(name = "deleted", nullable = false)
    private boolean deleted;

}
