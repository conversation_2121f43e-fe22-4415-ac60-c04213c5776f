//package com.microteam.base.agency.dao.impl;
//
//import com.microteam.base.agency.dao.FootballAgencyStudentCourseAlbumDao;
//import com.microteam.base.entity.agency.FootballAgencyStudentCourseAlbum;
//import com.microteam.base.common.base.AbstractHibernateDao;
//import org.apache.log4j.Logger;
//import org.springframework.stereotype.Repository;
//
//@Repository("footballAgencyStudentCourseAlbumDao")
//public class FootballAgencyStudentCourseAlbumDaoImpl extends
//        AbstractHibernateDao<FootballAgencyStudentCourseAlbum> implements FootballAgencyStudentCourseAlbumDao {
//    static Logger logger = Logger.getLogger(FootballAgencyStudentCourseAlbumDaoImpl.class.getName());
//
//    public FootballAgencyStudentCourseAlbumDaoImpl() {
//        super();
//
//        setClazz(FootballAgencyStudentCourseAlbum.class);
//    }
//
//
//}
