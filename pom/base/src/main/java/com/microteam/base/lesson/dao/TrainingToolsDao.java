package com.microteam.base.lesson.dao;


import com.microteam.base.entity.lesson.TrainingTools;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

public interface TrainingToolsDao extends JpaRepository<TrainingTools, Long>, JpaSpecificationExecutor<TrainingTools> {

    @Query("from TrainingTools as tool " +
            "where tool.id in (:idList) " +
            "and tool.deleted = false")
    List<TrainingTools> findByIdList(@Param("idList") List<Long> idList);

    //通过课程ID获取训练工具
    @Query("from TrainingTools as tools" +
            " where tools.deleted = false " +
            " order by tools.id asc ")
    List<TrainingTools> findAllTrainingTools();

    @Query(value = "SELECT tool.* " +
            "FROM training_tools as tool " +
            "LEFT JOIN courseware_tools as cour " +
            "ON cour.toolId = tool.id " +
            "WHERE cour.coursewareId = :courwareId", nativeQuery = true)
    List<TrainingTools> findByCourwareId(@Param("courwareId") Long courwareId);

    @Query(value = "select tools.* " +
            "from training_tools as tools " +
            "right join football_lesson_training_tools as ltt " +
            "on tools.id = ltt.toolId " +
            "where ltt.lessonId = (:lessonId) " +
            "and ltt.deleted = 0 " +
            "and tools.deleted = 0 ", nativeQuery = true)
    List<TrainingTools> findByLessonId(@Param("lessonId") Long lessonId);

    TrainingTools findByName(String name);

    @Query(value = "from TrainingTools as tools " +
            "where tools.id = (:id) " +
            "and tools.deleted=false ")
    TrainingTools findByIds(@Param("id")Long id);
}
