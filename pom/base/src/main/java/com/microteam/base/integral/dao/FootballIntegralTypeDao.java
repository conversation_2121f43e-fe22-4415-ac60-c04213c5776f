package com.microteam.base.integral.dao;


import com.microteam.base.entity.integral.FootballIntegralType;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

public interface FootballIntegralTypeDao extends JpaRepository<FootballIntegralType, Long>, JpaSpecificationExecutor<FootballIntegralType> {

    @Query("from FootballIntegralType as footballIntegralType " +
            "where footballIntegralType.id = (:id) " +
            "and footballIntegralType.deleted = false ")
    FootballIntegralType findById(@Param("id") long id);
}
