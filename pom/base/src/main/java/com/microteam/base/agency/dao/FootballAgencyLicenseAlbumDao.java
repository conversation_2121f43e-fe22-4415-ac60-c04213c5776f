package com.microteam.base.agency.dao;

import com.microteam.base.entity.agency.FootballAgency;
import com.microteam.base.entity.agency.FootballAgencyLicenseAlbum;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

public interface FootballAgencyLicenseAlbumDao extends JpaRepository<FootballAgencyLicenseAlbum, Long>, JpaSpecificationExecutor<FootballAgencyLicenseAlbum> {
    //查询机构的营业执照
    @Query("from FootballAgencyLicenseAlbum as agencyLicenseAlbum " +
            "where agencyLicenseAlbum.footballAgency=?1 " +
            "and agencyLicenseAlbum.deleted=false ")
    List<FootballAgencyLicenseAlbum> findLicenseByAgency(FootballAgency footballAgency);
}
