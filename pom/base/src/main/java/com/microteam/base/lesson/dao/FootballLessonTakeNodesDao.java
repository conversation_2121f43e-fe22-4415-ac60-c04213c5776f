package com.microteam.base.lesson.dao;


import com.microteam.base.entity.lesson.FootballLessonTakeNodes;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

public interface FootballLessonTakeNodesDao extends JpaRepository<FootballLessonTakeNodes, Long>, JpaSpecificationExecutor<FootballLessonTakeNodes> {

    @Query("from FootballLessonTakeNodes as node " +
            "left join fetch node.user as user " +
            "left join fetch node.footballTeam as team " +
            "left join fetch node.lesson as lesson " +
            "where lesson.id = :lessonId " +
            "and node.deleted = false")
    List<FootballLessonTakeNodes> findByLessonId(@Param("lessonId") Long lessonId);

    @Query("from FootballLessonTakeNodes as node " +
            "left join fetch node.user as user " +
            "left join fetch node.footballTeam as team " +
            "left join fetch node.lesson as lesson " +
            "where user.id = :userId " +
            "and team.id = :teamId " +
            "and lesson.id = :lessonId " +
            "and node.deleted = false")
    FootballLessonTakeNodes findByLessonIdAndTeamIdAndUserId(@Param("lessonId") Long lessonId, @Param("teamId") Long teamId, @Param("userId") Long userId);

//    boolean saveNode(LessonTakeNodePojo pojo);

    @Query("from FootballLessonTakeNodes as node " +
            "left join fetch node.user as user " +
            "left join fetch node.footballTeam as team " +
            "left join fetch node.lesson as lesson " +
            "where team.id = :teamId " +
            "and lesson.id = :lessonId " +
            "and node.deleted = false")
    List<FootballLessonTakeNodes> findByLessonIdAndTeamId(@Param("lessonId") Long lessonId, @Param("teamId") Long teamId);

    @Query("delete from FootballLessonTakeNodes as node " +
            "where node.footballTeam.id = (:teamId) ")
    @Modifying
    int deleteByTeamId(@Param("teamId") Long teamId);
}
