package com.microteam.base.agency.dao;

import com.microteam.base.entity.agency.FootballAgencyClassEnrollment;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

public interface FootballAgencyClassEnrollmentDao extends JpaRepository<FootballAgencyClassEnrollment, Long>, JpaSpecificationExecutor<FootballAgencyClassEnrollment> {
    //根据Id查询
    @Query("from FootballAgencyClassEnrollment as agencyClassEnrollment " +
            "left join fetch agencyClassEnrollment.footballAgency as footballAgency " +
            "left join fetch footballAgency.groups as groups " +
            "left join fetch agencyClassEnrollment.footballAgencyClassCourse as footballAgencyClassCourse " +
            "left join fetch agencyClassEnrollment.footballAgencyClass as footballAgencyClass " +
            "left join fetch footballAgencyClass.user as user " +
            "where agencyClassEnrollment.id=?1 " +
            "and agencyClassEnrollment.deleted=false ")
    FootballAgencyClassEnrollment findById(long id);

    //查询机构下的报名情况
    @Query("from FootballAgencyClassEnrollment as agencyClassEnrollment " +
            "where agencyClassEnrollment.footballAgency.id = (:agencyId) " +
            "and agencyClassEnrollment.deleted=false ")
    List<FootballAgencyClassEnrollment> findByAgencyId(@Param("agencyId") Long agencyId);

    //分页查询机构下的报名情况
    @Query("from FootballAgencyClassEnrollment as agencyClassEnrollment " +
            "left join fetch agencyClassEnrollment.footballAgencyClass as footballAgencyClass " +
            "where agencyClassEnrollment.footballAgency.id = (:agencyId) " +
            "and agencyClassEnrollment.deleted=false ")
    List<FootballAgencyClassEnrollment> findByAgencyIdForPage(@Param("agencyId") Long agencyId, Pageable pageable);

    @Query("from FootballAgencyClassEnrollment as agencyClassEnrollment " +
            "left join fetch agencyClassEnrollment.footballAgencyClass as footballAgencyClass " +
            "where agencyClassEnrollment.footballAgency.id = (:agencyId) " +
            "and agencyClassEnrollment.footballAgencyClass.id = (:agencyClassId) " +
            "and agencyClassEnrollment.deleted=false ")
    List<FootballAgencyClassEnrollment> findByAgencyIdAndAgencyClassId(@Param("agencyId") Long agencyId, @Param("agencyClassId") Long agencyClassId);

    @Query("from FootballAgencyClassEnrollment as agencyClassEnrollment " +
            "left join fetch agencyClassEnrollment.footballAgencyClass as footballAgencyClass " +
            "where agencyClassEnrollment.footballAgency.id = (:agencyId) " +
            "and agencyClassEnrollment.footballAgencyClass.id = (:agencyClassId) " +
            "and agencyClassEnrollment.deleted=false ")
    List<FootballAgencyClassEnrollment> findByAgencyIdAndAgencyClassIdForPage(@Param("agencyId") Long agencyId, @Param("agencyClassId") Long agencyClassId, Pageable pageable);

}
