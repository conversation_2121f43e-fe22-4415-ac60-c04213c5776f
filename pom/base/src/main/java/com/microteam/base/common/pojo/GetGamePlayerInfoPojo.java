package com.microteam.base.common.pojo;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class GetGamePlayerInfoPojo {
    private Long teamGameEnrollId;
    private Long poloShirt;
    private String nickName;
    private String location;
    private Integer firstPlay;
    private Long memberId;
    private String userHeadImg;
    private String teamName;
    private String abbreviationName;
    //进球
    private Long goalsfo;
    private Long goalsfor;
    //射正
    private Long shoot;
    //助攻
    private Long assist;
    //威胁球
    private Long menace;
    //角球
    private Long corner;
    //任意球
    private Long freeKick;
    //点球
    private Long penaltyKick;
    //头球
    private Long head;
    //过人
    private Long excel;
    //解围
    private Long save;
    //抢断
    private Long holdUp;
    //射偏
    private Long shootAside;
    //犯规
    private Long foul;
    //越位
    private Long offSide;
    //红牌
    private Long redCard;
    //黄牌
    private Long yellowCard;
    //乌龙球
    private Long ownGoal;
    //冒顶
    private Long roof;
    //踢空
    private Long kickEmpty;
    //浪射
    private Long waveShoot;
    //停球失误
    private Long stopFault;
    //传球失误
    private Long passFault;
    //防守失误
    private Long defendFault;

}
