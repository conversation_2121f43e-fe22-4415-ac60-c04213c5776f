package com.microteam.base.entity.agency;


import com.microteam.base.common.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;
import java.io.Serializable;

@Entity
@Table(name = "football_agency_message_board")
@Getter
@Setter
public class FootballAgencyMessageBoard extends BaseEntity implements Serializable {

    @ManyToOne
    @JoinColumn(name = "agencyId", nullable = false)
    private FootballAgency footballAgency;
    @Column(name = "fromUser")
    private Long fromUser;
    @Column(name = "toUser")
    private Long toUser;
    @Column(name = "questMessage", length = 65535)
    private String questMessage;
    @Column(name = "answerMessage", length = 65535)
    private String answerMessage;

}
