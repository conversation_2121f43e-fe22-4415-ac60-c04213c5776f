package com.microteam.base.common.util.jdbc;


import java.lang.reflect.Field;

public class SqlInner {

//    public static boolean addRecordList(List<FootballTeamMatchVersus> matchVersusList) throws Exception {
//        PreparedStatement pst = null;
//        ResultSet rs = null;
//        Connection conn = null;
//        JDBCUtil.init();
//        try {
//            conn = JDBCUtil.getConnection();
//            conn.setAutoCommit(false);
//            //根据类的字段拼接sql
//            String sql = sql(FootballTeamMatchVersus.class);
//            //创建预处理
//            pst = conn.prepareStatement(sql);
//            FootballTeamMatchVersus matchVersus;
//            for (int i = 0, length = matchVersusList.size(); i < length; i++) {
//                matchVersus = matchVersusList.get(i);
//                prepareParams(pst, matchVersus, FootballTeamMatchVersus.class);
//                pst.addBatch();
//                //1000条执行一次
//                if (i % 1000 == 0) {
//                    //执行
//                    pst.executeBatch();
//                    //清空
//                    pst.clearBatch();
//                }
//            }
//            //执行剩余的sql
//            pst.executeBatch();
//            //手动提交
//            conn.commit();
//            return true;
//        } catch (Exception e) {
//            e.printStackTrace();
//            return false;
//        } finally {
//            JDBCUtil.free(rs, pst, conn);
//        }
//    }

    public static String sql(Class clz) {
        StringBuffer sql = new StringBuffer("insert into football_team_match_versus(");
        //反射获取fields
        Field[] fields = clz.getDeclaredFields();
        for (Field field : fields) {
            //match和user外键特殊处理,其它直接使用字段名
            if ("footballTeamMatch".equals(field.getName())) {
                sql.append("matchId,");
            } else if ("user".equals(field.getName())) {
                sql.append("creator,");
            } else {
                sql.append(field.getName());
                sql.append(",");
            }
        }
        sql.deleteCharAt(sql.length() - 1);
        sql.append(")");
        sql.append(" values(");
        for (int i = 0, length = fields.length; i < length; i++) {
            sql.append("?,");
        }
        sql.deleteCharAt(sql.length() - 1);
        sql.append(")");
        System.out.println(sql);
        return sql.toString();
    }

//    private static void prepareParams(PreparedStatement pst, Object obj, Class clz) throws Exception {
//        //反射获取所有字段Fields
//        Field[] fields = clz.getDeclaredFields();
//        Method method;
//        Field field;
//        Object tmpObj;
//        for (int i = 0; i < fields.length; i++) {
//            field = fields[i];
//            //根据字段名得到getter方法
//            PropertyDescriptor pd = new PropertyDescriptor(field.getName(), clz);
//            method = pd.getReadMethod();
//            //调用获取的getter方法，得到对应的值
//            tmpObj = method.invoke(obj);
//            //match和user特殊处理
//            if ("footballTeamMatch".equals(field.getName())) {
//                pst.setObject(i + 1, ((FootballTeamMatch) tmpObj).getId());
//            } else if ("user".equals(field.getName())) {
//                pst.setObject(i + 1, ((User) tmpObj).getId());
//            } else {
//                pst.setObject(i + 1, tmpObj);
//            }
//        }
//    }
}
