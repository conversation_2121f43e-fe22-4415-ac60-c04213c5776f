package com.microteam.base.entity.agency;


import com.microteam.base.common.base.BaseEntity;
import com.microteam.base.entity.user.User;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;
import java.io.Serializable;

@Entity
@Table(name = "football_agency_subalbum")
@Getter
@Setter
public class FootballAgencySubalbum extends BaseEntity implements Serializable {

    @ManyToOne
    @JoinColumn(name = "albumId", nullable = false)
    private FootballAgencyAlbum footballAgencyAlbum;
    @ManyToOne
    @JoinColumn(name = "userId", nullable = false)
    private User user;
    @Column(name = "imgNetUrl", length = 250)
    private String imgNetUrl;
    @Column(name = "imgUrl", length = 250)
    private String imgUrl;
    @Column(name = "imgName", length = 50)
    private String imgName;
    @Column(name = "imgLength")
    private Long imgLength;

}
