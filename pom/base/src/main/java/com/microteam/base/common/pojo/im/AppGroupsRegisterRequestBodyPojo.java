package com.microteam.base.common.pojo.im;


import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Getter;
import lombok.Setter;


/**
 * APP RestClient group register Request body 群组注册  pojo类；；
 */
@Getter
@Setter
@JsonIgnoreProperties(ignoreUnknown = true)
public class AppGroupsRegisterRequestBodyPojo implements java.io.Serializable {

    private String groupname;
    private String groupnickname; //昵称；
    private String desc; //群组描述
    //private String   owner;
    private int maxusers;
    private short groupStyle;
    private String groupRole;
    //private String    groupType; //群组的类型；=chatType;
    private String[] members; //群组成员；

}
