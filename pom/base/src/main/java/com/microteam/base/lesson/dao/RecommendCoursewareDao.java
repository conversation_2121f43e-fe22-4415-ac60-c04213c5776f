package com.microteam.base.lesson.dao;

import com.microteam.base.entity.lesson.RecommendCourseware;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

public interface RecommendCoursewareDao extends JpaRepository<RecommendCourseware, Long>, JpaSpecificationExecutor<RecommendCourseware> {

    @Query("from RecommendCourseware as recommendCourseware " +
            "left join recommendCourseware.courseware as courseware " +
            "where recommendCourseware.type = (:type) " +
            "and recommendCourseware.deleted = false " +
            "order by recommendCourseware.order ")
    List<RecommendCourseware> findByType(@Param("type") Integer type, Pageable pageable);

    @Query("from RecommendCourseware as recommendCourseware " +
            "left join recommendCourseware.courseware as courseware " +
            "where recommendCourseware.type = (:recommendType) " +
            "and (courseware.type = (:coursewareType) or (:coursewareType) = 0) " +
            "and recommendCourseware.deleted = false " +
            "order by recommendCourseware.order ")
    List<RecommendCourseware> findByRecommendTypeAndCoursewareType(@Param("recommendType") int recommendType, @Param("coursewareType") int coursewareType, Pageable pageable);
}
