//package com.microteam.base.agency.dao.impl;
//
//import com.microteam.base.agency.dao.FootballAgencyStudentDao;
//import com.microteam.base.entity.agency.FootballAgency;
//import com.microteam.base.entity.agency.FootballAgencyClass;
//import com.microteam.base.entity.agency.FootballAgencyClassCourse;
//import com.microteam.base.entity.agency.FootballAgencyStudent;
//import com.microteam.base.common.base.AbstractHibernateDao;
//import com.microteam.base.entity.user.User;
//import org.apache.log4j.Logger;
//import org.hibernate.Hibernate;
//import org.hibernate.query.Query;
//import org.springframework.stereotype.Repository;
//
//import java.util.ArrayList;
//import java.util.List;
//
//@Repository("footballAgencyStudentDao")
//public class FootballAgencyStudentDaoImpl extends
//        AbstractHibernateDao<FootballAgencyStudent> implements FootballAgencyStudentDao {
//    static Logger logger = Logger.getLogger(FootballAgencyStudentDaoImpl.class.getName());
//
//    public FootballAgencyStudentDaoImpl() {
//        super();
//
//        setClazz(FootballAgencyStudent.class);
//    }
//
//    //查询学生
//    @SuppressWarnings("unchecked")
//    @Override
//    public FootballAgencyStudent findStudentByAgencyAndAgencyClassAndAgencyClassCourseAndUser(
//            FootballAgency agency, FootballAgencyClass agencyClass,
//            FootballAgencyClassCourse agencyClassCourse, User user) {
//        String hql = "from FootballAgencyStudent as agencyStudent where agencyStudent.footballAgency=? and agencyStudent.footballAgencyClass=? and agencyStudent.footballAgencyClassCourse=? and agencyStudent.user=? and agencyStudent.deleted=false";
//        Query query = getCurrentSession().createQuery(hql).setParameter(0, agency).setParameter(1, agencyClass).setParameter(2, agencyClassCourse).setParameter(3, user);
//        List<FootballAgencyStudent> list = query.list();
//        if (list != null && list.size() > 0) {
//            return list.get(0);
//        }
//        return null;
//    }
//
//    @SuppressWarnings("unchecked")
//    public List<FootballAgencyStudent> findStudentByAgencyAndAgencyClassAndUser(
//            FootballAgency agency, FootballAgencyClass agencyClass, User user) {
//        String hql = "from FootballAgencyStudent as agencyStudent where agencyStudent.footballAgency=? and agencyStudent.footballAgencyClass=? and agencyStudent.user=? and agencyStudent.deleted=false";
//        Query query = getCurrentSession().createQuery(hql).setParameter(0, agency).setParameter(1, agencyClass).setParameter(2, user);
//        List<FootballAgencyStudent> list = query.list();
//        return list;
//    }
//
//    //查询课程学员名单列表
//    @SuppressWarnings("unchecked")
//    @Override
//    public List<FootballAgencyStudent> findCourseStudentList(FootballAgency agency,
//                                                             FootballAgencyClass agencyClass,
//                                                             FootballAgencyClassCourse agencyClassCourse, int pageSize, int page) {
//        String hql = "from FootballAgencyStudent as agencyStudent left join fetch agencyStudent.user as user where agencyStudent.footballAgency=? and agencyStudent.footballAgencyClass=? and agencyStudent.footballAgencyClassCourse=? and agencyStudent.deleted=false";
//        Query query = getCurrentSession().createQuery(hql).setParameter(0, agency).setParameter(1, agencyClass).setParameter(2, agencyClassCourse);
//        query.setFirstResult((page - 1) * pageSize);
//        query.setMaxResults(pageSize);
//        List<FootballAgencyStudent> list = query.list();
//        return list;
//    }
//
//    //查询机构下的学生
//    @SuppressWarnings("unchecked")
//    @Override
//    public List<FootballAgencyStudent> findStudentByAgency(FootballAgency agency) {
//
//        String hql = "from FootballAgencyStudent as agencyStudent where agencyStudent.footballAgency=? and agencyStudent.deleted=false ";
//        Query query = getCurrentSession().createQuery(hql).setParameter(0, agency);
//        List<FootballAgencyStudent> list = query.list();
//        return list;
//    }
//
//    @SuppressWarnings("unchecked")
//    @Override
//    public List<FootballAgencyStudent> findStudentByAgencyAndUser(
//            FootballAgency agency, User user) {
//        String hql = "from FootballAgencyStudent as agencyStudent left join fetch agencyStudent.footballAgencyClass as footballAgencyClass  where agencyStudent.footballAgency=? and agencyStudent.user=? and agencyStudent.deleted=false";
//        Query query = getCurrentSession().createQuery(hql).setParameter(0, agency).setParameter(1, user);
//        List<FootballAgencyStudent> list = query.list();
//        return list;
//    }
//
//    //查询机构下的学生
//    @SuppressWarnings("unchecked")
//    @Override
//    public List<FootballAgencyStudent> findStudentByAgencyAndSearch(
//            FootballAgency agency, int pageSize, int page, String search) {
//
//        Query query = null;
//        String hql = "from FootballAgencyStudent as agencyStudent left join fetch agencyStudent.user as user where agencyStudent.footballAgency=? and agencyStudent.studentName like ? and agencyStudent.deleted=false ";
//        query = getCurrentSession().createQuery(hql).setParameter(0, agency).setParameter(1, "%" + search + "%");
//        query.setFirstResult((page - 1) * pageSize);
//        query.setMaxResults(pageSize);
//        List<FootballAgencyStudent> list = query.list();
//        return list;
//    }
//
//    //查询机构下的学生
//    @SuppressWarnings("unchecked")
//    @Override
//    public List<FootballAgencyStudent> findStudentByAgencyForPage(
//            FootballAgency agency, int pageSize, int page) {
//        Query query = null;
//        String hql = "from FootballAgencyStudent as agencyStudent left join fetch agencyStudent.user as user where agencyStudent.footballAgency=?  and agencyStudent.deleted=false ";
//        query = getCurrentSession().createQuery(hql).setParameter(0, agency);
//        query.setFirstResult((page - 1) * pageSize);
//        query.setMaxResults(pageSize);
//        List<FootballAgencyStudent> list = query.list();
//        return list;
//    }
//
//    //根据Id查询机构下的学员信息
//    @SuppressWarnings("unchecked")
//    @Override
//    public FootballAgencyStudent findStudentById(long id) {
//
//        String hql = "from FootballAgencyStudent as agencyStudent left join fetch agencyStudent.user as user where agencyStudent.id=?  and agencyStudent.deleted=false ";
//        Query query = getCurrentSession().createQuery(hql).setParameter(0, id);
//        List<FootballAgencyStudent> list = query.list();
//        if (list != null && list.size() > 0) {
//            return list.get(0);
//        }
//        return null;
//    }
//
//    //查询机构班级学生数量
//    @SuppressWarnings("unchecked")
//    @Override
//    public List<FootballAgencyStudent> findStudentByAgencyAndClass(
//            FootballAgency agency, FootballAgencyClass agencyClass) {
//        String hql = "select distinct new com.microteam.base.entity.agency.FootballAgencyStudent(agencyStudent.footballAgency,agencyStudent.user) " +
//                "from FootballAgencyStudent as agencyStudent " +
//                "left join agencyStudent.user as user " +
//                "left join  agencyStudent.footballAgency as footballAgency " +
//                "where agencyStudent.footballAgency=? " +
//                "and agencyStudent.footballAgencyClass =? " +
//                "and agencyStudent.deleted=false ";
//        Query query = getCurrentSession().createQuery(hql).setParameter(0, agency).setParameter(1, agencyClass);
//        List<FootballAgencyStudent> list = query.list();
//        return list;
//    }
//
//    @SuppressWarnings("unchecked")
//    @Override
//    public List<FootballAgencyStudent> findStudentByAgencyAndClassForPage(
//            FootballAgency agency, FootballAgencyClass agencyClass, int page,
//            int pageSize) {
//        String hql = " from FootballAgencyStudent as agencyStudent left join fetch agencyStudent.user as user  where agencyStudent.footballAgency=? and agencyStudent.footballAgencyClass =? and agencyStudent.deleted=false ";
//        Query query = getCurrentSession().createQuery(hql).setParameter(0, agency).setParameter(1, agencyClass);
//        query.setFirstResult((page - 1) * pageSize);
//        query.setMaxResults(pageSize);
//        List<FootballAgencyStudent> list = query.list();
//        return list;
//    }
//
//    @SuppressWarnings("unchecked")
////    @Override
////    public List<FootballAgencyStudent> findStudentByUser(User user, String cityCode, String countyCode, String search) {
////
////        //String hql="from FootballAgencyStudent as agencyStudent left join fetch agencyStudent.footballAgency as footballAgency where agencyStudent.user=? and agencyStudent.deleted=false ";
////        String hql = "from FootballAgencyStudent as agencyStudent left join fetch agencyStudent.footballAgency as footballAgency  left join fetch  footballAgency.groups as groups ";
////        Query query = null;
////        if (!"".equals(search)) {
////            if ("1000000".equals(cityCode) || "".equals(cityCode)) {
////                //查询全国的
////                hql += " where footballAgency.agencyName like ?  and footballAgency.countryCode='1000000'   and footballAgency.deleted=false  and agencyStudent.user=? and agencyStudent.deleted=false order by agencyStudent.createTime desc ";
////                query = getCurrentSession().createQuery(hql)
////                        .setParameter(0, "%" + search + "%")
////                        .setParameter(1, user);
////            } else {
////                //查询直辖市、特别行政区
////                if ("110000".equals(cityCode) ||
////                        "120000".equals(cityCode) ||
////                        "310000".equals(cityCode) ||
////                        "500000".equals(cityCode) ||
////                        "710000".equals(cityCode) ||
////                        "810000".equals(cityCode) ||
////                        "820000".equals(cityCode)) {
////                    hql += " where footballAgency.agencyName like ?  and footballAgency.provinceCode=?   and footballAgency.deleted=false and agencyStudent.user=? and agencyStudent.deleted=false order by agencyStudent.createTime desc ";
////                    query = getCurrentSession().createQuery(hql)
////                            .setParameter(0, "%" + search + "%")
////                            .setParameter(1, cityCode)
////                            .setParameter(2, user);
////                } else {
////                    //普通城市
////                    if ((countyCode != null) && (!countyCode.equals(""))) {
////                        hql += " where  footballAgency.agencyName like ? and footballAgency.cityCode=? and footballAgency.countyCode=?   and footballAgency.deleted=false  and agencyStudent.user=? and agencyStudent.deleted=false order by agencyStudent.createTime desc ";
////                        query = getCurrentSession().createQuery(hql)
////                                .setParameter(0, "%" + search + "%")
////                                .setParameter(1, cityCode)
////                                .setParameter(2, countyCode)
////                                .setParameter(3, user);
////                    } else {
////                        hql += " where footballAgency.agencyName like ?  and footballAgency.cityCode=?   and footballAgency.deleted=false  and agencyStudent.user=? and agencyStudent.deleted=false order by agencyStudent.createTime desc ";
////                        query = getCurrentSession().createQuery(hql)
////                                .setParameter(0, "%" + search + "%")
////                                .setParameter(1, cityCode)
////                                .setParameter(2, user);
////                    }
////                    //普通城市
////                }
////
////            }
////        } else {
////            if ("1000000".equals(cityCode) || "".equals(cityCode)) {
////                //查询全国的
////                hql += " where  footballAgency.countryCode='1000000'   and footballAgency.deleted=false  and agencyStudent.user=? and agencyStudent.deleted=false order by agencyStudent.createTime desc ";
////                query = getCurrentSession().createQuery(hql)
////                        .setParameter(0, user);
////            } else {
////                //查询直辖市、特别行政区
////                if ("110000".equals(cityCode) ||
////                        "120000".equals(cityCode) ||
////                        "310000".equals(cityCode) ||
////                        "500000".equals(cityCode) ||
////                        "710000".equals(cityCode) ||
////                        "810000".equals(cityCode) ||
////                        "820000".equals(cityCode)) {
////                    hql += " where  footballAgency.provinceCode=?   and footballAgency.deleted=false  and agencyStudent.user=? and agencyStudent.deleted=false order by agencyStudent.createTime desc ";
////                    query = getCurrentSession().createQuery(hql)
////                            .setParameter(0, cityCode)
////                            .setParameter(1, user);
////                } else {
////                    //普通城市
////                    if ((countyCode != null) && (!countyCode.equals(""))) {
////                        hql += " where footballAgency.cityCode=?  and  footballAgency.countyCode=?   and footballAgency.deleted=false  and agencyStudent.user=? and agencyStudent.deleted=false order by agencyStudent.createTime desc ";
////                        query = getCurrentSession().createQuery(hql)
////                                .setParameter(0, cityCode)
////                                .setParameter(1, countyCode)
////                                .setParameter(2, user);
////                    } else {
////                        hql += " where  footballAgency.cityCode=?   and footballAgency.deleted=false  and agencyStudent.user=? and agencyStudent.deleted=false order by agencyStudent.createTime desc ";
////                        query = getCurrentSession().createQuery(hql)
////                                .setParameter(0, cityCode)
////                                .setParameter(1, user);
////                    }
////                    //普通城市
////                }
////
////            }
////        }//else
////        List<FootballAgencyStudent> list = query.list();
////        if (list.size() > 0) {
////            List<FootballAgencyStudent> listnew = new ArrayList<FootballAgencyStudent>();
////            for (int i = 0; i < list.size(); i++) {
////                FootballAgencyStudent agencyStudent = list.get(i);
////                Hibernate.initialize(agencyStudent);// 获取赖加载的集合内容；
////                Hibernate.initialize(agencyStudent.getFootballAgency());
////                Hibernate.initialize(agencyStudent.getFootballAgency().getGroups());
////                listnew.add(i, agencyStudent);
////            }//for
////            return listnew;
////        }//if
////        return null;
////    }
//
//}
