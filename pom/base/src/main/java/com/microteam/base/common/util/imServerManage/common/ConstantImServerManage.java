package com.microteam.base.common.util.imServerManage.common;

public class ConstantImServerManage {

public static final String HuanxinHostName="https://a1.easemob.com/vsteam/microteam"	;
public static final int Maxusers=500; 
}


//IM SERVER 账号管理 


/*   
账号：vsteamAdmin
密码：vsTeam20150115

应用标识(AppKey):	vsteam#microteam
创建时间:	2015-01-19 12:29:34
最后修改时间:	2015-01-19 12:29:34
用户注册模式:	开放注册            切换
client_id:	YXA6xTaBUJ-TEeS7pUn4Bfu81Q
client_secret:	YXA6oZs6Rj-2PHWwp7HRKtrKcJjRe8A

*/


/*
client_id 和 client_secret可以在环信管理后台的app详情页面看到

Path : /{org_name}/{app_name}/token
HTTP Method : POST
URL Params ： 无
Request Headers : {“Content-Type”:”application/json”}
Request Body ： {“grant_type”: “client_credentials”,”client_id”: “{app的client_id}”,”client_secret”: “{app的client_secret}”}
Response Body ：

key	value
access_token	token值
expires_in	有效时间,秒为单位, 默认是七天,在有效期内是不需要重复获取的
application	当前app的UUID值

*/


//uri=    http://a1.easemob.com//vsteam/microteam/token

//{"grant_type":"client_credentials","client_id":"YXA6xTaBUJ-TEeS7pUn4Bfu81Q","client_secret":"YXA6oZs6Rj-2PHWwp7HRKtrKcJjRe8A"}

//返回内容：
//{"access_token":"YWMtM1iGIsiLEeSeRB_MoJkpZQAAAU1B6_IUt57ZnHHow5kGTxVdEmpTc2Y0d_g","expires_in":5184000,"application":"c5368150-9f93-11e4-bba5-49f805fbbcd5"} 


//2015-3-18 12:22;

//{"access_token":"YWMt9GxknM0mEeSOjMs6jDgVdwAAAU1gH5UzdkSEbgjBcZy3dGlUa_nEQL41tP8","expires_in":5184000,"application":"c5368150-9f93-11e4-bba5-49f805fbbcd5"}


//2015-3-26 16:50;
//{"access_token":"YWMtC_T7NtOWEeS2mmEhQ6SpMAAAAU2KSfbnFTfX7J2w_kRpxXAOY2QAYxRqKHk","expires_in":5184000,"application":"c5368150-9f93-11e4-bba5-49f805fbbcd5"}



/*           //环信 资料账号
 * 
 * {
  "action" : "post",
  "application" : "c5368150-9f93-11e4-bba5-49f805fbbcd5",
  "path" : "/users",
  "uri" : "https://a1.easemob.com/vsteam/microteam/users",
  "entities" : [ {
    "uuid" : "267c963a-c89c-11e4-a59e-9d022000b18b",
    "type" : "user",
    "created" : 1426153371411,
    "modified" : 1426153371411,
    "username" : "0000000004",
    "activated" : true
  } ],
  "timestamp" : 1426153371411,
  "duration" : 25,
  "organization" : "vsteam",
  "applicationName" : "microteam"
}
 
 */   
   
 


////////////群组创建；

/*
"groupname":"testrestgrp12", //群组名称, 此属性为必须的
"desc":"server create group", //群组描述, 此属性为必须的
"public":true, //是否是公开群, 此属性为必须的
"maxusers":300, //群组成员最大数(包括群主), 值为数值类型,默认值200,此属性为可选的
"approval":true, //加入公开群是否需要批准, 没有这个属性的话默认是true（不需要群主批准，直接加入）, 此属性为可选的
"owner":"jma1", //群组的管理员, 此属性为必须的
"members":["jma2","jma3"] //群组成员,此属性为可选的,但是如果加了此项,数组元素至少一个（注：
*/


//http://a1.easemob.com//vsteam/microteam/chatgroups

//{“Authorization”:”Bearer YWMtM1iGIsiLEeSeRB_MoJkpZQAAAU1B6_IUt57ZnHHow5kGTxVdEmpTc2Y0d_g”}

//{"groupname":"gandy1group","desc":"server create group","public":true,"maxusers:500,"approval":false,"owner":"0000000063","members":["0000000064","0000000020"]}

/*

{
	  "action" : "post",
	  "application" : "c5368150-9f93-11e4-bba5-49f805fbbcd5",
	  "uri" : "https://a1.easemob.com/vsteam/microteam",
	  "entities" : [ ],
	  "data" : {
	    "groupid" : "1426250510748676"
	  },
	  "timestamp" : 1426250510743,
	  "duration" : 23944,
	  "organization" : "vsteam",
	  "applicationName" : "microteam"
	}

*/








   
    
