package com.microteam.base.common.util.imServerManage.pojo;


import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import java.util.Date;

/**
*  IM Response body  pojo类；；
* 
* @param 
* @param 
* @return
*/
@JsonIgnoreProperties(ignoreUnknown = true)
public class ImServerResponseBodyPojoForAddMemberOne implements java.io.Serializable {

	/**
	 * 
	 */
	private String action;
	private String application;
	private ImResponseParams params; //?????
	private String path;
	private String uri;            
		
	private ImResponseEntities[] entities; //?????entities
	private ImResponseDataForAddMemberOne data; 
	private Date timestamp; //注意时间类型；java.util.Date
	private Long duration;
	private String organization;
	private String applicationName;

	public ImServerResponseBodyPojoForAddMemberOne() {
	}

	public ImServerResponseBodyPojoForAddMemberOne(String action, String application, ImResponseParams params, String path,String uri,
			ImResponseEntities[] entities,ImResponseDataForAddMemberOne data, Date timestamp, Long duration,String organization,String applicationName) {
		this.action = action;
		this.application = application;
		this.params = params;
		this.path = path;
		this.uri = uri;
		this.entities = entities;
		this.data = data;
		this.timestamp = timestamp;
		this.duration = duration;
		this.organization = organization;
		this.applicationName = applicationName;
		
	}

	public String getAction() {
		return this.action;
	}

	public void setAction(String action) {
		this.action = action;
	}

	public String getApplication() {
		return this.application;
	}

	public void setApplication(String application) {
		this.application = application;
	}

	public ImResponseParams getParams() {
		return this.params;
	}

	public void setParams(ImResponseParams params) {
		this.params = params;
	}

	public String getPath() {
		return this.path;
	}

	public void setPath(String path) {
		this.path = path;
	}

	
	
	public String getUri() {
		return this.uri;
	}

	public void setUri(String uri) {
		this.uri = uri;
	}

	public ImResponseEntities[] getEntities() {
		return this.entities;
	}

	public void setEntities(ImResponseEntities[] entities) {
		this.entities = entities;
	}
	
	public ImResponseDataForAddMemberOne getData() {
		return this.data;
	}

	public void setData(ImResponseDataForAddMemberOne data) {
		this.data = data;
	}
	
	public Date getTimestamp() {
		return this.timestamp;
	}

	public void setTimestamp(Date timestamp) {
		this.timestamp = timestamp;
	}

	public Long getDuration() {
		return this.duration;
	}

	public void setDuration(Long duration) {
		this.duration = duration;
	}

	public String getOrganization() {
		return this.organization;
	}

	public void setOrganization(String organization) {
		this.organization = organization;
	}

	public String getApplicationName() {
		return this.applicationName;
	}

	public void setApplicationName(String applicationName) {
		this.applicationName = applicationName;
	}

		
	
	
}
