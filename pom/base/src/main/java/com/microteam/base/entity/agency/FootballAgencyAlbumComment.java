package com.microteam.base.entity.agency;


import com.microteam.base.common.base.BaseEntity;
import com.microteam.base.entity.user.User;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;
import java.io.Serializable;

@Entity
@Table(name = "football_agency_album_comment")
@Getter
@Setter
public class FootballAgencyAlbumComment extends BaseEntity implements Serializable {

    @ManyToOne
    @JoinColumn(name = "albumId", nullable = false)
    private FootballAgencyAlbum footballAgencyAlbum;
    @ManyToOne
    @JoinColumn(name = "userId", nullable = false)
    private User user;
    @Column(name = "comment", length = 65535)
    private String comment;

}
