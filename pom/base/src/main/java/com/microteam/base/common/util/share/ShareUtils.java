package com.microteam.base.common.util.share;


/*
 * <AUTHOR> zhaoxuebin * @date :2017年12月12日上午10:37:05 *@version 1.0 * @parameter * @since * @return
 */

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONException;
import com.alibaba.fastjson.JSONObject;
import com.microteam.base.common.constant.FootballContants;
import com.microteam.base.common.pojo.GetGamePlayerInfoPojo;
import com.microteam.base.common.pojo.LevelHonorPojo;
import com.microteam.base.common.pojo.SimpleHonorPojo;
import com.microteam.base.common.util.team.FootballTeamGameUtils;
import com.microteam.base.entity.team.*;
import com.microteam.base.entity.user.User;

import java.lang.reflect.Field;
import java.util.*;

/*
 * <AUTHOR>
 * @version $Id: ShareUtils.java, v 0.1 2017年12月12日 上午10:37:05 Administrator Exp $
 */
public class ShareUtils {

    public static String numberToContestRule(Short s) {
        String str = "";
        if (s == 1) {
            str = "5人制";
        } else if (s == 2) {
            str = "7-9人制";
        } else if (s == 3) {
            str = "11人制";
        } else if (s == 4) {
            str = "其他";
        }
        return str;
    }

    public static String numberToColor(Short s) {
        String str = "";
        if (s == 1) {
            str = "red";
        }
        if (s == 2) {
            str = "green";
        }
        if (s == 3) {
            str = "yellow";
        }
        if (s == 4) {
            str = "white";
        }
        if (s == 5) {
            str = "blue";
        }
        if (s == 6) {
            str = "black";
        }
        if (s == 7) {
            str = "other";
        }
        return str;
    }

    //1:CF（前锋）2:CM(中场)3:CB（后卫）4:GK（守门员）
    public static Short teamPositionToNumber(String position) {
        short positionShort;
        switch (position) {
            case "CF":
                positionShort = 1;
                break;
            case "CM":
                positionShort = 2;
                break;
            case "CB":
                positionShort = 3;
                break;
            case "GK":
                positionShort = 4;
                break;
            default:
                positionShort = 1;
                break;
        }
        return positionShort;
    }

    public static String dataName(String dataName) {
        String tempName;
        switch (dataName) {
            case "goalsfor":
                tempName = "进球";
                break;
            case "shoot":
                tempName = "射正";
                break;
            case "assist":
                tempName = "射偏";
                break;
            case "menace":
                tempName = "威胁球";
                break;
            case "corner":
                tempName = "角球";
                break;
            case "freeKick":
                tempName = "任意球";
                break;
            case "penaltyKick":
                tempName = "点球";
                break;
            case "head":
                tempName = "头球";
                break;
            case "excel":
                tempName = "过人";
                break;
            case "save":
                tempName = "解围";
                break;
            case "holdUp":
                tempName = "抢断";
                break;
            case "shootAside":
                tempName = "射偏";
                break;
            case "foul":
                tempName = "犯规";
                break;
            case "offSide":
                tempName = "越位";
                break;
            case "yellowCard":
                tempName = "黄牌";
                break;
            case "ownGoal":
                tempName = "乌龙球";
                break;
            case "roof":
                tempName = "冒顶";
                break;
            case "kickEmpty":
                tempName = "踢空";
                break;
            case "waveShoot":
                tempName = "浪射";
                break;
            case "stopFault":
                tempName = "停球失误";
                break;
            case "passFault":
                tempName = "传球失误";
                break;
            case "defendFault":
                tempName = "防守失误";
                break;
            default:
                tempName = "错误";
                break;
        }
        return tempName;
    }

    public static int color(String dataName) {
        int color;
        switch (dataName) {
            case "goalsfor":
                color = 0;
                break;
            case "shoot":
                color = 0;
                break;
            case "assist":
                color = 0;
                break;
            case "menace":
                color = 0;
                break;
            case "corner":
                color = 0;
                break;
            case "freeKick":
                color = 0;
                break;
            case "penaltyKick":
                color = 0;
                break;
            case "head":
                color = 0;
                break;
            case "excel":
                color = 0;
                break;
            case "save":
                color = 0;
                break;
            case "holdUp":
                color = 0;
                break;
            case "shootAside":
                color = 1;
                break;
            case "foul":
                color = 1;
                break;
            case "offSide":
                color = 1;
                break;
            case "yellowCard":
                color = 1;
                break;
            case "ownGoal":
                color = 1;
                break;
            case "roof":
                color = 1;
                break;
            case "kickEmpty":
                color = 1;
                break;
            case "waveShoot":
                color = 1;
                break;
            case "stopFault":
                color = 1;
                break;
            case "passFault":
                color = 1;
                break;
            case "defendFault":
                color = 1;
                break;
            default:
                color = 0;
                break;
        }
        return color;
    }

    public static List<User> teamUserToUsers(List<FootballTeamUser> teamUsers) {
        List<User> users = new ArrayList<>();
        if (teamUsers != null && teamUsers.size() > 0) {
            for (FootballTeamUser teamUser : teamUsers) {
                users.add(teamUser.getUser());
            }
        }
        return users;
    }

    public static List<User> enrollUserToUsers(List<FootballTeamGameEnroll> enrolls) {
        List<User> users = new ArrayList<>();
        if (enrolls != null && enrolls.size() > 0) {
            for (FootballTeamGameEnroll enroll : enrolls) {
                users.add(enroll.getUser());
            }
        }
        return users;
    }


    public static Map<String, Object> pickTeamColor(String teamName, FootballTeamGame teamGame, List<FootballTeamColor> colorList, Map<String, Object> map) {
        if (teamName.equals(teamGame.getFootballTeam().getTeamName())) {
            map.put("teamColor", colorUrl(teamGame.getClothColor(), colorList));
        } else if (teamName.equals(teamGame.getOpponent())) {
            map.put("teamColor", colorUrl(teamGame.getOpponentClothColor(), colorList));
        }
        return map;
    }

    public static String colorUrl(Short s, List<FootballTeamColor> colorList) {
        String color;
        switch (s) {
            case 1:
                color = colorList.get(0).getTeamColorNetUrl();
                break;//red
            case 2:
                color = colorList.get(1).getTeamColorNetUrl();
                break;//green
            case 3:
                color = colorList.get(2).getTeamColorNetUrl();
                break;//yellow
            case 4:
                color = colorList.get(3).getTeamColorNetUrl();
                break;//white
            case 5:
                color = colorList.get(4).getTeamColorNetUrl();
                break;//blue
            case 6:
                color = colorList.get(5).getTeamColorNetUrl();
                break;//black
            case 7:
                color = colorList.get(6).getTeamColorNetUrl();
                break;//gray
            default:
                color = colorList.get(0).getTeamColorNetUrl();
                break;
        }
        return color;
    }

    public static Map<String, Object> pickTeamColor(FootballTeam team, Map<String, Object> map, int type, List<FootballTeamColor> colorList) {
        String color;
        String key;
        Integer variable;
        if (type == 1) {
            key = "hostColor";
            variable = team.getHostColor();
//        } else if (type == 2) {
        } else {
            key = "guestColor";
            variable = team.getGuestColor();
        }
        color = colorUrl(variable.shortValue(), colorList);
        map.put(key, color);
        return map;
    }

    public static JSONArray pickHonorIcon(Map<Integer, LevelHonorPojo> map, List<HonorIcon> iconList) throws JSONException {
        Map<String, String> iconMap;
        iconMap = iconListToMap(iconList);
        JSONArray result = new JSONArray();
        for (Map.Entry<Integer, LevelHonorPojo> entry : map.entrySet()) {
            Integer key = entry.getKey();
            LevelHonorPojo levelHonorPojo = entry.getValue();
            JSONObject tempObject = new JSONObject();
            JSONArray array = new JSONArray();
            String level = getLevel(key);
            tempObject.put("level", getLevelToUtf(key));
            tempObject.put("iconList", array);
            List<SimpleHonorPojo> list = levelHonorPojo.getSimpleHonorList();
            for (SimpleHonorPojo simpleHonorPojo : list) {
                JSONObject object = new JSONObject();
                String rank = getRanking(simpleHonorPojo.getRanking());
                String iconNetUrl = iconMap.get(level + "_" + rank);
                object.put("iconNetUrl", iconNetUrl);
                object.put("name", simpleHonorPojo.getName());
                object.put("time", simpleHonorPojo.getTime());
                array.add(object);
            }
            result.add(tempObject);
        }
        return result;
    }

    public static String getLevel(Integer key) {
        String level;
        switch (key) {
            case 1:
                level = "world";
                break;
            case 2:
                level = "country";
                break;
            case 3:
                level = "province";
                break;
            case 4:
                level = "city";
                break;
            case 5:
                level = "county";
                break;
            default:
                level = "country";
                break;
        }
        return level;
    }

    public static String getLevelToUtf(Integer key) {
        String level;
        switch (key) {
            case 1:
                level = "世界级";
                break;
            case 2:
                level = "国家级";
                break;
            case 3:
                level = "省级";
                break;
            case 4:
                level = "市级";
                break;
            case 5:
                level = "区级";
                break;
            default:
                level = "世界级";
                break;
        }
        return level;
    }

    public static String getRanking(Integer key) {
        String ranking;
        switch (key) {
            case 1:
                ranking = "first";
                break;
            case 2:
                ranking = "second";
                break;
            case 3:
                ranking = "third";
                break;
            case 4:
                ranking = "fourth";
                break;
            case 5:
                ranking = "fifth";
                break;
            default:
                ranking = "first";
                break;
        }
        return ranking;
    }

    public static Map<String, String> iconListToMap(List<HonorIcon> iconList) {
        Map<String, String> map = new HashMap<>();
        for (HonorIcon honorIcon : iconList) {
            map.put(honorIcon.getDescribe(), honorIcon.getIconNetUrl());
        }
        return map;
    }

    public static JSONArray packTeamNumber(List<PoloShirt> poloList) {
        JSONArray array = new JSONArray();
        Map<Integer, String> map = new LinkedHashMap<>();
        try {
            for (int i = 1; i <= 99; i++) {
                map.put(i, null);
            }
            for (PoloShirt polo : poloList) {
                map.put(polo.getNumber().intValue(), polo.getUser().getNickName());
            }
            for (Map.Entry<Integer, String> entry : map.entrySet()) {
                Map<String, Object> object = new LinkedHashMap<>();
                object.put("number", entry.getKey());
                object.put("name", entry.getValue() == null ? "" : entry.getValue());
                array.add(object);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return array;
    }

    public static JSONArray statisticData(String dataName, long hostSum, long guestSum, JSONArray array, int index) {
        Map<String, Object> map = new HashMap<>();
        String tempName;
        try {
            switch (dataName) {
                case "goalsfor":
                    tempName = "进球";
                    break;
                case "shoot":
                    tempName = "射正";
                    break;
                case "holdUp":
                    tempName = "抢断";
                    break;
                case "freeKick":
                    tempName = "任意球";
                    break;
                case "penaltykick":
                    tempName = "点球";
                    break;
                case "redcard":
                    tempName = "红牌";
                    break;
                case "yellowcard":
                    tempName = "黄牌";
                    break;
                case "offSide":
                    tempName = "越位";
                    break;
                case "foul":
                    tempName = "犯规";
                    break;
                default:
                    tempName = "进球";
                    break;
            }
            map.put("dataName", tempName);
            map.put("hostSum", hostSum);
            map.put("guestSum", guestSum);
            JSONObject object = new JSONObject(map);

            array.add(index, object);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return array;
    }

    public static JSONArray packPlayerInfo(List<GetGamePlayerInfoPojo> list) throws Exception {
        JSONArray array = new JSONArray();
        if (list.size() > 0) {
            for (GetGamePlayerInfoPojo pojo : list) {
                Map<String, Object> map = new HashMap<>();
                map.put("teamName", pojo.getTeamName());
                map.put("nickName", pojo.getNickName());
                map.put("location", pojo.getLocation());
                map.put("userHeadImg", pojo.getUserHeadImg());
                map.put("abbreviationName", pojo.getAbbreviationName());
                map.put("poloShirt", pojo.getPoloShirt());
                JSONArray dataArray = packPlayerData(pojo);
                map.put("dataInfo", dataArray);
                array.add(map);
            }
        }
        return array;
    }

    public static JSONArray packPlayerData(GetGamePlayerInfoPojo pojo) throws Exception {
        Class<?> classs = pojo.getClass();
        JSONArray dataArray = new JSONArray();
        Field[] filed = classs.getDeclaredFields();
        for (int i = 10; i < filed.length; i++) {
            Map<String, Object> map = new HashMap<>();
            filed[i].setAccessible(true);
            if (filed[i].get(pojo) != null && (Long) filed[i].get(pojo) > 0L) {
                map.put("name", dataName(filed[i].getName()));
                map.put("count", filed[i].get(pojo));
                map.put("color", color(filed[i].getName()));
            }
            if (map.size() > 0) {
                dataArray.add(map);
            }
        }
        return dataArray;
    }


    //map : key:userId value:动作发生的（时间毫秒数）
    public static String getOnceTime(Map<Long, List<Long>> map, FootballTeamGame game, User user) {
        StringBuilder s = new StringBuilder();

        Long startTime = game.getCompetitionTime().getTime();   //比赛开始时间
        Long midTime1 = game.getCompetitionTime().getTime() + FootballContants.GAMEHALF;  //上半场结束时间
        Long midTime2 = midTime1 + FootballContants.GAMEREST;             //下半场开始时间
        Long endTime = game.getCompetitionTime().getTime() + FootballContants.GAMEDURATION;   //比赛结束时间
        List<Long> minList = new ArrayList<>();
        if (map.containsKey(user.getId())) {
            List<Long> timeList = map.get(user.getId());
            for (Long time : timeList) {
                Long interval = 0L;
                if (time >= startTime && time < midTime1) {
                    interval = time - startTime;
                } else if (time >= midTime1 && time <= midTime2) {
                    interval = FootballContants.GAMEHALF;
                } else if (time > midTime2 && time <= endTime) {
                    interval = time - midTime2;
                }
                Long min = (interval % (1000 * 60 * 60)) / (1000 * 60); //转换成分钟数
                minList.add(min);
            }
        }

        for (int i = 0; i < minList.size(); i++) {
            s.append(minList.get(i)).append(" '");
            if (i != minList.size() - 1) {
                s.append(",");
            }
        }
        return s.toString();
    }

    public static Map<String, Object> convertNodeTime(Map<Long, List<FootballTeamGameOnceNode>> onceMap, Map<Long, Long> nodeMap, FootballTeamGame game, User user, String proper,Map<Long, Long> nodePointMap) throws JSONException {
        Map<String, Object> tempMap = new HashMap<>();
        Long startTime = game.getCompetitionTime().getTime();   //比赛开始时间
        Long midTime1 = game.getCompetitionTime().getTime() + FootballContants.GAMEHALF;  //上半场结束时间
        Long midTime2 = midTime1 + FootballContants.GAMEREST;             //下半场开始时间
        Long endTime = game.getCompetitionTime().getTime() + FootballContants.GAMEDURATION;   //比赛结束时间

        if (nodeMap.containsKey(user.getId()) && nodeMap.get(user.getId()) > 0L) {
            tempMap.put("userId", user.getId());
            tempMap.put("nickName", user.getNickName());
            tempMap.put("count", nodeMap.get(user.getId()));
            JSONArray array = new JSONArray();  // 开始封装 动作发生时间 的数组
            if (onceMap.containsKey(user.getId())) {
                List<FootballTeamGameOnceNode> timeList = onceMap.get(user.getId());
                for (FootballTeamGameOnceNode timeNode : timeList) {
                    long time = timeNode.getOccurTime().getTime();
                    Map<String, Object> tempMap2 = new HashMap<>();
                    //转换分钟数
                    Long interval = 0L;
                    if (time >= startTime && time < midTime1) {
                        interval = time - startTime;
                    } else if (time >= midTime1 && time <= midTime2) {
                        interval = FootballContants.GAMEHALF;
                    } else if (time > midTime2 && time <= endTime) {
                        interval = time - startTime - FootballContants.GAMEREST;
                    }
                    Long min = interval / (1000 * 60); //转换成分钟数
                    if (min.equals(0L)) {
                        min = 1L;
                    }
                    tempMap2.put("timeTable", min);
                    array.add(tempMap2);
                }
            }else{
                for(int k=0;k<nodeMap.get(user.getId()).intValue();k++){
                    Map<String,Object> tempMap2 = new HashMap<>();
                    tempMap2.put("timeTable",-1);
                    array.add(tempMap2);
                }
            }

            // 保证数组长度与count一致，缺的补-1
            if (array.size() < nodeMap.get(user.getId()).intValue()) {
                int u = 0;
                if(!proper.equals("goalsfor")){
                    u = nodeMap.get(user.getId()).intValue() - array.size();
                }else {
                    u =  (nodeMap.get(user.getId()).intValue()-nodePointMap.get(user.getId()).intValue())-array.size();
                }
                for (int k = 0; k < u; k++) {
                    Map<String, Object> tempMap2 = new HashMap<>();
                    tempMap2.put("timeTable", -1);
                    array.add(tempMap2);
                }
            }

            //数组是点球或进球时 在循环外往发生时间数组里添加 是否为点球的属性  because这个属性是固有存在的
            if (proper.equals("goalsfor") || proper.equals("penaltyKick")) {
                if (array.size() == 0) {
                    for (int k = 0; k < nodeMap.get(user.getId()).intValue(); k++) {
                        JSONObject object = new JSONObject();
                        array.add(object);
                    }
                }
                for (int i = 0; i < array.size(); i++) {
                    JSONObject jsonObject = array.getJSONObject(i);
                    if (proper.equals("goalsfor")) {  //不是点球
                        jsonObject.put("isPenalty", 0);
                        array.set(i,jsonObject);
                    } else if (proper.equals("penaltyKick")) { //是点球
                        jsonObject.put("isPenalty", 1);
                        array.set(i,jsonObject);
                    }
                }
            }
            tempMap.put("when", array);
            System.out.println(tempMap);
        }
        return tempMap;
    }


}
