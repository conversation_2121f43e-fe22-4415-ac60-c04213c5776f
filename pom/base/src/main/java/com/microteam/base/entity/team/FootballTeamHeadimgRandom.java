package com.microteam.base.entity.team;

import com.microteam.base.common.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.io.Serializable;

@Entity
@Table(name = "football_team_headimg_random")
@Getter
@Setter
public class FootballTeamHeadimgRandom extends BaseEntity implements Serializable {

    @Column(name = "teamImgNetUrl", length = 250)
    private String teamImgNetUrl;
    @Column(name = "teamImgUrl", length = 250)
    private String teamImgUrl;
    @Column(name = "fileName", length = 250)
    private String fileName;
    @Column(name = "length")
    private Long length;
    @Column(name = "enabled", nullable = false)
    private boolean enabled;

}
