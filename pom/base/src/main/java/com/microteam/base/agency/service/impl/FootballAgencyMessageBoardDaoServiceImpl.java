package com.microteam.base.agency.service.impl;

import com.microteam.base.agency.dao.FootballAgencyMessageBoardDao;
import com.microteam.base.agency.service.FootballAgencyMessageBoardDaoService;
import com.microteam.base.entity.agency.FootballAgency;
import com.microteam.base.entity.agency.FootballAgencyMessageBoard;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.List;

@Service("footballAgencyMessageBoardDaoService")
public class FootballAgencyMessageBoardDaoServiceImpl implements FootballAgencyMessageBoardDaoService {
    static Logger logger = Logger.getLogger(FootballAgencyMessageBoardDaoServiceImpl.class.getName());
    @Autowired
    private FootballAgencyMessageBoardDao dao;

    public FootballAgencyMessageBoardDaoServiceImpl() {
        super();

    }

    //分页查询机构下的留言板
    @Override
    public List<FootballAgencyMessageBoard> findMessBoardByPage(FootballAgency agency, int page, int pageSize) {
        if (page > 0) {
            page--;
        }
        Pageable pageable = PageRequest.of(page, pageSize);
        return dao.findMessBoardByPage(agency, pageable);
    }


}
