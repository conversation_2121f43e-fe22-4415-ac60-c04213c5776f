//package com.microteam.base.agency.dao.impl;
//
//import com.microteam.base.agency.dao.FootballAgencyStudentScoreAlbumDao;
//import com.microteam.base.entity.agency.FootballAgencyStudentScore;
//import com.microteam.base.entity.agency.FootballAgencyStudentScoreAlbum;
//import com.microteam.base.common.base.AbstractHibernateDao;
//import org.apache.log4j.Logger;
//import org.hibernate.query.Query;
//import org.springframework.stereotype.Repository;
//
//import java.util.List;
//
//@Repository("footballAgencyStudentScoreAlbumDao")
//public class FootballAgencyStudentScoreAlbumDaoImpl extends AbstractHibernateDao<FootballAgencyStudentScoreAlbum>
//        implements FootballAgencyStudentScoreAlbumDao {
//    static Logger logger = Logger.getLogger(FootballAgencyStudentScoreAlbumDaoImpl.class.getName());
//
//    public FootballAgencyStudentScoreAlbumDaoImpl() {
//        super();
//        setClazz(FootballAgencyStudentScoreAlbum.class);
//
//    }
//
//    //查询学员分数相册
//    @SuppressWarnings("unchecked")
//    @Override
//    public List<FootballAgencyStudentScoreAlbum> findStudentScoreAlbumByScore(
//            FootballAgencyStudentScore agencyStudentScore) {
//        String hql = "from FootballAgencyStudentScoreAlbum as agencyStudentScoreAlbum where agencyStudentScoreAlbum.footballAgencyStudentScore=? and footballAgencyStudentScore.deleted=false ";
//        Query query = getCurrentSession().createQuery(hql).setParameter(0, agencyStudentScore);
//        List<FootballAgencyStudentScoreAlbum> list = query.list();
//        return list;
//    }
//
//
//}
