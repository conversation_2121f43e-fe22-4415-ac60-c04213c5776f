package com.microteam.base.lesson.dao;


import com.microteam.base.entity.lesson.FootballCourseWare;
import com.microteam.base.lesson.service.FootballCourseWareDaoService;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

public interface FootballCourseWareDao extends JpaRepository<FootballCourseWare, Long>, JpaSpecificationExecutor<FootballCourseWare> {

    @Query("from FootballCourseWare as courseWare " +
            "where courseWare.deleted = false " +
            "and courseWare.openness = 1")
    List<FootballCourseWare> findOpennessForPage(Pageable pageable);

    @Query("from FootballCourseWare as courseWare " +
            "where courseWare.id in (:idList) " +
            "and courseWare.deleted = false")
    List<FootballCourseWare> findByIdList(@Param("idList") List<Long> idList);

    @Query("from FootballCourseWare as courseWare " +
            "where courseWare.id = (:id) " +
            "and courseWare.deleted = false")
    FootballCourseWare findById(@Param("id") long id);

    @Query("from FootballCourseWare as courseWare " +
            "left join fetch courseWare.motion as motion " +
            "where (courseWare.grade = (:grade) or (:grade) = 0) " +
            "and (motion.id = (:motionId) or (:motionId) = 0) " +
            "and (courseWare.level = (:level) or (:level) = 0) " +
            "and courseWare.deleted = false " +
            "and courseWare.vsteam = 1 ")
    List<FootballCourseWare> findByGradeAndMotionIdAndLevelAndOrder(@Param("grade") int grade, @Param("motionId") Long motionId, @Param("level") int level, Pageable pageable);

    @Query("from FootballCourseWare as courseWare " +
            "where courseWare.courseName = (:courseName) " +
            "and courseWare.deleted = false ")
    FootballCourseWare findByCourseName(@Param("courseName") String courseName);

    @Query("from FootballCourseWare as courseWare " +
            "where courseWare.deleted = false ")
    List<FootballCourseWare> findHotTop(Pageable pageable);

    @Query("from FootballCourseWare as courseWare " +
            "where courseWare.deleted = false ")
    List<FootballCourseWare> findNewTop(Pageable pageable);

    @Query("from FootballCourseWare as courseware " +
            "where (courseware.type = (:type) or (:type) = 0) " +
            "and courseware.deleted = false ")
    List<FootballCourseWare> findByType(@Param("type") Integer type, Pageable pageable);

    @Query("from FootballCourseWare as courseware " +
            "where courseware.courseName like (:searchName) " +
            "and (courseware.type = (:type) or (:type) = 0) " +
            "and courseware.deleted = false ")
    List<FootballCourseWare> searchByNameAndType(@Param("searchName") String searchName, @Param("type") int type, Pageable pageable);

    @Query("from FootballCourseWare as courseware " +
            "where courseware.choice = (:choice) " +
            "and courseware.deleted = false " +
            "and courseware.founderId = (:userId) " +
            "order by courseware.createTime desc ")
    List<FootballCourseWare> findByChoice(@Param("choice") Integer choice, @Param("userId") Long userId);

    @Query("from FootballCourseWare as courseware " +
            "where courseware.vsteam = '0' " +
            "and courseware.deleted = false " +
            "and courseware.founderId = (:userId) " +
            "order by courseware.createTime desc ")
    List<FootballCourseWare> findBynoVsteam(@Param("userId") Long userId);

    @Query("from FootballCourseWare as courseware " +
            "where courseware.vsteam = '1' " +
            "and courseware.deleted = false")
    List<FootballCourseWare> findByVsteam();

    @Query("from FootballCourseWare as courseware " +
            "where courseware.open = (:open) " +
            "and courseware.deleted = false " +
            "order by courseware.createTime desc ")
    List<FootballCourseWare> findByOpen(@Param("open") Integer open);

    @Query("update FootballCourseWare as courseware " +
            "set courseware.headImg = (:headImg),courseware.courseNetUrl = (:headImg) " +
            "where courseware.id = (:id) " +
            "and courseware.deleted = false ")
    @Modifying
    int updateImg(@Param("id") Long id,@Param("headImg") String headImg);

    @Query("update FootballCourseWare as courseware " +
            "set courseware.video = (:video),courseware.courseNetUrl = (:video),courseware.isVideo = 1 " +
            "where courseware.id = (:id) " +
            "and courseware.deleted = false ")
    @Modifying
    int updateVideo(@Param("id") Long id,@Param("video") String video);

    @Query("update FootballCourseWare as courseware " +
            "set courseware.open = (:open) " +
            "where courseware.id = (:id) " +
            "and courseware.deleted = false")
    @Modifying
    int saveFoundIsOpen(@Param("id") Long id,@Param("open")Integer open);

    @Query("from FootballCourseWare as courseware " +
            "where courseware.divide = (:divide) " +
            "and courseware.deleted = false")
    List<FootballCourseWare> findByDivide(@Param("divide") Integer divide);

    @Query("update FootballCourseWare as courseware " +
            "set courseware.quote = (:quote) " +
            "where courseware.id = (:id) " +
            "and courseware.deleted =false ")
    @Modifying
    int updateQuite(@Param("id") Long id,@Param("quote") Integer quote);

    @Query(" from FootballCourseWare as courseware " +
            "where courseware.actionClassification = (:actionClassification) " +
            "and courseware.deleted = false " +
            "and courseware.level = (:level)")
    @Modifying
    List<FootballCourseWare> remCourseware(@Param("actionClassification")Integer actionClassification,@Param("level")Integer level);
}
