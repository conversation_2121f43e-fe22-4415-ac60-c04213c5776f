package com.microteam.base.entity.lesson;


import com.microteam.base.common.base.BaseEntity;
import com.microteam.base.entity.team.FootballTeam;
import com.microteam.base.entity.user.User;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;
import java.io.Serializable;

@Entity
@Table(name = "football_lesson_second_enroll")
@Getter
@Setter
public class FootballLessonSecondEnroll extends BaseEntity implements Serializable {
    @ManyToOne
    @JoinColumn(name = "userId", nullable = false)
    private User user;
    @ManyToOne
    @JoinColumn(name = "lessonId")
    private FootballLesson lesson;
    @ManyToOne
    @JoinColumn(name = "teamId")
    private FootballTeam team;
    @Column(name = "groupName")
    private String groupName;
    @Column(name = "isGroup")
    private boolean group;
    @Column(name = "timeSlot")
    private String timeSlot;
    @Column(name = "needHomework")
    private boolean needHomework;
    @Column(name = "equipmentStatus")
    private Integer equipmentStatus;
    @Column(name = "uploadStatus")
    private Integer uploadStatus;
    @Column(name = "isStartUpHardWare")
    private Integer isStartUpHardWare;

}
