//package com.microteam.base.common.util.system;
//
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.beans.factory.annotation.Value;
//import org.springframework.http.*;
//import org.springframework.stereotype.Component;
//import org.springframework.web.client.RestTemplate;
//import org.springframework.web.util.UriComponents;
//import org.springframework.web.util.UriComponentsBuilder;
//
//import java.net.URI;
//
//@Component
//public class RestUtil {
//
//    @Value("${com.microteam.dao-url}")
//    private String daoUrl;
//
//    @Autowired
//    private RestTemplate restTemplate;
//
//    public <T> T send(String url, String accessToken, Class<T> clz) {
//        UriComponents uriComponents = UriComponentsBuilder.fromUriString(url).build().encode();
//        URI uri = uriComponents.toUri();
//        String requestJson = "";
//        HttpHeaders headers = new HttpHeaders();
//        headers.add("Authentication", accessToken);
//        HttpEntity<String> httpEntity = new HttpEntity<>(requestJson, headers);
//        ResponseEntity<T> responseEntity = restTemplate.exchange(uri, HttpMethod.GET, httpEntity, clz);
//        return responseEntity.getBody();
//    }
//
//    public String send(String url) {
//        url = daoUrl + url;
//        UriComponents uriComponents = UriComponentsBuilder.fromUriString(url).build().encode();
//        URI uri = uriComponents.toUri();
//        String requestJson = "";
//        HttpHeaders headers = new HttpHeaders();
//        headers.setContentType(MediaType.APPLICATION_JSON);
//        HttpEntity<String> httpEntity = new HttpEntity<>(requestJson, headers);
//        ResponseEntity<String> responseEntity = restTemplate.exchange(uri, HttpMethod.GET, httpEntity, String.class);
//        return responseEntity.getBody();
//    }
//
//    public <T> T send(String url, Class<T> clz) {
//        url = daoUrl + url;
//        UriComponents uriComponents = UriComponentsBuilder.fromUriString(url).build().encode();
//        URI uri = uriComponents.toUri();
//        String requestJson = "";
//        HttpHeaders headers = new HttpHeaders();
//        headers.setContentType(MediaType.APPLICATION_JSON);
//        HttpEntity<String> httpEntity = new HttpEntity<>(requestJson, headers);
//        ResponseEntity<T> responseEntity = restTemplate.exchange(uri, HttpMethod.GET, httpEntity, clz);
//        return responseEntity.getBody();
//    }
//
//    public <T> T send(String url, Class<T> clz, String requestJson) {
//        url = daoUrl + url;
//        UriComponents uriComponents = UriComponentsBuilder.fromUriString(url).build().encode();
//        URI uri = uriComponents.toUri();
//        HttpHeaders headers = new HttpHeaders();
//        headers.setContentType(MediaType.APPLICATION_JSON);
//        HttpEntity<String> httpEntity = new HttpEntity<>(requestJson, headers);
//        ResponseEntity<T> responseEntity = restTemplate.exchange(uri, HttpMethod.GET, httpEntity, clz);
//        return responseEntity.getBody();
//    }
//
//    public String send(String url, String requestJson) {
//        url = daoUrl + url;
//        UriComponents uriComponents = UriComponentsBuilder.fromUriString(url).build().encode();
//        URI uri = uriComponents.toUri();
//        HttpHeaders headers = new HttpHeaders();
//        headers.setContentType(MediaType.APPLICATION_JSON);
//        HttpEntity<String> httpEntity = new HttpEntity<>(requestJson, headers);
//        ResponseEntity<String> responseEntity = restTemplate.exchange(uri, HttpMethod.GET, httpEntity, String.class);
//        return responseEntity.getBody();
//    }
//
//    public String send(String url, String requestJson, HttpMethod httpMethod) {
//        url = daoUrl + url;
//        UriComponents uriComponents = UriComponentsBuilder.fromUriString(url).build().encode();
//        URI uri = uriComponents.toUri();
//        HttpHeaders headers = new HttpHeaders();
//        headers.setContentType(MediaType.APPLICATION_JSON);
//        HttpEntity<String> httpEntity = new HttpEntity<>(requestJson, headers);
//        ResponseEntity<String> responseEntity = restTemplate.exchange(uri, httpMethod, httpEntity, String.class);
//        return responseEntity.getBody();
//    }
//
//}
