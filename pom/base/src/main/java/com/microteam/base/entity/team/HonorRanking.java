package com.microteam.base.entity.team;

import com.microteam.base.common.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.io.Serializable;

@Entity
@Table(name = "honor_ranking")
@Getter
@Setter
public class HonorRanking extends BaseEntity implements Serializable {

    @Basic
    @Column(name = "rankingName")
    private String rankingName;

}
