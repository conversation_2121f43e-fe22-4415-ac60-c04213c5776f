package com.microteam.base.entity.agency;


import com.microteam.base.common.base.BaseEntity;
import com.microteam.base.entity.user.Groups;
import com.microteam.base.entity.user.User;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;

@Entity
@Table(name = "football_agency")
@Getter
@Setter
public class FootballAgency extends BaseEntity {

    @ManyToOne
    @JoinColumn(name = "groupsId", nullable = false)
    private Groups groups;
    @ManyToOne
    @JoinColumn(name = "creatorId", nullable = false)
    private User user;
    @Column(name = "agencyName", nullable = false, length = 100)
    private String agencyName;//机构名称
    @Column(name = "agencyAbbreviation", length = 50)
    private String agencyAbbreviation;//机构简称
    @Column(name = "legalPerson", length = 50)
    private String legalPerson;//联系人
    @Column(name = "idCard", length = 30)
    private String idCard;
    @Column(name = "license", length = 100)
    private String license;
    @Column(name = "taxNumber", length = 100)
    private String taxNumber;//公司纳税登记号
    @Column(name = "registerAddr", length = 200)
    private String registerAddr;//注册地址
    @Column(name = "teacherIntro", length = 65535)
    private String teacherIntro;//机构介绍
    @Column(name = "agencyIntro", length = 65535)
    private String agencyIntro;
    @Column(name = "agencyDetails", length = 65535)
    private String agencyDetails;
    @Column(name = "logoImgNetUrl", length = 250)
    private String logoImgNetUrl;
    @Column(name = "logoImgUrl", length = 250)
    private String logoImgUrl;
    @Column(name = "logoImgName", length = 50)
    private String logoImgName;
    @Column(name = "logoImgLength")
    private Long logoImgLength;
    @Column(name = "eduIdea", length = 65535)
    private String eduIdea;
    @Column(name = "courseContent")
    private String courseContent;
    @Column(name = "security", length = 65535)
    private String security;
    @Column(name = "weChatNumber", length = 50)
    private String weChatNumber;
    @Column(name = "weChatNetUrl", length = 250)
    private String weChatNetUrl;
    @Column(name = "weChatUrl", length = 250)
    private String weChatUrl;
    @Column(name = "webSite", length = 200)
    private String webSite;
    @Column(name = "mail", length = 100)
    private String mail;
    @Column(name = "telephone", length = 50)
    private String telephone;
    @Column(name = "countryCode", length = 15)
    private String countryCode;
    @Column(name = "provinceCode", length = 15)
    private String provinceCode;
    @Column(name = "cityCode", length = 15)
    private String cityCode;
    @Column(name = "countyCode", length = 15)
    private String countyCode;
    @Column(name = "longitude", length = 20)
    private String longitude;
    @Column(name = "latitude", length = 20)
    private String latitude;
    @Column(name = "location", length = 200)
    private String location;
    @Column(name = "busLine", length = 200)
    private String busLine;
    @Column(name = "credits")
    private Integer credits;
    @Column(name = "creditsRank")
    private String creditsRank;
    @Column(name = "withHonor", length = 65535)
    private String withHonor;
    @Column(name = "development", length = 65535)
    private String development;
    @Column(name = "type")
    private Integer type;
    /*
     * 新加字段
     * 审核状态： 1审核中，2：审核通过
     */
    @Column(name = "audit", length = 1)
    private Integer audit;

}
