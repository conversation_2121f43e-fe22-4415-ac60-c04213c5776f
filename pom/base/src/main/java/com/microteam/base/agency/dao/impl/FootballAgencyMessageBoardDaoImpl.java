//package com.microteam.base.agency.dao.impl;
//
//import com.microteam.base.agency.dao.FootballAgencyMessageBoardDao;
//import com.microteam.base.entity.agency.FootballAgency;
//import com.microteam.base.entity.agency.FootballAgencyMessageBoard;
//import com.microteam.base.common.base.AbstractHibernateDao;
//import org.apache.log4j.Logger;
//import org.hibernate.query.Query;
//import org.springframework.stereotype.Repository;
//
//import java.util.List;
//
//@Repository("footballAgencyMessageBoardDao")
//public class FootballAgencyMessageBoardDaoImpl extends AbstractHibernateDao<FootballAgencyMessageBoard>
//        implements FootballAgencyMessageBoardDao {
//    static Logger logger = Logger.getLogger(FootballAgencyMessageBoardDaoImpl.class.getName());
//
//    public FootballAgencyMessageBoardDaoImpl() {
//        super();
//        setClazz(FootballAgencyMessageBoard.class);
//
//    }
//
//    //分页查询机构下的留言板
//    @SuppressWarnings("unchecked")
//    @Override
//    public List<FootballAgencyMessageBoard> findMessBoardByPage(
//            FootballAgency agency, int page, int pageSize) {
//
//        String hql = "from FootballAgencyMessageBoard as agencyMessageBoard where agencyMessageBoard.footballAgency=? and agencyMessageBoard.deleted=false order by agencyMessageBoard.createTime desc ";
//        Query query = getCurrentSession().createQuery(hql).setParameter(0, agency);
//        query.setFirstResult((page - 1) * pageSize);
//        query.setMaxResults(pageSize);
//        List<FootballAgencyMessageBoard> list = query.list();
//        return list;
//    }
//
//}
