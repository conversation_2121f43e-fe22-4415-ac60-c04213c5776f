package com.microteam.base.entity.agency;


import com.microteam.base.common.base.BaseEntity;
import com.microteam.base.entity.user.User;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;
import java.io.Serializable;

@Entity
@Table(name = "football_agency_album")
@Getter
@Setter
public class FootballAgencyAlbum extends BaseEntity implements Serializable {

    @ManyToOne
    @JoinColumn(name = "agencyId", nullable = false)
    private FootballAgency footballAgency;
    @ManyToOne
    @JoinColumn(name = "userId")
    private User user;
    @Column(name = "albumName", length = 100)
    private String albumName;
    @Column(name = "albumDesc", length = 200)
    private String albumDesc;

}
