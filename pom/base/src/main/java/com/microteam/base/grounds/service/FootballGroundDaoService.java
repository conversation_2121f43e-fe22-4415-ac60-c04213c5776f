package com.microteam.base.grounds.service;

import com.microteam.base.entity.grounds.FootballGround;

import java.util.List;


public interface FootballGroundDaoService {
    //同时得到FootballGround的内容；
    FootballGround findByGroundName(String groundName);

    //同时得到FootballGrounds的内容；
    FootballGround findById(long id);

    //gerald 根据code查询所有场地
//    List<FootballGround> findByCodeForPage(String cityCode, String countryCode, int page, int pageSize);

    //模糊关键字查询场地
//    List<FootballGround> findByCodeAndSearchForPage(String cityCode, String countryCode, int page, int pageSize, String search);

    //同时得到USER的FootballGround的内容；
    List<FootballGround> findByUserIdForPage(Long userId, int page, int pageSize);

    List<FootballGround> findByCountryCodeForPage(String countryCode, int page, int pageSize);

    FootballGround save(FootballGround footballGround);

}
