//package com.microteam.base.grounds.dao.impl;
//
//import com.microteam.base.common.base.AbstractHibernateDao;
//import com.microteam.base.grounds.dao.FootballGroundDiscountDao;
//import com.microteam.base.entity.grounds.FootballGroundDiscount;
//import org.apache.log4j.Logger;
//import org.hibernate.Hibernate;
//import org.hibernate.query.Query;
//import org.springframework.stereotype.Repository;
//
//import java.util.ArrayList;
//import java.util.List;
//
//@Repository("footballGroundDiscountDao")
//public class FootballGroundDiscountDaoImpl extends AbstractHibernateDao<FootballGroundDiscount> implements FootballGroundDiscountDao {
//    static Logger logger = Logger.getLogger(FootballGroundDiscountDaoImpl.class.getName());
//
//    public FootballGroundDiscountDaoImpl() {
//        super();
//        setClazz(FootballGroundDiscount.class);
//    }
//
//    @Override
//    public List<FootballGroundDiscount> findByDiscountAndCodeForPage(String discount, int page, int pageSize,
//                                                                     String cityCode, String countryCode) {
//        String hql = "from FootballGroundDiscount as grounds " +
//                "left join grounds.footballGround as footballGrounds " +
//                "left join footballGrounds.user as user " +
//                "left join footballGrounds.footballGroundType as footballGroundsType " +
//                "where grounds.discountType=? ";
//        Query query;
//        if ("".equals(countryCode) || countryCode == null || countryCode.equals("null")) {
//            if ("".equals(cityCode) || cityCode == null || cityCode.equals("null")) {
//                hql += " and grounds.countryCode='1000000' and grounds.deleted=false  order by grounds.publishTime desc";
//                query = getCurrentSession().createQuery(hql).setParameter(0, discount);
//                query.setFirstResult((page - 1) * pageSize);
//                query.setMaxResults(pageSize);
//            } else {
//                if ("110000".equals(cityCode) ||
//                        "120000".equals(cityCode) ||
//                        "310000".equals(cityCode) ||
//                        "500000".equals(cityCode) ||
//                        "710000".equals(cityCode) ||
//                        "810000".equals(cityCode) ||
//                        "820000".equals(cityCode)) {
//                    hql += " and grounds.countryCode='1000000' and grounds.provinceCode=? and grounds.deleted=false  order by grounds.publishTime desc";
//                    query = getCurrentSession().createQuery(hql)
//                            .setParameter(0, discount).setParameter(1, cityCode);
//                    query.setFirstResult((page - 1) * pageSize);
//                    query.setMaxResults(pageSize);
//                } else {
//                    hql += " and grounds.countryCode='1000000' and grounds.cityCode=? and grounds.deleted=false  order by grounds.publishTime desc";
//                    query = getCurrentSession().createQuery(hql)
//                            .setParameter(0, discount).setParameter(1, cityCode);
//                    query.setFirstResult((page - 1) * pageSize);
//                    query.setMaxResults(pageSize);
//                }
//            }
//
//            // 默认查询中国的
//        } else {
//            if ("".equals(cityCode) || cityCode == null || cityCode == "null") {
//                hql += " and grounds.countryCode=? and grounds.deleted=false  order by grounds.publishTime desc";
//                query = getCurrentSession().createQuery(hql)
//                        .setParameter(0, discount).setParameter(1, countryCode);
//                query.setFirstResult((page - 1) * pageSize);
//                query.setMaxResults(pageSize);
//            } else {
//                if ("110000".equals(cityCode) ||
//                        "120000".equals(cityCode) ||
//                        "310000".equals(cityCode) ||
//                        "500000".equals(cityCode) ||
//                        "710000".equals(cityCode) ||
//                        "810000".equals(cityCode) ||
//                        "820000".equals(cityCode)) {
//                    hql += " and grounds.countryCode=? and grounds.provinceCode=? and grounds.deleted=false  order by grounds.publishTime desc";
//                    query = getCurrentSession().createQuery(hql)
//                            .setParameter(0, discount).setParameter(1, countryCode)
//                            .setParameter(2, cityCode);
//                    query.setFirstResult((page - 1) * pageSize);
//                    query.setMaxResults(pageSize);
//                } else {
//                    hql += " and grounds.countryCode=? and grounds.cityCode=? and grounds.deleted=false order by grounds.publishTime desc";
//                    System.out.println(hql);
//                    query = getCurrentSession().createQuery(hql)
//                            .setParameter(0, discount).setParameter(1, countryCode)
//                            .setParameter(2, cityCode);
//                    query.setFirstResult((page - 1) * pageSize);
//                    query.setMaxResults(pageSize);
//                }
//            }
//        }
//
//        List<Object[]> list = query.list();
//        if (list != null && list.size() > 0) {
//            List<FootballGroundDiscount> listnew = new ArrayList<FootballGroundDiscount>();
//            for (int i = 0; i < list.size(); i++) {
//                Object[] obj = list.get(i);
//
//                FootballGroundDiscount grounds = (FootballGroundDiscount) obj[0];
//                // FootballGrounds grounds=(FootballGrounds)list.get(i);
//
//                Hibernate.initialize(grounds);// 获取赖加载的集合内容；
//                Hibernate.initialize(grounds.getFootballGround());// 获取赖加载的集合内容；
//                Hibernate.initialize(grounds.getFootballGround().getUser());// 获取赖加载的集合内容；
//                Hibernate.initialize(grounds.getFootballGround().getFootballGroundType());
//                listnew.add(i, grounds);
//
//            }
//            return listnew;
//        }
//        return null;
//    }
//
//    @Override
//    public FootballGroundDiscount findById(long id) {
//        String hql = "from FootballGroundDiscount as grounds " +
//                "left join fetch grounds.footballGround as footballGrounds " +
//                "left join fetch footballGrounds.user as user " +
//                "left join fetch footballGrounds.footballGroundType as footballGroundsType " +
//                "where grounds.id=? " +
//                "and grounds.deleted=false ";
//        Query query = getCurrentSession().createQuery(hql).setParameter(0, id);
//        List<FootballGroundDiscount> list = query.list();
//        if (list != null && list.size() > 0) {
//            return list.get(0);
//        }
//        return null;
//    }
//
//    @Override
//    public List<FootballGroundDiscount> findListDiscountGroundsBySearchContentAndCityOrCounty(
//            String discountType, String cityCode, String countryCode, int page,
//            int pageSize, String search) {
//        String hql = "from FootballGroundDiscount as grounds left join  grounds.footballGround as footballGrounds left join footballGrounds.user as user left join  footballGrounds.footballGroundType as footballGroundsType  where grounds.discountType=? ";
//        Query query = null;
//        if ("".equals(countryCode) || countryCode == null
//                || countryCode.equals("null")) {
//            if ("".equals(cityCode) || cityCode == null || cityCode.equals("null")) {
//                hql += " and footballGrounds.groundName like ? and grounds.countryCode='1000000' and grounds.deleted=false  order by grounds.publishTime desc";
//                query = getCurrentSession().createQuery(hql).setParameter(0,
//                        discountType).setParameter(1,
//                        "%" + search + "%");
//                query.setFirstResult((page - 1) * pageSize);
//                query.setMaxResults(pageSize);
//            } else {
//                if ("110000".equals(cityCode) ||
//                        "120000".equals(cityCode) ||
//                        "310000".equals(cityCode) ||
//                        "500000".equals(cityCode) ||
//                        "710000".equals(cityCode) ||
//                        "810000".equals(cityCode) ||
//                        "820000".equals(cityCode)) {
//                    hql += " and footballGrounds.groundName like ? and grounds.countryCode='1000000' and grounds.provinceCode=? and grounds.deleted=false order by grounds.publishTime desc";
//                    query = getCurrentSession().createQuery(hql)
//                            .setParameter(0, discountType).setParameter(1,
//                                    "%" + search + "%").setParameter(2, cityCode);
//                    query.setFirstResult((page - 1) * pageSize);
//                    query.setMaxResults(pageSize);
//                } else {
//                    hql += " and footballGrounds.groundName like ? and grounds.countryCode='1000000' and grounds.cityCode=? and grounds.deleted=false order by grounds.publishTime desc";
//                    query = getCurrentSession().createQuery(hql)
//                            .setParameter(0, discountType).setParameter(1,
//                                    "%" + search + "%").setParameter(2, cityCode);
//                    query.setFirstResult((page - 1) * pageSize);
//                    query.setMaxResults(pageSize);
//                }
//            }
//
//            // 默认查询中国的
//        } else {
//            if ("".equals(cityCode) || cityCode == null || cityCode == "null") {
//                hql += " and footballGrounds.groundName like ?	 and grounds.countryCode=? and grounds.deleted=false  order by grounds.publishTime desc";
//                query = getCurrentSession().createQuery(hql)
//                        .setParameter(0, discountType).setParameter(1,
//                                "%" + search + "%").setParameter(2, countryCode);
//                query.setFirstResult((page - 1) * pageSize);
//                query.setMaxResults(pageSize);
//            } else {
//                if ("110000".equals(cityCode) ||
//                        "120000".equals(cityCode) ||
//                        "310000".equals(cityCode) ||
//                        "500000".equals(cityCode) ||
//                        "710000".equals(cityCode) ||
//                        "810000".equals(cityCode) ||
//                        "820000".equals(cityCode)) {
//                    hql += " and footballGrounds.groundName like ?	 and grounds.countryCode=? and grounds.provinceCode=? and grounds.deleted=false  order by grounds.publishTime desc";
//                    query = getCurrentSession().createQuery(hql)
//                            .setParameter(0, discountType).setParameter(1,
//                                    "%" + search + "%").setParameter(2, countryCode)
//                            .setParameter(3, cityCode);
//                    query.setFirstResult((page - 1) * pageSize);
//                    query.setMaxResults(pageSize);
//                } else {
//                    hql += " and footballGrounds.groundName like ?	 and grounds.countryCode=? and grounds.cityCode=? and grounds.deleted=false  order by grounds.publishTime desc";
//                    System.out.println(hql);
//                    query = getCurrentSession().createQuery(hql)
//                            .setParameter(0, discountType).setParameter(1,
//                                    "%" + search + "%").setParameter(2, countryCode)
//                            .setParameter(3, cityCode);
//                    query.setFirstResult((page - 1) * pageSize);
//                    query.setMaxResults(pageSize);
//                }
//            }
//        }
//
//        List<Object[]> list = query.list();
//        if (list != null && list.size() > 0) {
//            List<FootballGroundDiscount> listnew = new ArrayList<FootballGroundDiscount>();
//            for (int i = 0; i < list.size(); i++) {
//                Object[] obj = list.get(i);
//
//                FootballGroundDiscount grounds = (FootballGroundDiscount) obj[0];
//                Hibernate.initialize(grounds);// 获取赖加载的集合内容；
//                Hibernate.initialize(grounds.getFootballGround());// 获取赖加载的集合内容；
//                Hibernate.initialize(grounds.getFootballGround().getUser());// 获取赖加载的集合内容；
//                Hibernate.initialize(grounds.getFootballGround().getFootballGroundType());
//                listnew.add(i, grounds);
//
//            }
//            return listnew;
//        }
//        return null;
//    }
//
//}
