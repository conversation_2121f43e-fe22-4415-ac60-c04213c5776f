//package com.microteam.base.grounds.dao.impl;
//
//import com.microteam.base.common.base.AbstractHibernateDao;
//import com.microteam.base.grounds.dao.FootballGroundTypeDao;
//import com.microteam.base.entity.grounds.FootballGroundType;
//import org.apache.log4j.Logger;
//import org.hibernate.query.Query;
//import org.springframework.stereotype.Repository;
//
//import java.util.List;
//
//@Repository("footballGroundTypeDao")
//public class FootballGroundTypeDaoImpl extends AbstractHibernateDao<FootballGroundType> implements FootballGroundTypeDao {
//    static Logger logger = Logger.getLogger(FootballGroundTypeDaoImpl.class.getName());
//
//    public FootballGroundTypeDaoImpl() {
//        super();
//        setClazz(FootballGroundType.class);
//    }
//
//    @Override
//    public FootballGroundType findByTypeName(String typeName) {
//        String hql = "from FootballGroundType as footballGroundsType " +
//                "where footballGroundsType.typeName=? " +
//                "and footballGroundsType.deleted=false";
//        Query query = getCurrentSession().createQuery(hql)
//                .setParameter(0, typeName);
//        List<FootballGroundType> list = query.list();
//        if (list.size() == 1)
//            return list.get(0);
//        return null;
//    }
//
//}
