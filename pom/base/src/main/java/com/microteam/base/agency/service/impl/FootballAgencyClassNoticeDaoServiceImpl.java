package com.microteam.base.agency.service.impl;

import com.microteam.base.agency.dao.FootballAgencyClassNoticeDao;
import com.microteam.base.entity.agency.FootballAgencyClassNotice;
import com.microteam.base.agency.service.FootballAgencyClassNoticeDaoService;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.List;

@Service("footballAgencyClassNoticeDaoService")
public class FootballAgencyClassNoticeDaoServiceImpl implements FootballAgencyClassNoticeDaoService {
    static Logger logger = Logger.getLogger(FootballAgencyDaoServiceImpl.class.getName());
    @Autowired
    private FootballAgencyClassNoticeDao dao;

    //分页查询通知
    @Override
    public List<FootballAgencyClassNotice> findByAgencyIdAndAgencyClassIdForPage(Long agencyId, Long agencyClassId, int pageSize, int page) {
        if (page > 0) {
            page--;
        }
        Pageable pageable = PageRequest.of(page, pageSize);
        return dao.findByAgencyIdAndAgencyClassIdForPage(agencyId, agencyClassId, pageable);
    }

    @Override
    public FootballAgencyClassNotice findById(long id) {
        return dao.findById(id);
    }


}
