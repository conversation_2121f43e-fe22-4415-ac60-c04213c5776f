package com.microteam.base.lesson.service;


import com.microteam.base.entity.lesson.FootballLessonEnroll;

import java.math.BigInteger;
import java.util.Date;
import java.util.List;

public interface FootballLessonEnrollDaoService {

    List<FootballLessonEnroll> findByUserIdForDay(Long userId, Date date);

    List<FootballLessonEnroll> findByTeamId(Long teamId);

    List<BigInteger> findFinishedByUserIdOrderByStartTimeDescForPage(Long userId, Integer page, Integer pageSize);

    List<BigInteger> findUnFinishedByUserIdOrderByStartTimeForPage(Long userId, Integer page, Integer pageSize);

    List<FootballLessonEnroll> findByLessonIdAndTeamId(Long lessonId, Long teamId);

    List<FootballLessonEnroll> findByLessonId(Long lessonId);

    FootballLessonEnroll findByUserIdAndLessonIdAndTeamId(Long userId, Long lessonId, Long teamId);

    FootballLessonEnroll findByUserIdAndLessonId(Long userId, Long lessonId);

    int deleteByTeamId(Long teamId);

    FootballLessonEnroll save(FootballLessonEnroll footballLessonEnroll);

    List<FootballLessonEnroll> saveAll(List<FootballLessonEnroll> list);
}
