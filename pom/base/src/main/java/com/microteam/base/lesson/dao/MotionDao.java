package com.microteam.base.lesson.dao;

import com.microteam.base.entity.lesson.Motion;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;

import java.util.List;


public interface MotionDao extends JpaRepository<Motion, Long>, JpaSpecificationExecutor<Motion> {

    @Query("from Motion as motion " +
            "where motion.deleted = false " +
            "and motion.id <> 0 ")
    List<Motion> findAll();
}
