package com.microteam.base.agency.service.impl;

import com.microteam.base.agency.dao.FootballAgencyGroundAlbumDao;
import com.microteam.base.entity.agency.FootballAgencyGround;
import com.microteam.base.entity.agency.FootballAgencyGroundAlbum;
import com.microteam.base.agency.service.FootballAgencyGroundAlbumDaoService;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service("footballAgencyGroundAlbumDaoService")
public class FootballAgencyGroundAlbumDaoServiceImpl implements FootballAgencyGroundAlbumDaoService {
    static Logger logger = Logger.getLogger(FootballAgencyGroundAlbumDaoServiceImpl.class.getName());
    @Autowired
    private FootballAgencyGroundAlbumDao dao;

    public FootballAgencyGroundAlbumDaoServiceImpl() {
        super();

    }

    //查询机构场地的相册
    @Override
    public List<FootballAgencyGroundAlbum> findGroundAlbumByAgency(
            FootballAgencyGround agencyGround) {
        return dao.findGroundAlbumByAgency(agencyGround);
    }

    @Override
    public FootballAgencyGroundAlbum save(FootballAgencyGroundAlbum footballAgencyGroundAlbum) {
        return dao.save(footballAgencyGroundAlbum);
    }
}
