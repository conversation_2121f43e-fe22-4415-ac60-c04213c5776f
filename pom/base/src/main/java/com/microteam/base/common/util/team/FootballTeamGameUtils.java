package com.microteam.base.common.util.team;


import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONException;
import com.alibaba.fastjson.JSONObject;
import com.microteam.base.common.constant.FootballContants;
import com.microteam.base.common.pojo.DataRankingPojo;
import com.microteam.base.common.pojo.RankingPojo;
import com.microteam.base.common.pojo.team.CurvePojo;
import com.microteam.base.common.pojo.team.KickStatePojo;
import com.microteam.base.common.util.common.TranslatorUtil;
import com.microteam.base.common.util.user.DataUtil;
import com.microteam.base.entity.team.*;
import com.microteam.base.entity.user.User;
import com.microteam.base.entity.user.UserHardware;
import com.microteam.base.entity.user.UserHardwareData;
import com.microteam.base.entity.user.UserHeadimg;
import com.microteam.base.team.dto.FootballTeamGameStatisticsPlayerDto;

import java.beans.PropertyDescriptor;
import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.PreparedStatement;
import java.util.*;

/*
 * @Description: 工具类
 * @author: zhaoxuebin
 * @date 2018年1月24日 下午4:49:25
 */
public class FootballTeamGameUtils {

    public static Map<String, Object> packbackGame(List<FootballTeamTakeNode> teamNode, List<FootballTeamGameStatisticsPlayer> teamPlayer, Map<String, Object> backMap, User user) throws NoSuchMethodException, InvocationTargetException, IllegalAccessException {
        int wordIndex = 0;  //评语编号
        String bestName = null;
        Long number = 0L; //对应数值
        if (user != null) {
            bestName = user.getNickName();
            Long goalsfor = 0L;
            Long assist = 0L;
            DataRankingPojo passPojo;
            DataRankingPojo movePojo;
            Long holdUp = 0L;
            Long save = 0L;
            Long fault = -1L;  //停球失误+防守失误
            passPojo = rankingValueForsta(teamPlayer, "passBallCounts");
            movePojo = rankingValueForsta(teamPlayer, "wholeMoveDistance");


            FootballTeamTakeNode node = null;
            if (teamNode != null && teamNode.size() > 0) {
                for (FootballTeamTakeNode takeNode : teamNode) {
                    if (takeNode.getUser().getId().equals(user.getId())) {
                        node = takeNode;
                    }
                }
            }

            if (node != null) {
                goalsfor = node.getGoalsfor() == null ? Long.valueOf(0) : node.getGoalsfor();
                assist = node.getAssist() == null ? Long.valueOf(0) : node.getAssist();
                holdUp = node.getHoldUp() == null ? Long.valueOf(0) : node.getHoldUp();
                save = node.getSave() == null ? Long.valueOf(0) : node.getSave();
                fault = (node.getStopFault() == null ? Long.valueOf(0) : node.getStopFault()) + (node.getDefendFault() == null ? 0L : node.getDefendFault());
            }

            //进球3个或以上
            if (goalsfor >= 3L) {   //进球3个
                wordIndex = 1;
                number = goalsfor;
            } else if (goalsfor == 2L) { //进球2个
                wordIndex = 2;
                number = goalsfor;
            } else if (assist >= 3L) { //助攻3个以上
                wordIndex = 3;
                number = assist;
            } else if (assist == 2L) {  //助攻2个
                wordIndex = 4;
                number = assist;
            } else if (passPojo.getRankingList().get(0).getUserId().equals(user.getId()) && passPojo.getRankingList().get(0).getValue() > 0) { //传球排名第一
                wordIndex = 5;
                number = passPojo.getRankingList().get(0).getValue().longValue();
            } else if (movePojo.getRankingList().get(0).getUserId().equals(user.getId()) && movePojo.getRankingList().get(0).getValue() > 0) { //跑动第一
                wordIndex = 6;
                number = movePojo.getRankingList().get(0).getValue().longValue();
            } else if (goalsfor == 1L) { //进球1个
                wordIndex = 7;
                number = goalsfor;
            } else if (assist == 1L) {   //助攻1个
                wordIndex = 8;
                number = assist;
            } else if (holdUp >= 1L) {   //抢断1个以上
                wordIndex = 9;
                number = holdUp;
            } else if (save >= 1L) {   // 解围1个以上
                wordIndex = 10;
                number = save;
            } else if (fault > -1L) { //用户有录入失误数据
                wordIndex = 11;
                number = fault;
            } else if (fault == -1L) {   //用户没有录入失误数据
                wordIndex = 12;
                number = 0L;
            }
        }
        backMap.put("wordIndex", wordIndex);
        backMap.put("bestName", bestName);
        backMap.put("number", number);
        return backMap;
    }

    public static JSONArray sortForWhenArray(JSONArray whenArray) throws JSONException {
        JSONArray haveTime = new JSONArray();
        JSONArray noHaveTime = new JSONArray();
        for (int i = 0; i < whenArray.size(); i++) {
            JSONObject object = whenArray.getJSONObject(i);
            if (object.getLong("timeTable") == -1L) {
                noHaveTime.add(object);
            } else {
                haveTime.add(object);
            }
        }
        //把有时间的升序排列
        for (int i = 0; i < haveTime.size(); i++) {
            for (int j = i + 1; j < haveTime.size(); j++) {
                JSONObject iObject = haveTime.getJSONObject(i);
                JSONObject jObject = haveTime.getJSONObject(j);
                if (iObject.getInteger("timeTable") > jObject.getInteger("timeTable")) {
                    haveTime.add(i, jObject);
                    haveTime.add(j, iObject);
                }
            }
        }
        whenArray = new JSONArray();
        for (int i = 0; i < haveTime.size(); i++) {
            whenArray.add(haveTime.get(i));
        }
        for (int i = 0; i < noHaveTime.size(); i++) {
            whenArray.add(noHaveTime.get(i));
        }

        return whenArray;
    }

    private static DataRankingPojo rankingValueForsta(List<FootballTeamGameStatisticsPlayer> statisticsList, String typeName) throws NoSuchMethodException, InvocationTargetException, IllegalAccessException {
        DataRankingPojo dataRankingPojo = new DataRankingPojo();
        Double sum = (double) 0;
        List<RankingPojo> rankingPojoList = new ArrayList<>();
        for (FootballTeamGameStatisticsPlayer statistics : statisticsList) {
            Method method = FootballTeamGameStatisticsPlayer.class.getMethod("get" + typeName.substring(0, 1).toUpperCase() + typeName.substring(1));
            Long value;
            if (method.invoke(statistics) instanceof Integer) {
                value = ((Integer) method.invoke(statistics)).longValue();
            } else if (method.invoke(statistics) instanceof Double) {
                value = ((Double) method.invoke(statistics)).longValue();
            } else {
                value = (Long) method.invoke(statistics);
            }

            if (value == null) {
                value = 0L;
            }
            Double doubleValue = value.doubleValue();
            User user = statistics.getUser();
            sum += doubleValue;
            RankingPojo rankingPojo = new RankingPojo();
            rankingPojo.setNickName(user.getNickName());
            rankingPojo.setUserId(user.getId());
            rankingPojo.setValue(doubleValue);
            rankingPojoList.add(rankingPojo);
        }
        rankingPojoList.sort((o1, o2) -> {
            if (o1.getValue() > o2.getValue()) {
                return -1;
            }
            if (o1.getValue().equals(o2.getValue())) {
                return 0;
            }
            return 1;
        });
        dataRankingPojo.setSum(sum);
        dataRankingPojo.setRankingList(rankingPojoList);
        return dataRankingPojo;
    }


    public static List<Long> jArrayToList(JSONArray ownArray, JSONArray joinArray) throws NumberFormatException, JSONException {
        List<Long> list = new ArrayList<>();
        if (ownArray != null && ownArray.size() > 0) {
            for (int i = 0; i < ownArray.size(); i++) {
                list.add(Long.parseLong(ownArray.getString(i)));
            }
        }
        if (joinArray != null && joinArray.size() > 0) {
            for (int j = 0; j < joinArray.size(); j++) {
                list.add(Long.parseLong(joinArray.getString(j)));
            }
        }
        return list;
    }


    /**
     * @param teamNode
     * @param passBallMap
     * @param moveMap
     * @param map
     * @return Map<String, Object>
     * @Description: 算出最佳球员和评价
     * <AUTHOR>
     * @date 2018年1月25日 下午3:00:29
     */
    public static Map<String, Object> packBestFootballer(List<FootballTeamTakeNode> teamNode, Map<User, Integer> passBallMap, Map<User, Long> moveMap, Map<String, Object> map, FootballTeam team) {
        int wordIndex;
        String bestName = ""; //最佳球员
        Long number = 0L; //对应数值
        for (FootballTeamTakeNode node : teamNode) {
            //进球三个以上
            if (node.getGoalsfor() >= 3 && node.getGoalsfor() > number) {
                wordIndex = 1;
                bestName = node.getUser().getNickName();
                number = node.getGoalsfor();
                map.put("wordIndex", wordIndex);
                map.put("bestName", bestName);
                map.put("count", number);
                return map;
            }
            //进球两个
            if (node.getGoalsfor() == 2 && node.getGoalsfor() > number) {
                wordIndex = 2;
                bestName = node.getUser().getNickName();
                number = node.getGoalsfor();
                map.put("wordIndex", wordIndex);
                map.put("bestName", bestName);
                map.put("count", number);
                return map;
            }
            //助攻3个以上
            if (node.getAssist() >= 3 && node.getAssist() > number) {
                wordIndex = 3;
                bestName = node.getUser().getNickName();
                number = node.getAssist();
                map.put("wordIndex", wordIndex);
                map.put("bestName", bestName);
                map.put("count", number);
                return map;
            }
            //助攻2个
            if (node.getAssist() == 2 && node.getAssist() > number) {
                wordIndex = 4;
                bestName = node.getUser().getNickName();
                number = node.getAssist();
                map.put("wordIndex", wordIndex);
                map.put("bestName", bestName);
                map.put("count", number);
                return map;
            }
        }
        //传球第一
        if (passBallMap != null && passBallMap.size() > 0) {
            Integer passCount = 0;
            for (Map.Entry<User, Integer> entry : passBallMap.entrySet()) {
                if (entry.getValue() >= passCount) {
                    passCount = entry.getValue();
                    bestName = entry.getKey().getNickName();
                }
            }
            number = passCount.longValue();
            wordIndex = 5;
            map.put("wordIndex", wordIndex);
            map.put("bestName", bestName);
            map.put("count", number);
            return map;
        }
        //跑动第一
        if (moveMap != null && moveMap.size() > 0) {
            Long move = 0L;
            for (Map.Entry<User, Long> entry : moveMap.entrySet()) {
                if (entry.getValue() >= move) {
                    move = entry.getValue();
                    bestName = entry.getKey().getNickName();
                }
            }
            number = move;
            if (number > 0) {
                wordIndex = 6;
                map.put("wordIndex", wordIndex);
                map.put("bestName", bestName);
                BigDecimal b = new BigDecimal(number.doubleValue() / 1000);
                map.put("count", b.setScale(2, RoundingMode.HALF_UP).doubleValue());
                return map;
            }
        }
        for (FootballTeamTakeNode node : teamNode) {
            //进球1个
            if (node.getGoalsfor() == 1 && node.getGoalsfor() > number) {
                wordIndex = 7;
                bestName = node.getUser().getNickName();
                number = node.getGoalsfor();
                map.put("wordIndex", wordIndex);
                map.put("bestName", bestName);
                map.put("count", number);
                return map;
            }
            //助攻1个
            if (node.getAssist() == 1 && node.getAssist() > number) {
                wordIndex = 8;
                bestName = node.getUser().getNickName();
                number = node.getAssist();
                map.put("wordIndex", wordIndex);
                map.put("bestName", bestName);
                map.put("count", number);
                return map;
            }
            //抢断1个以上
            if (node.getHoldUp() >= 1 && node.getHoldUp() > number) {
                wordIndex = 9;
                bestName = node.getUser().getNickName();
                number = node.getHoldUp();
                map.put("wordIndex", wordIndex);
                map.put("bestName", bestName);
                map.put("count", number);
                return map;
            }
            //解围1个以上
            if (node.getSave() != null && (node.getSave() >= 1 && node.getSave() > number)) {
                wordIndex = 10;
                bestName = node.getUser().getNickName();
                number = node.getSave();
                map.put("wordIndex", wordIndex);
                map.put("bestName", bestName);
                map.put("count", number);
                return map;
            }
            //有录入失误数据
            if (node.getStopFault() != null && (node.getStopFault() > 0 || node.getPassFault() > 0 || node.getDefendFault() > 0)) {
                wordIndex = 11;
                bestName = node.getUser().getNickName();
                number = node.getStopFault() + node.getPassFault() + node.getDefendFault();
                map.put("wordIndex", wordIndex);
                map.put("bestName", bestName);
                map.put("count", number);
                return map;
            }
        }
        //没有录入失误数据
        wordIndex = 12;
        bestName = team.getUser().getNickName();
        number = 0L;
        map.put("wordIndex", wordIndex);
        map.put("bestName", bestName);
        map.put("count", number);
        return map;
    }

    /**
     * @param gameFlowArray
     * @return JSONArray
     * @throws JSONException
     * @Description: 赛程同一天的分到一个组数里并升序排列
     * <AUTHOR>
     * @date 2018年1月26日 下午4:56:15
     */
    public static JSONArray packTheSameDay(JSONArray gameFlowArray) throws JSONException {
        Map<Integer, JSONArray> sameDayMap = new HashMap<>();
        for (int i = 0; i < gameFlowArray.size(); i++) {
            Calendar c = Calendar.getInstance();
            JSONObject object = gameFlowArray.getJSONObject(i);
            Date time = new Date(object.getLong("time"));
            c.setTime(time);
            int day = c.get(Calendar.DAY_OF_MONTH);
            if (sameDayMap.containsKey(day)) {
                sameDayMap.get(day).add(object);
            } else {
                JSONArray array = new JSONArray();
                array.add(object);
                sameDayMap.put(day, array);
            }
        }
        if (sameDayMap.size() > 0) {
            gameFlowArray = new JSONArray();
            for (Map.Entry<Integer, JSONArray> entry : sameDayMap.entrySet()) {
                gameFlowArray.add(DataUtil.sortArrayByLongValue(entry.getValue(), "time"));
            }
        }
        return gameFlowArray;
    }

    /**
     * @param list
     * @return int
     * @Description: 计算拥有硬件设备的人数
     * <AUTHOR>
     * @date 2018年2月1日 上午11:58:48
     */
    public static int getHaveHardwareUserCount(List<UserHardware> list) {
        int count;
        Map<Long, Long> map = new HashMap<>();
        if (list != null) {
            for (UserHardware userHardware : list) {
                map.put(userHardware.getUser().getId(), userHardware.getId());
            }
        }
        count = map.size();
        return count;
    }


    /**
     * @param list
     * @return int
     * @Description: 计算球队的平均年龄
     * <AUTHOR>
     * @date 2018年2月1日 下午2:17:52
     */
    public static int getAvgAgeOfTeam(List<FootballTeamUser> list) {
        if (list == null || list.size() == 0) {
            return 0;
        }
        int sumAge = 0;
        for (FootballTeamUser footballTeamUser : list) {
            if (footballTeamUser.getUser().getAges() != null) {
                sumAge += footballTeamUser.getUser().getAges().intValue();
            }
        }
        return Math.round((float) sumAge / (float) list.size());
    }

    /**
     * @param teamName
     * @param game
     * @return boolean
     * @Description: 判断是不是主场
     * <AUTHOR>
     * @date 2018年2月1日 下午3:14:50
     */
    public static boolean isHomeCourt(String teamName, FootballTeamGame game) {
        boolean isHomeCourt = false;
        if (teamName.equals(game.getFootballTeam().getTeamName())) {
            isHomeCourt = true;
        }
        return isHomeCourt;
    }

    /**
     * @param gameList
     * @return List<FootballTeamGame>
     * @Description: 按照与当前时间的绝对差值升序排列
     * <AUTHOR>
     * @date 2018年2月6日 上午9:35:12
     */
    public static List<FootballTeamGame> sortGameListByTimeAbs(List<FootballTeamGame> gameList) {
        if (gameList != null && gameList.size() > 0) {
            long now = new Date().getTime();
            for (int i = 0; i < gameList.size(); i++) {
                for (int j = i + 1; j < gameList.size(); j++) {
                    FootballTeamGame iGame = gameList.get(i);
                    FootballTeamGame jGame = gameList.get(j);
                    /*System.out.println(Math.abs(iGame.getCompetitionTime().getOccurTime()-now)+"-------"+Math.abs(jGame.getCompetitionTime().getOccurTime()-now));*/
                    if (Math.abs(iGame.getCompetitionTime().getTime() - now) > Math.abs(jGame.getCompetitionTime().getTime() - now)) {
                        gameList.set(i, jGame);
                        gameList.set(j, iGame);
                    }
                }
            }
        }
        return gameList;
    }

    public static JSONArray stringToJSONArray(String s) throws JSONException {
        JSONArray j = new JSONArray();
        if (s != null && !"".equals(s)) {
            j = JSONArray.parseArray(s);
        }
        return j;
    }


    /**
     * @param poloList
     * @return Map<Integer, String>
     * @Description: 封装球衣号码和名字
     * <AUTHOR>
     * @date 2018年2月23日 下午4:42:18
     */
    public static Map<Integer, String> packTeamNumber(List<PoloShirt> poloList) {
        Map<Integer, String> map = new LinkedHashMap<>();
        for (int i = 1; i <= 99; i++) {
            map.put(i, null);
        }
        for (PoloShirt polo : poloList) {
            map.put(polo.getNumber().intValue(), polo.getUser().getNickName());
        }
        return map;
    }


    /**
     * @param teamRole
     * @return String
     * @Description: 根据角色获得数组名称
     * <AUTHOR>
     * @date 2018年3月1日 下午3:50:28
     */
    public static String getArrayNameByMemberTeamRole(String teamRole) {
        String arrayName = "joinTeamsArray";
        if ("teamFans".equals(teamRole)) {
            arrayName = "fansTeamsArray";
        } else if ("teamCoach".equals(teamRole)) {
            arrayName = "ownerTeamsArray";
        } else if ("teamOwner".equals(teamRole)) {
            arrayName = "ownerTeamsArray";
        } else if ("teamLeader".equals(teamRole)) {
            arrayName = "ownerTeamsArray";
        } else if ("teamManage".equals(teamRole)) {
            arrayName = "ownerTeamsArray";
        } else if ("cheerTeam".equals(teamRole)) {
            arrayName = "followTeamsArray";
        }
        return arrayName;
    }


    public static int getTeamTypeByArrayName(String arrayName) {
        int i = 0;
        if ("ownerTeamsArray".equals(arrayName)) {
            i = 1; // 1代表我创建的球队
        } else if ("joinTeamsArray".equals(arrayName)) {
            i = 2; // 2代表我加入的球队
        } else if ("followTeamsArray".equals(arrayName)) {
            i = 3; // 3代表我关注的球队
        } else if ("fansTeamsArray".equals(arrayName)) {
            i = 4; // 4代表我的粉丝球队
        }
        return i;
    }

    /**
     * @return JSONArray
     * @throws JSONException
     * @Description: 获得用户在球队的角色和用户id
     * <AUTHOR>
     * @date 2018年3月2日 上午11:11:34
     */
//    public static JSONArray getUserTeamRoleAndId(JSONArray jArray, List<String> arrayNameList, FootballTeamMemberCount teamMemberCount, List<Long> userIdList) throws JSONException {
//
//        if (arrayNameList != null && arrayNameList.size() > 0) {
//            for (int i = 0; i < arrayNameList.size(); i++) {
//                String arrayName = arrayNameList.get(i);
//                String userRole = null;
//
//                String arrayString = null;
//                JSONObject object = null;
//                JSONArray array = null;
//                if (arrayName.equals("manageArray")) {
//                    arrayString = teamMemberCount.getManageArray();
//                    userRole = "teamManage";
//                } else if (arrayName.equals("playerArray")) {
//                    arrayString = teamMemberCount.getPlayerArray();
//                    userRole = "teamPlayer";
//                } else if (arrayName.equals("coachArray")) {
//                    arrayString = teamMemberCount.getCoachArray();
//                    userRole = "teamCoach";
//                } else if (arrayName.equals("leaderArray")) {
//                    arrayString = teamMemberCount.getLeaderArray();
//                    userRole = "teamLeader";
//                } else if (arrayName.equals("fansArray")) {
//                    arrayString = teamMemberCount.getFansArray();
//                    userRole = "teamFans";
//                }
//                object = JSONObject.parseObject(arrayString);
//                array = object.getJSONArray(arrayNameList.get(i));
//
//                if (array != null && array.size() > 0) {
//                    for (int j = 0; j < array.size(); j++) {
//                        long userId = Long.parseLong(array.getString(j));
//                        Map<String, Object> tempMap = new HashMap<String, Object>();
//                        tempMap.put("userRole", userRole);
//                        tempMap.put("userId", userId);
//                        userIdList.add(userId);
//                        jArray.add(tempMap);
//                    }
//                }
//            }
//        }
//        return jArray;
//    }

//    /**
//     * @param userCount
//     * @return List<Long>
//     * @throws Exception
//     * @Description: 得到我创建的 加入的 成为粉丝的球队
//     * <AUTHOR>
//     * @date 2018年3月5日 下午2:18:06
//     */
//    public static List<Long> getWorkTeamId(FootballTeamMemberUserCount userCount) throws Exception {
//        List<Long> teamIdList = new ArrayList<Long>();
//        if (userCount != null && !userCount.equals("")) {
//            JSONObject ownerObject = JSONObject.parseObject(userCount.getOwnerTeamsArray());
//            JSONObject joinObject = JSONObject.parseObject(userCount.getJoinTeamsArray());
//            JSONObject fansObject = JSONObject.parseObject(userCount.getFansTeamsArray());
//
//            JSONArray ownerjArray = ownerObject.getJSONArray("ownerTeamsArray");
//            JSONArray joinjArray = joinObject.getJSONArray("joinTeamsArray");
//            /*JSONArray fansjArray = fansObject.getJSONArray("fansTeamsArray");*/
//
//            teamIdList = getTeamIdList(ownerjArray, joinjArray, null, teamIdList);  //去掉粉丝关联的球队
//
//        }
//        return teamIdList;
//    }
    public static List<Long> getTeamIdList(JSONArray array, JSONArray array2, JSONArray array3, List<Long> teamIdList) {
        if (array != null && array.size() > 0) {
            for (int i = 0; i < array.size(); i++) {
                teamIdList.add(Long.parseLong(array.getString(i)));
            }
        }

        if (array2 != null && array2.size() > 0) {
            for (int i = 0; i < array2.size(); i++) {
                teamIdList.add(Long.parseLong(array2.getString(i)));
            }
        }

        if (array3 != null && array3.size() > 0) {
            for (int i = 0; i < array3.size(); i++) {
                teamIdList.add(Long.parseLong(array3.getString(i)));
            }
        }
        return teamIdList;
    }


    //    /**
//     * @param footballteamemberCount
//     * @return JSONArray
//     * @Description: 获得球队队员和粉丝的id（返回一个JSONArray）
//     * <AUTHOR>
//     * @date 2018年3月7日 下午3:39:24
//     */
//    public static JSONArray getTeamPlayAndFansId(FootballTeamMemberCount footballteamemberCount) {
//        String playerString = footballteamemberCount.getPlayerArray();
//        String fansString = footballteamemberCount.getFansArray();
//
//        JSONObject playerObject = JSONObject.parseObject(playerString);
//        JSONObject fansObject = JSONObject.parseObject(fansString);
//
//        JSONArray playerArray = playerObject.getJSONArray("playerArray");
//        JSONArray fansArray = fansObject.getJSONArray("fansArray");
//
//        JSONArray array = mergeArray(playerArray, fansArray);
//        return array;
//
//    }
    public static JSONArray mergeArray(JSONArray playerArray, JSONArray fansArray) {
        JSONArray array = new JSONArray();

        List<JSONArray> list = new ArrayList<>();
        list.add(playerArray);
        list.add(fansArray);

        for (JSONArray tempArray : list) {
            for (int j = 0; j < tempArray.size(); j++) {
                array.add(tempArray.getString(j));
            }
        }
        return array;
    }

    /**
     * @return long
     * @Description: 获得随机号码
     * <AUTHOR>
     * @date 2018年3月15日 上午10:06:17
     */
    public static long getRandomTeamNumber(List<PoloShirt> poloList) {
        long number = 0L;
        Map<Integer, String> map = new LinkedHashMap<>();
        for (int i = 1; i <= 99; i++) {
            map.put(i, null);
        }

        for (PoloShirt polo : poloList) {
            map.put(polo.getNumber().intValue(), polo.getUser().getNickName());
        }
        int end = 99;
        int start = 1;
        for (Integer key : map.keySet()) {
            Random random = new Random();
            Integer ramdomNumber = random.nextInt(end - start + 1) + start;
            if (map.containsKey(ramdomNumber) && map.get(ramdomNumber) == null) {
                number = ramdomNumber.longValue();
                break;
            }
        }
        return number;
    }

    /**
     * @param player
     * @return int
     * @Description: 计算卡路里
     * <AUTHOR>
     * @date 2018年3月21日 下午4:15:20
     */
    public static int calculateMoveCalorie(FootballTeamGameStatisticsPlayer player) {
        int calorie = 0;
        if (player != null) {
            String highCalorie = player.getHighMoveCalorie();
            String midCalorie = player.getMidMoveCalorie();
            String lowCalorie = player.getLowMoveCalorie();
            String normalCalorie = player.getNormalMoveCalorie();
            JSONArray highArray = null;
            JSONArray midArray = null;
            JSONArray lowArray = null;
            JSONArray normalArray = null;
            try {
                if (highCalorie != null && !"".equals(highCalorie)) {
                    highArray = JSONArray.parseArray(highCalorie);
                }
                if (midCalorie != null && !"".equals(midCalorie)) {
                    midArray = JSONArray.parseArray(midCalorie);
                }
                if (lowCalorie != null && !"".equals(midCalorie)) {
                    lowArray = JSONArray.parseArray(lowCalorie);
                }
                if (normalCalorie != null && !"".equals(normalCalorie)) {
                    normalArray = JSONArray.parseArray(normalCalorie);
                }
                List<JSONArray> list = new ArrayList<>();
                if (highArray != null) {
                    list.add(highArray);
                }
                if (midArray != null) {
                    list.add(midArray);
                }
                if (lowArray != null) {
                    list.add(midArray);
                }
                if (normalArray != null) {
                    list.add(normalArray);
                }
                for (JSONArray tempJArray : list) {
                    for (int j = 0; j < tempJArray.size(); j++) {
                        JSONObject tempObject = tempJArray.getJSONObject(j);
                        calorie += tempObject.getInteger("moveCalarie");
                    }
                }
            } catch (JSONException e) {
                e.printStackTrace();
            }
        }
        return calorie;
    }

    /**
     * @param user
     * @param userId
     * @param index
     * @param index
     * @param userHeadimg
     * @param userGameDataArray
     * @param resultArray
     * @return JSONArray
     * @Description: 封装比赛数据
     * <AUTHOR>
     * @date 2018年3月21日 下午5:31:08
     */
    public static JSONArray pickGameData(User user, String userId, int index, UserHeadimg userHeadimg, JSONArray userGameDataArray, JSONArray resultArray) {
        JSONObject object = new JSONObject();
        try {

            object.put("userId", userId);
            if (user != null) {
                object.put("nickName", user.getNickName());
            } else {
                object.put("nickName", "");
            }
            if (userHeadimg != null) {
                object.put("userHeadImg", userHeadimg.getHeadImgNetUrl());
            } else {
                object.put("userHeadImg", "");
            }
            if (index == 0 || index == 2 || index == 3 || index == 4 || index == 6 || index == 7 || index == 8 || index == 9 || index == 10 || index == 11 || index == 12) {
                object.put("value", userGameDataArray.get(index));
            } else {
                double temp = (double) userGameDataArray.get(index) / 1000;
                BigDecimal b = new BigDecimal(temp);
                Double value = b.setScale(2, RoundingMode.HALF_UP).doubleValue();
                object.put("value", value);
            }
            if (!userGameDataArray.get(index).equals(0) && !userGameDataArray.get(index).equals((double) 0)) {
                resultArray.add(object);
            }
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return resultArray;
    }

    public static List<User> packUserList(List<FootballTeamGameStatisticsPlayer> playerList, List<FootballTeamTakeNode> nodeList) {
        List<User> userList = new ArrayList<>();
        Map<Long, User> tempMap = new HashMap<>();
        for (FootballTeamGameStatisticsPlayer teamGameStatisticsPlayer : playerList) {
            User tempUser = teamGameStatisticsPlayer.getUser();
            if (tempUser != null) {
                tempMap.put(tempUser.getId(), tempUser);
            }
        }

        for (FootballTeamTakeNode node : nodeList) {
            User tempUser = node.getUser();
            if (tempUser != null) {
                tempMap.put(tempUser.getId(), tempUser);
            }
        }

        if (tempMap.size() > 0) {
            userList.addAll(tempMap.values());
        }
        return userList;
    }

    public static Map<String, Object> avgDataCompare(Map<String, Object> map, List<FootballTeamGameStatisticsPlayerDto> allPlayerList,
                                                     int avgMove, int passBallSum, int carryDistance, double topSpeed, int passBallSite, Long gameId) throws Exception {
        map = compare(allPlayerList, "wholeMoveDistance", ((Integer) avgMove).doubleValue(), map, "avgMove", gameId);
        map = compare(allPlayerList, "passBallCounts", ((Integer) passBallSum).doubleValue(), map, "passBall", gameId);
        map = compare(allPlayerList, "carryDistance", ((Integer) carryDistance).doubleValue(), map, "carryDistance", gameId);
        map = compare(allPlayerList, "maxSprintSpeed", topSpeed, map, "topSpeed", gameId);
        map = compare(allPlayerList, "passBallSite", ((Integer) passBallSite).doubleValue(), map, "passBallSite", gameId);
        return map;
    }


    private static Map<String, Object> compare(List<FootballTeamGameStatisticsPlayerDto> allPlayerList, String propertyName, double data, Map<String, Object> map, String key, Long gameId) throws Exception {
        String getName = "get" + TranslatorUtil.getGetter(propertyName);
        double sum = 0;
        double avg = 0;
        int compare;

        if (allPlayerList != null && allPlayerList.size() > 0) {
            switch (propertyName) {
                case "maxSprintSpeed":
                    for (FootballTeamGameStatisticsPlayerDto player : allPlayerList) {
                    /*if(gameId.equals(player.getFootballTeamGame().getId())){
                        data = player.getMaxSprintSpeed();
                    }*/
                        sum += player.getMaxSprintSpeed();
                        avg = sum / allPlayerList.size();
                    }
                    break;
                case "passBallSite":
                    Integer passSum = 0;
                    Integer passError = 0;

                    Integer historyPassSum = 0;
                    Integer historyPassErrorSum = 0;

                    for (FootballTeamGameStatisticsPlayerDto player : allPlayerList) {

                        if (gameId.equals(player.getGameId())) {
                            passSum += player.getPassBallCounts().intValue();
                            passError += player.getPassBallError().intValue();
                        }

                        historyPassSum += player.getPassBallCounts().intValue();
                        historyPassErrorSum += player.getPassBallError().intValue();
                    }
                    if (passSum + passError > 0) {
                        double rate = (passSum.doubleValue()) / (passSum.doubleValue() + passError.doubleValue());
                        rate *= 100.0;
                        data = (Math.round(rate * 10.0) / 10.0);
                    }

                    if (historyPassSum + historyPassErrorSum > 0) {
                        double historyRate = (historyPassSum.doubleValue()) / (historyPassSum.doubleValue() + historyPassErrorSum.doubleValue());
                        historyRate *= 100.0;
                        avg = historyRate;
                    }
                    break;
                default:
                    Method getter = allPlayerList.get(0).getClass().getMethod(getName);
                    for (FootballTeamGameStatisticsPlayerDto player : allPlayerList) {
                        if (getter.invoke(player) instanceof Long) {
                            sum += ((Long) getter.invoke(player)).intValue();
                        } else if (getter.invoke(player) instanceof Double) {
                            sum += Math.round(((Double) getter.invoke(player)));
                        } else {
                            sum += (int) getter.invoke(player);
                        }
                    }
                    avg = sum / allPlayerList.size();
                    break;
            }
        }
        if (data > avg) {
            compare = 2;    //上升
        } else if (data < avg) {
            compare = 0;    //下降
        } else {
            compare = 1;    //相等
        }
        Map<String, Object> tempMap = new HashMap<>();
        tempMap.put("data", (Math.round(data * 10.0) / 10.0)); //保留一位小数
        tempMap.put("compare", compare);
        tempMap.put("provinceCode", "440000");
        tempMap.put("cityCode", "440300");
        tempMap.put("countyCode", "440305");
        tempMap.put("rank", 1);
        map.put(key, tempMap);
        return map;
    }

    public static double getInstanceof(Method getter, FootballTeamGameStatisticsPlayer player) {
        double data = 0;
        try {
//            if (getter.invoke(player) instanceof Long) {
//                data = ((Long) getter.invoke(player)).intValue();
//            } else if (getter.invoke(player) instanceof Integer) {
//                data = (Integer) getter.invoke(player);
//            }
            String value = String.valueOf(getter.invoke(player));
            data = Double.valueOf(value);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return data;
    }

    public static JSONArray dealGoalAndPoint(JSONArray whenArray, JSONArray whenArray2) throws JSONException {
        //根据jsonArray的长度确定哪个是进球的时间数组
        JSONArray goalArray;
        JSONArray pointArray = new JSONArray();
        if (whenArray.size() > whenArray2.size()) {
            goalArray = whenArray;
            pointArray = whenArray2;
        } else if (whenArray.size() < whenArray2.size()) {
            goalArray = whenArray2;
            pointArray = whenArray;
        } else {
            goalArray = whenArray;
            for (int i = 0; i < goalArray.size(); i++) {
                JSONObject o = goalArray.getJSONObject(i);
                if (o.getInteger("isPenalty") == 0) {
                    /*o.remove("isPenalty");*/
                    o.put("isPenalty", 1);
                }
            }
        }
        if (whenArray.size() != whenArray2.size()) {
            for (int i = 0; i < goalArray.size(); i++) {
                if (i < pointArray.size()) {
                    goalArray.set(goalArray.size() - i - 1, pointArray.getJSONObject(i));
                }
            }
        }
        DataUtil.sortArrayByLongValue(goalArray, "timeTable");
        return goalArray;
    }


    //合并同场比赛曲线
    public static CurvePojo mergeCurvePojo(CurvePojo pojo1, CurvePojo pojo2) {
        List<Integer> yo1 = pojo1.getAxisY();
        List<Integer> yo2 = pojo2.getAxisY();

        for (int i = 0; i < yo1.size(); i++) {
            yo1.set(i, yo1.get(i) + yo2.get(i));
            if (yo1.get(i) > pojo1.getMaxY()) {
                pojo1.setMaxY(yo1.get(i));
            }
        }
        /*pojo1.setMaxY(FootballTeamGameUtils.getWholeTen(pojo1.getMaxY()));*/  //Y轴最大值向上取整十数
        return pojo1;
    }

    public static List<KickStatePojo> packKickState(List<UserHardwareData> dataList) throws JSONException {
        Random random = new Random();
        List<KickStatePojo> statePojoList = new ArrayList<>();
        if (dataList != null && dataList.size() > 0) {
            for (UserHardwareData data : dataList) {
                if ((data.getKickBallStartTime() != null && data.getKickBallData() != null && !data.getKickBallData().equals("")) && data.getIsStartUp() == 3 &&
                        (data.getKickBallState() == null || data.getKickBallState().equals(""))) {
                    JSONArray array = JSONArray.parseArray(data.getKickBallData());
                    for (int i = 0; i < array.size(); i++) {
                        KickStatePojo pojo = new KickStatePojo();
                        int state = random.nextInt(4);
                        pojo.setState(state + 1);
                        pojo.setSec(array.getInteger(i));
                        pojo.setKickTime(data.getKickBallStartTime().getTime() + pojo.getSec() * 1000);
                        statePojoList.add(pojo);
                    }
                }
            }
        }
        return statePojoList;
    }

    //pojo1和pojo2可能是不同场比赛
    //setMaxY
    public static CurvePojo mergeDiffGameCurvePojo(CurvePojo pojo1, CurvePojo pojo2) {
        List<Integer> y1 = pojo1.getAxisY();
        List<Integer> y2 = pojo2.getAxisY();
        List<Long> x1 = pojo1.getAxisXLong();
        List<Long> x2 = pojo2.getAxisXLong();
        List<Integer> forgetY = new ArrayList<>();
        for (int i = 0; i < x1.size(); i++) {
            for (int j = 0; j < x2.size(); j++) {
                if (!forgetY.contains(j)) {
                    if (x1.get(i) >= x2.get(j)) {
                        y1.set(i, y1.get(i) + y2.get(j));
                        forgetY.add(j);
                    }
                }
            }
        }
        pojo1.setAxisY(y1);
        Integer maxY_new = 0;
        for (Integer aY1 : y1) {
            if (aY1 > maxY_new) {
                maxY_new = aY1;
            }
        }
        pojo1.setMaxY(maxY_new);
        return pojo1;
    }

    // 每个点累加
    public static CurvePojo addUp(CurvePojo pojo) {
        List<Integer> listY = pojo.getAxisY();
        for (int i = 1; i < listY.size(); i++) {
            listY.set(i, listY.get(i) + listY.get(i - 1));
        }
        pojo.setAxisY(listY);
        pojo.setMaxY(pojo.getAxisY().get(pojo.getAxisY().size() - 1));
        return pojo;
    }

    public static Integer getWholeTen(Integer in) {
        Integer wholeTenY;
        if ((in % 10) > 0) {
            wholeTenY = in + (10 - (in % 10));
        } else {
            wholeTenY = in;
        }
        return wholeTenY;
    }

    public static CurvePojo avgAxisY(CurvePojo pojo, int size) {
        if (size > 0) {
            List<Integer> listY = pojo.getAxisY();
            for (int i = 0; i < listY.size(); i++) {
                listY.set(i, new BigDecimal((listY.get(i) / size)).setScale(0, BigDecimal.ROUND_FLOOR).intValue());
            }
            pojo.setAxisY(listY);
            pojo.setMaxY(pojo.getAxisY().get(pojo.getAxisY().size() - 1));
        }
        return pojo;
    }

    public static JSONObject packCurve(CurvePojo pojo) throws JSONException {
        JSONObject curveObject = new JSONObject();
        if (pojo != null) {
            curveObject.put("axisX", pojo.getAxisX());
            curveObject.put("axisY", pojo.getAxisY());
            curveObject.put("maxX", pojo.getMaxX());
            curveObject.put("maxY", pojo.getMaxY());
            curveObject.put("realMin", pojo.getRealMin());
        }
        return curveObject;
    }

    public static Integer getStepCount(FootballTeamGameStatisticsPlayer player) throws JSONException {
        int stepCount = 0;
        if (player != null) {
            stepCount += getStepByData(player.getHighMoveCalorie());
            stepCount += getStepByData(player.getMidMoveCalorie());
            stepCount += getStepByData(player.getLowMoveCalorie());
            stepCount += getStepByData(player.getNormalMoveCalorie());
        } else {
            stepCount = -1;
        }
        return stepCount;
    }

    private static int getStepByData(String data) throws JSONException {
        int tempStep = 0;
        if (data != null && !"".equals(data)) {
            JSONArray array = JSONArray.parseArray(data);
            for (int i = 0; i < array.size(); i++) {
                if (array.getJSONObject(i).containsKey("stepCount")) {
                    tempStep += array.getJSONObject(i).getInteger("stepCount");
                }
            }
        }
        return tempStep;
    }

    public static String sql(Class clz) {
        StringBuilder sql = new StringBuilder("insert into football_team_game_passmap(");
        //反射获取fields
        Field[] fields = clz.getDeclaredFields();

        for (Field field : fields) {
            sql.append(field.getName());
            sql.append(",");
        }
        sql.deleteCharAt(sql.length() - 1);
        sql.append(")");
        sql.append(" values(");
        for (int i = 0, length = fields.length; i < length; i++) {
            sql.append("?,");
        }
        sql.deleteCharAt(sql.length() - 1);
        sql.append(")");
        sql.append(" on DUPLICATE key update count=?,updatetime = now()");
        return sql.toString();
    }

    public static void prepareParams(PreparedStatement pst, Object obj, Class clz) throws Exception {
        //反射获取所有字段Fields
        Field[] fields = clz.getDeclaredFields();
        Method method;
        Field field;
        Object tmpObj;
        for (int i = 0; i < fields.length + 1; i++) {
            if (i == fields.length) {
                field = fields[5];
            } else {
                field = fields[i];
            }
            //根据字段名得到getter方法
            System.out.println("--------------" + field.getName());
            PropertyDescriptor pd;
            if (field.getName().equals("competition")) {
                pd = new PropertyDescriptor("Match", clz);
            } else {
                pd = new PropertyDescriptor(field.getName(), clz);
            }

            method = pd.getReadMethod();
            //调用获取的getter方法，得到对应的值
            tmpObj = method.invoke(obj);
            //match和user特殊处理
            if ("footballTeam".equals(field.getName())) {
                pst.setObject(i + 1, ((FootballTeam) tmpObj).getId());
            } else if ("user".equals(field.getName())) {
                pst.setObject(i + 1, ((User) tmpObj).getId());
            } else {
                pst.setObject(i + 1, tmpObj);
            }
        }
    }

    public static List<FootballTeamGamePassMap> getPassList(FootballTeamGame game, FootballTeam team, JSONArray touchArray) throws JSONException {
        Map<String, Integer> map = new HashMap<>();
        List<FootballTeamGamePassMap> list = new ArrayList<>();
        if (touchArray != null && touchArray.size() > 0) {
            for (int i = 0; i < touchArray.size() - 1; i++) {
                JSONObject object = touchArray.getJSONObject(i);
                long userId = object.getLong("userId");
                long userId_next = touchArray.getJSONObject(i + 1).getLong("userId");
                long touchTime_next = touchArray.getJSONObject(i + 1).getLong("touchTime");
                long interval = Math.abs(object.getLong("touchTime") - touchTime_next);

                if (userId != userId_next && interval <= FootballContants.PASSBALLCUTOFF) {
                    String key = userId + "_" + userId_next;
                    if (!map.containsKey(userId + "_" + userId_next)) {
                        map.put(key, 1);
                    } else {
                        Integer count = map.get(userId + "_" + userId_next) + 1;
                        map.put(key, count);
                    }
                }
            }
        }
        for (Map.Entry<String, Integer> entry : map.entrySet()) {
            FootballTeamGamePassMap passMap = new FootballTeamGamePassMap();
            String[] idArray = entry.getKey().split("_");
            Long passUserId = Long.parseLong(idArray[0]);
            Long byPassUserId = Long.parseLong(idArray[1]);

            passMap.setGameId(game.getId());
            passMap.setTeamId(team.getId());
            passMap.setPassUserId(passUserId);
            passMap.setByPassUserId(byPassUserId);
            passMap.setCount(entry.getValue());
            passMap.setCreateTime(new Date());
            passMap.setDeleted(false);
            list.add(passMap);
        }
        return list;
    }
}
