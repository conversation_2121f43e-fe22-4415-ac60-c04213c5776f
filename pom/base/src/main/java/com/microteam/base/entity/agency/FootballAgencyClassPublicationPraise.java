package com.microteam.base.entity.agency;


import com.microteam.base.common.base.BaseEntity;
import com.microteam.base.entity.user.User;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;
import java.io.Serializable;

@Entity
@Table(name = "football_agency_class_publication_praise")
@Getter
@Setter
public class FootballAgencyClassPublicationPraise extends BaseEntity implements Serializable {

    @ManyToOne
    @JoinColumn(name = "publicationId", nullable = false)
    private FootballAgencyClassPublication footballAgencyClassPublication;
    @ManyToOne
    @JoinColumn(name = "userId", nullable = false)
    private User user;
    @Column(name = "isPraised")
    private Boolean isPraised;

}
