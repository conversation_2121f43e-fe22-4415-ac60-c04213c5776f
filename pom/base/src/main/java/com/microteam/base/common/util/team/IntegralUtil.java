package com.microteam.base.common.util.team;

import com.microteam.base.entity.integral.FootballIntegralHistory;
import com.microteam.base.entity.integral.FootballIntegralTeam;
import com.microteam.base.entity.integral.FootballIntegralType;
import com.microteam.base.entity.team.FootballTeam;
import com.microteam.base.integral.dao.FootballIntegralTeamDao;
import com.microteam.base.integral.dao.FootballIntegralTypeDao;
import com.microteam.base.integral.service.FootballIntegralHistoryDaoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class IntegralUtil {
    @Autowired
    FootballIntegralHistoryDaoService footballIntegralHistoryDaoService;
    @Autowired
    FootballIntegralTeamDao footballIntegralTeamDao;
    @Autowired
    FootballIntegralTypeDao footballIntegralTypeDao;

    public int findRangeByTeamId(Long teamId, List<Long> teamIdList, FootballIntegralTeam integralTeam) {
        List<FootballIntegralHistory> integralHistoryList = footballIntegralHistoryDaoService.findNewByTeamId(teamId, integralTeam.getLastAdd());
        addNewIntegral(integralHistoryList, integralTeam);
        if (teamIdList != null && teamIdList.size() > 0) {
            return footballIntegralTeamDao.findRangeByTeamId(teamId, teamIdList);
        }
        return 1;
    }

    public int addNewIntegral(List<FootballIntegralHistory> integralHistoryList, FootballIntegralTeam integralTeam) {

        int integral = integralTeam.getIntegral();
        if (integralHistoryList != null && !integralHistoryList.isEmpty()) {
            Long lastAdd = integralTeam.getLastAdd();
            for (FootballIntegralHistory integralHistory : integralHistoryList) {
                Long integralTypeId = integralHistory.getTypeId();
                FootballIntegralType integralType = footballIntegralTypeDao.findById(integralTypeId).orElse(new FootballIntegralType());
                integral += integralType.getIntegral();
                lastAdd = integralHistory.getId();
            }
            integralTeam.setLastAdd(lastAdd);
            integralTeam.setIntegral(integral);
            footballIntegralTeamDao.save(integralTeam);
        }
        return integral;
    }

    public static boolean isFilledInTeam(FootballTeam team) {
        //球队简称
        if (team.getTeamAbbreviation() == null) {
            return false;
        }
        //微队号
        if (team.getVsteamNumber() == null) {
            return false;
        }
        //所属区域
        if (team.getCountryCode() == null) {
            return false;
        }
        if (team.getProvinceCode() == null) {
            return false;
        }
        if (team.getCityCode() == null) {
            return false;
        }
        if (team.getCountyCode() == null) {
            return false;
        }
        //球队属性
        if (team.getFootballTeamNatureType() == null) {
            return false;
        }
        //主要活动场地
        if (team.getPlayGround() == null) {
            return false;
        }
        //联系人
//        if (team.getContact() == null) {
//            return false;
//        }
        //联系电话
//        if (team.getTelephone() == null) {
//            return false;
//        }
        //球队简介
//        if (team.getDescription() == null) {
//            return false;
//        }
        //球星介绍
//        if (team.getPlayStar() == null) {
//            return false;
//        }
        //实力自评
//        if (team.getSelfEvaluation() == null) {
//            return false;
//        }
        //个性签名

        //擅长赛制
        if (team.getFavorRules() == null) {
            return false;
        }
        //主队队服
        if (team.getHostColor() == null) {
            return false;
        }
        //客队队服
        return team.getGuestColor() != null;
    }
}
