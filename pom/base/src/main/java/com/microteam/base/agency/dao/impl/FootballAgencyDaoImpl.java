//package com.microteam.base.agency.dao.impl;
//
//import com.microteam.base.agency.dao.FootballAgencyDao;
//import com.microteam.base.entity.agency.FootballAgency;
//import com.microteam.base.common.base.AbstractHibernateDao;
//import com.microteam.base.entity.user.User;
//import org.apache.log4j.Logger;
//import org.hibernate.query.Query;
//import org.springframework.stereotype.Repository;
//
//import java.util.List;
//
//@Repository("footballAgencyDao")
//public class FootballAgencyDaoImpl extends
//        AbstractHibernateDao<FootballAgency> implements FootballAgencyDao {
//    static Logger logger = Logger.getLogger(FootballAgencyDaoImpl.class.getName());
//
//    public FootballAgencyDaoImpl() {
//        super();
//        setClazz(FootballAgency.class);
//    }
//
//    //根据Id查询机构
//    @SuppressWarnings("unchecked")
//    @Override
//    public FootballAgency findById(long id) {
//        String hql = "from FootballAgency as footballAgency " +
//                "left join fetch footballAgency.user as user " +
//                "left join fetch footballAgency.groups as groups " +
//                "where footballAgency.id=? " +
//                "and footballAgency.deleted=false ";
//        Query query = getCurrentSession().createQuery(hql).setParameter(0, id);
//        List<FootballAgency> list = query.list();
//        if (list != null && list.size() > 0) {
//            return list.get(0);
//        }
//        return null;
//    }
//
////    //查询培训机构列表
////    @Override
////    public List<FootballAgency> findByCodeAndSearchForPage(String cityCode, String countyCode, int page, int pageSize, String search) {
////        //模糊查询机构
////        String hql = "from FootballAgency as footballAgency left join fetch footballAgency.user as user left join fetch footballAgency.groups as groups ";
////        Query query = null;
////        if (!"".equals(search)) {
////            if ("1000000".equals(cityCode) || "".equals(cityCode)) {
////                //查询全国的
////                hql += " where footballAgency.agencyName like ?  and footballAgency.countryCode='1000000'   and footballAgency.deleted=false  order by footballAgency.credits desc";
////                query = getCurrentSession().createQuery(hql)
////                        .setParameter(0, "%" + search + "%");
////                query.setFirstResult((page - 1) * pageSize);
////                query.setMaxResults(pageSize);
////            } else {
////                //查询直辖市、特别行政区
////                if ("110000".equals(cityCode) ||
////                        "120000".equals(cityCode) ||
////                        "310000".equals(cityCode) ||
////                        "500000".equals(cityCode) ||
////                        "710000".equals(cityCode) ||
////                        "810000".equals(cityCode) ||
////                        "820000".equals(cityCode)) {
////                    hql += " where footballAgency.agencyName like ?  and footballAgency.provinceCode=?   and footballAgency.deleted=false  order by footballAgency.credits desc";
////                    query = getCurrentSession().createQuery(hql)
////                            .setParameter(0, "%" + search + "%")
////                            .setParameter(1, cityCode);
////                    query.setFirstResult((page - 1) * pageSize);
////                    query.setMaxResults(pageSize);
////                } else {
////                    //普通城市
////                    if ((countyCode != null) && (!countyCode.equals(""))) {
////                        hql += " where  footballAgency.agencyName like ? and footballAgency.cityCode=? and footballAgency.countyCode=?   and footballAgency.deleted=false  order by footballAgency.credits desc";
////                        query = getCurrentSession().createQuery(hql)
////                                .setParameter(0, "%" + search + "%")
////                                .setParameter(1, cityCode)
////                                .setParameter(2, countyCode);
////                        query.setFirstResult((page - 1) * pageSize);
////                        query.setMaxResults(pageSize);
////                    } else {
////                        hql += " where footballAgency.agencyName like ?  and footballAgency.cityCode=?   and footballAgency.deleted=false  order by footballAgency.credits desc";
////                        query = getCurrentSession().createQuery(hql)
////                                .setParameter(0, "%" + search + "%")
////                                .setParameter(1, cityCode);
////                        query.setFirstResult((page - 1) * pageSize);
////                        query.setMaxResults(pageSize);
////                    }
////                    //普通城市
////                }
////
////            }
////        } else {
////            if ("1000000".equals(cityCode) || "".equals(cityCode)) {
////                //查询全国的
////                hql += " where  footballAgency.countryCode='1000000'   and footballAgency.deleted=false  order by footballAgency.credits desc";
////                query = getCurrentSession().createQuery(hql);
////                query.setFirstResult((page - 1) * pageSize);
////                query.setMaxResults(pageSize);
////            } else {
////                //查询直辖市、特别行政区
////                if ("110000".equals(cityCode) ||
////                        "120000".equals(cityCode) ||
////                        "310000".equals(cityCode) ||
////                        "500000".equals(cityCode) ||
////                        "710000".equals(cityCode) ||
////                        "810000".equals(cityCode) ||
////                        "820000".equals(cityCode)) {
////                    hql += " where  footballAgency.provinceCode=?   and footballAgency.deleted=false  order by footballAgency.credits desc";
////                    query = getCurrentSession().createQuery(hql)
////                            .setParameter(0, cityCode);
////                    query.setFirstResult((page - 1) * pageSize);
////                    query.setMaxResults(pageSize);
////                } else {
////                    //普通城市
////                    if ((countyCode != null) && (!countyCode.equals(""))) {
////                        hql += " where footballAgency.cityCode=?  and  footballAgency.countyCode=?   and footballAgency.deleted=false  order by footballAgency.credits desc";
////                        query = getCurrentSession().createQuery(hql)
////                                .setParameter(0, cityCode)
////                                .setParameter(1, countyCode);
////                        query.setFirstResult((page - 1) * pageSize);
////                        query.setMaxResults(pageSize);
////                    } else {
////                        hql += " where  footballAgency.cityCode=?   and footballAgency.deleted=false  order by footballAgency.credits desc";
////                        query = getCurrentSession().createQuery(hql)
////                                .setParameter(0, cityCode);
////                        query.setFirstResult((page - 1) * pageSize);
////                        query.setMaxResults(pageSize);
////                    }
////                    //普通城市
////                }
////
////            }
////        }//else
////
////        List<FootballAgency> list = query.list();
////        return list;
////    }
//
////    @Override
////    public List<FootballAgency> findAgencyListByPageAndMy(String cityCode, String countyCode, int page, int pageSize, String search, User user) {
////
////        //模糊查询机构
////        String hql = "from FootballAgency as footballAgency left join fetch footballAgency.user as user left join fetch footballAgency.groups as groups ";
////        Query query = null;
////        if (!"".equals(search)) {
////            if ("1000000".equals(cityCode) || "".equals(cityCode)) {
////                //查询全国的
////                hql += " where footballAgency.agencyName like ? and footballAgency.user=? and footballAgency.countryCode='1000000'   and footballAgency.deleted=false  order by footballAgency.credits desc";
////                query = getCurrentSession().createQuery(hql)
////                        .setParameter(0, "%" + search + "%")
////                        .setParameter(1, user);
////                query.setFirstResult((page - 1) * pageSize);
////                query.setMaxResults(pageSize);
////            } else {
////                //查询直辖市、特别行政区
////                if ("110000".equals(cityCode) ||
////                        "120000".equals(cityCode) ||
////                        "310000".equals(cityCode) ||
////                        "500000".equals(cityCode) ||
////                        "710000".equals(cityCode) ||
////                        "810000".equals(cityCode) ||
////                        "820000".equals(cityCode)) {
////                    hql += " where footballAgency.agencyName like ?  and footballAgency.provinceCode=? and footballAgency.user=?  and footballAgency.deleted=false  order by footballAgency.credits desc";
////                    query = getCurrentSession().createQuery(hql)
////                            .setParameter(0, "%" + search + "%")
////                            .setParameter(1, cityCode)
////                            .setParameter(2, user);
////                    query.setFirstResult((page - 1) * pageSize);
////                    query.setMaxResults(pageSize);
////                } else {
////                    //普通城市
////                    if ((countyCode != null) && (!countyCode.equals(""))) {
////                        hql += " where  footballAgency.agencyName like ? and footballAgency.cityCode=? and footballAgency.countyCode=? and footballAgency.user=?  and footballAgency.deleted=false  order by footballAgency.credits desc";
////                        query = getCurrentSession().createQuery(hql)
////                                .setParameter(0, "%" + search + "%")
////                                .setParameter(1, cityCode)
////                                .setParameter(2, countyCode)
////                                .setParameter(3, user);
////                        query.setFirstResult((page - 1) * pageSize);
////                        query.setMaxResults(pageSize);
////                    } else {
////                        hql += " where footballAgency.agencyName like ?  and footballAgency.cityCode=? and footballAgency.user=?  and footballAgency.deleted=false  order by footballAgency.credits desc";
////                        query = getCurrentSession().createQuery(hql)
////                                .setParameter(0, "%" + search + "%")
////                                .setParameter(1, cityCode)
////                                .setParameter(2, user);
////                        query.setFirstResult((page - 1) * pageSize);
////                        query.setMaxResults(pageSize);
////                    }
////                    //普通城市
////                }
////
////            }
////        } else {
////            if ("1000000".equals(cityCode) || "".equals(cityCode)) {
////                //查询全国的
////                hql += " where  footballAgency.countryCode='1000000' and footballAgency.user=?   and footballAgency.deleted=false  order by footballAgency.credits desc";
////                query = getCurrentSession().createQuery(hql)
////                        .setParameter(0, user);
////                query.setFirstResult((page - 1) * pageSize);
////                query.setMaxResults(pageSize);
////            } else {
////                //查询直辖市、特别行政区
////                if ("110000".equals(cityCode) ||
////                        "120000".equals(cityCode) ||
////                        "310000".equals(cityCode) ||
////                        "500000".equals(cityCode) ||
////                        "710000".equals(cityCode) ||
////                        "810000".equals(cityCode) ||
////                        "820000".equals(cityCode)) {
////                    hql += " where  footballAgency.provinceCode=? and footballAgency.user=?   and footballAgency.deleted=false  order by footballAgency.credits desc";
////                    query = getCurrentSession().createQuery(hql)
////                            .setParameter(0, cityCode)
////                            .setParameter(1, user);
////                    query.setFirstResult((page - 1) * pageSize);
////                    query.setMaxResults(pageSize);
////                } else {
////                    //普通城市
////                    if ((countyCode != null) && (!countyCode.equals(""))) {
////                        hql += " where footballAgency.cityCode=?  and  footballAgency.countyCode=? and footballAgency.user=?  and footballAgency.deleted=false  order by footballAgency.credits desc";
////                        query = getCurrentSession().createQuery(hql)
////                                .setParameter(0, cityCode)
////                                .setParameter(1, countyCode)
////                                .setParameter(2, user);
////                        query.setFirstResult((page - 1) * pageSize);
////                        query.setMaxResults(pageSize);
////                    } else {
////                        hql += " where  footballAgency.cityCode=? and footballAgency.user=?   and footballAgency.deleted=false  order by footballAgency.credits desc";
////                        query = getCurrentSession().createQuery(hql)
////                                .setParameter(0, cityCode)
////                                .setParameter(1, user);
////                        query.setFirstResult((page - 1) * pageSize);
////                        query.setMaxResults(pageSize);
////                    }
////                    //普通城市
////                }
////
////            }
////        }//else
////
////        List<FootballAgency> list = query.list();
////        return list;
////    }
//
//    @SuppressWarnings("unchecked")
////    @Override
////    public List<FootballAgency> findAgencyListByPageAndNotMy(String cityCode,
////                                                             String countyCode, int page, int pageSize, String search, User user) {
////        //模糊查询机构
////        String hql = "from FootballAgency as footballAgency left join fetch footballAgency.user as user left join fetch footballAgency.groups as groups ";
////        Query query = null;
////        if (!"".equals(search)) {
////            if ("1000000".equals(cityCode) || "".equals(cityCode)) {
////                //查询全国的
////                hql += " where footballAgency.agencyName like ? and footballAgency.user<>? and footballAgency.countryCode='1000000'   and footballAgency.deleted=false  order by footballAgency.credits desc";
////                query = getCurrentSession().createQuery(hql)
////                        .setParameter(0, "%" + search + "%")
////                        .setParameter(1, user);
////                query.setFirstResult((page - 1) * pageSize);
////                query.setMaxResults(pageSize);
////            } else {
////                //查询直辖市、特别行政区
////                if ("110000".equals(cityCode) ||
////                        "120000".equals(cityCode) ||
////                        "310000".equals(cityCode) ||
////                        "500000".equals(cityCode) ||
////                        "710000".equals(cityCode) ||
////                        "810000".equals(cityCode) ||
////                        "820000".equals(cityCode)) {
////                    hql += " where footballAgency.agencyName like ?  and footballAgency.provinceCode=? and footballAgency.user<>?  and footballAgency.deleted=false  order by footballAgency.credits desc";
////                    query = getCurrentSession().createQuery(hql)
////                            .setParameter(0, "%" + search + "%")
////                            .setParameter(1, cityCode)
////                            .setParameter(2, user);
////                    query.setFirstResult((page - 1) * pageSize);
////                    query.setMaxResults(pageSize);
////                } else {
////                    //普通城市
////                    if ((countyCode != null) && (!countyCode.equals(""))) {
////                        hql += " where  footballAgency.agencyName like ? and footballAgency.cityCode=? and footballAgency.countyCode=? and footballAgency.user<>?  and footballAgency.deleted=false  order by footballAgency.credits desc";
////                        query = getCurrentSession().createQuery(hql)
////                                .setParameter(0, "%" + search + "%")
////                                .setParameter(1, cityCode)
////                                .setParameter(2, countyCode)
////                                .setParameter(3, user);
////                        query.setFirstResult((page - 1) * pageSize);
////                        query.setMaxResults(pageSize);
////                    } else {
////                        hql += " where footballAgency.agencyName like ?  and footballAgency.cityCode=? and footballAgency.user<>?  and footballAgency.deleted=false  order by footballAgency.credits desc";
////                        query = getCurrentSession().createQuery(hql)
////                                .setParameter(0, "%" + search + "%")
////                                .setParameter(1, cityCode)
////                                .setParameter(2, user);
////                        query.setFirstResult((page - 1) * pageSize);
////                        query.setMaxResults(pageSize);
////                    }
////                    //普通城市
////                }
////
////            }
////        } else {
////            if ("1000000".equals(cityCode) || "".equals(cityCode)) {
////                //查询全国的
////                hql += " where  footballAgency.countryCode='1000000' and footballAgency.user<>?   and footballAgency.deleted=false  order by footballAgency.credits desc";
////                query = getCurrentSession().createQuery(hql)
////                        .setParameter(0, user);
////                query.setFirstResult((page - 1) * pageSize);
////                query.setMaxResults(pageSize);
////            } else {
////                //查询直辖市、特别行政区
////                if ("110000".equals(cityCode) ||
////                        "120000".equals(cityCode) ||
////                        "310000".equals(cityCode) ||
////                        "500000".equals(cityCode) ||
////                        "710000".equals(cityCode) ||
////                        "810000".equals(cityCode) ||
////                        "820000".equals(cityCode)) {
////                    hql += " where  footballAgency.provinceCode=? and footballAgency.user<>?   and footballAgency.deleted=false  order by footballAgency.credits desc";
////                    query = getCurrentSession().createQuery(hql)
////                            .setParameter(0, cityCode)
////                            .setParameter(1, user);
////                    query.setFirstResult((page - 1) * pageSize);
////                    query.setMaxResults(pageSize);
////                } else {
////                    //普通城市
////                    if ((countyCode != null) && (!countyCode.equals(""))) {
////                        hql += " where footballAgency.cityCode=?  and  footballAgency.countyCode=? and footballAgency.user<>?  and footballAgency.deleted=false  order by footballAgency.credits desc";
////                        query = getCurrentSession().createQuery(hql)
////                                .setParameter(0, cityCode)
////                                .setParameter(1, countyCode)
////                                .setParameter(2, user);
////                        query.setFirstResult((page - 1) * pageSize);
////                        query.setMaxResults(pageSize);
////                    } else {
////                        hql += " where  footballAgency.cityCode=? and footballAgency.user<>?   and footballAgency.deleted=false  order by footballAgency.credits desc";
////                        query = getCurrentSession().createQuery(hql)
////                                .setParameter(0, cityCode)
////                                .setParameter(1, user);
////                        query.setFirstResult((page - 1) * pageSize);
////                        query.setMaxResults(pageSize);
////                    }
////                    //普通城市
////                }
////            }
////        }//else
////
////        List<FootballAgency> list = query.list();
////        return list;
////    }
//
////    @Override
////    public List<FootballAgency> findAgencyListByPageAndMyInfo(String cityCode, String countyCode, int page, int pageSize, String search, String auditStr, User user) {
////
////        int audit = Integer.parseInt(auditStr);
////        //模糊查询机构
////        String hql = "from FootballAgency as footballAgency left join fetch footballAgency.user as user left join fetch footballAgency.groups as groups ";
////        Query query = null;
////        if (!"".equals(search)) {
////            if ("1000000".equals(cityCode) || "".equals(cityCode)) {
////                //查询全国的
////                hql += " where footballAgency.agencyName like ? and footballAgency.user=? and footballAgency.audit=? and footballAgency.countryCode='1000000'   and footballAgency.deleted=false  order by footballAgency.credits desc";
////                query = getCurrentSession().createQuery(hql)
////                        .setParameter(0, "%" + search + "%")
////                        .setParameter(1, user).
////                                setParameter(2, audit);
////                query.setFirstResult((page - 1) * pageSize);
////                query.setMaxResults(pageSize);
////            } else {
////                //查询直辖市、特别行政区
////                if ("110000".equals(cityCode) ||
////                        "120000".equals(cityCode) ||
////                        "310000".equals(cityCode) ||
////                        "500000".equals(cityCode) ||
////                        "710000".equals(cityCode) ||
////                        "810000".equals(cityCode) ||
////                        "820000".equals(cityCode)) {
////                    hql += " where footballAgency.agencyName like ?  and footballAgency.provinceCode=? and footballAgency.user=?  and footballAgency.audit=? and footballAgency.deleted=false  order by footballAgency.credits desc";
////                    query = getCurrentSession().createQuery(hql)
////                            .setParameter(0, "%" + search + "%")
////                            .setParameter(1, cityCode)
////                            .setParameter(2, user)
////                            .setParameter(3, audit);
////                    query.setFirstResult((page - 1) * pageSize);
////                    query.setMaxResults(pageSize);
////                } else {
////                    //普通城市
////                    if ((countyCode != null) && (!countyCode.equals(""))) {
////                        hql += " where  footballAgency.agencyName like ? and footballAgency.cityCode=? and footballAgency.countyCode=? and footballAgency.user=?  and footballAgency.audit=?  and footballAgency.deleted=false  order by footballAgency.credits desc";
////                        query = getCurrentSession().createQuery(hql)
////                                .setParameter(0, "%" + search + "%")
////                                .setParameter(1, cityCode)
////                                .setParameter(2, countyCode)
////                                .setParameter(3, user)
////                                .setParameter(4, audit);
////                        query.setFirstResult((page - 1) * pageSize);
////                        query.setMaxResults(pageSize);
////                    } else {
////                        hql += " where footballAgency.agencyName like ?  and footballAgency.cityCode=? and footballAgency.user=? and footballAgency.audit=?   and footballAgency.deleted=false  order by footballAgency.credits desc";
////                        query = getCurrentSession().createQuery(hql)
////                                .setParameter(0, "%" + search + "%")
////                                .setParameter(1, cityCode)
////                                .setParameter(2, user)
////                                .setParameter(3, audit);
////                        query.setFirstResult((page - 1) * pageSize);
////                        query.setMaxResults(pageSize);
////                    }
////                    //普通城市
////                }
////            }
////        } else {
////            if ("1000000".equals(cityCode) || "".equals(cityCode)) {
////                //查询全国的
////                hql += " where  footballAgency.countryCode='1000000' and footballAgency.user=?  and footballAgency.audit=?   and footballAgency.deleted=false  order by footballAgency.credits desc";
////                query = getCurrentSession().createQuery(hql)
////                        .setParameter(0, user)
////                        .setParameter(1, audit);
////                query.setFirstResult((page - 1) * pageSize);
////                query.setMaxResults(pageSize);
////            } else {
////                //查询直辖市、特别行政区
////                if ("110000".equals(cityCode) ||
////                        "120000".equals(cityCode) ||
////                        "310000".equals(cityCode) ||
////                        "500000".equals(cityCode) ||
////                        "710000".equals(cityCode) ||
////                        "810000".equals(cityCode) ||
////                        "820000".equals(cityCode)) {
////                    hql += " where  footballAgency.provinceCode=? and footballAgency.user=?  and footballAgency.audit=?    and footballAgency.deleted=false  order by footballAgency.credits desc";
////                    query = getCurrentSession().createQuery(hql)
////                            .setParameter(0, cityCode)
////                            .setParameter(1, user)
////                            .setParameter(2, audit);
////                    query.setFirstResult((page - 1) * pageSize);
////                    query.setMaxResults(pageSize);
////                } else {
////                    //普通城市
////                    if ((countyCode != null) && (!countyCode.equals(""))) {
////                        hql += " where footballAgency.cityCode=?  and  footballAgency.countyCode=? and footballAgency.user=?  and footballAgency.audit=?  and footballAgency.deleted=false  order by footballAgency.credits desc";
////                        query = getCurrentSession().createQuery(hql)
////                                .setParameter(0, cityCode)
////                                .setParameter(1, countyCode)
////                                .setParameter(2, user)
////                                .setParameter(3, audit);
////                        query.setFirstResult((page - 1) * pageSize);
////                        query.setMaxResults(pageSize);
////                    } else {
////                        hql += " where  footballAgency.cityCode=? and footballAgency.user=? and footballAgency.audit=?     and footballAgency.deleted=false  order by footballAgency.credits desc";
////                        query = getCurrentSession().createQuery(hql)
////                                .setParameter(0, cityCode)
////                                .setParameter(1, user)
////                                .setParameter(2, audit);
////                        query.setFirstResult((page - 1) * pageSize);
////                        query.setMaxResults(pageSize);
////                    }
////                    //普通城市
////                }
////            }
////        }//else
////        List<FootballAgency> list = query.list();
////        return list;
////    }
//
////    @Override
////    public List<FootballAgency> findAgencyListByPageAndMyInfoAndType(String cityCode, String countyCode, int page, int pageSize, String search, String auditStr, User user, Integer type) {
////        int audit = Integer.parseInt(auditStr);
////        //模糊查询机构
////        String hql = "from FootballAgency as footballAgency left join fetch footballAgency.user as user left join fetch footballAgency.groups as groups ";
////        Query query = null;
////        if (!"".equals(search)) {
////            if ("1000000".equals(cityCode) || "".equals(cityCode)) {
////                //查询全国的
////                hql += " where footballAgency.agencyName like ? and footballAgency.user=? and footballAgency.audit=? and footballAgency.countryCode='1000000'   and footballAgency.deleted=false and footballAgency.type = (:type)  order by footballAgency.credits desc";
////                query = getCurrentSession().createQuery(hql)
////                        .setParameter(0, "%" + search + "%")
////                        .setParameter(1, user)
////                        .setParameter(2, audit)
////                        .setParameter("type", type)
////                        .setFirstResult((page - 1) * pageSize)
////                        .setMaxResults(pageSize);
////            } else {
////                //查询直辖市、特别行政区
////                if ("110000".equals(cityCode) ||
////                        "120000".equals(cityCode) ||
////                        "310000".equals(cityCode) ||
////                        "500000".equals(cityCode) ||
////                        "710000".equals(cityCode) ||
////                        "810000".equals(cityCode) ||
////                        "820000".equals(cityCode)) {
////                    hql += " where footballAgency.agencyName like ?  and footballAgency.provinceCode=? and footballAgency.user=?  and footballAgency.audit=? and footballAgency.deleted=false and footballAgency.type = (:type) order by footballAgency.credits desc";
////                    query = getCurrentSession().createQuery(hql)
////                            .setParameter(0, "%" + search + "%")
////                            .setParameter(1, cityCode)
////                            .setParameter(2, user)
////                            .setParameter(3, audit)
////                            .setParameter("type", type);
////                    query.setFirstResult((page - 1) * pageSize);
////                    query.setMaxResults(pageSize);
////                } else {
////                    //普通城市
////                    if ((countyCode != null) && (!countyCode.equals(""))) {
////                        hql += " where  footballAgency.agencyName like ? and footballAgency.cityCode=? and footballAgency.countyCode=? and footballAgency.user=?  and footballAgency.audit=?  and footballAgency.deleted=false and footballAgency.type = (:type)  order by footballAgency.credits desc";
////                        query = getCurrentSession().createQuery(hql)
////                                .setParameter(0, "%" + search + "%")
////                                .setParameter(1, cityCode)
////                                .setParameter(2, countyCode)
////                                .setParameter(3, user)
////                                .setParameter(4, audit)
////                                .setParameter("type", type);
////                        query.setFirstResult((page - 1) * pageSize);
////                        query.setMaxResults(pageSize);
////                    } else {
////                        hql += " where footballAgency.agencyName like ?  and footballAgency.cityCode=? and footballAgency.user=? and footballAgency.audit=?   and footballAgency.deleted=false and footballAgency.type = (:type)  order by footballAgency.credits desc";
////                        query = getCurrentSession().createQuery(hql)
////                                .setParameter(0, "%" + search + "%")
////                                .setParameter(1, cityCode)
////                                .setParameter(2, user)
////                                .setParameter(3, audit)
////                                .setParameter("type", type);
////                        query.setFirstResult((page - 1) * pageSize);
////                        query.setMaxResults(pageSize);
////                    }
////                    //普通城市
////                }
////            }
////        } else {
////            if ("1000000".equals(cityCode) || "".equals(cityCode)) {
////                //查询全国的
////                hql += " where  footballAgency.countryCode='1000000' and footballAgency.user=?  and footballAgency.audit=?   and footballAgency.deleted=false and footballAgency.type = (:type) order by footballAgency.credits desc";
////                query = getCurrentSession().createQuery(hql)
////                        .setParameter(0, user)
////                        .setParameter(1, audit)
////                        .setParameter("type", type);
////                query.setFirstResult((page - 1) * pageSize);
////                query.setMaxResults(pageSize);
////            } else {
////                //查询直辖市、特别行政区
////                if ("110000".equals(cityCode) ||
////                        "120000".equals(cityCode) ||
////                        "310000".equals(cityCode) ||
////                        "500000".equals(cityCode) ||
////                        "710000".equals(cityCode) ||
////                        "810000".equals(cityCode) ||
////                        "820000".equals(cityCode)) {
////                    hql += " where  footballAgency.provinceCode=? and footballAgency.user=?  and footballAgency.audit=?    and footballAgency.deleted=false and footballAgency.type = (:type)  order by footballAgency.credits desc";
////                    query = getCurrentSession().createQuery(hql)
////                            .setParameter(0, cityCode)
////                            .setParameter(1, user)
////                            .setParameter(2, audit)
////                            .setParameter("type", type);
////                    query.setFirstResult((page - 1) * pageSize);
////                    query.setMaxResults(pageSize);
////                } else {
////                    //普通城市
////                    if ((countyCode != null) && (!countyCode.equals(""))) {
////                        hql += " where footballAgency.cityCode=?  and  footballAgency.countyCode=? and footballAgency.user=?  and footballAgency.audit=?  and footballAgency.deleted=false and footballAgency.type = (:type)  order by footballAgency.credits desc";
////                        query = getCurrentSession().createQuery(hql)
////                                .setParameter(0, cityCode)
////                                .setParameter(1, countyCode)
////                                .setParameter(2, user)
////                                .setParameter(3, audit)
////                                .setParameter("type", type);
////                        query.setFirstResult((page - 1) * pageSize);
////                        query.setMaxResults(pageSize);
////                    } else {
////                        hql += " where  footballAgency.cityCode=? and footballAgency.user=? and footballAgency.audit=?     and footballAgency.deleted=false and footballAgency.type = (:type)  order by footballAgency.credits desc";
////                        query = getCurrentSession().createQuery(hql)
////                                .setParameter(0, cityCode)
////                                .setParameter(1, user)
////                                .setParameter(2, audit)
////                                .setParameter("type", type);
////                        query.setFirstResult((page - 1) * pageSize);
////                        query.setMaxResults(pageSize);
////                    }
////                    //普通城市
////                }
////            }
////        }//else
////        List<FootballAgency> list = query.list();
////        return list;
////    }
//
//    @Override
//    public List<FootballAgency> findYouthAgencyListByIdList(List<Long> idList) {
//        String hql = "from FootballAgency as footballAgency left join fetch footballAgency.user as user left join fetch footballAgency.groups as groups where footballAgency.id in (:idList) and footballAgency.deleted=false ";
//        Query query = getCurrentSession().createQuery(hql).setParameterList("idList", idList);
//        List<FootballAgency> list = query.list();
//        return list;
//    }
//
////    @Override
////    public List<FootballAgency> findAgencyListAboutMeOrNot(Long userId,int page, int pageSize,String cityCode, String countyCode,String search, Integer audit,Integer type,boolean isAboutMe) {
////        String sql;
////        String sqlend;
////        if (!isAboutMe) {
////            sql = "SELECT DISTINCT fa.* FROM football_agency AS fa " +
////                    "LEFT JOIN `user` AS u ON fa.creatorId = u.id " +
////                    "LEFT JOIN mt_groups AS mt ON fa.groupsId = mt.id";
////            sqlend = " AND NOT EXISTS (SELECT au.agencyId FROM agency_user AS au WHERE au.userId = :userId AND au.deleted = 0 AND au.agencyId = fa.id) ORDER BY fa.createTime DESC";
////        } else {
////            sql = "SELECT DISTINCT fa.* FROM football_agency AS fa " +
////                    "LEFT JOIN agency_user AS au ON fa.id = au.agencyId " +
////                    "LEFT JOIN `user` AS u ON fa.creatorId = u.id " +
////                    "LEFT JOIN mt_groups AS mt ON fa.groupsId = mt.id";
////            sqlend = " and au.userId = :userId AND au.roleId IN ( 1, 2, 5, 6 ) AND au.deleted = 0 ORDER BY CASE au.roleId WHEN 1 THEN 0 WHEN 5 THEN 1 WHEN 6 THEN 2 WHEN 2 THEN 3 ELSE 4 END,fa.createTime DESC";
////        }
////        Query query = null;
////        //模糊查询机构
////        if (!"".equals(search)) {
////            if ("1000000".equals(cityCode) || "".equals(cityCode)) {
////                //查询全国的
////                if (type.equals(3)) {
////                    sql += " where fa.agencyName like :search and fa.audit=:audit and fa.countryCode='1000000' " + sqlend;
////                    query = getCurrentSession().createNativeQuery(sql).addEntity(FootballAgency.class)
////                            .setParameter("userId",userId)
////                            .setParameter("search", "%" + search + "%")
////                            .setParameter("audit", audit);
////                } else {
////                    sql += " where fa.agencyName like :search and fa.audit=:audit and fa.countryCode='1000000' and fa.type = :type " + sqlend;
////                    query = getCurrentSession().createNativeQuery(sql).addEntity(FootballAgency.class)
////                            .setParameter("userId",userId)
////                            .setParameter("search", "%" + search + "%")
////                            .setParameter("audit", audit)
////                            .setParameter("type", type);
////                }
////            } else {
////                //查询直辖市、特别行政区
////                if ("110000".equals(cityCode) || "120000".equals(cityCode) || "310000".equals(cityCode) || "500000".equals(cityCode) || "710000".equals(cityCode) || "810000".equals(cityCode) || "820000".equals(cityCode)) {
////                    if (type.equals(3)) {
////                        sql += " where fa.agencyName like :search  and fa.provinceCode=:provinceCode and fa.audit=:audit " + sqlend;
////                        query = getCurrentSession().createNativeQuery(sql).addEntity(FootballAgency.class)
////                                .setParameter("userId",userId)
////                                .setParameter("search", "%" + search + "%")
////                                .setParameter("provinceCode", cityCode)
////                                .setParameter("audit", audit);
////                    } else {
////                        sql += " where fa.agencyName like :search  and fa.provinceCode=:provinceCode and fa.audit=:audit and fa.type = :type" + sqlend;
////                        query = getCurrentSession().createNativeQuery(sql).addEntity(FootballAgency.class)
////                                .setParameter("userId",userId)
////                                .setParameter("search", "%" + search + "%")
////                                .setParameter("provinceCode", cityCode)
////                                .setParameter("audit", audit)
////                                .setParameter("type", type);
////                    }
////                } else {
////                    //普通城市
////                    if ((countyCode != null) && (!countyCode.equals(""))) {
////                        if (type.equals(3)) {
////                            sql += " where  fa.agencyName like :search and fa.cityCode=:cityCode and fa.countyCode=:countyCode and fa.audit=:audit" + sqlend;
////                            query = getCurrentSession().createNativeQuery(sql).addEntity(FootballAgency.class)
////                                    .setParameter("userId",userId)
////                                    .setParameter("search", "%" + search + "%")
////                                    .setParameter("cityCode", cityCode)
////                                    .setParameter("countyCode", countyCode)
////                                    .setParameter("audit", audit);
////                        } else {
////                            sql += " where  fa.agencyName like :search and fa.cityCode=:cityCode and fa.countyCode=:countyCode and fa.audit=:audit and fa.type = :type" + sqlend;
////                            query = getCurrentSession().createNativeQuery(sql).addEntity(FootballAgency.class)
////                                    .setParameter("userId",userId)
////                                    .setParameter("search", "%" + search + "%")
////                                    .setParameter("cityCode", cityCode)
////                                    .setParameter("countyCode", countyCode)
////                                    .setParameter("audit", audit)
////                                    .setParameter("type", type);
////                        }
////
////                    } else {
////                        if (type.equals(3)) {
////                            sql += " where fa.agencyName like :search and fa.cityCode=:cityCode and fa.audit=:audit" + sqlend;
////                            query = getCurrentSession().createNativeQuery(sql).addEntity(FootballAgency.class)
////                                    .setParameter("userId",userId)
////                                    .setParameter("search", "%" + search + "%")
////                                    .setParameter("cityCode", cityCode)
////                                    .setParameter("audit", audit);
////                        } else {
////                            sql += " where fa.agencyName like :search  and fa.cityCode=:cityCode and fa.audit=:audit and fa.type = :type" + sqlend;
////                            query = getCurrentSession().createNativeQuery(sql).addEntity(FootballAgency.class)
////                                    .setParameter("userId",userId)
////                                    .setParameter(search, "%" + search + "%")
////                                    .setParameter(cityCode, cityCode)
////                                    .setParameter("audit", audit)
////                                    .setParameter("type", type);
////                        }
////                    }
////                }
////            }
////        } else {
////            if ("1000000".equals(cityCode) || "".equals(cityCode)) {
////                //查询全国的
////                if (type.equals(3)) {
////                    sql += " where  fa.countryCode='1000000' and fa.audit=:audit" + sqlend;
////                    /* query = getCurrentSession().createNativeQuery(sql).addEntity("fa", FootballAgency.class).addJoin("u", "fa.user").addJoin("mt", "fa.groups")*/
////                    query = getCurrentSession().createNativeQuery(sql).addEntity(FootballAgency.class)
////                            .setParameter("userId", userId)
////                            .setParameter("audit", audit);
////                } else {
////                    sql += " where  fa.countryCode='1000000' and fa.audit=:audit and fa.type = :type" + sqlend;
////                    query = getCurrentSession().createNativeQuery(sql).addEntity(FootballAgency.class)
////                            .setParameter("userId", userId)
////                            .setParameter("audit", audit)
////                            .setParameter("type", type);
////                }
////            } else {
////                //查询直辖市、特别行政区
////                if ("110000".equals(cityCode) || "120000".equals(cityCode) || "310000".equals(cityCode) || "500000".equals(cityCode) || "710000".equals(cityCode) || "810000".equals(cityCode) || "820000".equals(cityCode)) {
////                    if (type.equals(3)) {
////                        sql += " where  fa.provinceCode=:provinceCode and fa.audit=:audit" + sqlend;
////                        query = getCurrentSession().createNativeQuery(sql).addEntity(FootballAgency.class)
////                                .setParameter("userId", userId)
////                                .setParameter("provinceCode", cityCode)
////                                .setParameter("audit", audit);
////                    } else {
////                        sql += " where  fa.provinceCode=:provinceCode and fa.audit=:audit and fa.type = :type" + sqlend;
////                        query = getCurrentSession().createNativeQuery(sql).addEntity(FootballAgency.class)
////                                .setParameter("userId", userId)
////                                .setParameter("provinceCode", cityCode)
////                                .setParameter("audit", audit)
////                                .setParameter("type", type);
////                    }
////
////                } else {
////                    //普通城市
////                    if ((countyCode != null) && (!countyCode.equals(""))) {
////                        if (type.equals(3)) {
////                            sql += " where fa.cityCode=:cityCode  and  fa.countyCode=:countyCode and fa.audit=:audit" + sqlend;
////                            query = getCurrentSession().createNativeQuery(sql).addEntity(FootballAgency.class)
////                                    .setParameter("userId", userId)
////                                    .setParameter("cityCode", cityCode)
////                                    .setParameter("countyCode", countyCode)
////                                    .setParameter("audit", audit);
////                        } else {
////                            sql += " where fa.cityCode=:cityCode and  fa.countyCode=:countyCode and fa.audit=:audit and fa.type = :type" + sqlend;
////                            query = getCurrentSession().createNativeQuery(sql).addEntity(FootballAgency.class)
////                                    .setParameter("userId", userId)
////                                    .setParameter("cityCode", cityCode)
////                                    .setParameter("countyCode", countyCode)
////                                    .setParameter("audit", audit)
////                                    .setParameter("type", type);
////                        }
////                    } else {
////                        if (type.equals(3)) {
////                            sql += " where  fa.cityCode=:cityCode and fa.audit=:audit" + sqlend;
////                            query = getCurrentSession().createNativeQuery(sql).addEntity(FootballAgency.class)
////                                    .setParameter("userId", userId)
////                                    .setParameter("cityCode", cityCode)
////                                    .setParameter("audit", audit);
////                        } else {
////                            sql += " where  fa.cityCode=:cityCode and fa.audit=:audit and fa.type = :type" + sqlend;
////                            query = getCurrentSession().createNativeQuery(sql).addEntity(FootballAgency.class)
////                                    .setParameter("userId", userId)
////                                    .setParameter("cityCode", cityCode)
////                                    .setParameter("audit", audit)
////                                    .setParameter("type", type);
////                        }
////                    }
////                }
////            }
////        }
////        query.setFirstResult((page - 1) * pageSize);
////        query.setMaxResults(pageSize);
////        List<FootballAgency> list = query.list();
////        return list;
////    }
//
////    public List<FootballAgency> findAgencyListAll(int page, int pageSize, String cityCode, String countyCode, String search, Integer audit, Integer type) {
////        String sql = "SELECT DISTINCT\n" +
////                "\tfa.* \n" +
////                "FROM\n" +
////                "\tfootball_agency AS fa\n" +
////                "\tLEFT JOIN agency_user AS au ON fa.id = au.agencyId \n" +
////                "\tLEFT JOIN `user` AS u ON fa.creatorId = u.id\n" +
////                "\tLEFT JOIN mt_groups AS mt ON fa.groupsId = mt.id\n";
////        String sqlfix = " AND au.deleted = 0 order by fa.createTime DESC";
////        Query query = null;
////        //模糊查询机构
////        if (!"".equals(search)) {
////            if ("1000000".equals(cityCode) || "".equals(cityCode)) {
////                //查询全国的
////                if (type.equals(3)) {
////                    sql += " where fa.agencyName like :search and fa.audit=:audit and fa.countryCode='1000000'" + sqlfix;
////                    query = getCurrentSession().createNativeQuery(sql).addEntity(FootballAgency.class)
////                            .setParameter("search", "%" + search + "%")
////                            .setParameter("audit", audit);
////                } else {
////                    sql += " where fa.agencyName like :search and fa.audit=:audit and fa.countryCode='1000000' and fa.type = :type " + sqlfix;
////                    query = getCurrentSession().createNativeQuery(sql).addEntity(FootballAgency.class)
////                            .setParameter("search", "%" + search + "%")
////                            .setParameter("audit", audit)
////                            .setParameter("type", type);
////                }
////            } else {
////                //查询直辖市、特别行政区
////                if ("110000".equals(cityCode) || "120000".equals(cityCode) || "310000".equals(cityCode) || "500000".equals(cityCode) || "710000".equals(cityCode) || "810000".equals(cityCode) || "820000".equals(cityCode)) {
////                    if (type.equals(3)) {
////                        sql += " where fa.agencyName like :search and fa.audit=:audit  and fa.provinceCode=:cityCode and fa.type = :type" + sqlfix;
////                        query = getCurrentSession().createNativeQuery(sql).addEntity(FootballAgency.class)
////                                .setParameter("search", "%" + search + "%")
////                                .setParameter("cityCode", cityCode)
////                                .setParameter("audit", audit);
////                    } else {
////                        sql += " where fa.agencyName like :search and fa.audit=:audit  and fa.provinceCode=:provinceCode and fa.type = :type" + sqlfix;
////                        query = getCurrentSession().createNativeQuery(sql).addEntity(FootballAgency.class)
////                                .setParameter("search", "%" + search + "%")
////                                .setParameter("provinceCode", cityCode)
////                                .setParameter("audit", audit)
////                                .setParameter("type", type);
////                    }
////                } else {
////                    //普通城市
////                    if ((countyCode != null) && (!countyCode.equals(""))) {
////                        if (type.equals(3)) {
////                            sql += " where  fa.agencyName like :search and fa.audit = :audit and fa.cityCode= :cityCode and fa.countyCode=:countyCode" + sqlfix;
////                            query = getCurrentSession().createNativeQuery(sql).addEntity(FootballAgency.class)
////                                    .setParameter("search", "%" + search + "%")
////                                    .setParameter("cityCode", cityCode)
////                                    .setParameter("countyCode", countyCode)
////                                    .setParameter("audit", audit);
////                        } else {
////                            sql += " where  fa.agencyName like :search and fa.audit=:audit and fa.cityCode=:cityCode and fa.countyCode=:countyCode and fa.type = :type" + sqlfix;
////                            query = getCurrentSession().createNativeQuery(sql).addEntity(FootballAgency.class)
////                                    .setParameter("search", "%" + search + "%")
////                                    .setParameter("cityCode", cityCode)
////                                    .setParameter("countyCode", countyCode)
////                                    .setParameter("audit", audit)
////                                    .setParameter("type", type);
////                        }
////                    } else {
////                        if (type.equals(3)) {
////                            sql += " where fa.agencyName like :search  and fa.cityCode=:cityCode and fa.audit=:audit " + sqlfix;
////                            query = getCurrentSession().createNativeQuery(sql).addEntity(FootballAgency.class)
////                                    .setParameter("search", "%" + search + "%")
////                                    .setParameter("cityCode", cityCode)
////                                    .setParameter("audit", audit);
////                        } else {
////                            sql += " where fa.agencyName like :search  and fa.cityCode=:cityCode and fa.audit=:audit  and fa.type = :type" + sqlfix;
////                            query = getCurrentSession().createNativeQuery(sql).addEntity(FootballAgency.class)
////                                    .setParameter("search", "%" + search + "%")
////                                    .setParameter("cityCode", cityCode)
////                                    .setParameter("audit", audit)
////                                    .setParameter("type", type);
////                        }
////                    }
////                }
////            }
////        } else {
////            if ("1000000".equals(cityCode) || "".equals(cityCode)) {
////                //查询全国的
////                if (type.equals(3)) {
////                    sql += " where  fa.countryCode='1000000' and fa.audit=:audit " + sqlfix;
////                    query = getCurrentSession().createNativeQuery(sql).addEntity(FootballAgency.class)
////                            .setParameter("audit", audit);
////                } else {
////                    sql += " where  fa.countryCode='1000000' and fa.audit=:audit and fa.type = :type" + sqlfix;
////                    query = getCurrentSession().createNativeQuery(sql).addEntity(FootballAgency.class)
////                            .setParameter("audit", audit)
////                            .setParameter("type", type);
////                }
////            } else {
////                //查询直辖市、特别行政区
////                if ("110000".equals(cityCode) || "120000".equals(cityCode) || "310000".equals(cityCode) || "500000".equals(cityCode) || "710000".equals(cityCode) || "810000".equals(cityCode) || "820000".equals(cityCode)) {
////                    if (type.equals(3)) {
////                        sql += " where  fa.provinceCode=:provinceCode and fa.audit=:audit" + sqlfix;
////                        query = getCurrentSession().createNativeQuery(sql).addEntity(FootballAgency.class)
////                                .setParameter("provinceCode", cityCode)
////                                .setParameter("audit", audit);
////                    } else {
////                        sql += " where  fa.provinceCode=:provinceCode and fa.audit=:audit and fa.type = :type" + sqlfix;
////                        query = getCurrentSession().createNativeQuery(sql).addEntity(FootballAgency.class)
////                                .setParameter("provinceCode", cityCode)
////                                .setParameter("audit", audit)
////                                .setParameter("type", type);
////                    }
////                } else {
////                    //普通城市
////                    if ((countyCode != null) && (!countyCode.equals(""))) {
////                        if (type.equals(3)) {
////                            sql += " where fa.cityCode=:cityCode  and  fa.countyCode=:countyCode and fa.audit=:audit" + sqlfix;
////                            query = getCurrentSession().createNativeQuery(sql).addEntity(FootballAgency.class)
////                                    .setParameter("cityCode", cityCode)
////                                    .setParameter("countyCode", countyCode)
////                                    .setParameter("audit", audit);
////                        } else {
////                            sql += " where fa.cityCode=:cityCode and  fa.countyCode=:countyCode and fa.audit=:audit and fa.type = :type" + sqlfix;
////                            query = getCurrentSession().createNativeQuery(sql).addEntity(FootballAgency.class)
////                                    .setParameter("cityCode", cityCode)
////                                    .setParameter("countyCode", countyCode)
////                                    .setParameter("audit", audit)
////                                    .setParameter("type", type);
////                        }
////                    } else {
////                        if (type.equals(3)) {
////                            sql += " where  fa.cityCode=:cityCode and fa.audit=:audit" + sqlfix;
////                            query = getCurrentSession().createNativeQuery(sql).addEntity(FootballAgency.class)
////                                    .setParameter("cityCode", cityCode)
////                                    .setParameter("audit", audit);
////                        } else {
////                            sql += " where  fa.cityCode=:cityCode and fa.audit=:audit and fa.type = :type" + sqlfix;
////                            query = getCurrentSession().createNativeQuery(sql).addEntity(FootballAgency.class)
////                                    .setParameter("cityCode", cityCode)
////                                    .setParameter("audit", audit)
////                                    .setParameter("type", type);
////                        }
////                    }
////                }
////            }
////        }
////        query.setFirstResult((page - 1) * pageSize);
////        query.setMaxResults(pageSize);
////        List<FootballAgency> list = query.list();
////        return list;
////    }
//}
