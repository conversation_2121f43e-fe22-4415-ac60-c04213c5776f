package com.microteam.base.userHelp.dao;


import com.microteam.base.entity.userHelp.UserHelpFeedback;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

public interface UserHelpFeedbackDao extends JpaRepository<UserHelpFeedback, Long>, JpaSpecificationExecutor<UserHelpFeedback> {

    @Query("from UserHelpFeedback as userHelpFeedback " +
            "where userHelpFeedback.id=?1 " +
            "and userHelpFeedback.deleted=false")
    UserHelpFeedback findById(long id);

    @Query("from UserHelpFeedback as userHelpFeedback " +
            "where userHelpFeedback.user.id = (:userId) " +
            "and userHelpFeedback.feedBackMessage = (:message) " +
            "and userHelpFeedback.deleted=false ")
    UserHelpFeedback findByUserIdAndMessage(@Param("userId") Long userId, @Param("message") String message);
}
