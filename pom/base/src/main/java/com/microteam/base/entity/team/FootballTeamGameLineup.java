package com.microteam.base.entity.team;

import com.microteam.base.common.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.io.Serializable;

@Getter
@Setter
@Entity
@Table(name = "football_team_game_lineup")
public class FootballTeamGameLineup extends BaseEntity implements Serializable {


    @Column(name = "gameId", nullable = false)
    private Long gameId;
    @Column(name = "teamId", nullable = false)
    private Long teamId;
    @Column(name = "userId", nullable = false)
    private Long userId;
    @Column(name = "position", nullable = false)
    private String position;
}
