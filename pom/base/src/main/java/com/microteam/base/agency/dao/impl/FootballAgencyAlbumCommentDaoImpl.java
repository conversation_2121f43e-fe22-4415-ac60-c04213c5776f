//package com.microteam.base.agency.dao.impl;
//
//import com.microteam.base.agency.dao.FootballAgencyAlbumCommentDao;
//import com.microteam.base.entity.agency.FootballAgencyAlbum;
//import com.microteam.base.entity.agency.FootballAgencyAlbumComment;
//import com.microteam.base.common.base.AbstractHibernateDao;
//import org.apache.log4j.Logger;
//import org.hibernate.Hibernate;
//import org.hibernate.query.Query;
//import org.springframework.stereotype.Repository;
//
//import java.util.ArrayList;
//import java.util.List;
//
//@Repository("footballAgencyAlbumCommentDao")
//public class FootballAgencyAlbumCommentDaoImpl extends AbstractHibernateDao<FootballAgencyAlbumComment>
//        implements FootballAgencyAlbumCommentDao {
//    static Logger logger = Logger.getLogger(FootballAgencyAlbumCommentDaoImpl.class.getName());
//
//    public FootballAgencyAlbumCommentDaoImpl() {
//        super();
//
//        setClazz(FootballAgencyAlbumComment.class);
//    }
//
//    //查询相册评论
//    @SuppressWarnings("unchecked")
//    @Override
//    public List<FootballAgencyAlbumComment> findCommentByAlbum(
//            FootballAgencyAlbum agencyAlbum) {
//
//        String hql = "from FootballAgencyAlbumComment as footballAgencyAlbumComment where footballAgencyAlbumComment.footballAgencyAlbum=? and footballAgencyAlbumComment.deleted=false ";
//        Query query = getCurrentSession().createQuery(hql).setParameter(0, agencyAlbum);
//        List<FootballAgencyAlbumComment> list = query.list();
//        return list;
//    }
//
//    //分页查询相册评论
//    @SuppressWarnings("unchecked")
//    @Override
//    public List<FootballAgencyAlbumComment> findCommentByAlbumByPage(
//            FootballAgencyAlbum agencyAlbum, int page, int pageSize) {
//
//        String hql = "from FootballAgencyAlbumComment as footballAgencyAlbumComment left join fetch footballAgencyAlbumComment.user as user where footballAgencyAlbumComment.footballAgencyAlbum=? and footballAgencyAlbumComment.deleted=false ";
//        Query query = getCurrentSession().createQuery(hql).setParameter(0, agencyAlbum);
//        query.setFirstResult((page - 1) * pageSize);
//        query.setMaxResults(pageSize);
//        List<FootballAgencyAlbumComment> list = query.list();
//        if (list.size() > 0) {
//            List<FootballAgencyAlbumComment> listnew = new ArrayList<FootballAgencyAlbumComment>();
//            for (int i = 0; i < list.size(); i++) {
//                FootballAgencyAlbumComment agencyAlbumComment = list.get(i);
//                Hibernate.initialize(agencyAlbumComment);// 获取赖加载的集合内容；
//                Hibernate.initialize(agencyAlbumComment.getUser());// 获取赖加载的集合内容；
//                listnew.add(i, agencyAlbumComment);
//            }
//            return listnew;
//        }
//        return null;
//    }
//
//}
