package com.microteam.base.common.util.imServerManage.pojo;

import java.io.Serializable;

public class ImTransferGroupRequestBody implements Serializable{

	private String newowner;

	public ImTransferGroupRequestBody() {
		// TODO Auto-generated constructor stub
	}

	public ImTransferGroupRequestBody(String newowner) {
		this.newowner = newowner;
	}

	public String getNewowner() {
		return newowner;
	}

	public void setNewowner(String newowner) {
		this.newowner = newowner;
	}
	
	
}
