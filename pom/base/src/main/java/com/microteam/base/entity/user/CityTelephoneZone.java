package com.microteam.base.entity.user;


import com.microteam.base.common.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;
import java.io.Serializable;

@Entity
@Table(name = "city_telephone_zone", uniqueConstraints = @UniqueConstraint(columnNames = "cityCode"))
@Getter
@Setter
public class CityTelephoneZone extends BaseEntity implements Serializable {

    @Column(name = "cityCode", unique = true, nullable = false, length = 50)
    private String cityCode;
    @Column(name = "cityName", nullable = false, length = 50)
    private String cityName;
    @Column(name = "telZone", nullable = false, length = 50)
    private String telZone;

}
