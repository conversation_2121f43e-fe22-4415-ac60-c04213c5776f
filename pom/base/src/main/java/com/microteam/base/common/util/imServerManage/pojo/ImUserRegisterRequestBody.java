package com.microteam.base.common.util.imServerManage.pojo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;


/**
*  IM Request body  pojo类；；
* 
* @param 
* @param 
* @return
*/
@JsonIgnoreProperties(ignoreUnknown = true)
public class ImUserRegisterRequestBody implements java.io.Serializable{

	
	

		/**
		 * 
		 */
		
		private String username;
		
		private String password; 
		private String nickname; 
		

		public ImUserRegisterRequestBody() {
		}

		public ImUserRegisterRequestBody(String username, 
				String password,String nickname) {
			
			this.username = username;
			
			this.password = password;
			
			this.nickname = nickname;
			
		}

		
		public String getUsername() {
			return this.username;
		}

		public void setUsername(String username) {
			this.username = username;
		}


		public String getPassword() {
			return this.password;
		}

		public void setPassword(String password) {
			this.password = password;
		}
		
		public String getNickname() {
			return this.nickname;
		}

		public void setNickname(String nickname) {
			this.nickname = nickname;
		}
		
	
	
}
