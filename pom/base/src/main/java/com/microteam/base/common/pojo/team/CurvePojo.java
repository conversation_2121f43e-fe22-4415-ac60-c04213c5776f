package com.microteam.base.common.pojo.team;

import java.util.ArrayList;
import java.util.List;

public class CurvePojo {
    private List<Double> axisX = new ArrayList<>(); //X轴(保留一位小数)
    private List<Integer> axisY = new ArrayList<>();//Y轴
    private List<Long> axisXLong = new ArrayList<>();
    private Double maxX;        //X轴最大值
    private Integer maxY;       //Y轴最大值
    private Integer sumY;

    public Integer getSumY() {
        return sumY;
    }

    public void setSumY(Integer sumY) {
        this.sumY = sumY;
    }

    private Double realMin;     //实际运动的分钟数

    public List<Double> getAxisX() {
        return axisX;
    }

    public void setAxisX(List<Double> axisX) {
        this.axisX = axisX;
    }

    public List<Integer> getAxisY() {
        return axisY;
    }

    public void setAxisY(List<Integer> axisY) {
        this.axisY = axisY;
    }

    public Double getMaxX() {
        return maxX;
    }

    public void setMaxX(Double maxX) {
        this.maxX = maxX;
    }

    public Integer getMaxY() {
        return maxY;
    }

    public void setMaxY(Integer maxY) {
        this.maxY = maxY;
    }

    public Double getRealMin() {
        return realMin;
    }

    public void setRealMin(Double realMin) {
        this.realMin = realMin;
    }

    public List<Long> getAxisXLong() {
        return axisXLong;
    }

    public void setAxisXLong(List<Long> axisXLong) {
        this.axisXLong = axisXLong;
    }
}
