//package com.microteam.base.agency.dao.impl;
//
//import com.microteam.base.agency.dao.FootballAgencyStudentScoreDao;
//import com.microteam.base.entity.agency.FootballAgencyClassCourse;
//import com.microteam.base.entity.agency.FootballAgencyStudentScore;
//import com.microteam.base.common.base.AbstractHibernateDao;
//import org.apache.log4j.Logger;
//import org.hibernate.query.Query;
//import org.springframework.stereotype.Repository;
//
//import java.util.List;
//
//@Repository("footballAgencyStudentScoreDao")
//public class FootballAgencyStudentScoreDaoImpl extends
//        AbstractHibernateDao<FootballAgencyStudentScore> implements FootballAgencyStudentScoreDao {
//    static Logger logger = Logger.getLogger(FootballAgencyStudentScoreDaoImpl.class.getName());
//
//    public FootballAgencyStudentScoreDaoImpl() {
//        super();
//        setClazz(FootballAgencyStudentScore.class);
//
//    }
//
//    //查询课程下所有学员的分数
//    @SuppressWarnings("unchecked")
//    @Override
//    public List<FootballAgencyStudentScore> findScoreByCourse(
//            FootballAgencyClassCourse agencyClassCourse) {
//
//        String hql = "from FootballAgencyStudentScore as agencyStudentScore left join fetch agencyStudentScore.footballAgencyStudent as footballAgencyStudent where agencyStudentScore.footballAgencyClassCourse=? and agencyStudentScore.deleted=false ";
//        Query query = getCurrentSession().createQuery(hql).setParameter(0, agencyClassCourse);
//        List<FootballAgencyStudentScore> list = query.list();
//        return list;
//    }
//
//    @SuppressWarnings("unchecked")
//    @Override
//    public FootballAgencyStudentScore findScoreById(long id) {
//
//        String hql = "from FootballAgencyStudentScore as agencyStudentScore left join fetch agencyStudentScore.footballAgencyStudent as footballAgencyStudent where agencyStudentScore.id=? and agencyStudentScore.deleted=false ";
//        Query query = getCurrentSession().createQuery(hql).setParameter(0, id);
//        List<FootballAgencyStudentScore> list = query.list();
//        if (list != null && list.size() > 0) {
//            return list.get(0);
//        }
//        return null;
//    }
//
//
//}
