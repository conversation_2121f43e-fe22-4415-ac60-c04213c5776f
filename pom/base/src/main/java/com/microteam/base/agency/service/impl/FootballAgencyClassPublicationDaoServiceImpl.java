package com.microteam.base.agency.service.impl;

import com.microteam.base.agency.dao.FootballAgencyClassPublicationDao;
import com.microteam.base.agency.service.FootballAgencyClassPublicationDaoService;
import com.microteam.base.entity.agency.FootballAgencyClassPublication;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.List;

@Service("footballAgencyClassPublicationDaoService")
public class FootballAgencyClassPublicationDaoServiceImpl implements FootballAgencyClassPublicationDaoService {
    static Logger logger = Logger.getLogger(FootballAgencyClassPublicationDaoServiceImpl.class.getName());
    @Autowired
    private FootballAgencyClassPublicationDao dao;

    @Override
    public List<FootballAgencyClassPublication> findByAgencyIdAndAgencyClassIdForPage(Long agencyId, Long agencyClassId, int pageSize, int page) {
        if (page > 0) {
            page--;
        }
        Pageable pageable = PageRequest.of(page, pageSize);
        return dao.findByAgencyIdAndAgencyClassIdForPage(agencyId, agencyClassId, pageable);
    }

    @Override
    public List<FootballAgencyClassPublication> findByAgencyIdForPage(Long agencyId, int pageSize, int page) {
        if (page > 0) {
            page--;
        }
        Pageable pageable = PageRequest.of(page, pageSize);
        return dao.findByAgencyIdForPage(agencyId, pageable);
    }

    @Override
    public List<FootballAgencyClassPublication> findByAgencyIdAndJudgeForPage(Long agencyId, Long judge, int pageSize, int page) {
        if (page > 0) {
            page--;
        }
        Pageable pageable = PageRequest.of(page, pageSize);
        return dao.findByAgencyIdAndJudgeForPage(agencyId, judge, pageable);
    }

    @Override
    public FootballAgencyClassPublication findById(long id) {
        return dao.findById(id);
    }

}
