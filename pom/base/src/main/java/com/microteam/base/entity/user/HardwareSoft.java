package com.microteam.base.entity.user;


import com.microteam.base.common.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.io.Serializable;

@Entity
@Table(name = "hardware_soft")
@Getter
@Setter
public class HardwareSoft extends BaseEntity implements Serializable {

    @Column(name = "userId")
    private Long userId;
    @Column(name = "versionNumber", length = 200)
    private String versionNumber;
    @Column(name = "netUrl", length = 250)
    private String netUrl;
    @Column(name = "localUr", length = 250)
    private String localUr;
    @Column(name = "fileName", length = 50)
    private String fileName;
    @Column(name = "fileLength")
    private Long fileLength;

}
