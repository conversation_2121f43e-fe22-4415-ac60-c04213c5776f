//package com.microteam.base.agency.dao.impl;
//
//import com.microteam.base.agency.dao.FootballAgencySubalbumDao;
//import com.microteam.base.entity.agency.FootballAgencyAlbum;
//import com.microteam.base.entity.agency.FootballAgencySubalbum;
//import com.microteam.base.common.base.AbstractHibernateDao;
//import org.apache.log4j.Logger;
//import org.hibernate.query.Query;
//import org.springframework.stereotype.Repository;
//
//import java.util.List;
//
//@Repository("footballAgencySubalbumDao")
//public class FootballAgencySubalbumDaoImpl extends
//        AbstractHibernateDao<FootballAgencySubalbum> implements FootballAgencySubalbumDao {
//    static Logger logger = Logger.getLogger(FootballAgencySubalbumDaoImpl.class.getName());
//
//    public FootballAgencySubalbumDaoImpl() {
//        super();
//
//        setClazz(FootballAgencySubalbum.class);
//    }
//
//    //查询机构的图片列表
//    @SuppressWarnings("unchecked")
//    @Override
//    public List<FootballAgencySubalbum> findSubAlbumByAlbum(
//            FootballAgencyAlbum agencyAlbum) {
//        String hql = "from FootballAgencySubalbum as footballAgencySubalbum left join fetch footballAgencySubalbum.user as user where footballAgencySubalbum.footballAgencyAlbum=? and footballAgencySubalbum.deleted=false order by footballAgencySubalbum.createTime desc ";
//        Query query = getCurrentSession().createQuery(hql).setParameter(0, agencyAlbum);
//        List<FootballAgencySubalbum> list = query.list();
//        return list;
//    }
//
//
//}
