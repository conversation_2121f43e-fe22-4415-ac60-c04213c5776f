package com.microteam.base.common.constant;


import com.microteam.base.common.base.FinalList;

import java.util.ArrayList;
import java.util.List;

/**
 * 整个项目的全局常量；；
 */
public class Contants {

    public static final String ServerHostName = "cn.vsteam";
    public static final String ServerHostIP = "************";
    public static final String ServerpORT = "8080"; //SSL=443;
    public static final String ServerpORTSSL = "443"; //SSL=443;
    public static final String ProjectName = "vsteam";
    public static final String UriPath = "http://" + ServerHostIP + ":" + ServerpORT + "/" + ProjectName;
    public static final String UriPathSSL = "https://" + ServerHostIP + ":" + ServerpORTSSL + "/" + ProjectName;
    public static final String UploadUriPath = "/upload/images/users";
    public static final boolean IsUserIMRegister = true;//true; //正式发布时，为true;
    public static final String ImAccessToken = "YWMtyNV7jAWkEeW9f_vx2OdYqwAAAU7SWKFPS1-FfzAL25TR08qKbNMYk9420T8"; //2015-5-29 11:23;
    public static final String stateCode = "1000000";
    //--gerald 发布球赛默认attention
    public static final String RELEASEATTENTION = "请大家跟帖报名";
    //--gerald Im的clientId
    public static final String ClientId = "YXA6xTaBUJ-TEeS7pUn4Bfu81Q";
    //--gerald Im的clientSecret
    public static final String ClientSecret = "YXA6oZs6Rj-2PHWwp7HRKtrKcJjRe8A";
    //--gerald Im的grant_type
    public static final String GrantType = "client_credentials";
    //--gerald 访问秘钥vsteamToken用MD5生成的token
    public static final String APPACCESSTOKEN = "1a399b71ee198fa5ce3d4865568903e6";

    public static final FinalList<Integer> codeListOfCAN;
//    public static final List<Integer> codeListOfCAN = new ArrayList<>();

    static {
        List<Integer> list = new ArrayList<>();
        list.add(639);
        list.add(579);
        list.add(437);
        list.add(431);
        list.add(306);
        list.add(819);
        list.add(418);
        list.add(450);
        list.add(514);
        list.add(902);
        list.add(905);
        list.add(416);
        list.add(613);
        list.add(705);
        list.add(519);
        list.add(867);
        list.add(709);
        list.add(506);
        list.add(204);
        list.add(807);
        list.add(780);
        list.add(604);
        list.add(250);
        list.add(403);
        list.add(647);
        list.add(587);
        list.add(825);
        list.add(778);
        list.add(236);
        list.add(782);
        list.add(365);
        list.add(226);
        list.add(289);
        list.add(548);
        list.add(249);
        list.add(343);
        list.add(873);
        list.add(581);
        list.add(438);
        codeListOfCAN = new FinalList<>(list);
    }
}





