package com.microteam.base.agency.service.impl;

import com.microteam.base.agency.dao.FootballAgencyStudentCourseAlbumDao;
import com.microteam.base.agency.service.FootballAgencyStudentCourseAlbumDaoService;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service("footballAgencyStudentCourseAlbumDaoService")
public class FootballAgencyStudentCourseAlbumDaoServiceImpl implements FootballAgencyStudentCourseAlbumDaoService {
    static Logger logger = Logger.getLogger(FootballAgencyStudentCourseAlbumDaoServiceImpl.class.getName());
    @Autowired
    private FootballAgencyStudentCourseAlbumDao dao;

    public FootballAgencyStudentCourseAlbumDaoServiceImpl() {
        super();

    }

}
