package com.microteam.base.telMailValiManage.pojo;

public class MobileValidatePojo implements java.io.Serializable{
	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	/**
	 * 
	 */
	private String phoneNumber;
	private long startTimestamp;
	private long endTimestamp;
	public MobileValidatePojo() {
	}
	public MobileValidatePojo(String phoneNumber, long startTimestamp,
			long endTimestamp) {
		super();
		this.phoneNumber = phoneNumber;
		this.startTimestamp = startTimestamp;
		this.endTimestamp = endTimestamp;
	}
	public String getPhoneNumber() {
		return phoneNumber;
	}
	public void setPhoneNumber(String phoneNumber) {
		this.phoneNumber = phoneNumber;
	}
	public long getStartTimestamp() {
		return startTimestamp;
	}
	public void setStartTimestamp(long startTimestamp) {
		this.startTimestamp = startTimestamp;
	}
	public long getEndTimestamp() {
		return endTimestamp;
	}
	public void setEndTimestamp(long endTimestamp) {
		this.endTimestamp = endTimestamp;
	}
	

}
