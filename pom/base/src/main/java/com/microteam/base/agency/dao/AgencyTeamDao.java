package com.microteam.base.agency.dao;

import com.microteam.base.entity.agency.AgencyTeam;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

public interface AgencyTeamDao extends JpaRepository<AgencyTeam, Long>, JpaSpecificationExecutor<AgencyTeam> {

    @Query(value = "select count(1) from agency_team as agencyTeam " +
            " where agencyTeam.agencyId = (:agencyId) " +
            " and agencyTeam.deleted = 0 ", nativeQuery = true)
    Integer findContByAgencyId(@Param("agencyId") Long agencyId);

    @Query(" from AgencyTeam as agencyTeam " +
            " left join fetch agencyTeam.agency as agency " +
            " left join fetch agencyTeam.team as team " +
            " where agency.id = (:agencyId) " +
            " and agencyTeam.deleted = false " +
            " and agency.deleted = false " +
            " and team.deleted = false ")
    List<AgencyTeam> findByAgencyId(@Param("agencyId") Long agencyId);

    @Query(" from AgencyTeam as agencyTeam " +
            " left join fetch agencyTeam.agency as agency " +
            " left join fetch agencyTeam.team as team " +
            " where agency.id = (:agencyId) " +
            " and agencyTeam.deleted = false " +
            " and agency.deleted = false " +
            " and team.deleted = false ")
    List<AgencyTeam> findByAgencyIdForPage(@Param("agencyId") Long agencyId, Pageable pageable);

    @Query(" from AgencyTeam as agencyTeam " +
            " left join fetch agencyTeam.agency as agency " +
            " left join fetch agencyTeam.team as team " +
            " where team.id = (:teamId) " +
            " and agencyTeam.deleted = false " +
            " and agency.deleted = false " +
            " and team.deleted = false ")
    AgencyTeam findByTeamId(@Param("teamId") Long teamId);

    @Query(" from AgencyTeam as agencyTeam " +
            " left join fetch agencyTeam.agency as agency " +
            " left join fetch agencyTeam.team as team " +
            " left join fetch team.user as teamUser" +
            " where teamUser.id = (:userId) " +
            " and agencyTeam.deleted = false " +
            " and agency.deleted = false " +
            " and team.deleted = false ")
    List<AgencyTeam> findByTeamUserId(@Param("userId") Long userId);

    @Query("delete from AgencyTeam as agencyTeam " +
            "where agencyTeam.team.id = (:teamId) ")
    @Modifying
    int deleteByTeamId(@Param("teamId") Long teamId);
}
