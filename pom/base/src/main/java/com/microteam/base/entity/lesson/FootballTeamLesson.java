package com.microteam.base.entity.lesson;

import com.microteam.base.common.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.io.Serializable;

@Entity
@Table(name = "football_team_lesson")
@Getter
@Setter
public class FootballTeamLesson extends BaseEntity implements Serializable {

    @Column(name = "lessonId", nullable = false)
    private Long lessonId;
    @Column(name = "teamId", nullable = false)
    private Long teamId;

}
