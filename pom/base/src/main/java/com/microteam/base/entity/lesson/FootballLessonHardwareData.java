package com.microteam.base.entity.lesson;

import com.microteam.base.common.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

@Entity
@Table(name = "football_lesson_hardware_data")
@Getter
@Setter
public class FootballLessonHardwareData extends BaseEntity implements Serializable {

    @Column(name = "hardwareId", nullable = false)
    private Long hardwareId;
    @Column(name = "lessonId", nullable = false)
    private Long lessonId;
    @Column(name = "userId", nullable = false)
    private Long userId;
    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "kickBallStartTime", length = 19)
    private Date kickBallStartTime;
    @Column(name = "kickBallData", length = 65535)
    private String kickBallData;
    @Column(name = "highSpeedMoveData", length = 65535)
    private String highSpeedMoveData;
    @Column(name = "midSpeedMoveData", length = 65535)
    private String midSpeedMoveData;
    @Column(name = "lowSpeedMoveData", length = 65535)
    private String lowSpeedMoveData;
    @Column(name = "normalSpeedMoveData", length = 65535)
    private String normalSpeedMoveData;
    @Column(name = "isStartUp")
    private Integer isStartUp;
    @Column(name = "velocity")
    private Long velocity;
    @Column(name = "velocityTime")
    private Long velocityTime;
    @Column(name = "footBall_data", length = 65535)
    private String footBallData;
    @Column(name = "touchCounts")
    private Integer touchCounts;
    @Column(name = "passBallCounts")
    private Integer passBallCounts;
    @Column(name = "oneFootPassCount")
    private Integer oneFootPassCount;
    @Column(name = "carryTime")
    private Integer carryTime;
    @Column(name = "carryCounts")
    private Integer carryCounts;
    @Column(name = "carryDistance")
    private Long carryDistance;
    @Column(name = "highCarryDistance")
    private Integer highCarryDistance;
    @Column(name = "midCarryDistance")
    private Integer midCarryDistance;
    @Column(name = "lowCarryDistance")
    private Integer lowCarryDistance;
    @Column(name = "normalCarryDistance")
    private Integer normalCarryDistance;
    @Column(name = "calorieCurveData", length = 65535)
    private String calorieCurveData;
    @Column(name = "calorie")
    private Long calorie;
    @Column(name = "highMoveCount")
    private Integer highMoveCount;
    @Column(name = "midMoveCount")
    private Integer midMoveCount;
    @Column(name = "lowMoveCount")
    private Integer lowMoveCount;
    @Column(name = "normalMoveCount")
    private Integer normalMoveCount;


    public Date getKickBallStartTime() {
        if (this.kickBallStartTime == null) {
            return null;
        }
        return (Date) kickBallStartTime.clone();
    }

    public void setKickBallStartTime(Date kickBallStarTime) {
        this.kickBallStartTime = (Date) kickBallStarTime.clone();
    }

}
