//package com.microteam.base.agency.dao.impl;
//
//import com.microteam.base.agency.dao.FootballAgencyClassPublicationCommentDao;
//import com.microteam.base.entity.agency.FootballAgencyClassPublicationComment;
//import com.microteam.base.common.base.AbstractHibernateDao;
//import org.apache.log4j.Logger;
//import org.hibernate.query.Query;
//import org.springframework.stereotype.Repository;
//
//import java.util.List;
//
//@Repository("footballAgencyClassPublicationCommentDao")
//public class FootballAgencyClassPublicationCommentDaoImpl extends AbstractHibernateDao<FootballAgencyClassPublicationComment> implements FootballAgencyClassPublicationCommentDao {
//    static Logger logger = Logger.getLogger(FootballAgencyClassPublicationCommentDaoImpl.class.getName());
//
//    public FootballAgencyClassPublicationCommentDaoImpl() {
//        super();
//        setClazz(FootballAgencyClassPublicationComment.class);
//    }
//
//    //查询公告评论
//    @Override
//    public List<FootballAgencyClassPublicationComment> findByPublicationId(Long publicationId) {
//        String hql = "from FootballAgencyClassPublicationComment as agencyClassPublicationComment " +
//                "left join fetch agencyClassPublicationComment.user as user " +
//                "where agencyClassPublicationComment.footballAgencyClassPublication.id = (:publicationId) " +
//                "and agencyClassPublicationComment.deleted=false ";
//        Query query = getCurrentSession().createQuery(hql)
//                .setParameter("publicationId", publicationId);
//        return (List<FootballAgencyClassPublicationComment>) query.list();
//    }
//
//    /**
//     * 分页查询
//     */
//    @Override
//    public List<FootballAgencyClassPublicationComment> findByPublicationIdForPage(Long publicationId, int page, int pagerSize) {
//        String hql = "from FootballAgencyClassPublicationComment as agencyClassPublicationComment " +
//                "left join fetch agencyClassPublicationComment.user as user " +
//                "where agencyClassPublicationComment.footballAgencyClassPublication.id = (:publicationId) " +
//                "and agencyClassPublicationComment.deleted=false ";
//        Query query = getCurrentSession().createQuery(hql)
//                .setParameter("publicationId", publicationId);
//        query.setFirstResult((page - 1) * pagerSize);
//        query.setMaxResults(pagerSize);
//        return (List<FootballAgencyClassPublicationComment>) query.list();
//    }
//
//
//}
