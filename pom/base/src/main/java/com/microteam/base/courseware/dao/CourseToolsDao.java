package com.microteam.base.courseware.dao;

import com.microteam.base.entity.courseware.CourseWareTools;
import com.microteam.base.entity.lesson.FootballCourseWare;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

import java.util.List;

public interface CourseToolsDao extends JpaRepository<CourseWareTools, Long>, JpaSpecificationExecutor<FootballCourseWare> {

    List<CourseWareTools> findByCoursewareId(long courseWareId);
}
