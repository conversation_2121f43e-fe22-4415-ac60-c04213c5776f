//package com.microteam.base.agency.dao.impl;
//
//import com.microteam.base.agency.dao.FootballAgencyCoachDao;
//import com.microteam.base.common.base.AbstractHibernateDao;
//import com.microteam.base.entity.agency.FootballAgency;
//import com.microteam.base.entity.agency.FootballAgencyClass;
//import com.microteam.base.entity.agency.FootballAgencyClassCourse;
//import com.microteam.base.entity.agency.FootballAgencyCoach;
//import com.microteam.base.entity.user.User;
//import org.apache.log4j.Logger;
//import org.hibernate.Hibernate;
//import org.hibernate.query.Query;
//import org.springframework.stereotype.Repository;
//
//import java.util.ArrayList;
//import java.util.List;
//
//@Repository("footballAgencyCoachDao")
//public class FootballAgencyCoachDaoImpl extends AbstractHibernateDao<FootballAgencyCoach> implements FootballAgencyCoachDao {
//    static Logger logger = Logger.getLogger(FootballAgencyCoachDaoImpl.class.getName());
//
//    public FootballAgencyCoachDaoImpl() {
//        super();
//        setClazz(FootballAgencyCoach.class);
//    }
//
//    //查询教练
//    @Override
//    public FootballAgencyCoach findByAgencyIdAndAgencyClassIdAndAgencyClassCourseIdAndUserId(Long agencyId, Long agencyClassId, Long agencyClassCourseId, Long userId) {
//        String hql = "from FootballAgencyCoach as agencyCoach " +
//                "where agencyCoach.footballAgency.id = (:agencyId) " +
//                "and agencyCoach.footballAgencyClass.id = (:agencyClassId) " +
//                "and agencyCoach.footballAgencyClassCourse.id = (:agencyClassCourseId) " +
//                "and agencyCoach.user.id = (:userId) " +
//                "and agencyCoach.deleted=false ";
//        Query query = getCurrentSession().createQuery(hql)
//                .setParameter("agencyId", agencyId)
//                .setParameter("agencyClassId", agencyClassId)
//                .setParameter("agencyClassCourseId", agencyClassCourseId)
//                .setParameter("userId", userId);
//        List<FootballAgencyCoach> list = query.list();
//        if (list != null && list.size() > 0) {
//            return list.get(0);
//        }
//        return null;
//    }
//
//    @Override
//    public List<FootballAgencyCoach> findByAgencyIdAndAgencyClassIdAndUserId(Long agencyId, Long agencyClassId, Long userId) {
//        String hql = "from FootballAgencyCoach as agencyCoach " +
//                "where agencyCoach.footballAgency.id = (:agencyId) " +
//                "and agencyCoach.footballAgencyClass.id = (:agencyClassId) " +
//                "and agencyCoach.user.id = (:userId) " +
//                "and agencyCoach.deleted=false ";
//        Query query = getCurrentSession().createQuery(hql)
//                .setParameter("agencyId", agencyId)
//                .setParameter("agencyClassId", agencyClassId)
//                .setParameter("userId", userId);
//        return (List<FootballAgencyCoach>) query.list();
//    }
//
//    @Override
//    public List<FootballAgencyCoach> findByAgencyIdAndUserId(Long agencyId, Long userId) {
//        String hql = "from FootballAgencyCoach as agencyCoach " +
//                "where agencyCoach.footballAgency.id = (:agencyId) " +
//                "and agencyCoach.user.id = (:userId) " +
//                "and agencyCoach.deleted=false ";
//        Query query = getCurrentSession().createQuery(hql)
//                .setParameter("agencyId", agencyId)
//                .setParameter("userId", userId);
//        return (List<FootballAgencyCoach>) query.list();
//    }
//
//    //查询班级课程教练
//    @Override
//    public List<FootballAgencyCoach> findByAgencyIdAndAgencyClassIdAndAgencyClassCourseId(Long agencyId, Long agencyClassId, Long agencyClassCourseId) {
//        String hql = "from FootballAgencyCoach as agencyCoach " +
//                "left join fetch agencyCoach.user as user " +
//                "where agencyCoach.footballAgency.id = (:agencyId) " +
//                "and agencyCoach.footballAgencyClass.id = (:agencyClassId) " +
//                "and agencyCoach.footballAgencyClassCourse.id = (:agencyClassCourseId) " +
//                "and agencyCoach.deleted=false ";
//        Query query = getCurrentSession().createQuery(hql)
//                .setParameter("agencyId", agencyId)
//                .setParameter("agencyClassId", agencyClassId)
//                .setParameter("agencyClassCourseId", agencyClassCourseId);
//        return (List<FootballAgencyCoach>) query.list();
//    }
//
//    //查询机构下的所有教练
//    @Override
//    public List<FootballAgencyCoach> findByAgencyId(Long agencyId) {
//        String hql = "from FootballAgencyCoach as agencyCoach " +
//                "left join fetch agencyCoach.user as user " +
//                "where agencyCoach.footballAgency.id = (:agencyId) " +
//                "and agencyCoach.deleted=false ";
//        Query query = getCurrentSession().createQuery(hql)
//                .setParameter("agencyId", agencyId);
//        return (List<FootballAgencyCoach>) query.list();
//    }
//
//    @Override
//    public List<FootballAgencyCoach> findByAgencyIdForPage(Long agencyId, int pageSize, int page) {
//        String hql = "from FootballAgencyCoach as agencyCoach " +
//                "left join fetch agencyCoach.user as user " +
//                "left join fetch agencyCoach.footballAgencyClass as footballAgencyClass " +
//                "where agencyCoach.footballAgency.id = (:agencyId) " +
//                "and agencyCoach.deleted=false ";
//        Query query = getCurrentSession().createQuery(hql)
//                .setParameter("agencyId", agencyId);
//        query.setFirstResult((page - 1) * pageSize);
//        query.setMaxResults(pageSize);
//        return (List<FootballAgencyCoach>) query.list();
//    }
//
//    @Override
//    public List<FootballAgencyCoach> findByAgencyIdAndSearchForPage(Long agencyId, int pageSize, int page, String search) {
//        String hql = "";
//        Query query = null;
//        if (!"".equals(search) && search != null) {
//            hql = "from FootballAgencyCoach as agencyCoach " +
//                    "left join fetch agencyCoach.user as user " +
//                    "left join fetch agencyCoach.footballAgencyClass as footballAgencyClass " +
//                    "where agencyCoach.footballAgency.id = (:agencyId) " +
//                    "and agencyCoach.coachName like (:coachName) " +
//                    "and agencyCoach.deleted=false ";
//            query = getCurrentSession().createQuery(hql)
//                    .setParameter("agencyId", agencyId)
//                    .setParameter("coachName", "%" + search + "%");
//        } else {
//            hql = "from FootballAgencyCoach as agencyCoach " +
//                    "left join fetch agencyCoach.user as user " +
//                    "left join fetch agencyCoach.footballAgencyClass as footballAgencyClass " +
//                    "where agencyCoach.footballAgency.id = (:agencyId) " +
//                    "and agencyCoach.deleted=false ";
//            query = getCurrentSession().createQuery(hql)
//                    .setParameter("agencyId", agencyId);
//        }
//        query.setFirstResult((page - 1) * pageSize);
//        query.setMaxResults(pageSize);
//        return (List<FootballAgencyCoach>) query.list();
//    }
//
//    @Override
//    public List<FootballAgencyCoach> findByAgencyIdAndAgencyClassIdAndAgencyClassCourseIdForPage(Long agencyId, Long agencyClassId, Long agencyClassCourseId, int pageSize, int page) {
//        String hql = "from FootballAgencyCoach as agencyCoach " +
//                "left join fetch agencyCoach.user as user " +
//                "where agencyCoach.footballAgency.id = (:agencyId) " +
//                "and agencyCoach.footballAgencyClass.id = (:agencyClassId) " +
//                "and agencyCoach.footballAgencyClassCourse.id = (:agencyClassCourseId) " +
//                "and agencyCoach.deleted=false ";
//        Query query = getCurrentSession().createQuery(hql)
//                .setParameter("agencyId", agencyId)
//                .setParameter("agencyClassId", agencyClassId)
//                .setParameter("agencyClassCourseId", agencyClassCourseId);
//        query.setFirstResult((page - 1) * pageSize);
//        query.setMaxResults(pageSize);
//        return (List<FootballAgencyCoach>) query.list();
//    }
//
////    @Override
////    public List<FootballAgencyCoach> findCoachByUser(User user, String cityCode, String countyCode, String search) {
////        //模糊查询机构教练
////        String hql = "from FootballAgencyCoach as agencyCoach " +
////                "left join fetch agencyCoach.footballAgency as footballAgency " +
////                "left join fetch footballAgency.groups as groups ";
////        Query query = null;
////        if (!"".equals(search)) {
////            if ("1000000".equals(cityCode) || "".equals(cityCode)) {
////                //查询全国的
////                hql += " where footballAgency.agencyName like ? " +
////                        "and footballAgency.countryCode='1000000' " +
////                        "and footballAgency.deleted=false " +
////                        "and agencyCoach.user=? " +
////                        "and agencyCoach.deleted=false " +
////                        "order by agencyCoach.createTime desc ";
////                query = getCurrentSession().createQuery(hql)
////                        .setParameter(0, "%" + search + "%")
////                        .setParameter(1, user);
////            } else {
////                //查询直辖市、特别行政区
////                if ("110000".equals(cityCode) ||
////                        "120000".equals(cityCode) ||
////                        "310000".equals(cityCode) ||
////                        "500000".equals(cityCode) ||
////                        "710000".equals(cityCode) ||
////                        "810000".equals(cityCode) ||
////                        "820000".equals(cityCode)) {
////                    hql += " where footballAgency.agencyName like ? " +
////                            "and footballAgency.provinceCode=? " +
////                            "and footballAgency.deleted=false " +
////                            "and agencyCoach.user=? " +
////                            "and agencyCoach.deleted=false " +
////                            "order by agencyCoach.createTime desc ";
////                    query = getCurrentSession().createQuery(hql)
////                            .setParameter(0, "%" + search + "%")
////                            .setParameter(1, cityCode)
////                            .setParameter(2, user);
////                } else {
////                    //普通城市
////                    if ((countyCode != null) && (!countyCode.equals(""))) {
////                        hql += " where footballAgency.agencyName like ? " +
////                                "and footballAgency.cityCode=? " +
////                                "and footballAgency.countyCode=? " +
////                                "and footballAgency.deleted=false " +
////                                "and agencyCoach.user=? " +
////                                "and agencyCoach.deleted=false " +
////                                "order by agencyCoach.createTime desc ";
////                        query = getCurrentSession().createQuery(hql)
////                                .setParameter(0, "%" + search + "%")
////                                .setParameter(1, cityCode)
////                                .setParameter(2, countyCode)
////                                .setParameter(3, user);
////                    } else {
////                        hql += " where footballAgency.agencyName like ? " +
////                                "and footballAgency.cityCode=? " +
////                                "and footballAgency.deleted=false " +
////                                "and agencyCoach.user=? " +
////                                "and agencyCoach.deleted=false " +
////                                "order by agencyCoach.createTime desc ";
////                        query = getCurrentSession().createQuery(hql)
////                                .setParameter(0, "%" + search + "%")
////                                .setParameter(1, cityCode)
////                                .setParameter(2, user);
////                    }
////                }
////            }
////        } else {
////            if ("1000000".equals(cityCode) || "".equals(cityCode)) {
////                //查询全国的
////                hql += " where  footballAgency.countryCode='1000000'   and footballAgency.deleted=false   and agencyCoach.user=? and agencyCoach.deleted=false order by agencyCoach.createTime desc ";
////                query = getCurrentSession().createQuery(hql)
////                        .setParameter(0, user);
////            } else {
////                //查询直辖市、特别行政区
////                if ("110000".equals(cityCode) ||
////                        "120000".equals(cityCode) ||
////                        "310000".equals(cityCode) ||
////                        "500000".equals(cityCode) ||
////                        "710000".equals(cityCode) ||
////                        "810000".equals(cityCode) ||
////                        "820000".equals(cityCode)) {
////                    hql += " where  footballAgency.provinceCode=?   and footballAgency.deleted=false   and agencyCoach.user=? and agencyCoach.deleted=false order by agencyCoach.createTime desc ";
////                    query = getCurrentSession().createQuery(hql)
////                            .setParameter(0, cityCode)
////                            .setParameter(1, user);
////                } else {
////                    //普通城市
////                    if ((countyCode != null) && (!countyCode.equals(""))) {
////                        hql += " where footballAgency.cityCode=?  and  footballAgency.countyCode=?   and footballAgency.deleted=false   and agencyCoach.user=? and agencyCoach.deleted=false order by agencyCoach.createTime desc ";
////                        query = getCurrentSession().createQuery(hql)
////                                .setParameter(0, cityCode)
////                                .setParameter(1, countyCode)
////                                .setParameter(2, user);
////                    } else {
////                        hql += " where  footballAgency.cityCode=?   and footballAgency.deleted=false   and agencyCoach.user=? and agencyCoach.deleted=false order by agencyCoach.createTime desc ";
////                        query = getCurrentSession().createQuery(hql)
////                                .setParameter(0, cityCode)
////                                .setParameter(1, user);
////                    }
////                    //普通城市
////                }
////
////            }
////        }//else
////        List<FootballAgencyCoach> list = query.list();
////        if (list.size() > 0) {
////            List<FootballAgencyCoach> listnew = new ArrayList<FootballAgencyCoach>();
////            for (int i = 0; i < list.size(); i++) {
////                FootballAgencyCoach agencyCoach = list.get(i);
////                Hibernate.initialize(agencyCoach);// 获取赖加载的集合内容；
////                Hibernate.initialize(agencyCoach.getFootballAgency());
////                Hibernate.initialize(agencyCoach.getFootballAgency().getGroups());
////                listnew.add(i, agencyCoach);
////            }//for
////            return listnew;
////        }//if
////        return null;
////    }
//
//    @Override
//    public FootballAgencyCoach findAllById(long id) {
//        String hql = "from FootballAgencyCoach as agencyCoach " +
//                "left join fetch agencyCoach.footballAgency as footballAgency " +
//                "left join fetch agencyCoach.footballAgencyClass as footballAgencyClass " +
//                "left join fetch agencyCoach.footballAgencyClassCourse as footballAgencyClassCourse " +
//                "left join fetch agencyCoach.user as user " +
//                "where agencyCoach.id =?";
//        Query query = getCurrentSession().createQuery(hql)
//                .setParameter(0, id);
//        List<FootballAgencyCoach> list = query.list();
//        if (list != null && list.size() > 0) {
//            return list.get(0);
//        }
//        return null;
//    }
//
//    @Override
//    public FootballAgencyCoach findById(long id) {
//        String hql = "from FootballAgencyCoach as agencyCoach " +
//                "left join fetch agencyCoach.user as user " +
//                "left join fetch agencyCoach.footballAgencyClass as footballAgencyClass " +
//                "where agencyCoach.id =? " +
//                "and agencyCoach.deleted=false ";
//        Query query = getCurrentSession().createQuery(hql)
//                .setParameter(0, id);
//        List<FootballAgencyCoach> list = query.list();
//        if (list != null && list.size() > 0) {
//            return list.get(0);
//        }
//        return null;
//    }
//
//    @Override
//    public List<FootballAgencyCoach> findByAgencyIdAndAgencyClassId(Long agencyId, Long agencyClassId) {
//        String hql = "select distinct new com.microteam.base.entity.agency.FootballAgencyCoach(agencyCoach.footballAgency,agencyCoach.user) " +
//                "from FootballAgencyCoach as agencyCoach " +
//                "left join agencyCoach.user as user " +
//                "left join  agencyCoach.footballAgency as footballAgency " +
//                "where agencyCoach.footballAgency.id = (:agencyId) " +
//                "and agencyCoach.footballAgencyClass.id = (:agencyClassId) " +
//                "and agencyCoach.deleted=false ";
//        Query query = getCurrentSession().createQuery(hql)
//                .setParameter("agencyId", agencyId)
//                .setParameter("agencyClassId", agencyClassId);
//        List<FootballAgencyCoach> list = query.list();
//        //处理懒加载
//        if (list.size() > 0) {
//            List<FootballAgencyCoach> listnew = new ArrayList<FootballAgencyCoach>();
//            for (int i = 0; i < list.size(); i++) {
//                FootballAgencyCoach agencyCoach = list.get(i);
//                Hibernate.initialize(agencyCoach);// 获取赖加载的集合内容；
//                Hibernate.initialize(agencyCoach.getUser());// 获取赖加载的集合内容；
//                listnew.add(i, agencyCoach);
//            }
//            return listnew;
//        }
//        return null;
//    }
//
//    @Override
//    public List<FootballAgencyCoach> findByAgencyIdAndAgencyClassIdForPage(Long agencyId, Long agencyClassId, int page, int pageSize) {
//        String hql = " from FootballAgencyCoach as agencyCoach " +
//                "left join fetch agencyCoach.user as user " +
//                "where agencyCoach.footballAgency.id = (:agencyId) " +
//                "and agencyCoach.footballAgencyClass.id = (:agencyClassId) " +
//                "and agencyCoach.deleted=false ";
//        Query query = getCurrentSession().createQuery(hql)
//                .setParameter("agencyId", agencyId)
//                .setParameter("agencyClassId", agencyClassId);
//        query.setFirstResult((page - 1) * pageSize);
//        query.setMaxResults(pageSize);
//        List<FootballAgencyCoach> list = query.list();
//        return list;
//    }
//
//    @Override
//    public List<FootballAgencyCoach> findByAgencyIdAndAgencyClassIdAndCoachNameForPage(Long agencyId, Long agencyClassId, int page, int pageSize, String search) {
//        String hql = "from FootballAgencyCoach as agencyCoach " +
//                "left join fetch agencyCoach.user as user " +
//                "where agencyCoach.footballAgency.id = (:agencyId) " +
//                "and agencyCoach.footballAgencyClass.id = (:agencyClassId) " +
//                "and agencyCoach.coachName like (:coachName) " +
//                "and agencyCoach.deleted = false ";
//        Query query = null;
//        if ("".equals(search) || search == null) {
//            hql += "where agencyCoach.footballAgency.id = (:agencyId) " +
//                    "and agencyCoach.footballAgencyClass.id = (:agencyClassId) " +
//                    "and agencyCoach.deleted=false ";
//            query = getCurrentSession().createQuery(hql)
//                    .setParameter("agencyId", agencyId)
//                    .setParameter("agencyClassId", agencyClassId);
//        } else {
//            hql += "where agencyCoach.footballAgency.id = (:agencyId) " +
//                    "and agencyCoach.footballAgencyClass.id = (:agencyClassId) " +
//                    "and agencyCoach.coachName like (:coachName) " +
//                    "and agencyCoach.deleted=false ";
//            query = getCurrentSession().createQuery(hql)
//                    .setParameter("agencyId", agencyId)
//                    .setParameter("agencyClassId", agencyClassId)
//                    .setParameter("coachName", "%" + search + "%");
//        }
//        query.setFirstResult((page - 1) * pageSize);
//        query.setMaxResults(pageSize);
//        List<FootballAgencyCoach> list = query.list();
//        return list;
//    }
//
//
//    //删除班级下的所有教练
//    @Override
//    public void delByAgencyIdAndAgencyClassId(Long agencyId, Long agencyClassId) {
//        String hql = "delete from FootballAgencyCoach as agencyCoach " +
//                "where agencyCoach.footballAgency.id = (:agencyId) " +
//                "and agencyCoach.footballAgencyClass.id = (:agencyClassId) ";
//        Query query = getCurrentSession().createQuery(hql)
//                .setParameter("agencyId", agencyId)
//                .setParameter("agencyClassId", agencyClassId);
//        query.executeUpdate();
//    }
//
//    //查询教练是否报名了
//    @SuppressWarnings("unchecked")
//    @Override
//    public List<FootballAgencyCoach> findCoachIsEnrolled(FootballAgency agency,
//                                                         User user) {
//        //delete true 表示教练报名了，但是还没审批同意加入机构
//        String hql = "from FootballAgencyCoach as agencyCoach where agencyCoach.footballAgency =?  and agencyCoach.user=? ";
//        Query query = getCurrentSession().createQuery(hql).setParameter(0, agency).setParameter(1, user);
//        List<FootballAgencyCoach> list = query.list();
//        return list;
//    }
//
//    //分页查询机构教练报名申请
//    @SuppressWarnings("unchecked")
//    @Override
//    public List<FootballAgencyCoach> findCoachEnrollByAgencyForPage(
//            FootballAgency agency, int pageSize, int page) {
//        //true or false 用来判断是否申请报名
//        String hql = "from FootballAgencyCoach as agencyCoach left join fetch agencyCoach.user as user where agencyCoach.footballAgency =? and agencyCoach.deleted=true ";
//        Query query = getCurrentSession().createQuery(hql).setParameter(0, agency);
//        query.setFirstResult((page - 1) * pageSize);
//        query.setMaxResults(pageSize);
//        List<FootballAgencyCoach> list = query.list();
//        return list;
//    }
//
//    //查询课程下的所有教练
//    @SuppressWarnings("unchecked")
//    @Override
//    public List<FootballAgencyCoach> findCoachByCourse(
//            FootballAgencyClassCourse agencyClassCourse) {
//
//        String hql = "from FootballAgencyCoach as agencyCoach left join fetch agencyCoach.user as user where agencyCoach.footballAgencyClassCourse =? and agencyCoach.deleted=true ";
//        Query query = getCurrentSession().createQuery(hql).setParameter(0, agencyClassCourse);
//        List<FootballAgencyCoach> list = query.list();
//        return list;
//    }
//
//    @SuppressWarnings("unchecked")
//    @Override
//    public List<FootballAgencyCoach> findCoachBySearch(FootballAgency agency,
//                                                       String search) {
//
//        String hql = "from FootballAgencyCoach as agencyCoach left join fetch agencyCoach.user as user ";
//        hql += " where agencyCoach.footballAgency=? and agencyCoach.coachName like ? and agencyCoach.deleted=false order by agencyCoach.createTime desc ";
//        Query query = getCurrentSession().createQuery(hql)
//                .setParameter(0, agency)
//                .setParameter(1, "%" + search + "%");
//        List<FootballAgencyCoach> list = query.list();
//        return list;
//    }
//
//    @SuppressWarnings("unchecked")
//    @Override
//    public List<FootballAgencyCoach> findCoachEnrollByAgency(
//            FootballAgency agency) {
//        //true or false 用来判断是否申请报名
//        String hql = "from FootballAgencyCoach as agencyCoach left join fetch agencyCoach.user as user where agencyCoach.footballAgency =? and agencyCoach.deleted=true ";
//        Query query = getCurrentSession().createQuery(hql).setParameter(0, agency);
//        List<FootballAgencyCoach> list = query.list();
//        return list;
//    }
//
//    //分页模糊查询教练申请表
//    @SuppressWarnings("unchecked")
//    @Override
//    public List<FootballAgencyCoach> findCoachEnrollBySearch(
//            FootballAgency agency, int pageSize, int page, String search) {
//        String hql = "from FootballAgencyCoach as agencyCoach left join fetch agencyCoach.user as user  ";
//        Query query = null;
//        if ("".equals(search) || search == null) {
//            hql += "where agencyCoach.footballAgency =?  and agencyCoach.deleted=true ";
//            query = getCurrentSession().createQuery(hql).setParameter(0, agency);
//        } else {
//            hql += "where agencyCoach.footballAgency =? and agencyCoach.coachName like ? and agencyCoach.deleted=true ";
//            query = getCurrentSession().createQuery(hql).setParameter(0, agency).setParameter(1, "%" + search + "%");
//        }
//        query.setFirstResult((page - 1) * pageSize);
//        query.setMaxResults(pageSize);
//        List<FootballAgencyCoach> list = query.list();
//        if (list.size() > 0) {
//            List<FootballAgencyCoach> listnew = new ArrayList<FootballAgencyCoach>();
//            for (int i = 0; i < list.size(); i++) {
//                FootballAgencyCoach agencyCoach = list.get(i);
//                Hibernate.initialize(agencyCoach);// 获取赖加载的集合内容；
//                Hibernate.initialize(agencyCoach.getUser());
//                listnew.add(i, agencyCoach);
//            }//for
//            return listnew;
//        }//if
//        return null;
//    }
//
//    //删除课程下的教练
//    @Override
//    public void delCoachByAgency(FootballAgency agency,
//                                 FootballAgencyClass agencyClass,
//                                 FootballAgencyClassCourse agencyClassCourse) {
//
//        String hql = "delete FootballAgencyCoach as agencyCoach where agencyCoach.footballAgency=? and agencyCoach.footballAgencyClass=? and agencyCoach.footballAgencyClassCourse=? ";
//        Query query = getCurrentSession().createQuery(hql).setParameter(0, agency).setParameter(1, agencyClass).setParameter(2, agencyClassCourse);
//        query.executeUpdate();
//    }
//
//
//}
