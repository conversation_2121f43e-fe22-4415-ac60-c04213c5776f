package com.microteam.base.agency.dao;

import com.microteam.base.entity.agency.FootballAgencyClass;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

public interface FootballAgencyClassDao extends JpaRepository<FootballAgencyClass, Long>, JpaSpecificationExecutor<FootballAgencyClass> {
    //查询机构下的班级
    @Query("from FootballAgencyClass as footballAgencyClass " +
            "where footballAgencyClass.footballAgency.id = (:agencyId) " +
            "and footballAgencyClass.deleted=false ")
    List<FootballAgencyClass> findByAgencyId(@Param("agencyId") Long agencyId);

    @Query("from FootballAgencyClass as footballAgencyClass " +
            "left join fetch footballAgencyClass.groups as groups " +
            "left join fetch footballAgencyClass.user as user " +
            "where footballAgencyClass.footballAgency.id = (:agencyId) " +
            "and footballAgencyClass.deleted = false " +
            "and footballAgencyClass.isOver = (:isOver) ")
    List<FootballAgencyClass> findHistoryByAgencyId(@Param("agencyId") Long agencyId, @Param("isOver") boolean isOver, Pageable pageable);

    @Query("from FootballAgencyClass as footballAgencyClass " +
            "left join fetch footballAgencyClass.user as user " +
            "left join fetch footballAgencyClass.groups as groups " +
            "where footballAgencyClass.id=?1 " +
            "and footballAgencyClass.deleted=false ")
    FootballAgencyClass findById(long id);
}
