//package com.microteam.base.agency.dao.impl;
//
//import com.microteam.base.agency.dao.FootballAgencyClassAlbumDao;
//import com.microteam.base.entity.agency.FootballAgencyClass;
//import com.microteam.base.entity.agency.FootballAgencyClassAlbum;
//import com.microteam.base.common.base.AbstractHibernateDao;
//import org.apache.log4j.Logger;
//import org.hibernate.query.Query;
//import org.springframework.stereotype.Repository;
//
//import java.util.List;
//
//@Repository("footballAgencyClassAlbumDao")
//public class FootballAgencyClassAlbumDaoImpl extends AbstractHibernateDao<FootballAgencyClassAlbum> implements FootballAgencyClassAlbumDao {
//    static Logger logger = Logger.getLogger(FootballAgencyClassAlbumDaoImpl.class.getName());
//
//    public FootballAgencyClassAlbumDaoImpl() {
//        super();
//        setClazz(FootballAgencyClassAlbum.class);
//    }
//
//
//    @Override
//    public FootballAgencyClassAlbum findByAgencyClassIdAndAlbumName(FootballAgencyClass agencyClassId, String albumName) {
//        String hql = "from FootballAgencyClassAlbum as footballAgencyClassAlbum " +
//                "where footballAgencyClassAlbum.footballAgencyClass.id = (:agencyClassId) " +
//                "and footballAgencyClassAlbum.albumName = (:albumName) " +
//                "and footballAgencyClassAlbum.deleted=false ";
//        Query query = getCurrentSession().createQuery(hql)
//                .setParameter("agencyClassId", agencyClassId)
//                .setParameter("albumName", albumName);
//        List<FootballAgencyClassAlbum> list = query.list();
//        if (list != null && list.size() > 0) {
//            return list.get(0);
//        }
//        return null;
//    }
//
//    @Override
//    public FootballAgencyClassAlbum findById(long id) {
//        String hql = "from FootballAgencyClassAlbum as footballAgencyClassAlbum " +
//                "left join fetch footballAgencyClassAlbum.user as user " +
//                "where footballAgencyClassAlbum.id=?  " +
//                "and  footballAgencyClassAlbum.deleted=false ";
//        Query query = getCurrentSession().createQuery(hql)
//                .setParameter(0, id);
//        List<FootballAgencyClassAlbum> list = query.list();
//        if (list != null && list.size() > 0) {
//            return list.get(0);
//        }
//        return null;
//    }
//
//    //分页查询班级相册
//    @Override
//    public List<FootballAgencyClassAlbum> findByAgencyClassIdForPage(FootballAgencyClass agencyClassId, int page, int pageSize) {
//        String hql = "from FootballAgencyClassAlbum as footballAgencyClassAlbum " +
//                "where footballAgencyClassAlbum.footballAgencyClass.id = (:agencyClassId) " +
//                "and footballAgencyClassAlbum.deleted=false ";
//        Query query = getCurrentSession().createQuery(hql)
//                .setParameter("agencyClassId", agencyClassId);
//        query.setFirstResult((page - 1) * pageSize);
//        query.setMaxResults(pageSize);
//        List<FootballAgencyClassAlbum> list = query.list();
//        return list;
//    }
//
//
//}
