//package com.microteam.base.agency.dao.impl;
//
//import com.microteam.base.agency.dao.FootballAgencyClassPublicationAlbumDao;
//import com.microteam.base.entity.agency.FootballAgencyClassPublicationAlbum;
//import com.microteam.base.common.base.AbstractHibernateDao;
//import org.apache.log4j.Logger;
//import org.hibernate.query.Query;
//import org.springframework.stereotype.Repository;
//
//import java.util.List;
//
//@Repository("footballAgencyClassPublicationAlbumDao")
//public class FootballAgencyClassPublicationAlbumDaoImpl extends AbstractHibernateDao<FootballAgencyClassPublicationAlbum> implements FootballAgencyClassPublicationAlbumDao {
//    static Logger logger = Logger.getLogger(FootballAgencyClassPublicationAlbumDaoImpl.class.getName());
//
//    public FootballAgencyClassPublicationAlbumDaoImpl() {
//        super();
//        setClazz(FootballAgencyClassPublicationAlbum.class);
//    }
//
//    @Override
//    public List<FootballAgencyClassPublicationAlbum> findByPublicationId(Long publicationId) {
//        String hql = "from FootballAgencyClassPublicationAlbum as footballAgencyClassPublicationAlbum " +
//                "where footballAgencyClassPublicationAlbum.footballAgencyClassPublication.id = (:publicationId) " +
//                "and footballAgencyClassPublicationAlbum.deleted=false ";
//        Query query = getCurrentSession().createQuery(hql)
//                .setParameter("publicationId", publicationId);
//        List<FootballAgencyClassPublicationAlbum> list = query.list();
//        return list;
//    }
//
//}
