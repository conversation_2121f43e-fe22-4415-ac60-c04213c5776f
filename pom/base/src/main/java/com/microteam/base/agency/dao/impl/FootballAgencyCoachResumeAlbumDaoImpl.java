//package com.microteam.base.agency.dao.impl;
//
//import com.microteam.base.agency.dao.FootballAgencyCoachResumeAlbumDao;
//import com.microteam.base.entity.agency.FootballAgencyCoach;
//import com.microteam.base.entity.agency.FootballAgencyCoachResumeAlbum;
//import com.microteam.base.common.base.AbstractHibernateDao;
//import org.apache.log4j.Logger;
//import org.hibernate.query.Query;
//import org.springframework.stereotype.Repository;
//
//import java.util.List;
//
//@Repository("footballAgencyCoachResumeAlbumDao")
//public class FootballAgencyCoachResumeAlbumDaoImpl extends
//        AbstractHibernateDao<FootballAgencyCoachResumeAlbum> implements FootballAgencyCoachResumeAlbumDao {
//    static Logger logger = Logger.getLogger(FootballAgencyCoachResumeAlbumDaoImpl.class.getName());
//
//    public FootballAgencyCoachResumeAlbumDaoImpl() {
//        super();
//
//        setClazz(FootballAgencyCoachResumeAlbum.class);
//    }
//
//    @SuppressWarnings("unchecked")
//    @Override
//    public List<FootballAgencyCoachResumeAlbum> findCoachResumeAlbumList(
//            FootballAgencyCoach agencyCoach) {
//
//        String hql = "from  FootballAgencyCoachResumeAlbum as agencyCoachResumeAlbum where agencyCoachResumeAlbum.footballAgencyCoach=?  and agencyCoachResumeAlbum.deleted=false ";
//        Query query = getCurrentSession().createQuery(hql).setParameter(0, agencyCoach);
//        List<FootballAgencyCoachResumeAlbum> list = query.list();
//        return list;
//    }
//
//
//}
