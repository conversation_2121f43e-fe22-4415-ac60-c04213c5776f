package com.microteam.base.entity.user;


import com.microteam.base.common.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

@Entity
@Table(name = "user_hardware")
@Getter
@Setter
public class UserHardware extends BaseEntity implements Serializable {

    @ManyToOne
    @JoinColumn(name = "userId", nullable = false)
    private User user;
    @Column(name = "hardwareMac", nullable = false, length = 100)
    private String hardwareMac;
    @Column(name = "identification", nullable = false, length = 100)
    private String identification;
    @Column(name = "hardwareName", length = 100, nullable = false)
    private String hardwareName;
    @Column(name = "versionNumber", length = 100, nullable = false)
    private String versionNumber;
    @Column(name = "hardwareKind")
    private int hardwareKind;
    @Column(name = "hardwareType", nullable = false)
    private int hardwareType;
    @Column(name = "upgradeStatus")
    private int upgradeStatus;
    @Column(name = "upgardeTime")
    private Date upgardeTime;
    @Column(name = "overTime")
    private Date overTime;
    @Column(name = "repeat")
    private boolean repeat;
    @Column(name = "isStartUp")
    private Boolean isStartUp;
    @Column(name = "energyvalue", nullable = false)
    private Integer energyValue;
    @Column(name = "deletetime")
    private Date deleteTime;

    public Date getDeleteTime() {
        if (this.deleteTime == null) {
            return null;
        }
        return (Date) deleteTime.clone();
    }

    public void setDeleteTime(Date deleteTime) {
        this.deleteTime = (Date) deleteTime.clone();
    }
}
