package com.microteam.base.agency.service.impl;

import com.microteam.base.agency.dao.FootballAgencyStudentDao;
import com.microteam.base.agency.service.FootballAgencyStudentDaoService;
import com.microteam.base.entity.agency.FootballAgency;
import com.microteam.base.entity.agency.FootballAgencyClass;
import com.microteam.base.entity.agency.FootballAgencyClassCourse;
import com.microteam.base.entity.agency.FootballAgencyStudent;
import com.microteam.base.entity.user.User;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.List;

@Service("footballAgencyStudentDaoService")
public class FootballAgencyStudentDaoServiceImpl implements FootballAgencyStudentDaoService {
    static Logger logger = Logger.getLogger(FootballAgencyStudentDaoServiceImpl.class.getName());
    @Autowired
    private FootballAgencyStudentDao dao;

    public FootballAgencyStudentDaoServiceImpl() {
        super();

    }

    @Override
    public FootballAgencyStudent findStudentByAgencyAndAgencyClassAndAgencyClassCourseAndUser(
            FootballAgency agency, FootballAgencyClass agencyClass,
            FootballAgencyClassCourse agencyClassCourse, User user) {

        return dao.findStudentByAgencyAndAgencyClassAndAgencyClassCourseAndUser(agency, agencyClass, agencyClassCourse, user);
    }

    @Override
    public List<FootballAgencyStudent> findCourseStudentList(FootballAgency agency,
                                                             FootballAgencyClass agencyClass,
                                                             FootballAgencyClassCourse agencyClassCourse, int pageSize, int page) {
        if (page > 0) {
            page--;
        }
        Pageable pageable = PageRequest.of(page, pageSize);
        return dao.findCourseStudentList(agency, agencyClass, agencyClassCourse, pageable);
    }

    //查询机构下的学生
    @Override
    public List<FootballAgencyStudent> findStudentByAgency(FootballAgency agency) {
        return dao.findStudentByAgency(agency);
    }

    @Override
    public List<FootballAgencyStudent> findStudentByAgencyAndUser(FootballAgency agency, User user) {
        return dao.findStudentByAgencyAndUser(agency, user);
    }

    //查询机构下的学生
    @Override
    public List<FootballAgencyStudent> findStudentByAgencyAndSearch(FootballAgency agency, int pageSize, int page, String studentName) {
        if (page > 0) {
            page--;
        }
        Pageable pageable = PageRequest.of(page, pageSize);
        return dao.findStudentByAgencyAndSearch(agency, studentName, pageable);
    }

    //查询机构下的学生
    @Override
    public List<FootballAgencyStudent> findStudentByAgencyForPage(FootballAgency agency, int pageSize, int page) {
        if (page > 0) {
            page--;
        }
        Pageable pageable = PageRequest.of(page, pageSize);
        return dao.findStudentByAgencyForPage(agency, pageable);
    }

    //根据Id查询机构下的学员信息
    @Override
    public FootballAgencyStudent findStudentById(long id) {

        return dao.findStudentById(id);
    }

    //查询机构班级学生数量
    @Override
    public List<FootballAgencyStudent> findStudentByAgencyAndClass(
            FootballAgency agency, FootballAgencyClass agencyClass) {
        return dao.findStudentByAgencyAndClass(agency, agencyClass);
    }

    @Override
    public List<FootballAgencyStudent> findStudentByAgencyAndClassForPage(
            FootballAgency agency, FootballAgencyClass agencyClass, int page,
            int pageSize) {
        if (page > 0) {
            page--;
        }
        Pageable pageable = PageRequest.of(page, pageSize);
        return dao.findStudentByAgencyAndClassForPage(agency, agencyClass, pageable);
    }

    @Override
    public List<FootballAgencyStudent> findStudentByAgencyAndAgencyClassAndUser(
            FootballAgency agency, FootballAgencyClass agencyClass, User user) {
        return dao.findStudentByAgencyAndAgencyClassAndUser(agency, agencyClass, user);
    }

//    @Override
//    public List<FootballAgencyStudent> findStudentByUser(User user, String cityCode, String countyCode, String search) {
//
//        return dao.findStudentByUser(user, cityCode, countyCode, search);
//    }


}
