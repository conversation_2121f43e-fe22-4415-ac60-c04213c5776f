package com.microteam.base.lesson.service.impl;

import com.microteam.base.entity.lesson.FootballLessonCourseWare;
import com.microteam.base.lesson.dao.FootballLessonCourseWareDao;
import com.microteam.base.lesson.service.FootballLessonCourseWareDaoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service("footballLessonCoursewareDaoService")
public class FootballLessonCourseWareDaoServiceImpl implements FootballLessonCourseWareDaoService {

    @Autowired
    private FootballLessonCourseWareDao dao;

    @Override
    public int deleteByLessonId(Long lessonId) {
        return dao.deleteByLessonId(lessonId);
    }

    @Override
    public List<Long> findByLessonId(Long lessonId) {
        List<FootballLessonCourseWare> list = dao.findByLessonId(lessonId);
        List<Long> result = new ArrayList<>();
        if (list != null && list.size() > 0) {
            for (FootballLessonCourseWare course : list) {
                result.add(course.getCourseId());
            }
        }
        return result;
    }

    @Override
    public List<FootballLessonCourseWare> findByLessonIdList(List<Long> lessonIdList) {
        if (lessonIdList != null && lessonIdList.size() > 0) {
            return dao.findByLessonIdList(lessonIdList);
        }
        return new ArrayList<>();
    }

    @Override
    public FootballLessonCourseWare save(FootballLessonCourseWare lessonCourseware) {
        return dao.save(lessonCourseware);
    }
}
