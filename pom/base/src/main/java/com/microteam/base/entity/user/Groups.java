package com.microteam.base.entity.user;


import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

@Entity
@Table(name = "mt_groups", uniqueConstraints = {
        @UniqueConstraint(columnNames = "imGroupId"),
        @UniqueConstraint(columnNames = "groupName")})
@Getter
@Setter
public class Groups implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false)
    protected Long id;
    @Column(name = "deleted", nullable = false)
    protected boolean deleted;
    @ManyToOne
    @JoinColumn(name = "userId", nullable = false)
    private User user;
    @ManyToOne
    @JoinColumn(name = "groupRoleId", nullable = false)
    private GroupRole groupRole;
    @Column(name = "imGroupId", unique = true, length = 64)
    private String imGroupId;
    @Column(name = "groupName", unique = true, nullable = false, length = 100)
    private String groupName;
    @Column(name = "isPublic")
    private Boolean isPublic;
    @Column(name = "membersOnly", nullable = false)
    private boolean membersOnly;
    @Column(name = "allowinvites")
    private Boolean allowinvites;
    @Column(name = "maxusers")
    private Integer maxusers;
    @Column(name = "affiliationsCount")
    private Integer affiliationsCount;
    @Column(name = "groupStyle")
    private Short groupStyle;
    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "registerTime", nullable = false, length = 19)
    private Date registerTime;
    @Column(name = "imEnabled", nullable = false)
    private boolean imEnabled;
    @Column(name = "enabled", nullable = false)
    private boolean enabled;

    public Date getRegisterTime() {
        if (this.registerTime == null) {
            return null;
        }
        return (Date) this.registerTime.clone();
    }

    public void setRegisterTime(Date registerTime) {
        this.registerTime = (Date) registerTime.clone();
    }

}
