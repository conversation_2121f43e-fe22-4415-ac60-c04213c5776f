package com.microteam.base.agency.dao;

import com.microteam.base.entity.agency.AgencyRole;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;


public interface AgencyRoleDao extends JpaRepository<AgencyRole, Long>, JpaSpecificationExecutor<AgencyRole> {

    @Query("from AgencyRole as agencyRole " +
            " where agencyRole.id = (:id) " +
            " and agencyRole.deleted = false ")
    AgencyRole findById(long id);

//    Integer findCountByAgencyIdAndRoleId(Long agencyId, Long roleId);
}

