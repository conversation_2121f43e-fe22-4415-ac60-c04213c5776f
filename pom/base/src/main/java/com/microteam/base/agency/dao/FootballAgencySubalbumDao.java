package com.microteam.base.agency.dao;

import com.microteam.base.entity.agency.FootballAgencyAlbum;
import com.microteam.base.entity.agency.FootballAgencySubalbum;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

public interface FootballAgencySubalbumDao extends JpaRepository<FootballAgencySubalbum, Long>, JpaSpecificationExecutor<FootballAgencySubalbum> {
    //查询机构的图片列表
    @Query("from FootballAgencySubalbum as footballAgencySubalbum " +
            "left join fetch footballAgencySubalbum.user as user " +
            "where footballAgencySubalbum.footballAgencyAlbum=?1 " +
            "and footballAgencySubalbum.deleted=false " +
            "order by footballAgencySubalbum.createTime desc ")
    List<FootballAgencySubalbum> findSubAlbumByAlbum(FootballAgencyAlbum agencyAlbum);
}
