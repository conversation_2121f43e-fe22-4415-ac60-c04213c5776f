package com.microteam.base.common.pojo.team;

import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
public class PassBallDataPojo {

    private PassBallDataValuePojo oneFootPassCount;
    private PassBallDataValuePojo twoFootPassCount;
    private PassBallDataValuePojo longPass;
    private PassBallDataValuePojo shortPass;
    private PassBallDataValuePojo passBallRate;
    private PassBallDataValuePojo maxSprintSpeed;
    private PassBallDataValuePojo maxConsecutivePasses;
    private PassBallDataValuePojo averConsecutivePasses;
    private List<Long> dataType;
    private List<PassLinePojo> passline;

}
