package com.microteam.base.entity.lesson;

import com.microteam.base.common.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.io.Serializable;

@Entity
@Table(name = "football_datatype")
@Getter
@Setter
public class FootballDataType extends BaseEntity implements Serializable {

    @Column(name = "typeName", length = 100)
    private String typeName;
    @Column(name = "typeIntro", length = 100)
    private String typeIntro;
    @Column(name = "type")
    private Integer type;

}
