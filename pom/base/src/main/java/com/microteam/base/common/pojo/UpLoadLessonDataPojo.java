
package com.microteam.base.common.pojo;

import java.util.Arrays;
import java.util.List;

public class UpLoadLessonDataPojo {
    private long lessonId;  //课程id
    private long userId;    //用户id

    private long userHardwareId;
    private long userHardwareDataId;

    private long kickBallStartTime;
    private long rightKickBallStartTime;

    private int[] kickBallData;
    private int[] rightKickBallData;

    private List<UserHardWareStepPojo> highMoveData;
    private List<UserHardWareStepPojo> midMoveData;
    private List<UserHardWareStepPojo> lowMoveData;
    private List<UserHardWareStepPojo> normalMoveData;

    private List<UserHardWareStepPojo> rightHighMoveData;
    private List<UserHardWareStepPojo> rightMidMoveData;
    private List<UserHardWareStepPojo> rightLowMoveData;
    private List<UserHardWareStepPojo> rightNormalMoveData;

    private int highMoveCount;//左脚高速运动次数
    private int midMoveCount;//左脚中速运动次数
    private int lowMoveCount;//左脚低速运动次数
    private int normalMoveCount;//左脚步行运动次数

    private int rightHighMoveCount;//右脚高速运动次数
    private int rightMidMoveCount;//右脚中速运动次数
    private int rightLowMoveCount;//右脚低速运动次数
    private int rightNormalMoveCount;//右脚步行运动次数

    private int exteriorData;//外脚背数据
    private int instepKickingData;//正脚背数据
    private int archData;//脚弓数据
    private int tiptoeData;//脚尖数据
    private int heelData;//脚后跟数据
    private int soleFootData;//脚底数据   

    private int rightExteriorData;    //右脚外脚背数据
    private int rightInstepKickingData;   //右脚正脚背数据
    private int rightArchData;  //右脚脚弓数据
    private int rightTiptoeData;   //右脚脚尖数据
    private int rightHeelData;  //右脚脚后跟数据
    private int rightSoleFootData;  //右脚脚底数据


    public String hardwareMac;
    public String hardwareName;
    public long hardwarePracticeId;
    public int isTrainning;
    public long teamGameId;
    public long teamId;
    public int velocity;
    public long velocityTime;
    public String versionNumber;


    public UpLoadLessonDataPojo() {
        super();
    }


    public long getLessonId() {
        return lessonId;
    }


    public void setLessonId(long lessonId) {
        this.lessonId = lessonId;
    }


    public long getUserId() {
        return userId;
    }


    public void setUserId(long userId) {
        this.userId = userId;
    }


    public long getUserHardwareId() {
        return userHardwareId;
    }


    public void setUserHardwareId(long userHardwareId) {
        this.userHardwareId = userHardwareId;
    }


    public long getUserHardwareDataId() {
        return userHardwareDataId;
    }

    public void setUserHardwareDataId(long userHardwareDataId) {
        this.userHardwareDataId = userHardwareDataId;
    }

    public long getKickBallStartTime() {
        return kickBallStartTime;
    }


    public void setKickBallStartTime(long kickBallStartTime) {
        this.kickBallStartTime = kickBallStartTime;
    }


    public long getRightKickBallStartTime() {
        return rightKickBallStartTime;
    }


    public void setRightKickBallStartTime(long rightKickBallStartTime) {
        this.rightKickBallStartTime = rightKickBallStartTime;
    }


    public int[] getKickBallData() {
        return kickBallData;
    }


    public void setKickBallData(int[] kickBallData) {
        this.kickBallData = kickBallData;
    }


    public int[] getRightKickBallData() {
        return rightKickBallData;
    }


    public void setRightKickBallData(int[] rightKickBallData) {
        this.rightKickBallData = rightKickBallData;
    }


    public List<UserHardWareStepPojo> getHighMoveData() {
        return highMoveData;
    }


    public void setHighMoveData(List<UserHardWareStepPojo> highMoveData) {
        this.highMoveData = highMoveData;
    }


    public List<UserHardWareStepPojo> getMidMoveData() {
        return midMoveData;
    }


    public void setMidMoveData(List<UserHardWareStepPojo> midMoveData) {
        this.midMoveData = midMoveData;
    }


    public List<UserHardWareStepPojo> getLowMoveData() {
        return lowMoveData;
    }


    public void setLowMoveData(List<UserHardWareStepPojo> lowMoveData) {
        this.lowMoveData = lowMoveData;
    }


    public List<UserHardWareStepPojo> getNormalMoveData() {
        return normalMoveData;
    }


    public void setNormalMoveData(List<UserHardWareStepPojo> normalMoveData) {
        this.normalMoveData = normalMoveData;
    }


    public List<UserHardWareStepPojo> getRightHighMoveData() {
        return rightHighMoveData;
    }


    public void setRightHighMoveData(List<UserHardWareStepPojo> rightHighMoveData) {
        this.rightHighMoveData = rightHighMoveData;
    }


    public List<UserHardWareStepPojo> getRightMidMoveData() {
        return rightMidMoveData;
    }


    public void setRightMidMoveData(List<UserHardWareStepPojo> rightMidMoveData) {
        this.rightMidMoveData = rightMidMoveData;
    }


    public List<UserHardWareStepPojo> getRightLowMoveData() {
        return rightLowMoveData;
    }


    public void setRightLowMoveData(List<UserHardWareStepPojo> rightLowMoveData) {
        this.rightLowMoveData = rightLowMoveData;
    }


    public List<UserHardWareStepPojo> getRightNormalMoveData() {
        return rightNormalMoveData;
    }


    public void setRightNormalMoveData(List<UserHardWareStepPojo> rightNormalMoveData) {
        this.rightNormalMoveData = rightNormalMoveData;
    }


    public int getHighMoveCount() {
        return highMoveCount;
    }


    public void setHighMoveCount(int highMoveCount) {
        this.highMoveCount = highMoveCount;
    }


    public int getMidMoveCount() {
        return midMoveCount;
    }


    public void setMidMoveCount(int midMoveCount) {
        this.midMoveCount = midMoveCount;
    }


    public int getLowMoveCount() {
        return lowMoveCount;
    }


    public void setLowMoveCount(int lowMoveCount) {
        this.lowMoveCount = lowMoveCount;
    }


    public int getNormalMoveCount() {
        return normalMoveCount;
    }


    public void setNormalMoveCount(int normalMoveCount) {
        this.normalMoveCount = normalMoveCount;
    }


    public int getRightHighMoveCount() {
        return rightHighMoveCount;
    }


    public void setRightHighMoveCount(int rightHighMoveCount) {
        this.rightHighMoveCount = rightHighMoveCount;
    }


    public int getRightMidMoveCount() {
        return rightMidMoveCount;
    }


    public void setRightMidMoveCount(int rightMidMoveCount) {
        this.rightMidMoveCount = rightMidMoveCount;
    }


    public int getRightLowMoveCount() {
        return rightLowMoveCount;
    }


    public void setRightLowMoveCount(int rightLowMoveCount) {
        this.rightLowMoveCount = rightLowMoveCount;
    }


    public int getRightNormalMoveCount() {
        return rightNormalMoveCount;
    }


    public void setRightNormalMoveCount(int rightNormalMoveCount) {
        this.rightNormalMoveCount = rightNormalMoveCount;
    }


    public int getExteriorData() {
        return exteriorData;
    }


    public void setExteriorData(int exteriorData) {
        this.exteriorData = exteriorData;
    }


    public int getInstepKickingData() {
        return instepKickingData;
    }


    public void setInstepKickingData(int instepKickingData) {
        this.instepKickingData = instepKickingData;
    }


    public int getArchData() {
        return archData;
    }


    public void setArchData(int archData) {
        this.archData = archData;
    }


    public int getTiptoeData() {
        return tiptoeData;
    }


    public void setTiptoeData(int tiptoeData) {
        this.tiptoeData = tiptoeData;
    }


    public int getHeelData() {
        return heelData;
    }


    public void setHeelData(int heelData) {
        this.heelData = heelData;
    }


    public int getSoleFootData() {
        return soleFootData;
    }


    public void setSoleFootData(int soleFootData) {
        this.soleFootData = soleFootData;
    }


    public int getRightExteriorData() {
        return rightExteriorData;
    }


    public void setRightExteriorData(int rightExteriorData) {
        this.rightExteriorData = rightExteriorData;
    }


    public int getRightInstepKickingData() {
        return rightInstepKickingData;
    }


    public void setRightInstepKickingData(int rightInstepKickingData) {
        this.rightInstepKickingData = rightInstepKickingData;
    }


    public int getRightArchData() {
        return rightArchData;
    }


    public void setRightArchData(int rightArchData) {
        this.rightArchData = rightArchData;
    }


    public int getRightTiptoeData() {
        return rightTiptoeData;
    }


    public void setRightTiptoeData(int rightTiptoeData) {
        this.rightTiptoeData = rightTiptoeData;
    }


    public int getRightHeelData() {
        return rightHeelData;
    }


    public void setRightHeelData(int rightHeelData) {
        this.rightHeelData = rightHeelData;
    }


    public int getRightSoleFootData() {
        return rightSoleFootData;
    }


    public void setRightSoleFootData(int rightSoleFootData) {
        this.rightSoleFootData = rightSoleFootData;
    }


    public String getHardwareMac() {
        return hardwareMac;
    }


    public void setHardwareMac(String hardwareMac) {
        this.hardwareMac = hardwareMac;
    }


    public String getHardwareName() {
        return hardwareName;
    }


    public void setHardwareName(String hardwareName) {
        this.hardwareName = hardwareName;
    }


    public long getHardwarePracticeId() {
        return hardwarePracticeId;
    }


    public void setHardwarePracticeId(long hardwarePracticeId) {
        this.hardwarePracticeId = hardwarePracticeId;
    }


    public int getIsTrainning() {
        return isTrainning;
    }


    public void setIsTrainning(int isTrainning) {
        this.isTrainning = isTrainning;
    }


    public long getTeamGameId() {
        return teamGameId;
    }


    public void setTeamGameId(long teamGameId) {
        this.teamGameId = teamGameId;
    }


    public long getTeamId() {
        return teamId;
    }


    public void setTeamId(long teamId) {
        this.teamId = teamId;
    }


    public int getVelocity() {
        return velocity;
    }


    public void setVelocity(int velocity) {
        this.velocity = velocity;
    }


    public long getVelocityTime() {
        return velocityTime;
    }


    public void setVelocityTime(long velocityTime) {
        this.velocityTime = velocityTime;
    }


    public String getVersionNumber() {
        return versionNumber;
    }


    public void setVersionNumber(String versionNumber) {
        this.versionNumber = versionNumber;
    }


    @Override
    public String toString() {
        return "UpLoadLessonDataPojo [lessonId=" + lessonId + ", userId=" + userId + ", userHardwareId=" + userHardwareId + ", userHardwareDataId="
                + userHardwareDataId + ", kickBallStartTime=" + kickBallStartTime + ", rightKickBallStartTime=" + rightKickBallStartTime
                + ", kickBallData=" + Arrays.toString(kickBallData) + ", rightKickBallData=" + Arrays.toString(rightKickBallData) + ", highMoveData="
                + highMoveData + ", midMoveData=" + midMoveData + ", lowMoveData=" + lowMoveData + ", normalMoveData=" + normalMoveData
                + ", rightHighMoveData=" + rightHighMoveData + ", rightMidMoveData=" + rightMidMoveData + ", rightLowMoveData=" + rightLowMoveData
                + ", rightNormalMoveData=" + rightNormalMoveData + ", exteriorData=" + exteriorData + ", instepKickingData=" + instepKickingData
                + ", archData=" + archData + ", tiptoeData=" + tiptoeData + ", heelData=" + heelData + ", soleFootData=" + soleFootData
                + ", rightExteriorData=" + rightExteriorData + ", rightInstepKickingData=" + rightInstepKickingData + ", rightArchData="
                + rightArchData + ", rightTiptoeData=" + rightTiptoeData + ", rightHeelData=" + rightHeelData + ", rightSoleFootData="
                + rightSoleFootData + ", hardwareMac=" + hardwareMac + ", hardwareName=" + hardwareName + ", hardwarePracticeId=" + hardwarePracticeId
                + ", isTrainning=" + isTrainning + ", teamGameId=" + teamGameId + ", teamId=" + teamId + ", velocity=" + velocity + ", velocityTime="
                + velocityTime + ", versionNumber=" + versionNumber + "]";
    }


}
