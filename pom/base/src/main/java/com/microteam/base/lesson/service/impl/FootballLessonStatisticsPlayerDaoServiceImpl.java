package com.microteam.base.lesson.service.impl;

import com.microteam.base.common.pojo.LessonSummaryAverage;
import com.microteam.base.common.pojo.lesson.LessonData;
import com.microteam.base.common.pojo.team.FootTeamballPassballAverage;
import com.microteam.base.common.util.common.RedisUtil;
import com.microteam.base.entity.lesson.FootballLessonStatisticsPlayer;
import com.microteam.base.lesson.dao.FootballLessonStatisticsPlayerDao;
import com.microteam.base.lesson.service.FootballLessonStatisticsPlayerDaoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.persistence.Tuple;
import java.util.ArrayList;
import java.util.List;

@Service("footballLessonStatisticsPlayerDaoService")
public class FootballLessonStatisticsPlayerDaoServiceImpl implements FootballLessonStatisticsPlayerDaoService {

    @Autowired
    FootballLessonStatisticsPlayerDao dao;

    @Autowired
    private RedisUtil redisUtil;

    @Override
    public FootballLessonStatisticsPlayer findByUserIdAndLessonId(Long userId, Long lessonId) {
        return dao.findByUserIdAndLessonId(userId, lessonId);
    }

    @Override
    public boolean delByUserIdAndLessonId(Long userId, Long lessonId) {
        return dao.delByUserIdAndLessonId(userId, lessonId);
    }

    @Override
    public List<FootballLessonStatisticsPlayer> findByLessonId(Long lessonId) {
        return dao.findByLessonId(lessonId);
    }

    @Override
    public List<FootballLessonStatisticsPlayer> findByLessonIdAndUserIdList(Long lessonId, List<Long> userIdList) {
        if (userIdList != null && userIdList.size() > 0) {
            return dao.findByLessonIdAndUserIdList(lessonId, userIdList);
        }
        return new ArrayList<>();
    }

    @Override
    public LessonData getDataByLessonIdAndTeamId(Long lessonId, Long teamId) {
        return dao.getDataByLessonIdAndTeamId(lessonId, teamId);
    }

    @Override
    public LessonData getDataByTeamId(Long teamId) {
        return dao.getDataByTeamId(teamId);
    }

    @Override
    public List<FootballLessonStatisticsPlayer> findByLessonIdAndTeamId(Long lessonId, Long teamId) {
        return dao.findByLessonIdAndTeamId(lessonId, teamId);
    }

    @Override
    public List<FootballLessonStatisticsPlayer> findByTeamId(Long teamId) {
        return dao.findByTeamId(teamId);
    }

    @Override
    public FootballLessonStatisticsPlayer findByTeamIdAndUserIdAndLessonId(Long teamId, Long lessonId, Long userId) {
        return dao.findByTeamIdAndUserIdAndLessonId(teamId, lessonId, userId);
    }

    @Override
    public FootTeamballPassballAverage findAveragePassBallByUserId(Long userId) {
        return dao.findAveragePassBallByUserId(userId);
    }

    @Override
    public LessonSummaryAverage getLessonSummaryAverage(long userId) {
        Tuple tuple = dao.getLessonSummaryAverage(userId);
        return new LessonSummaryAverage(tuple);
    }

    @Override
    public FootballLessonStatisticsPlayer getLessonPassballAnalysis(long lessonId, long teamId, long userId) {
        return dao.getLessonPassballAnalysis(lessonId, teamId, userId);
    }

    @Override
    public int deleteByTeamId(Long teamId) {
        return dao.deleteByTeamId(teamId);
    }
}
