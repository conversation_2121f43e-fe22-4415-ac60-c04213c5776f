package com.microteam.base.entity.agency;

import com.microteam.base.common.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.io.Serializable;

@Entity
@Table(name = "agency_role")
@Getter
@Setter
public class AgencyRole extends BaseEntity implements Serializable {

    @Column(name = "roleName")
    private String roleName;
    @Column(name = "description")
    private String description;

}
