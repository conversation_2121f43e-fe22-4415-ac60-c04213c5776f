package com.microteam.base.grounds.service;

import com.microteam.base.entity.grounds.FootballGroundDiscount;


public interface FootballGroundDiscountDaoService {
    // 查询特价，免费场地
//    List<FootballGroundDiscount> findByDiscountAndCodeForPage(String discount, int page, int pageSize, String cityCode, String countryCode);

    // 查询具体打折场地
    FootballGroundDiscount findById(long id);

    // 模糊查询特价场地
//    List<FootballGroundDiscount> findListDiscountGroundsBySearchContentAndCityOrCounty(String discountType, String cityCode, String countryCode, int page, int pageSize, String search);
}
