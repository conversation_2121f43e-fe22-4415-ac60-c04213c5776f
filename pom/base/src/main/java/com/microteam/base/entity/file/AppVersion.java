package com.microteam.base.entity.file;

import com.microteam.base.common.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

@Entity
@Table(name = "app_version")
@Getter
@Setter
public class AppVersion extends BaseEntity implements Serializable {

    @Column(name = "create_user_id")
    private Long createUserId;
    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "create_date", length = 19)
    private Date createDate;
    @Column(name = "update_user_id")
    private Long updateUserId;
    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "update_date", length = 19)
    private Date updateDate;
    @Column(name = "app_name")
    private String appName;
    @Column(name = "versionName")
    private String versionName;
    @Column(name = "versionCode")
    private String versionCode;
    @Column(name = "downloadUrl")
    private String downloadUrl;
    @Column(name = "app_desc")
    private String appDesc;             //关于app的描述
    @Column(name = "upgrade_identifier")
    private String upgradeIdentifier;   //更新标识  1 不升级 2 可选升级 3 强制升级
    @Column(name = "upgrade_type")
    private String upgradeType;         //更新类型  Android   IOS
    @Column(name = "upgrade_info")
    private String upgradeInfo;         // 更新的信息
    @Column(name = "diffUpdate")
    private String diffUpdate;           // 1:apk文件  2:差分包文件
    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "publish_date", length = 19)
    private Date publishDate;

    public Date getCreateDate() {
        if (this.createDate == null) {
            return null;
        }
        return (Date) createDate.clone();
    }

    public void setCreateDate(Date createDate) {
        this.createDate = (Date) createDate.clone();
    }


    public Date getUpdateDate() {
        if (this.updateDate == null) {
            return null;
        }
        return (Date) updateDate.clone();
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = (Date) updateDate.clone();
    }


    public Date getPublishDate() {
        if (this.publishDate == null) {
            return null;
        }
        return (Date) publishDate.clone();
    }

    public void setPublishDate(Date publishDate) {
        this.publishDate = (Date) publishDate.clone();
    }


}
