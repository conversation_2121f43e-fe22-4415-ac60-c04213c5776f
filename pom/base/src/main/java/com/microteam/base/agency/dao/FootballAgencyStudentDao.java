package com.microteam.base.agency.dao;

import com.microteam.base.entity.agency.FootballAgency;
import com.microteam.base.entity.agency.FootballAgencyClass;
import com.microteam.base.entity.agency.FootballAgencyClassCourse;
import com.microteam.base.entity.agency.FootballAgencyStudent;
import com.microteam.base.entity.user.User;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

public interface FootballAgencyStudentDao extends JpaRepository<FootballAgencyStudent, Long>, JpaSpecificationExecutor<FootballAgencyStudent> {
    //查询学生
    @Query("from FootballAgencyStudent as agencyStudent " +
            "where agencyStudent.footballAgency=?1 " +
            "and agencyStudent.footballAgencyClass=?2 " +
            "and agencyStudent.footballAgencyClassCourse=?3 " +
            "and agencyStudent.user=?4 " +
            "and agencyStudent.deleted=false")
    FootballAgencyStudent findStudentByAgencyAndAgencyClassAndAgencyClassCourseAndUser(FootballAgency agency, FootballAgencyClass agencyClass, FootballAgencyClassCourse agencyClassCourse, User user);

    @Query("from FootballAgencyStudent as agencyStudent " +
            "where agencyStudent.footballAgency=?1 " +
            "and agencyStudent.footballAgencyClass=?2 " +
            "and agencyStudent.user=?3 " +
            "and agencyStudent.deleted=false")
    List<FootballAgencyStudent> findStudentByAgencyAndAgencyClassAndUser(FootballAgency agency, FootballAgencyClass agencyClass, User user);

    //查询课程学员名单列表
    @Query("from FootballAgencyStudent as agencyStudent " +
            "left join fetch agencyStudent.user as user " +
            "where agencyStudent.footballAgency=?1 " +
            "and agencyStudent.footballAgencyClass=?2 " +
            "and agencyStudent.footballAgencyClassCourse=?3 " +
            "and agencyStudent.deleted=false")
    List<FootballAgencyStudent> findCourseStudentList(FootballAgency agency, FootballAgencyClass agencyClass, FootballAgencyClassCourse agencyClassCourse, Pageable pageable);

    //查询机构下的学生
    @Query("from FootballAgencyStudent as agencyStudent " +
            "where agencyStudent.footballAgency=?1 " +
            "and agencyStudent.deleted=false ")
    List<FootballAgencyStudent> findStudentByAgency(FootballAgency agency);

    //查询机构下的学生
    @Query("from FootballAgencyStudent as agencyStudent " +
            "left join fetch agencyStudent.user as user " +
            "where agencyStudent.footballAgency=?1 " +
            "and agencyStudent.studentName like ?2 " +
            "and agencyStudent.deleted=false ")
    List<FootballAgencyStudent> findStudentByAgencyAndSearch(FootballAgency agency, String studentName, Pageable pageable);

    @Query("from FootballAgencyStudent as agencyStudent " +
            "left join fetch agencyStudent.user as user " +
            "where agencyStudent.footballAgency=?1 " +
            "and agencyStudent.deleted=false ")
    List<FootballAgencyStudent> findStudentByAgencyForPage(FootballAgency agency, Pageable pageable);

    //判断是否是机构下的学生
    @Query("from FootballAgencyStudent as agencyStudent " +
            "left join fetch agencyStudent.footballAgencyClass as footballAgencyClass " +
            "where agencyStudent.footballAgency=?1 " +
            "and agencyStudent.user=?2 " +
            "and agencyStudent.deleted=false")
    List<FootballAgencyStudent> findStudentByAgencyAndUser(FootballAgency agency, User user);

//    List<FootballAgencyStudent> findStudentByUser(User user, String cityCode, String countyCode, String search);

    //根据Id查询机构下的学员信息
    @Query("from FootballAgencyStudent as agencyStudent " +
            "left join fetch agencyStudent.user as user " +
            "where agencyStudent.id=?1 " +
            "and agencyStudent.deleted=false ")
    FootballAgencyStudent findStudentById(long id);

    //查询机构班级学生数量
    @Query("select distinct new com.microteam.base.entity.agency.FootballAgencyStudent(agencyStudent.footballAgency,agencyStudent.user) " +
            "from FootballAgencyStudent as agencyStudent " +
            "left join agencyStudent.user as user " +
            "left join  agencyStudent.footballAgency as footballAgency " +
            "where agencyStudent.footballAgency=?1 " +
            "and agencyStudent.footballAgencyClass =?2 " +
            "and agencyStudent.deleted=false ")
    List<FootballAgencyStudent> findStudentByAgencyAndClass(FootballAgency agency, FootballAgencyClass agencyClass);

    @Query(" from FootballAgencyStudent as agencyStudent " +
            "left join fetch agencyStudent.user as user " +
            "where agencyStudent.footballAgency = ?1 " +
            "and agencyStudent.footballAgencyClass = ?2 " +
            "and agencyStudent.deleted=false ")
    List<FootballAgencyStudent> findStudentByAgencyAndClassForPage(FootballAgency agency, FootballAgencyClass agencyClass, Pageable pageable);
}
