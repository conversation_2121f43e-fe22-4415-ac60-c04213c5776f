package com.microteam.base.entity.integral;

import com.microteam.base.common.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.io.Serializable;

@Entity
@Table(name = "football_integral_type")
@Getter
@Setter
public class FootballIntegralType extends BaseEntity implements Serializable {

    @Basic
    @Column(name = "name", nullable = false)
    private String name;
    @Basic
    @Column(name = "integral", nullable = false)
    private int integral;
    @Basic
    @Column(name = "type", nullable = false)
    private int type;

}
