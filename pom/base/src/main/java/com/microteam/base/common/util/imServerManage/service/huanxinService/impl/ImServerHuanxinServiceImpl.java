package com.microteam.base.common.util.imServerManage.service.huanxinService.impl;


import com.microteam.base.common.constant.ConstantUserManage;
import com.microteam.base.common.constant.Contants;
import com.microteam.base.common.util.imServerManage.common.ConstantImServerManage;
import com.microteam.base.common.util.imServerManage.pojo.*;
import com.microteam.base.common.util.imServerManage.service.huanxinService.ImServerHuanxinService;
import com.microteam.base.entity.user.ChatsRecordBackuptime;
import com.microteam.base.entity.user.User;
import com.microteam.base.upush.UPushUtil;
import com.microteam.base.user.service.ChatsRecordBackuptimeDaoService;
import org.apache.http.client.HttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.*;
import org.springframework.http.client.ClientHttpRequestFactory;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.http.converter.FormHttpMessageConverter;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.converter.StringHttpMessageConverter;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;
import org.springframework.stereotype.Service;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponents;
import org.springframework.web.util.UriComponentsBuilder;

import java.io.IOException;
import java.net.URI;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.sql.Timestamp;
import java.util.*;

import static org.springframework.http.HttpStatus.FORBIDDEN;

/*环信的IM SERVER 接口service；*/
@Service("imServerHuanxinService")
public class ImServerHuanxinServiceImpl implements ImServerHuanxinService {
    static Logger logger = Logger.getLogger(ImServerHuanxinServiceImpl.class.getName());

    @Autowired
    private ChatsRecordBackuptimeDaoService chatsRecordBackuptimeDaoService;

    @Autowired
    private UPushUtil uPushUtil;

    private ChatsRecordBackuptime updateToken(ChatsRecordBackuptime chatsRecordBackuptime) {
        if (chatsRecordBackuptime == null) {
            chatsRecordBackuptime = new ChatsRecordBackuptime();
        }
        // 向环信更新访问token，创建http
        HttpClient httpClient = HttpClientBuilder.create().build();// import
        ClientHttpRequestFactory requestFactory = new HttpComponentsClientHttpRequestFactory(httpClient); // import
        RestTemplate restTemplate = new RestTemplate(requestFactory); // import
        //创建uri
        UriComponents uriComponents = UriComponentsBuilder.fromUriString(ConstantImServerManage.HuanxinHostName + "/token").build().encode();
        URI uri = uriComponents.toUri();
        //设置数据的格式
        HttpHeaders requestHeaders = new HttpHeaders(); // import
        requestHeaders.setAccept(Collections.singletonList(new MediaType("application", "json"))); // import
        requestHeaders.setContentType(new MediaType("Application", "json"));
        AppImTokenPojo appImTokenPojo = new AppImTokenPojo();
        appImTokenPojo.setClient_id(Contants.ClientId);
        appImTokenPojo.setClient_secret(Contants.ClientSecret);
        appImTokenPojo.setGrant_type(Contants.GrantType);
        // for post方法使用；
        HttpEntity<AppImTokenPojo> requestEntity = new HttpEntity<AppImTokenPojo>(appImTokenPojo, requestHeaders);// import
        List<HttpMessageConverter<?>> messageConverters = new ArrayList<HttpMessageConverter<?>>();
        messageConverters.add(new FormHttpMessageConverter());
        messageConverters.add(new StringHttpMessageConverter());
        messageConverters.add(new MappingJackson2HttpMessageConverter());
        restTemplate.setMessageConverters(messageConverters);
        ResponseEntity<AppImTokenResponseBodyPojo> responseEntity = restTemplate.exchange(uri, HttpMethod.POST, requestEntity, AppImTokenResponseBodyPojo.class);
        if (responseEntity.getStatusCode().value() == 200) {
            AppImTokenResponseBodyPojo bodyContent = responseEntity.getBody();
            // 获取到Im最新的token
            if (bodyContent != null) {
                // 查询备份库token是否存在，不存在则创建 存在就更新
                chatsRecordBackuptime.setBackupTime(System.currentTimeMillis());
                chatsRecordBackuptime.setName(ConstantUserManage.ChatBackUpForToken);
                chatsRecordBackuptime.setUpdateTime(new Timestamp(System.currentTimeMillis()));
                chatsRecordBackuptime.setContent(bodyContent.getAccess_token());
                chatsRecordBackuptime.setDescription("IM_TOKEN_UPDATE");
                chatsRecordBackuptime.setDeleted(false);
                chatsRecordBackuptime.setEnabled(true);
                chatsRecordBackuptimeDaoService.save(chatsRecordBackuptime);
            }
        }
        return chatsRecordBackuptime;
    }


    /**
     * 环信的IM SERVER 接口service；；
     */
    @Override
    public ImResponseEntities[] registerUserImServer(User user) {//String imUsername,String imPassword  throws Exception
        // gerald --更新环信token
        ChatsRecordBackuptime chatsRecordBackuptime = chatsRecordBackuptimeDaoService.findByName(ConstantUserManage.ChatBackUpForToken);
        long time = new Date().getTime() - chatsRecordBackuptime.getUpdateTime().getTime();
        if (time > 2592000000L) {
            updateToken(chatsRecordBackuptime);
        }
        // gerald --更新环信token
        //注册环信用户
        ImResponseEntities[] entitiesArray;
        try {
            HttpClient httpClient = HttpClientBuilder.create().build();//import org.apache.http.client.HttpClient;
            ClientHttpRequestFactory requestFactory = new HttpComponentsClientHttpRequestFactory(httpClient); //import org.springframework.http.client.ClientHttpRequestFactory;
            RestTemplate restTemplate = new RestTemplate(requestFactory); //import org.springframework.web.client.RestTemplate;
            // Set uri path
            UriComponents uriComponents = UriComponentsBuilder.fromUriString(  //import org.springframework.web.util.UriComponents;
                    ConstantImServerManage.HuanxinHostName + "/users").build()
                    .encode();
            URI uri = uriComponents.toUri();
            //创建环信请求对象
            ImUserRegisterRequestBody imUserRegisterRequestBody = new ImUserRegisterRequestBody();
            //java Long类型转换成String ， 不足10位 在前面补0
            String imUsername = ConstantUserManage.getLongTenLetter(user.getId());//imUsername格式；为10位数，“0000000001"
            imUserRegisterRequestBody.setUsername(imUsername);
            imUserRegisterRequestBody.setPassword(user.getImPassWord());
            imUserRegisterRequestBody.setNickname(user.getNickName());  //nickname

            // Set the Accept header
            HttpHeaders requestHeaders = new HttpHeaders(); //import org.springframework.http.HttpHeaders;
            requestHeaders.setAccept(Collections.singletonList(new MediaType("application", "json"))); //import org.springframework.http.MediaType;
            //Content-Type
            requestHeaders.setContentType(new MediaType("Application", "json"));
            requestHeaders.add("Authorization", "Bearer " + chatsRecordBackuptime.getContent());
            //for get方法使用；
            //HttpEntity<?> requestEntity = new HttpEntity<Object>(requestHeaders);//import org.springframework.http.HttpEntity;

            //for POST方法使用；
            HttpEntity<ImUserRegisterRequestBody> requestEntity = new HttpEntity<ImUserRegisterRequestBody>(imUserRegisterRequestBody, requestHeaders);//import org.springframework.http.HttpEntity;

            // Make the HTTP GET request, marshaling the response from JSON to an array of Events

            //FOR get方法使用；
            //ResponseEntity<ResponsebodypojoId[]> responseEntity = restTemplate.exchange(url, HttpMethod.GET, requestEntity, ResponsebodypojoId[].class);
            //ResponsebodypojoId[] events = responseEntity.getBody();

            List<HttpMessageConverter<?>> messageConverters = new ArrayList<HttpMessageConverter<?>>();
            messageConverters.add(new FormHttpMessageConverter());
            messageConverters.add(new StringHttpMessageConverter());
            messageConverters.add(new MappingJackson2HttpMessageConverter());
            //messageConverters.add(new MappingXMLHttpMessageConverter());
            restTemplate.setMessageConverters(messageConverters);

            // Add the String message converter
            //restTemplate.getMessageConverters().add(new StringHttpMessageConverter());
            // Add the Jackson message converter
            //restTemplate.getMessageConverters().add(new MappingJackson2HttpMessageConverter());

            //for POST方法使用；
            ResponseEntity<ImServerResponseBodyPojo> responseEntity = restTemplate.exchange(uri, HttpMethod.POST, requestEntity, ImServerResponseBodyPojo.class);
            if (responseEntity.getStatusCode().value() == 200) {
                ImServerResponseBodyPojo bodyContent = responseEntity.getBody();
                entitiesArray = bodyContent.getEntities();
                if ((entitiesArray.length > 0) && (bodyContent.getApplication().equals("c5368150-9f93-11e4-bba5-49f805fbbcd5")) && (bodyContent.getOrganization().equals("vsteam")) && (bodyContent.getApplicationName().equals("microteam"))) {
                    return entitiesArray;
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            logger.debug(e.getMessage());
        }
        return null;
    }


    /**
     * gerald
     * 环信的IM SERVER 发文本信息给群组成员；
     *
     * @param mode="users" 或 "chatgroups"
     * @param
     * @return
     */
//    @SuppressWarnings("unchecked")
//    @Override
//    public boolean sendTextMessageToMmbers(String mode, String message, List<String> memberArray, String fromImUsername) {
//        //发文本消息
//        try {
//            ChatsRecordBackuptime chatsRecordBackuptime = chatsRecordBackuptimeDaoService.findByName(ConstantUserManage.ChatBackUpForToken);
//            if (chatsRecordBackuptime == null) {
//                chatsRecordBackuptime = new ChatsRecordBackuptime();
//                chatsRecordBackuptime = updateToken(chatsRecordBackuptime);
//            } else {
//                long time = new Date().getTime() - chatsRecordBackuptime.getUpdateTime().getTime();
//                if (time > 2592000000L) {
//                    chatsRecordBackuptime = updateToken(chatsRecordBackuptime);
//                }
//            }
//            // set httpClient instance
//            HttpClient httpClient = HttpClientBuilder.create().build();//import org.apache.http.client.HttpClient;
//            ClientHttpRequestFactory requestFactory = new HttpComponentsClientHttpRequestFactory(httpClient); //import org.springframework.http.client.ClientHttpRequestFactory;
//            RestTemplate restTemplate = new RestTemplate(requestFactory); //import org.springframework.web.client.RestTemplate;
//            // Set uri path
//            UriComponents uriComponents = UriComponentsBuilder.fromUriString(  //import org.springframework.web.util.UriComponents;
//                    ConstantImServerManage.HuanxinHostName + "/messages").build()
//                    // .expand(imGroupid)
//                    .encode();
//
//            URI uri = uriComponents.toUri();
//
//            // Set the Accept header
//            HttpHeaders requestHeaders = new HttpHeaders(); //import org.springframework.http.HttpHeaders;
//            requestHeaders.setAccept(Collections.singletonList(new MediaType("application", "json"))); //import org.springframework.http.MediaType;
//
//            //Content-Type
//            requestHeaders.setContentType(new MediaType("Application", "json"));
//            //requestHeaders.add("Authorization", "Bearer "+Contants.ImAccessToken);// YWMtM1iGIsiLEeSeRB_MoJkpZQAAAU1B6_IUt57ZnHHow5kGTxVdEmpTc2Y0d_g");
//            requestHeaders.add("Authorization", "Bearer " + chatsRecordBackuptime.getContent());
//
//            //for get方法使用；
//            //HttpEntity<?> requestEntity = new HttpEntity<Object>(requestHeaders);//import org.springframework.http.HttpEntity;
//
//            //for POST方法使用；
//            //HttpEntity<ImGroupRegisterRequestBodyPojo> requestEntity = new HttpEntity<ImGroupRegisterRequestBodyPojo>(imGroupRegisterRequestBodyPojo,requestHeaders);//import org.springframework.http.HttpEntity;
//
//            HashMap<String, Object> map = new HashMap<String, Object>();
//
//            map.put("target_type", mode);
//            map.put("target", memberArray);
//
//            HashMap<String, Object> msgMap = new HashMap<String, Object>();
//            msgMap.put("type", "txt");
//            JSONObject messageJson = JSONObject.parseObject(message);
//            String messageContent = messageJson.getString("message");
//            msgMap.put("msg", messageContent);
//
//            map.put("msg", msgMap);
//            //封装ext
//            ObjectMapper mapper = new ObjectMapper();
//            HashMap<String, Object> productMap = mapper.readValue(message, HashMap.class);
//            map.put("ext", productMap);
//            map.put("from", fromImUsername);
//            HttpEntity<HashMap<String, Object>> requestEntity = new HttpEntity<HashMap<String, Object>>(map, requestHeaders);//import org.springframework.http.HttpEntity;
//            // Make the HTTP GET request, marshaling the response from JSON to an array of Events
//            //FOR get方法使用；
//            //ResponseEntity<ResponsebodypojoId[]> responseEntity = restTemplate.exchange(url, HttpMethod.GET, requestEntity, ResponsebodypojoId[].class);
//            //ResponsebodypojoId[] events = responseEntity.getBody();
//            List<HttpMessageConverter<?>> messageConverters = new ArrayList<HttpMessageConverter<?>>();
//            messageConverters.add(new FormHttpMessageConverter());
//            messageConverters.add(new StringHttpMessageConverter());
//            messageConverters.add(new MappingJackson2HttpMessageConverter());
//            //messageConverters.add(new MappingXMLHttpMessageConverter());
//            restTemplate.setMessageConverters(messageConverters);
//            //for POST方法使用；
//            ResponseEntity<ImServerResponseBodyPojoForMapData> responseEntity = restTemplate.exchange(uri, HttpMethod.POST, requestEntity, ImServerResponseBodyPojoForMapData.class);
//            if (responseEntity.getStatusCode().value() == 200) {
//                ImServerResponseBodyPojoForMapData bodyContent = responseEntity.getBody();
//                HashMap<String, Object> datasMap = bodyContent.getData();
//                int count = 0;
//                if ((datasMap != null) && (bodyContent.getApplication().equals("c5368150-9f93-11e4-bba5-49f805fbbcd5")) && (bodyContent.getOrganization().equals("vsteam")) && (bodyContent.getApplicationName().equals("microteam"))) {
//                    for (Map.Entry<String, Object> entry : datasMap.entrySet()) {
//                        if (entry.getValue().equals("success")) {
//                            // System.out.println(entry.getKey()+"--->"+entry.getValue());
//                            count = count + 1;
//                            if (count == memberArray.size()) {
//                                return true;
//                            }
//                        }
//                    }
//                }
//            }
//        } catch (Exception e) {
//            e.printStackTrace();
//            logger.debug(e.getMessage());
//        }
//        return false;
//    }


    /**
     * 环信的IM SERVER 接口service；；
     */
    @Override
    public ImResponseDatas registerGroupImServer(ImGroupRegisterRequestBodyPojo imGroupRegisterRequestBodyPojo) { //需要 desc 和 members;

        try {
            ChatsRecordBackuptime chatsRecordBackuptime = chatsRecordBackuptimeDaoService.findByName(ConstantUserManage.ChatBackUpForToken);
            if (chatsRecordBackuptime == null) {
                chatsRecordBackuptime = new ChatsRecordBackuptime();
                chatsRecordBackuptime = updateToken(chatsRecordBackuptime);
            } else {
                long time = new Date().getTime() - chatsRecordBackuptime.getUpdateTime().getTime();
                if (time > 2592000000L) {
                    chatsRecordBackuptime = updateToken(chatsRecordBackuptime);
                }
            }

            // set httpClient instance
            HttpClient httpClient = HttpClientBuilder.create().build();//import org.apache.http.client.HttpClient;

            ClientHttpRequestFactory requestFactory = new HttpComponentsClientHttpRequestFactory(httpClient); //import org.springframework.http.client.ClientHttpRequestFactory;

            RestTemplate restTemplate = new RestTemplate(requestFactory); //import org.springframework.web.client.RestTemplate;

            // Set uri path
            UriComponents uriComponents = UriComponentsBuilder.fromUriString(  //import org.springframework.web.util.UriComponents;
                    ConstantImServerManage.HuanxinHostName + "/chatgroups").build()
                    //  .expand("42", "21")
                    .encode();

            URI uri = uriComponents.toUri();

            //set json body content

            if (imGroupRegisterRequestBodyPojo == null) {
                return null;
            }
            HashMap<String, Object> map = new HashMap<String, Object>();

            map.put("groupname", imGroupRegisterRequestBodyPojo.getGroupname());
            map.put("desc", imGroupRegisterRequestBodyPojo.getDesc());
            map.put("public", imGroupRegisterRequestBodyPojo.getPUBLIC());
            map.put("maxusers", imGroupRegisterRequestBodyPojo.getMaxusers());
            map.put("approval", imGroupRegisterRequestBodyPojo.getApproval());
            map.put("owner", imGroupRegisterRequestBodyPojo.getOwner());
            if (imGroupRegisterRequestBodyPojo.getMembers().length > 0) {
                String member = imGroupRegisterRequestBodyPojo.getMembers()[0];
                if (member != null && !member.equals(""))
                    map.put("members", imGroupRegisterRequestBodyPojo.getMembers());
            }


            // Set the Accept header
            HttpHeaders requestHeaders = new HttpHeaders(); //import org.springframework.http.HttpHeaders;
            requestHeaders.setAccept(Collections.singletonList(new MediaType("application", "json"))); //import org.springframework.http.MediaType;

            //Content-Type
            requestHeaders.setContentType(new MediaType("Application", "json"));
            //requestHeaders.add("Authorization", "Bearer "+Contants.ImAccessToken);// YWMtM1iGIsiLEeSeRB_MoJkpZQAAAU1B6_IUt57ZnHHow5kGTxVdEmpTc2Y0d_g");
            requestHeaders.add("Authorization", "Bearer " + chatsRecordBackuptime.getContent());

            //for get方法使用；
            //HttpEntity<?> requestEntity = new HttpEntity<Object>(requestHeaders);//import org.springframework.http.HttpEntity;

            //for POST方法使用；
            //HttpEntity<ImGroupRegisterRequestBodyPojo> requestEntity = new HttpEntity<ImGroupRegisterRequestBodyPojo>(imGroupRegisterRequestBodyPojo,requestHeaders);//import org.springframework.http.HttpEntity;

            HttpEntity<HashMap<String, Object>> requestEntity = new HttpEntity<HashMap<String, Object>>(map, requestHeaders);//import org.springframework.http.HttpEntity;

            // Make the HTTP GET request, marshaling the response from JSON to an array of Events

            //FOR get方法使用；
            //ResponseEntity<ResponsebodypojoId[]> responseEntity = restTemplate.exchange(url, HttpMethod.GET, requestEntity, ResponsebodypojoId[].class);
            //ResponsebodypojoId[] events = responseEntity.getBody();

            List<HttpMessageConverter<?>> messageConverters = new ArrayList<HttpMessageConverter<?>>();
            messageConverters.add(new FormHttpMessageConverter());
            messageConverters.add(new StringHttpMessageConverter());
            messageConverters.add(new MappingJackson2HttpMessageConverter());
            //messageConverters.add(new MappingXMLHttpMessageConverter());
            restTemplate.setMessageConverters(messageConverters);

            // Add the String message converter
            //restTemplate.getMessageConverters().add(new StringHttpMessageConverter());
            // Add the Jackson message converter
            //restTemplate.getMessageConverters().add(new MappingJackson2HttpMessageConverter());

            //for POST方法使用；
            ResponseEntity<ImServerResponseBodyPojo> responseEntity = restTemplate.exchange(uri, HttpMethod.POST, requestEntity, ImServerResponseBodyPojo.class);
            ImResponseDatas datasArray;
            if (responseEntity.getStatusCode().value() == 200) {

                ImServerResponseBodyPojo bodyContent = responseEntity.getBody();

                datasArray = bodyContent.getData();

                if ((datasArray != null) && (bodyContent.getApplication().equals("c5368150-9f93-11e4-bba5-49f805fbbcd5")) && (bodyContent.getOrganization().equals("vsteam")) && (bodyContent.getApplicationName().equals("microteam"))) {
                    return datasArray;
                }


            }

        } catch (Exception e) {
            e.printStackTrace();
            logger.debug(e.getMessage());
        }


        return null;
    }

    /**
     * 环信的IM SERVER 添加一个群组成员；
     *
     * @param
     * @param
     * @return
     */
    @Override
    public boolean addGroupMemberOneToImServer(String imGroupid, Long userid) { //需要 desc 和 members;
        //环信增加一个成员:POST  Path : /{org_name}/{app_name}/chatgroups/{group_id}/users/{username}
        if (this.isJoined(imGroupid, userid)) {
            return true;
        }
        try {
            ChatsRecordBackuptime chatsRecordBackuptime = chatsRecordBackuptimeDaoService.findByName(ConstantUserManage.ChatBackUpForToken);
            if (chatsRecordBackuptime == null) {
                chatsRecordBackuptime = new ChatsRecordBackuptime();
                chatsRecordBackuptime = updateToken(chatsRecordBackuptime);
            } else {
                long time = new Date().getTime() - chatsRecordBackuptime.getUpdateTime().getTime();
                if (time > 2592000000L) {
                    chatsRecordBackuptime = updateToken(chatsRecordBackuptime);
                }
            }

            // set httpClient instance
            HttpClient httpClient = HttpClientBuilder.create().build();//import org.apache.http.client.HttpClient;

            ClientHttpRequestFactory requestFactory = new HttpComponentsClientHttpRequestFactory(httpClient); //import org.springframework.http.client.ClientHttpRequestFactory;


            RestTemplate restTemplate = new RestTemplate(requestFactory); //import org.springframework.web.client.RestTemplate;


            String imUsername = ConstantUserManage.getLongTenLetter(userid);

            // Set uri path
            UriComponents uriComponents = UriComponentsBuilder.fromUriString(  //import org.springframework.web.util.UriComponents;
                    ConstantImServerManage.HuanxinHostName + "/chatgroups/{group_id}/users/{imUsername}").build()
                    .expand(imGroupid, imUsername)
                    .encode();

            URI uri = uriComponents.toUri();


            // Set the Accept header
            HttpHeaders requestHeaders = new HttpHeaders(); //import org.springframework.http.HttpHeaders;
            requestHeaders.setAccept(Collections.singletonList(new MediaType("application", "json"))); //import org.springframework.http.MediaType;

            //Content-Type
            requestHeaders.setContentType(new MediaType("Application", "json"));
            //requestHeaders.add("Authorization", "Bearer "+Contants.ImAccessToken);// YWMtM1iGIsiLEeSeRB_MoJkpZQAAAU1B6_IUt57ZnHHow5kGTxVdEmpTc2Y0d_g");
            requestHeaders.add("Authorization", "Bearer " + chatsRecordBackuptime.getContent());

            //for get方法使用；
            //HttpEntity<?> requestEntity = new HttpEntity<Object>(requestHeaders);//import org.springframework.http.HttpEntity;

            //for POST方法使用；
            //HttpEntity<ImGroupRegisterRequestBodyPojo> requestEntity = new HttpEntity<ImGroupRegisterRequestBodyPojo>(imGroupRegisterRequestBodyPojo,requestHeaders);//import org.springframework.http.HttpEntity;

            HttpEntity<?> requestEntity = new HttpEntity<Object>(requestHeaders);//import org.springframework.http.HttpEntity;

            // Make the HTTP GET request, marshaling the response from JSON to an array of Events

            //FOR get方法使用；
            //ResponseEntity<ResponsebodypojoId[]> responseEntity = restTemplate.exchange(url, HttpMethod.GET, requestEntity, ResponsebodypojoId[].class);
            //ResponsebodypojoId[] events = responseEntity.getBody();

            List<HttpMessageConverter<?>> messageConverters = new ArrayList<HttpMessageConverter<?>>();
            messageConverters.add(new FormHttpMessageConverter());
            messageConverters.add(new StringHttpMessageConverter());
            messageConverters.add(new MappingJackson2HttpMessageConverter());
            //messageConverters.add(new MappingXMLHttpMessageConverter());
            restTemplate.setMessageConverters(messageConverters);

            // Add the String message converter
            //restTemplate.getMessageConverters().add(new StringHttpMessageConverter());
            // Add the Jackson message converter
            //restTemplate.getMessageConverters().add(new MappingJackson2HttpMessageConverter());

            //for POST方法使用；
            ResponseEntity<ImServerResponseBodyPojoForAddMemberOne> responseEntity = restTemplate.exchange(uri, HttpMethod.POST, requestEntity, ImServerResponseBodyPojoForAddMemberOne.class);

            if (responseEntity.getStatusCode().value() == 200) {

                ImServerResponseBodyPojoForAddMemberOne bodyContent = responseEntity.getBody();

                ImResponseDataForAddMemberOne datasJson = bodyContent.getData();

                if ((datasJson != null) && (bodyContent.getApplication().equals("c5368150-9f93-11e4-bba5-49f805fbbcd5")) && (bodyContent.getOrganization().equals("vsteam")) && (bodyContent.getApplicationName().equals("microteam"))) {
                               /*    ImResponseEntities json=entitiesArray[0];

     		                      System.out.println("IMbodyContent.uuid="+json.getType()+ " ;" );
     		                      System.out.println("IMbodyContent.type="+json.getUsername()+ " ;" );
     		                      System.out.println("IMbodyContent.created="+json.getActivated()+ " ;" );
     		                      System.out.println("IMbodyContent.username="+json.getCreated()+ " ;" );
     		                      System.out.println("IMbodyContent.activated="+json.getModified()+ " ;" );


     		        	"data" : {
     		            "action" : "add_member",
     		            "result" : true,
     		            "groupid" : "1411816013089",
     		            "user" : "q4xpsfjfvf"
     		          },*/

                    if (datasJson.isResult() && datasJson.getUser().equals(imUsername) && datasJson.getGroupid().equals(imGroupid))
                        return true;
                }


            }


        } catch (Exception e) {
            logger.debug(e.getMessage());
        }


        return false;
    }


    /**
     * 环信的IM SERVER 删除一个 群组成员
     *
     * @param
     * @param
     * @return
     */
    @Override
    public boolean delGroupMemberOneToImServer(String imGroupid, Long userid) { //需要 desc 和 members;

        //环信增加一个成员:POST  Path : /{org_name}/{app_name}/chatgroups/{group_id}/users/{username}

        try {
            ChatsRecordBackuptime chatsRecordBackuptime = chatsRecordBackuptimeDaoService.findByName(ConstantUserManage.ChatBackUpForToken);
            if (chatsRecordBackuptime == null) {
                chatsRecordBackuptime = new ChatsRecordBackuptime();
                chatsRecordBackuptime = updateToken(chatsRecordBackuptime);
            } else {
                long time = new Date().getTime() - chatsRecordBackuptime.getUpdateTime().getTime();
                if (time > 2592000000L) {
                    chatsRecordBackuptime = updateToken(chatsRecordBackuptime);
                }
            }

            // set httpClient instance 删除用户
            String imUser = ConstantUserManage.getLongTenLetter(userid);
            HttpClient httpClient = HttpClientBuilder.create().build();// import
            // org.apache.http.client.HttpClient;
            ClientHttpRequestFactory requestFactory = new HttpComponentsClientHttpRequestFactory(
                    httpClient); // import
            // org.springframework.http.client.ClientHttpRequestFactory;
            RestTemplate restTemplate = new RestTemplate(requestFactory); // import
            // org.springframework.web.client.RestTemplate;
            UriComponents uriComponents = UriComponentsBuilder
                    .fromUriString( // import
                            // org.springframework.web.util.UriComponents;
                            ConstantImServerManage.HuanxinHostName
                                    + "/chatgroups/" + imGroupid + "/users/"
                                    + imUser).build().encode();
            URI uri = uriComponents.toUri();
            // Set the Accept header
            HttpHeaders requestHeaders = new HttpHeaders(); // import
            // org.springframework.http.HttpHeaders;
            requestHeaders.setAccept(Collections.singletonList(new MediaType(
                    "application", "json"))); // import
            // org.springframework.http.MediaType;
            // Content-Type
            requestHeaders.setContentType(new MediaType("Application", "json"));
            requestHeaders.add("Authorization", "Bearer "
                    + chatsRecordBackuptime.getContent());// YWMtM1iGIsiLEeSeRB_MoJkpZQAAAU1B6_IUt57ZnHHow5kGTxVdEmpTc2Y0d_g");
            // for get方法使用；
            HttpEntity<?> requestEntity = new HttpEntity<Object>(requestHeaders);// import
            // org.springframework.http.HttpEntity;
            List<HttpMessageConverter<?>> messageConverters = new ArrayList<HttpMessageConverter<?>>();
            messageConverters.add(new FormHttpMessageConverter());
            messageConverters.add(new StringHttpMessageConverter());
            messageConverters.add(new MappingJackson2HttpMessageConverter());
            // messageConverters.add(new MappingXMLHttpMessageConverter());
            restTemplate.setMessageConverters(messageConverters);
            // FOR get方法使用；
            ResponseEntity<ImDelGroupUserResponsePojo> responseEntity = restTemplate
                    .exchange(uri, HttpMethod.DELETE, requestEntity,
                            ImDelGroupUserResponsePojo.class);
            if (responseEntity.getStatusCode().value() == 200) {
                return true;
            }

            if (responseEntity.getStatusCode().value() == 404) {
                return false;
            }

            if (responseEntity.getStatusCode().value() == 401) {
                return false;
            }


        } catch (HttpClientErrorException hcee) {
            if (hcee.getStatusCode() == FORBIDDEN) {
                return true;
            }
        } catch (Exception e) {
            e.printStackTrace();
            logger.debug(e.getMessage());
        }


        return false;
    }


    //转让群组
    @Override
    public boolean transferGroupOwner(String imGroupid, Long userid) {
        //环信转让群组:POST  Path : /{org_name}/{app_name}/chatgroups/{group_id}
        try {
            ChatsRecordBackuptime chatsRecordBackuptime = chatsRecordBackuptimeDaoService.findByName(ConstantUserManage.ChatBackUpForToken);
            if (chatsRecordBackuptime == null) {
                chatsRecordBackuptime = new ChatsRecordBackuptime();
                chatsRecordBackuptime = updateToken(chatsRecordBackuptime);
            } else {
                long time = new Date().getTime() - chatsRecordBackuptime.getUpdateTime().getTime();
                if (time > 2592000000L) {
                    chatsRecordBackuptime = updateToken(chatsRecordBackuptime);
                }
            }
            //转移群组
            // set httpClient instance 转移群组
//            String imUser = ConstantUserManage.getLongTenLetter(userid);
            HttpClient httpClient = HttpClientBuilder.create().build();// import
            // org.apache.http.client.HttpClient;
            ClientHttpRequestFactory requestFactory = new HttpComponentsClientHttpRequestFactory(
                    httpClient); // import
            // org.springframework.http.client.ClientHttpRequestFactory;
            RestTemplate restTemplate = new RestTemplate(requestFactory); // import
            // org.springframework.web.client.RestTemplate;
            UriComponents uriComponents = UriComponentsBuilder
                    .fromUriString( // import
                            // org.springframework.web.util.UriComponents;
                            ConstantImServerManage.HuanxinHostName
                                    + "/chatgroups/" + imGroupid).build().encode();
            URI uri = uriComponents.toUri();
            ImTransferGroupRequestBody imTransferGroupRequestBody =
                    new ImTransferGroupRequestBody();
            String imUserid = ConstantUserManage.getLongTenLetter(userid);
            imTransferGroupRequestBody.setNewowner(imUserid);
            // Set the Accept header
            HttpHeaders requestHeaders = new HttpHeaders(); // import
            // org.springframework.http.HttpHeaders;
            requestHeaders.setAccept(Collections.singletonList(new MediaType(
                    "application", "json"))); // import
            // org.springframework.http.MediaType;
            // Content-Type
            requestHeaders.setContentType(new MediaType("Application", "json"));
            requestHeaders.add("Authorization", "Bearer "
                    + chatsRecordBackuptime.getContent());
            HttpEntity<ImTransferGroupRequestBody> requestEntity = new HttpEntity<ImTransferGroupRequestBody>(imTransferGroupRequestBody, requestHeaders);// import
            // org.springframework.http.HttpEntity;
            List<HttpMessageConverter<?>> messageConverters = new ArrayList<HttpMessageConverter<?>>();
            messageConverters.add(new FormHttpMessageConverter());
            messageConverters.add(new StringHttpMessageConverter());
            messageConverters.add(new MappingJackson2HttpMessageConverter());
            // messageConverters.add(new MappingXMLHttpMessageConverter());
            restTemplate.setMessageConverters(messageConverters);
            // FOR put方法使用；
            ResponseEntity<ImDelGroupUserResponsePojo> responseEntity = restTemplate
                    .exchange(uri, HttpMethod.PUT, requestEntity,
                            ImDelGroupUserResponsePojo.class);
            if (responseEntity.getStatusCode().value() == 200) {
                return true;
            }


            if (responseEntity.getStatusCode().value() == 401) {
                return false;
            }
        } catch (Exception e) {
            logger.debug(e.getMessage());
        }
        return false;
    }


    /**
     * gerald
     * 环信的IM SERVER 发送透传消息
     *
     * @param mode="users" 或 "chatgroups"
     * @param
     * @return
     */
//    @Override
//    public boolean sendTransparentTextMessageToMembers(String mode, String message, List<String> memberArray, String fromImUsername) {
//        //发文本消息
//        try {
//            ChatsRecordBackuptime chatsRecordBackuptime = chatsRecordBackuptimeDaoService.findByName(ConstantUserManage.ChatBackUpForToken);
//            if (chatsRecordBackuptime == null) {
//                chatsRecordBackuptime = new ChatsRecordBackuptime();
//                chatsRecordBackuptime = updateToken(chatsRecordBackuptime);
//            } else {
//                long time = new Date().getTime() - chatsRecordBackuptime.getUpdateTime().getTime();
//                if (time > 2592000000L) {
//                    chatsRecordBackuptime = updateToken(chatsRecordBackuptime);
//                }
//            }
//
//            // set httpClient instance
//            HttpClient httpClient = HttpClientBuilder.create().build();//import org.apache.http.client.HttpClient;
//
//
//            ClientHttpRequestFactory requestFactory = new HttpComponentsClientHttpRequestFactory(httpClient); //import org.springframework.http.client.ClientHttpRequestFactory;
//
//
//            RestTemplate restTemplate = new RestTemplate(requestFactory); //import org.springframework.web.client.RestTemplate;
//
//            // Set uri path
//            UriComponents uriComponents = UriComponentsBuilder.fromUriString(  //import org.springframework.web.util.UriComponents;
//                    ConstantImServerManage.HuanxinHostName + "/messages").build()
//                    // .expand(imGroupid)
//                    .encode();
//
//            URI uri = uriComponents.toUri();
//
//            // Set the Accept header
//            HttpHeaders requestHeaders = new HttpHeaders(); //import org.springframework.http.HttpHeaders;
//            requestHeaders.setAccept(Collections.singletonList(new MediaType("application", "json"))); //import org.springframework.http.MediaType;
//
//            //Content-Type
//            requestHeaders.setContentType(new MediaType("Application", "json"));
//            //requestHeaders.add("Authorization", "Bearer "+Contants.ImAccessToken);// YWMtM1iGIsiLEeSeRB_MoJkpZQAAAU1B6_IUt57ZnHHow5kGTxVdEmpTc2Y0d_g");
//            requestHeaders.add("Authorization", "Bearer " + chatsRecordBackuptime.getContent());
//
//            //for get方法使用；
//            //HttpEntity<?> requestEntity = new HttpEntity<Object>(requestHeaders);//import org.springframework.http.HttpEntity;
//
//            //for POST方法使用；
//            //HttpEntity<ImGroupRegisterRequestBodyPojo> requestEntity = new HttpEntity<ImGroupRegisterRequestBodyPojo>(imGroupRegisterRequestBodyPojo,requestHeaders);//import org.springframework.http.HttpEntity;
//
//            HashMap<String, Object> map = new HashMap<String, Object>();
//
//            map.put("target_type", mode);
//            map.put("target", memberArray);
//
//            HashMap<String, Object> msgMap = new HashMap<String, Object>();
//            msgMap.put("type", "cmd");
//            msgMap.put("action", message);
//
//            map.put("msg", msgMap);
//            map.put("from", fromImUsername);
//            HttpEntity<HashMap<String, Object>> requestEntity = new HttpEntity<HashMap<String, Object>>(map, requestHeaders);//import org.springframework.http.HttpEntity;
//
//            // Make the HTTP GET request, marshaling the response from JSON to an array of Events
//
//            //FOR get方法使用；
//            //ResponseEntity<ResponsebodypojoId[]> responseEntity = restTemplate.exchange(url, HttpMethod.GET, requestEntity, ResponsebodypojoId[].class);
//            //ResponsebodypojoId[] events = responseEntity.getBody();
//
//            List<HttpMessageConverter<?>> messageConverters = new ArrayList<HttpMessageConverter<?>>();
//            messageConverters.add(new FormHttpMessageConverter());
//            messageConverters.add(new StringHttpMessageConverter());
//            messageConverters.add(new MappingJackson2HttpMessageConverter());
//            //messageConverters.add(new MappingXMLHttpMessageConverter());
//            restTemplate.setMessageConverters(messageConverters);
//
//            //for POST方法使用；
//            ResponseEntity<ImServerResponseBodyPojoForMapData> responseEntity = restTemplate.exchange(uri, HttpMethod.POST, requestEntity, ImServerResponseBodyPojoForMapData.class);
//
//            if (responseEntity.getStatusCode().value() == 200) {
//
//
//                ImServerResponseBodyPojoForMapData bodyContent = responseEntity.getBody();
//
//                HashMap<String, Object> datasMap = bodyContent.getData();
//                int count = 0;
//                if ((datasMap != null) && (bodyContent.getApplication().equals("c5368150-9f93-11e4-bba5-49f805fbbcd5")) && (bodyContent.getOrganization().equals("vsteam")) && (bodyContent.getApplicationName().equals("microteam"))) {
//
//                    for (Map.Entry<String, Object> entry : datasMap.entrySet()) {
//
//                        if (entry.getValue().equals("success")) {
//                            // System.out.println(entry.getKey()+"--->"+entry.getValue());
//                            count = count + 1;
//                            if (count == memberArray.size()) {
//                                return true;
//
//                            }
//                        }
//
//                    }
//                }
//            }
//
//        } catch (Exception e) {
//            logger.debug(e.getMessage());
//        }
//
//        return false;
//    }


    /**
     * gerald
     * 环信的IM SERVER 发送透传扩展消息
     *
     * @param mode="users" 或 "chatgroups"
     * @param
     * @return
     */
//    @SuppressWarnings("unchecked")
//    @Override
//    public boolean sendTransparentTextExtMessageToMmbers(String mode, String message, List<String> memberArray, String fromImUsername) {
//        try {
//            ChatsRecordBackuptime chatsRecordBackuptime = chatsRecordBackuptimeDaoService.findByName(ConstantUserManage.ChatBackUpForToken);
//            if (chatsRecordBackuptime == null) {
//                chatsRecordBackuptime = new ChatsRecordBackuptime();
//                chatsRecordBackuptime = updateToken(chatsRecordBackuptime);
//            } else {
//                long time = new Date().getTime() - chatsRecordBackuptime.getUpdateTime().getTime();
//                if (time > 2592000000L) {
//                    chatsRecordBackuptime = updateToken(chatsRecordBackuptime);
//                }
//            }
//            // set httpClient instance
//            HttpClient httpClient = HttpClientBuilder.create().build();//import org.apache.http.client.HttpClient;
//            ClientHttpRequestFactory requestFactory = new HttpComponentsClientHttpRequestFactory(httpClient); //import org.springframework.http.client.ClientHttpRequestFactory;
//            RestTemplate restTemplate = new RestTemplate(requestFactory); //import org.springframework.web.client.RestTemplate;
//            // Set uri path
//            UriComponents uriComponents = UriComponentsBuilder.fromUriString(  //import org.springframework.web.util.UriComponents;
//                    ConstantImServerManage.HuanxinHostName + "/messages").build()
//                    // .expand(imGroupid)
//                    .encode();
//            URI uri = uriComponents.toUri();
//            // Set the Accept header
//            HttpHeaders requestHeaders = new HttpHeaders(); //import org.springframework.http.HttpHeaders;
//            requestHeaders.setAccept(Collections.singletonList(new MediaType("application", "json"))); //import org.springframework.http.MediaType;
//            //Content-Type
//            requestHeaders.setContentType(new MediaType("Application", "json"));
//            //requestHeaders.add("Authorization", "Bearer "+Contants.ImAccessToken);// YWMtM1iGIsiLEeSeRB_MoJkpZQAAAU1B6_IUt57ZnHHow5kGTxVdEmpTc2Y0d_g");
//            requestHeaders.add("Authorization", "Bearer " + chatsRecordBackuptime.getContent());
//            //for get方法使用；
//            //HttpEntity<?> requestEntity = new HttpEntity<Object>(requestHeaders);//import org.springframework.http.HttpEntity;
//            //for POST方法使用；
//            //HttpEntity<ImGroupRegisterRequestBodyPojo> requestEntity = new HttpEntity<ImGroupRegisterRequestBodyPojo>(imGroupRegisterRequestBodyPojo,requestHeaders);//import org.springframework.http.HttpEntity;
//            HashMap<String, Object> map = new HashMap<String, Object>();
//            map.put("target_type", mode);
//            map.put("target", memberArray);
//            HashMap<String, Object> msgMap = new HashMap<String, Object>();
//            JSONObject messageJson = JSON.parseObject(message);
//            String actionContent = messageJson.getString("action");
//            msgMap.put("type", "cmd");
//            msgMap.put("action", actionContent);
//            map.put("msg", msgMap);
//            //封装ext
//            ObjectMapper mapper = new ObjectMapper();
//            HashMap<String, Object> productMap = mapper.readValue(message, HashMap.class);
//            map.put("ext", productMap);
//            map.put("from", fromImUsername);
//            HttpEntity<HashMap<String, Object>> requestEntity = new HttpEntity<HashMap<String, Object>>(map, requestHeaders);//import org.springframework.http.HttpEntity;
//
//            // Make the HTTP GET request, marshaling the response from JSON to an array of Events
//
//            //FOR get方法使用；
//            //ResponseEntity<ResponsebodypojoId[]> responseEntity = restTemplate.exchange(url, HttpMethod.GET, requestEntity, ResponsebodypojoId[].class);
//            //ResponsebodypojoId[] events = responseEntity.getBody();
//
//            List<HttpMessageConverter<?>> messageConverters = new ArrayList<HttpMessageConverter<?>>();
//            messageConverters.add(new FormHttpMessageConverter());
//            messageConverters.add(new StringHttpMessageConverter());
//            messageConverters.add(new MappingJackson2HttpMessageConverter());
//            //messageConverters.add(new MappingXMLHttpMessageConverter());
//            restTemplate.setMessageConverters(messageConverters);
//            //for POST方法使用；
//            ResponseEntity<ImServerResponseBodyPojoForMapData> responseEntity = restTemplate.exchange(uri, HttpMethod.POST, requestEntity, ImServerResponseBodyPojoForMapData.class);
//            if (responseEntity.getStatusCode().value() == 200) {
//                ImServerResponseBodyPojoForMapData bodyContent = responseEntity.getBody();
//                HashMap<String, Object> datasMap = bodyContent.getData();
//                int count = 0;
//                if ((datasMap != null) && (bodyContent.getApplication().equals("c5368150-9f93-11e4-bba5-49f805fbbcd5")) && (bodyContent.getOrganization().equals("vsteam")) && (bodyContent.getApplicationName().equals("microteam"))) {
//                    for (Map.Entry<String, Object> entry : datasMap.entrySet()) {
//                        if (entry.getValue().equals("success")) {
//                            // System.out.println(entry.getKey()+"--->"+entry.getValue());
//                            count = count + 1;
//                            if (count == memberArray.size()) {
//                                return true;
//                            }
//                        }
//                    }
//                }
//            }
//        } catch (Exception e) {
//            e.printStackTrace();
//            logger.debug(e.getMessage());
//        }
//        return false;
//    }

    public List<String> joinedGroups(Long userid) {

        List<String> groupList = new ArrayList<>();
        try {
            ChatsRecordBackuptime chatsRecordBackuptime = chatsRecordBackuptimeDaoService.findByName(ConstantUserManage.ChatBackUpForToken);
            if (chatsRecordBackuptime == null) {
                chatsRecordBackuptime = new ChatsRecordBackuptime();
                chatsRecordBackuptime = updateToken(chatsRecordBackuptime);
            } else {
                long time = new Date().getTime() - chatsRecordBackuptime.getUpdateTime().getTime();
                if (time > 2592000000L) {
                    chatsRecordBackuptime = updateToken(chatsRecordBackuptime);
                }
            }
            // set httpClient instance
            HttpClient httpClient = HttpClientBuilder.create().build();//import org.apache.http.client.HttpClient;
            ClientHttpRequestFactory requestFactory = new HttpComponentsClientHttpRequestFactory(httpClient); //import org.springframework.http.client.ClientHttpRequestFactory;
            RestTemplate restTemplate = new RestTemplate(requestFactory); //import org.springframework.web.client.RestTemplate;
            StringBuffer sbUserId = new StringBuffer();
            sbUserId.append(userid);
            for (int i = sbUserId.length(); i < 10; i++) {
                sbUserId.insert(0, "0");
            }
            // Set uri path
            UriComponents uriComponents = UriComponentsBuilder.fromUriString(ConstantImServerManage.HuanxinHostName + "/users/" + sbUserId + "/joined_chatgroups").build().encode();
            URI uri = uriComponents.toUri();
            // Set the Accept header
            HttpHeaders requestHeaders = new HttpHeaders(); //import org.springframework.http.HttpHeaders;
            requestHeaders.setAccept(Collections.singletonList(new MediaType("application", "json"))); //import org.springframework.http.MediaType;
            //Content-Type
            requestHeaders.setContentType(new MediaType("Application", "json"));
            //requestHeaders.add("Authorization", "Bearer "+Contants.ImAccessToken);// YWMtM1iGIsiLEeSeRB_MoJkpZQAAAU1B6_IUt57ZnHHow5kGTxVdEmpTc2Y0d_g");
            requestHeaders.add("Authorization", "Bearer " + chatsRecordBackuptime.getContent());
            // Make the HTTP GET request, marshaling the response from JSON to an array of Events

            //FOR get方法使用；
            //ResponseEntity<ResponsebodypojoId[]> responseEntity = restTemplate.exchange(url, HttpMethod.GET, requestEntity, ResponsebodypojoId[].class);
            //ResponsebodypojoId[] events = responseEntity.getBody();

            List<HttpMessageConverter<?>> messageConverters = new ArrayList<HttpMessageConverter<?>>();
            messageConverters.add(new FormHttpMessageConverter());
            messageConverters.add(new StringHttpMessageConverter());
            messageConverters.add(new MappingJackson2HttpMessageConverter());
            //messageConverters.add(new MappingXMLHttpMessageConverter());
            restTemplate.setMessageConverters(messageConverters);

            AppImTokenPojo appImTokenPojo = new AppImTokenPojo();
            appImTokenPojo.setClient_id(Contants.ClientId);
            appImTokenPojo.setClient_secret(Contants.ClientSecret);
            appImTokenPojo.setGrant_type(Contants.GrantType);
            // for post方法使用；
            HttpEntity<AppImTokenPojo> requestEntity = new HttpEntity<AppImTokenPojo>(
                    appImTokenPojo, requestHeaders);// import

            //for POST方法使用；
            ResponseEntity<ImJoinedGroupsPojo> responseEntity = restTemplate.exchange(uri, HttpMethod.GET, requestEntity, ImJoinedGroupsPojo.class);
            if (responseEntity.getStatusCode().value() == 200) {
                ImJoinedGroupsPojo bodyContent = responseEntity.getBody();
                List<ImJoinedGroupsDataPojo> dataList = bodyContent.getData();
                if ((dataList != null) && (bodyContent.getApplication().equals("c5368150-9f93-11e4-bba5-49f805fbbcd5")) && (bodyContent.getOrganization().equals("vsteam")) && (bodyContent.getApplicationName().equals("microteam"))) {
                    for (ImJoinedGroupsDataPojo tmpObj : dataList) {
                        groupList.add(tmpObj.getGroupid());
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            logger.debug(e.getMessage());
        }
        return groupList;
    }

    public boolean isJoined(String groupId, Long userId) {
        List<String> groupList = this.joinedGroups(userId);
        for (String strGroupId : groupList) {
            if (groupId.equals(strGroupId)) {
                return true;
            }
        }
        return false;
    }

    @Override
    public void batchDelImUser(List<User> list) {
        int i = 0;
        for (User user : list) {
            String url = ConstantImServerManage.HuanxinHostName+"/users/"+ConstantUserManage.getLongTenLetter(user.getId());
            RestTemplate restTemplate = new RestTemplate();
            HttpMethod method = HttpMethod.DELETE;
            HttpHeaders requestHeaders = new HttpHeaders();
            requestHeaders.set("Content-Type","application/json");
            requestHeaders.set("Authorization","Bearer YWMtzUNQFgpqEeqZZY31jLEcYQAAAAAAAAAAAAAAAAAAAAHFNoFQn5MR5LulSfgF-7zVAgMAAAFugT9UBQBPGgCSZ_qCnqMFcEz0utVYZBnoKhWrp2jOzj3UjmqAZX00Gw");
            HttpEntity<?> requestEntity = new HttpEntity(requestHeaders);
            ResponseEntity<String> response = restTemplate.exchange(url, method, requestEntity, String.class);
            i++;
            System.out.println(response.getStatusCodeValue()+"--------"+ConstantUserManage.getLongTenLetter(user.getId())+"---------第"+i+"个");
        }
    }

 /*   @Override
    public boolean sendUMengMessage(String message, List<String> memberArray) {
        StringBuilder sb = new StringBuilder();
        for (String member : memberArray) {
            sb.append(member);
            sb.append(",");
        }
        String deviceTokens = sb.substring(0, sb.length() - 1);
        try {
            uPushUtil.sendAndroidListcast(deviceTokens, message);
            uPushUtil.sendIOSListcast(deviceTokens, message);
            return true;
        } catch (Exception e) {
            logger.error(e);
        }
        return false;
    }
*/
         @Override
         public boolean sendUMengMessage(String message, List<String> memberArray) {
             for (String member : memberArray) {
                 try {
                     uPushUtil.sendAndroidListcast(member, message);
                     uPushUtil.sendIOSListcast(member,message);
                 } catch (Exception e) {
                     e.printStackTrace();
                 }
             }
             return true;
         }
}
