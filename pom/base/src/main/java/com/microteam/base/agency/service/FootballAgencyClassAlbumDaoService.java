package com.microteam.base.agency.service;


import com.microteam.base.entity.agency.FootballAgencyClass;
import com.microteam.base.entity.agency.FootballAgencyClassAlbum;

import java.util.List;

public interface FootballAgencyClassAlbumDaoService {
    //查看班级相册是否存在
    FootballAgencyClassAlbum findByAgencyClassIdAndAlbumName(FootballAgencyClass agencyClassId, String albumName);

    FootballAgencyClassAlbum findById(long id);

    //分页查询班级相册
    List<FootballAgencyClassAlbum> findByAgencyClassIdForPage(FootballAgencyClass agencyClassId, int page, int pageSize);
}
