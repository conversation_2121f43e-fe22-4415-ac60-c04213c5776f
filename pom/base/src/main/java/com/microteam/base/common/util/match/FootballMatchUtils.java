package com.microteam.base.common.util.match;


import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.microteam.base.common.constant.FootballContants;
import com.microteam.base.common.util.common.TranslatorUtil;
import com.microteam.base.common.util.jdbc.JDBCUtil;
import com.microteam.base.entity.match.FootballTeamMatchVersus;
import com.microteam.base.entity.team.FootballTeam;
import com.microteam.base.entity.team.FootballTeamGame;
import com.microteam.base.entity.team.FootballTeamGameStatisticsPlayer;
import com.microteam.base.entity.team.FootballTeamTakeNode;
import com.microteam.base.entity.user.UserHeadimg;

import java.lang.reflect.Method;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class FootballMatchUtils {

    public static List<FootballTeamTakeNode> getSumByDataType(List<FootballTeamTakeNode> nodeList, String dataType) {
        List<FootballTeamTakeNode> jArray = new ArrayList<FootballTeamTakeNode>();
        switch (dataType) {
            case "goalsfor":
                for (FootballTeamTakeNode takeNode : nodeList) {
                    boolean state = false;
                    for (FootballTeamTakeNode node : jArray) {
                        if (takeNode.getUser().getId().equals(node.getUser().getId()) && takeNode.getFootballTeam().getId().equals(node.getFootballTeam().getId())) {
                            Long goalsfor = node.getGoalsfor();
                            goalsfor += takeNode.getGoalsfor();
                            node.setGoalsfor(goalsfor);
                            state = true;
                        } // if
                    }
                    if (!state) {
                        jArray.add(takeNode);
                    }
                }
                break;
            case "assist":
                for (FootballTeamTakeNode takeNode : nodeList) {
                    boolean state = false;
                    for (FootballTeamTakeNode node : jArray) {
                        if (takeNode.getUser().getId().equals(node.getUser().getId()) && takeNode.getFootballTeam().getId().equals(node.getFootballTeam().getId())) {
                            Long goalsfor = node.getAssist();
                            goalsfor += takeNode.getAssist();
                            node.setAssist(goalsfor);
                            state = true;
                        } // if
                    }
                    if (!state) {
                        jArray.add(takeNode);
                    }
                }

                break;
            case "fault":
                for (FootballTeamTakeNode takeNode : nodeList) {
                    boolean state = false;
                    for (FootballTeamTakeNode node : jArray) {
                        if (takeNode.getUser().getId().equals(node.getUser().getId()) && takeNode.getFootballTeam().getId().equals(node.getFootballTeam().getId())) {
                            Long goalsfor = node.getFault();
                            goalsfor += takeNode.getFault();
                            node.setFault(goalsfor);
                            state = true;
                        } // if
                    }
                    if (!state) {
                        jArray.add(takeNode);
                    }
                }
                break;
            case "shoot":
                for (FootballTeamTakeNode takeNode : nodeList) {
                    boolean state = false;
                    for (FootballTeamTakeNode node : jArray) {
                        if (takeNode.getUser().getId().equals(node.getUser().getId()) && takeNode.getFootballTeam().getId().equals(node.getFootballTeam().getId())) {
                            Long goalsfor = node.getShoot();
                            goalsfor += takeNode.getShoot();
                            node.setShoot(goalsfor);
                            state = true;
                        } // if
                    }
                    if (!state) {
                        jArray.add(takeNode);
                    }
                }
                break;
            case "shootAside":
                for (FootballTeamTakeNode takeNode : nodeList) {
                    boolean state = false;
                    for (FootballTeamTakeNode node : jArray) {
                        if (takeNode.getUser().getId().equals(node.getUser().getId()) && takeNode.getFootballTeam().getId().equals(node.getFootballTeam().getId())) {
                            Long goalsfor = node.getShootAside();
                            goalsfor += takeNode.getShootAside();
                            node.setShootAside(goalsfor);
                            state = true;
                        } // if
                    }
                    if (!state) {
                        jArray.add(takeNode);
                    }
                }
                break;
            case "waveShot":
                for (FootballTeamTakeNode takeNode : nodeList) {
                    boolean state = false;
                    for (FootballTeamTakeNode node : jArray) {
                        if (takeNode.getUser().getId().equals(node.getUser().getId()) && takeNode.getFootballTeam().getId().equals(node.getFootballTeam().getId())) {
                            Long goalsfor = node.getWaveShot();
                            goalsfor += takeNode.getWaveShot();
                            node.setWaveShot(goalsfor);
                            state = true;
                        } // if
                    }
                    if (!state) {
                        jArray.add(takeNode);
                    }
                }
                break;
            case "holdUp":
                for (FootballTeamTakeNode takeNode : nodeList) {
                    boolean state = false;
                    for (FootballTeamTakeNode node : jArray) {
                        if (takeNode.getUser().getId().equals(node.getUser().getId()) && takeNode.getFootballTeam().getId().equals(node.getFootballTeam().getId())) {
                            Long goalsfor = node.getHoldUp();
                            goalsfor += takeNode.getHoldUp();
                            node.setHoldUp(goalsfor);
                            state = true;
                        } // if
                    }
                    if (!state) {
                        jArray.add(takeNode);
                    }
                }
                break;
            case "excel":
                for (FootballTeamTakeNode takeNode : nodeList) {
                    boolean state = false;
                    for (FootballTeamTakeNode node : jArray) {
                        if (takeNode.getUser().getId().equals(node.getUser().getId()) && takeNode.getFootballTeam().getId().equals(node.getFootballTeam().getId())) {
                            Long goalsfor = node.getExcel();
                            goalsfor += takeNode.getExcel();
                            node.setExcel(goalsfor);
                            state = true;
                        } // if
                    }
                    if (!state) {
                        jArray.add(takeNode);
                    }
                }
                break;
            case "corner":
                for (FootballTeamTakeNode takeNode : nodeList) {
                    boolean state = false;
                    for (FootballTeamTakeNode node : jArray) {
                        if (takeNode.getUser().getId().equals(node.getUser().getId()) && takeNode.getFootballTeam().getId().equals(node.getFootballTeam().getId())) {
                            Long goalsfor = node.getCorner();
                            goalsfor += takeNode.getCorner();
                            node.setCorner(goalsfor);
                            state = true;
                        } // if
                    }
                    if (!state) {
                        jArray.add(takeNode);
                    }
                }
                break;
            case "freeKick":
                for (FootballTeamTakeNode takeNode : nodeList) {
                    boolean state = false;
                    for (FootballTeamTakeNode node : jArray) {
                        if (takeNode.getUser().getId().equals(node.getUser().getId()) && takeNode.getFootballTeam().getId().equals(node.getFootballTeam().getId())) {
                            Long goalsfor = node.getFreeKick();
                            goalsfor += takeNode.getFreeKick();
                            node.setFreeKick(goalsfor);
                            state = true;
                        } // if
                    }
                    if (!state) {
                        jArray.add(takeNode);
                    }
                }
                break;
            case "penaltykick":
                for (FootballTeamTakeNode takeNode : nodeList) {
                    boolean state = false;
                    for (FootballTeamTakeNode node : jArray) {
                        if (takeNode.getUser().getId().equals(node.getUser().getId()) && takeNode.getFootballTeam().getId().equals(node.getFootballTeam().getId())) {
                            Long goalsfor = node.getPenaltyKick();
                            goalsfor += takeNode.getPenaltyKick();
                            node.setPenaltyKick(goalsfor);
                            state = true;
                        } // if
                    }
                    if (!state) {
                        jArray.add(takeNode);
                    }
                }
                break;
            case "redcard":
                for (FootballTeamTakeNode takeNode : nodeList) {
                    boolean state = false;
                    for (FootballTeamTakeNode node : jArray) {
                        if (takeNode.getUser().getId().equals(node.getUser().getId()) && takeNode.getFootballTeam().getId().equals(node.getFootballTeam().getId())) {
                            Long goalsfor = node.getRedCard();
                            goalsfor += takeNode.getRedCard();
                            node.setRedCard(goalsfor);
                            state = true;
                        } // if
                    }
                    if (!state) {
                        jArray.add(takeNode);
                    }
                }
                break;
            case "yellowcard":
                for (FootballTeamTakeNode takeNode : nodeList) {
                    boolean state = false;
                    for (FootballTeamTakeNode node : jArray) {
                        if (takeNode.getUser().getId().equals(node.getUser().getId()) && takeNode.getFootballTeam().getId().equals(node.getFootballTeam().getId())) {
                            Long goalsfor = node.getYellowCard();
                            goalsfor += takeNode.getYellowCard();
                            node.setYellowCard(goalsfor);
                            state = true;
                        } // if
                    }
                    if (!state) {
                        jArray.add(takeNode);
                    }
                }
                break;
            case "ownGoal":
                for (FootballTeamTakeNode takeNode : nodeList) {
                    boolean state = false;
                    for (FootballTeamTakeNode node : jArray) {
                        if (takeNode.getUser().getId().equals(node.getUser().getId()) && takeNode.getFootballTeam().getId().equals(node.getFootballTeam().getId())) {
                            Long goalsfor = node.getOwnGoal();
                            goalsfor += takeNode.getOwnGoal();
                            node.setOwnGoal(goalsfor);
                            state = true;
                        } // if
                    }
                    if (!state) {
                        jArray.add(takeNode);
                    }
                }
                break;
        }

        return jArray;
    }

    /**
     * JSONArray去重处理
     */
    public static JSONArray outTheAgain(JSONArray jArray, String type) {
        JSONArray tempjArray = new JSONArray();
        try {
            if (jArray != null && jArray.size() > 0) {
                for (int i = 0; i < jArray.size(); i++) {
                    int num = 0;
                    if (tempjArray.size() > 0) {
                        for (int j = 0; j < tempjArray.size(); j++) {
                            if (tempjArray.getJSONObject(j).getString(type).equals(jArray.getJSONObject(i).getString(type))) {
                                num = 1;
                            }
                        }
                        if (num == 0) {
                            tempjArray.add(jArray.getJSONObject(i));
                        }
                    } else {
                        tempjArray.add(jArray.getJSONObject(i));
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        return tempjArray;
    }


    /**
     * 判断日程设置是否完毕
     */
    public static boolean isSetAllDay(List<FootballTeamMatchVersus> versusList) {
        boolean result = true;
        if (versusList != null && versusList.size() > 0) {
            for (FootballTeamMatchVersus footballTeamMatchVersus : versusList) {
                if (footballTeamMatchVersus.getMatchTime() == null) {
                    result = false;
                }
            }
        }
        return result;
    }

    //比赛录入数据归零批处理
    public static boolean theGameTakeNodesMarkZero(Long gameId) {
        PreparedStatement pst = null;
        ResultSet rs = null;
        Connection conn = null;
        JDBCUtil.init();
        try {

            conn = JDBCUtil.getConnection();
            conn.setAutoCommit(false);
            //根据类的字段拼接sql
            String sql = "UPDATE football_team_game_takenotes as takenote SET "
                    + "takenote.goals_for = 0,"
                    + "takenote.assist = 0,"
                    + "takenote.fault = 0,"
                    + "takenote.shoot = 0,"
                    + "takenote.shoot_aside = 0,"
                    + "takenote.wave_shot = 0,"
                    + "takenote.hold_up = 0,"
                    + "takenote.excel = 0,"
                    + "takenote.corner = 0,"
                    + "takenote.free_kick = 0,"
                    + "takenote.penaltykick = 0,"
                    + "takenote.redcard = 0,"
                    + "takenote.yellowcard = 0,"
                    + "takenote.own_goal = 0,"
                    + "takenote.menace = 0, "
                    + "takenote.save = 0, "
                    + "takenote.foul = 0, "
                    + "takenote.offSide = 0, "
                    + "takenote.roof = 0, "
                    + "takenote.head = 0, "
                    + "takenote.stopFault = 0, "
                    + "takenote.passFault = 0, "
                    + "takenote.defendFault = 0, "
                    + "takenote.kickEmpty = 0 "
                    + "WHERE takenote.team_game_id = " + gameId;
            //创建预处理
            pst = conn.prepareStatement(sql);
            pst.addBatch();
            //执行
            pst.executeBatch();
            //清空
            pst.clearBatch();
            //手动提交
            conn.commit();
            return true;

        } catch (Exception e) {
            e.printStackTrace();
            return false;
        } finally {
            JDBCUtil.free(rs, pst, conn);
        }
    }


    //比赛录入数据归零批处理
    public static boolean gamesDeleteOfTheMatch(Long matchId) {
        PreparedStatement pst = null;
        ResultSet rs = null;
        Connection conn = null;
        JDBCUtil.init();
        try {

            conn = JDBCUtil.getConnection();
            conn.setAutoCommit(false);
            //根据类的字段拼接sql
            String sql = "UPDATE football_team_game as game SET "
                    + "game.deleted = 1 "
                    + "WHERE game.matchId = " + matchId;
            //创建预处理
            pst = conn.prepareStatement(sql);
            pst.addBatch();
            //执行
            pst.executeBatch();
            //清空
            pst.clearBatch();
            //手动提交
            conn.commit();
            return true;

        } catch (Exception e) {
            e.printStackTrace();
            return false;
        } finally {
            JDBCUtil.free(rs, pst, conn);
        }
    }

    //封装透传消息
    public static String pickExtMessage(FootballTeamGame teamGame, FootballTeam team) {
        JSONObject sendObject = new JSONObject();
        sendObject.put("action", FootballContants.TEAMGAMEEND);
        sendObject.put("teamGameId", teamGame.getId());
        sendObject.put("sendTeamId", team.getId());
        sendObject.put("sendTeamName", team.getTeamName());
        sendObject.put("hostTeam", teamGame.getFootballTeam().getTeamName());
        sendObject.put("guestTeam", teamGame.getOpponent());
        sendObject.put("teamGameStartTime", teamGame.getCompetitionTime().getTime());
        sendObject.put("location", teamGame.getLocation());
        sendObject.put("hostScore", teamGame.getHostScore());
        sendObject.put("guestScore", teamGame.getGuestScore());
        return sendObject.toString();
    }

    public static Map<Long, UserHeadimg> userHeadListToMap(List<UserHeadimg> list) {
        Map<Long, UserHeadimg> userHeadMap = new HashMap<Long, UserHeadimg>();
        for (UserHeadimg userHeadimg : list) {
            userHeadMap.put(userHeadimg.getUser().getId(), userHeadimg);
        }
        return userHeadMap;
    }

    //封装赛后统计关键球员
    public static JSONArray pickKeyPlayer(List<FootballTeamTakeNode> list, Map<Long, UserHeadimg> userImgMap) throws Exception {
        JSONArray array = new JSONArray();
        //射手王(进球)
        array = findMaxNode(list, userImgMap, "goalsfor", array);
        //助攻王
        array = findMaxNode(list, userImgMap, "assist", array);
        //过人王
        array = findMaxNode(list, userImgMap, "excel", array);
        //威胁王
        array = findMaxNode(list, userImgMap, "menace", array);
        //抢断王
        array = findMaxNode(list, userImgMap, "holdUp", array);
        //解围王
        array = findMaxNode(list, userImgMap, "save", array);
        return array;
    }

    public static JSONArray pickKeyPlayer(List<FootballTeamTakeNode> list, List<FootballTeamGameStatisticsPlayer> list1, Map<Long, UserHeadimg> userImgMap) throws Exception {
        JSONArray array = new JSONArray();
        //射手王(进球)
        array = findMaxNode(list, userImgMap, "goalsfor", array);
        //助攻王
        array = findMaxNode(list, userImgMap, "assist", array);
        //传球王
        array = findMaxPlayer(list1, userImgMap, "passBallCounts", array);
        //跑动王
        array = findMaxPlayer(list1, userImgMap, "wholeMoveDistance", array);
        //威胁王
        array = findMaxNode(list, userImgMap, "menace", array);
        //过人王
        array = findMaxNode(list, userImgMap, "excel", array);
        /*//抢断王
        array = findMaxNode(list, userImgMap, "holdUp", array);
        //解围王
        array = findMaxNode(list, userImgMap, "save", array);*/


        return array;
    }

    private static JSONArray findMaxNode(List<FootballTeamTakeNode> list, Map<Long, UserHeadimg> userImgMap, String propertyName, JSONArray array) throws Exception {
        FootballTeamTakeNode node = null;
        Map<String, Object> map = new HashMap<String, Object>();
        if (list != null && list.size() > 0) {
            String getName = "get" + TranslatorUtil.getGetter(propertyName);
            Method getter = list.get(0).getClass().getMethod(getName);
            for (FootballTeamTakeNode footballTeamTakeNode : list) {
                if (node == null) {
                    node = footballTeamTakeNode;
                } else {
                    Long count = getter.invoke(footballTeamTakeNode) == null ? Long.valueOf(0) : (Long) getter.invoke(footballTeamTakeNode);     //所有录入数据都是Long型
                    Long count2 = getter.invoke(node) == null ? Long.valueOf(0) : (Long) getter.invoke(node);

                    if (count > count2) {
                        node = footballTeamTakeNode;
                    } else if (count.equals(count2)) {    //如果相等取录入时间靠后的记录
                        if (footballTeamTakeNode.getCreateTime().getTime() >= node.getCreateTime().getTime()) {
                            node = footballTeamTakeNode;
                        }
                    }
                }
            }

            if (getter.invoke(node) != null && (Long) getter.invoke(node) > 0) {
                map.put("count", getter.invoke(node));
                map.put("nickName", node.getUser().getNickName());
                map.put("dataType", propertyName);
                if (userImgMap.containsKey(node.getUser().getId())) {
                    if (userImgMap.get(node.getUser().getId()).getHeadImgNetUrl() == null || userImgMap.get(node.getUser().getId()).getHeadImgNetUrl().equals("")) {
                        map.put("userImg", "https://www.microteam.cn/vsteam/upload/images/users/randomHeadImg/azhaer.png");
                    } else {
                        map.put("userImg", userImgMap.get(node.getUser().getId()).getHeadImgNetUrl());
                    }
                } else {
                    map.put("userImg", "https://www.microteam.cn/vsteam/upload/images/users/randomHeadImg/azhaer.png");
                }
                if (map.size() > 0) {
                    array.set(array.size(), map);
                }
            }
        }
        return array;
    }

    private static JSONArray findMaxPlayer(List<FootballTeamGameStatisticsPlayer> list, Map<Long, UserHeadimg> userImgMap, String propertyName, JSONArray array) throws Exception {
        FootballTeamGameStatisticsPlayer player = null;
        Map<String, Object> map = new HashMap<>();
        if (list != null && list.size() > 0) {
            String getName = "get" + TranslatorUtil.getGetter(propertyName);
            Method getter = list.get(0).getClass().getMethod(getName);
            Long count = 0L;
            for (FootballTeamGameStatisticsPlayer footballTeamGameStatisticsPlayer : list) {
                Long count1;
                if (player == null) {
                    if (getter.invoke(footballTeamGameStatisticsPlayer) instanceof Integer) {
                        count1 = ((Integer) getter.invoke(footballTeamGameStatisticsPlayer)).longValue();
                    } else {
                        count1 = (Long) getter.invoke(footballTeamGameStatisticsPlayer);
                    }
                    count = count1;
                    player = footballTeamGameStatisticsPlayer;
                } else {
                    if (getter.invoke(footballTeamGameStatisticsPlayer) instanceof Integer) {
                        count1 = ((Integer) getter.invoke(footballTeamGameStatisticsPlayer)).longValue();
                    } else {
                        count1 = (Long) getter.invoke(footballTeamGameStatisticsPlayer);
                    }

                    if (count1 > count) {
                        count = count1;
                        player = footballTeamGameStatisticsPlayer;
                    } else if (count1.equals(count)) {
                        if (footballTeamGameStatisticsPlayer.getCreateTime().getTime() <= player.getCreateTime().getTime()) {
                            count = count1;
                            player = footballTeamGameStatisticsPlayer;
                        }
                    }
                }
            }
            if (count > 0) {
                map.put("count", count);
                map.put("nickName", player.getUser().getNickName());
                map.put("dataType", propertyName);
                if (userImgMap.containsKey(player.getUser().getId())) {
                    map.put("userImg", userImgMap.get(player.getUser().getId()).getHeadImgNetUrl());
                } else {
                    map.put("userImg", "");
                }
                if (map.size() > 0) {
                    array.set(array.size(), map);
                }
            }
        }
        return array;
    }
}
