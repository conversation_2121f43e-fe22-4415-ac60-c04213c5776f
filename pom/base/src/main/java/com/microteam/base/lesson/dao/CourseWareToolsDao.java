package com.microteam.base.lesson.dao;

import com.microteam.base.entity.courseware.CourseWareTools;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;

public interface CourseWareToolsDao extends JpaRepository<CourseWareTools, Long>, JpaSpecificationExecutor<CourseWareTools> {

    @Query(value = "from CourseWareTools as courseWareTools " +
            "left join fetch courseWareTools.tools as tools " +
            "where courseWareTools.coursewareId = (:coursewareId) " +
            "and tools.id = (:toolsId) " +
            "and courseWareTools.deleted = false ")
    CourseWareTools findByCoursewareIdAndToolsId(Long coursewareId, Long toolsId);
}
