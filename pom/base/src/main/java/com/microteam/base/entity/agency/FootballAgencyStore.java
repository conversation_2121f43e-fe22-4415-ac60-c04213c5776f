package com.microteam.base.entity.agency;


import com.microteam.base.entity.user.User;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

import static javax.persistence.GenerationType.IDENTITY;

@Entity
@Table(name = "football_agency_store")
@Getter
@Setter
public class FootballAgencyStore implements Serializable {
    @Id
    @GeneratedValue(strategy = IDENTITY)
    @Column(name = "id", unique = true, nullable = false)
    private Long id;
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "agencyId", nullable = false)
    private FootballAgency footballAgency;
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "userId", nullable = false)
    private User user;
    @Column(name = "store_name", length = 250)
    private String storeName;
    @Column(name = "buy_url", length = 15)
    private String buyUrl;
    @Column(name = "buy_adress", length = 15)
    private String buyAdress;
    @Column(name = "price", length = 15)
    private Long price;
    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "create_time", length = 19)
    private Date createTime;
    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "update_time", length = 19)
    private Date updateTime;
    @Column(name = "deleted", nullable = false)
    private boolean deleted;

}
