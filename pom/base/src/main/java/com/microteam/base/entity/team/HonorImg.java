package com.microteam.base.entity.team;

import com.microteam.base.common.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.io.Serializable;

@Entity
@Table(name = "honor_img")
@Getter
@Setter
public class HonorImg extends BaseEntity implements Serializable {

    @Basic
    @Column(name = "honorId")
    private Long honorId;
    @Basic
    @Column(name = "imgLength")
    private Long imgLength;
    @Basic
    @Column(name = "imgName")
    private String imgName;
    @Basic
    @Column(name = "imgNetUrl")
    private String imgNetUrl;
    @Basic
    @Column(name = "imgUrl")
    private String imgUrl;

}
