//package com.microteam.base.agency.dao.impl;
//
//import com.microteam.base.agency.dao.FootballAgencyLicenseAlbumDao;
//import com.microteam.base.entity.agency.FootballAgency;
//import com.microteam.base.entity.agency.FootballAgencyLicenseAlbum;
//import com.microteam.base.common.base.AbstractHibernateDao;
//import org.apache.log4j.Logger;
//import org.hibernate.query.Query;
//import org.springframework.stereotype.Repository;
//
//import java.util.List;
//
//@Repository("footballAgencyLicenseAlbumDao")
//public class FootballAgencyLicenseAlbumDaoImpl extends
//        AbstractHibernateDao<FootballAgencyLicenseAlbum> implements FootballAgencyLicenseAlbumDao {
//    static Logger logger = Logger.getLogger(FootballAgencyLicenseAlbumDaoImpl.class.getName());
//
//    public FootballAgencyLicenseAlbumDaoImpl() {
//        super();
//
//        setClazz(FootballAgencyLicenseAlbum.class);
//    }
//
//    //查询机构的营业执照
//    @SuppressWarnings("unchecked")
//    @Override
//    public List<FootballAgencyLicenseAlbum> findLicenseByAgency(
//            FootballAgency footballAgency) {
//
//        String hql = "from FootballAgencyLicenseAlbum as agencyLicenseAlbum where agencyLicenseAlbum.footballAgency=? and agencyLicenseAlbum.deleted=false ";
//        Query query = getCurrentSession().createQuery(hql).setParameter(0, footballAgency);
//        List<FootballAgencyLicenseAlbum> list = query.list();
//        return list;
//    }
//
//}
