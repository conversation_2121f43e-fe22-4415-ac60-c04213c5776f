package com.microteam.base.agency.service.impl;

import com.microteam.base.agency.dao.AgencyMessageDao;
import com.microteam.base.agency.service.AgencyMessageDaoService;
import com.microteam.base.entity.agency.AgencyMessage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
public class AgencyMessageDaoServiceImpl implements AgencyMessageDaoService {
    @Autowired
    private AgencyMessageDao dao;

    @Override
    public List<AgencyMessage> findByAgencyIdOrderByCreateTimeForPage(Long agencyId, int page, int pageSize) {
        if (page > 0) {
            page--;
        }
        Pageable pageable = PageRequest.of(page, pageSize);
        List<AgencyMessage> list = dao.findByAgencyIdOrderByCreateTimeForPage(agencyId, pageable);
        if (list == null) {
            return new ArrayList<>();
        }
        return list;
    }

    @Override
    public AgencyMessage save(AgencyMessage agencyMessage) {
        return dao.save(agencyMessage);
    }

}
