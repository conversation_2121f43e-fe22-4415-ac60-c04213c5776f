package com.microteam.base.agency.service.impl;

import com.microteam.base.agency.dao.FootballAgencyAlbumPraiseDao;
import com.microteam.base.entity.agency.FootballAgencyAlbumPraise;
import com.microteam.base.agency.service.FootballAgencyAlbumPraiseDaoService;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service("footballAgencyAlbumPraiseDaoService")
public class FootballAgencyAlbumPraiseDaoServiceImpl implements FootballAgencyAlbumPraiseDaoService {
    static Logger logger = Logger.getLogger(FootballAgencyAlbumPraiseDaoServiceImpl.class.getName());
    @Autowired
    private FootballAgencyAlbumPraiseDao dao;

    @Override
    public List<FootballAgencyAlbumPraise> findByAgencyAlbumId(Long agencyAlbumId) {
        return dao.findByAgencyAlbumId(agencyAlbumId);
    }

    @Override
    public FootballAgencyAlbumPraise findByAgencyAlbumIdAndUserId(Long agencyAlbumId, Long userId) {
        return dao.findByAgencyAlbumIdAndUserId(agencyAlbumId, userId);
    }


}
