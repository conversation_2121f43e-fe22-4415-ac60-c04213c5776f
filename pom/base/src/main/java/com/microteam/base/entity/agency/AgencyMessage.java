package com.microteam.base.entity.agency;


import com.microteam.base.common.base.BaseEntity;
import com.microteam.base.entity.team.FootballTeam;
import com.microteam.base.entity.user.User;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;

@Entity
@Table(name = "agency_message")
@Getter
@Setter
public class AgencyMessage extends BaseEntity {
    @ManyToOne
    @JoinColumn(name = "agencyId", nullable = false)
    private FootballAgency agency;
    @ManyToOne
    @JoinColumn(name = "userId", nullable = false)
    private User user;
    @Column(name = "message")
    private String message;
    @Column(name = "messageType")
    private Integer messageType;
    @ManyToOne
    @JoinColumn(name = "teamId")
    private FootballTeam team;

}
