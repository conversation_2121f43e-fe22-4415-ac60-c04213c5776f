package com.microteam.base.lesson.service.impl;

import com.microteam.base.entity.lesson.LessonImg;
import com.microteam.base.lesson.dao.LessonImgDao;
import com.microteam.base.lesson.service.LessonImgDaoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class LessonImgDaoServiceImpl implements LessonImgDaoService {

    @Autowired
    private LessonImgDao dao;

    @Override
    public List<LessonImg> findByLessonId(Long lessonId) {
        return dao.findByLessonId(lessonId);
    }

    @Override
    public List<LessonImg> saveAll(List<LessonImg> list) {
        return dao.saveAll(list);
    }

    @Override
    public LessonImg save(LessonImg lessonImg) {
        return dao.save(lessonImg);
    }
}
