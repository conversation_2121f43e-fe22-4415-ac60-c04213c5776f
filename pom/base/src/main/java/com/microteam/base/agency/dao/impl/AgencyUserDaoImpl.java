//package com.microteam.base.agency.dao.impl;
//
//import com.microteam.base.agency.dao.AgencyUserDao;
//import com.microteam.base.entity.agency.AgencyUser;
//import com.microteam.base.common.base.AbstractHibernateDao;
//import org.hibernate.query.Query;
//import org.hibernate.type.LongType;
//import org.springframework.stereotype.Repository;
//
//import java.util.List;
//
//@Repository
//public class AgencyUserDaoImpl extends AbstractHibernateDao<AgencyUser> implements AgencyUserDao {
//
//    @Override
//    public AgencyUser findById(long id) {
//        String hql = "from AgencyUser as agencyUser " +
//                " left join fetch agencyUser.agency as agency " +
//                " left join fetch agencyUser.user as user " +
//                " left join fetch agencyUser.role as role " +
//                " where agency.id = (:id) " +
//                " and agencyUser.deleted = false ";
//        Query query = getCurrentSession().createQuery(hql).setParameter("id", id);
//        return (AgencyUser) query.uniqueResult();
//    }
//
//    @Override
//    public AgencyUser findByAgencyIdAndUserIdAndRoleId(Long agencyId, Long userId, Long roleId) {
//        String hql = "from AgencyUser as agencyUser " +
//                " left join fetch agencyUser.agency as agency " +
//                " left join fetch agencyUser.user as user " +
//                " left join fetch agencyUser.role as role " +
//                " where agency.id = (:agencyId) " +
//                " and user.id = (:userId) " +
//                " and role.id = (:roleId) " +
//                " and agencyUser.deleted = false " +
//                " and agency.deleted = false " +
//                " and user.deleted = false ";
//        Query query = getCurrentSession().createQuery(hql)
//                .setParameter("agencyId", agencyId)
//                .setParameter("userId", userId)
//                .setParameter("roleId", roleId)
//                .setMaxResults(1);
//        return (AgencyUser) query.uniqueResult();
//    }
//
//    @Override
//    public Integer findCountByAgencyIdAndRoleId(Long agencyId, Long roleId) {
//        String sql = "select count(1) from agency_user as agencyUser " +
//                " where agencyUser.agencyId = (:agencyId) " +
//                " and agencyUser.roleId = (:roleId) " +
//                " and agencyUser.deleted = 0 ";
//        Query query = getCurrentSession().createNativeQuery(sql).setParameter("agencyId", agencyId).setParameter("roleId", roleId);
//        int count = ((Number) query.uniqueResult()).intValue();
//        return count;
//    }
//
//    @Override
//    public Integer findCountOfStuByAgencyId(Long agencyId) {
//        String sql = "SELECT COUNT(DISTINCT au.userId) AS count " +
//                "FROM agency_user AS au " +
//                "WHERE agencyId = :agencyId " +
//                "AND au.roleId in (1,5) " +
//                "AND au.deleted = 0";
//        Query query = getCurrentSession().createNativeQuery(sql).setParameter("agencyId", agencyId);
//        int count = ((Number) query.uniqueResult()).intValue();
//        return count;
//    }
//
//
//    @Override
//    public AgencyUser findOneByAgencyIdAndUserId(Long agencyId, Long userId) {
//        String hql = "from AgencyUser as agencyUser " +
//                " left join fetch agencyUser.agency as agency " +
//                " left join fetch agencyUser.user as user " +
//                " left join fetch agencyUser.role as role " +
//                " where agency.id = (:agencyId) " +
//                " and user.id = (:userId) " +
//                " and agencyUser.deleted = false " +
//                " order by INSTR('1765234',role.id) ";
//        Query query = getCurrentSession().createQuery(hql)
//                .setParameter("agencyId", agencyId)
//                .setParameter("userId", userId)
//                .setMaxResults(1);
//        return (AgencyUser) query.uniqueResult();
//    }
//
//    @Override
//    public List<AgencyUser> findByAgencyIdAndUserId(Long agencyId, Long userId) {
//        String hql = "from AgencyUser as agencyUser " +
//                " left join fetch agencyUser.agency as agency " +
//                " left join fetch agencyUser.user as user " +
//                " left join fetch agencyUser.role as role " +
//                " where agency.id = (:agencyId) " +
//                " and user.id = (:userId) " +
//                " and agencyUser.deleted = false ";
//        Query query = getCurrentSession().createQuery(hql)
//                .setParameter("agencyId", agencyId)
//                .setParameter("userId", userId);
//        return query.list();
//    }
//
//    @Override
//    public List<AgencyUser> findByAgencyIdAndRoleId(Long agencyId, Long roleId) {
//        String hql = "from AgencyUser as agencyUser " +
//                " left join fetch agencyUser.agency as agency " +
//                " left join fetch agencyUser.user as user " +
//                " left join fetch agencyUser.role as role " +
//                " where agency.id = (:agencyId) " +
//                " and role.id = (:roleId) " +
//                " and agencyUser.deleted = false ";
//        Query query = getCurrentSession().createQuery(hql)
//                .setParameter("agencyId", agencyId)
//                .setParameter("roleId", roleId);
//        return query.list();
//    }
//
//    @Override
//    public List<AgencyUser> findByAgencyIdAndRoleIdForPage(Long agencyId, Long roleId, int page, int pageSize) {
//        String hql = "from AgencyUser as agencyUser " +
//                " left join fetch agencyUser.agency as agency " +
//                " left join fetch agencyUser.user as user " +
//                " left join fetch agencyUser.role as role " +
//                " where agency.id = (:agencyId) " +
//                " and role.id = (:roleId) " +
//                " and agencyUser.deleted = false ";
//        Query query = getCurrentSession().createQuery(hql)
//                .setParameter("agencyId", agencyId)
//                .setParameter("roleId", roleId);
//        query.setFirstResult((page - 1) * pageSize);
//        query.setMaxResults(pageSize);
//        return query.list();
//    }
//
//    @Override
//    public List<AgencyUser> findByUserId(Long userId) {
//        String hql = "from AgencyUser as agencyUser " +
//                " left join fetch agencyUser.agency as agency " +
//                " left join fetch agencyUser.user as user " +
//                " left join fetch agencyUser.role as role " +
//                " where user.id = (:userId) " +
//                " and agencyUser.deleted = false " +
//                " order by agencyUser.createTime desc ";
//        Query query = getCurrentSession().createQuery(hql).setParameter("userId", userId);
//        return query.list();
//    }
//
//    @Override
//    public List<AgencyUser> findYouthAgencyByUserIdAndRoleIdList(Long userId, List<Long> roleIdList) {
//        String hql = "from AgencyUser as agencyUser " +
//                " left join fetch agencyUser.agency as agency " +
//                " left join fetch agencyUser.user as user " +
//                " left join fetch agencyUser.role as role " +
//                " where user.id = (:userId) " +
//                " and agencyUser.deleted = false " +
//                " and role.id in (:roleIdList) " +
//                " and agency.type = 2 " +
//                " order by agencyUser.createTime desc ";
//        Query query = getCurrentSession().createQuery(hql).setParameter("userId", userId).setParameterList("roleIdList", roleIdList);
//        return query.list();
//    }
//
//    @Override
//    public boolean delById(Long id) {
//        String hql = "update AgencyUser as agencyUser set agencyUser.deleted = 1 where agencyUser.id = (:id) and agencyUser.deleted = 0";
//        Query query = getCurrentSession().createQuery(hql).setParameter("id", id);
//        query.executeUpdate();
//        return true;
//    }
//
//    @Override
//    public List<Long> findUserIdListByTeamId(Long teamId) {
//        String sql = "SELECT aUser.userId FROM agency_user AS aUser WHERE EXISTS (SELECT * FROM agency_team AS aTeam WHERE aTeam.agencyId = aUser.agencyId AND aTeam.teamId = :teamId)";
//        Query query = getCurrentSession().createNativeQuery(sql).addScalar("userId",new LongType()).setParameter("teamId",teamId);
//        return query.list();
//    }
//}
