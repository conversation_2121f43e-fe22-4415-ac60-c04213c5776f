package com.microteam.base.agency.dao;

import com.microteam.base.entity.agency.FootballAgencyUserHasCount;
import com.microteam.base.entity.user.User;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;

public interface FootballAgencyUserHasCountDao extends JpaRepository<FootballAgencyUserHasCount, Long>, JpaSpecificationExecutor<FootballAgencyUserHasCount> {
    //查询用户的机构表
    @Query("from FootballAgencyUserHasCount as footballAgencyUserHasCount " +
            "where footballAgencyUserHasCount.user=?1 " +
            "and footballAgencyUserHasCount.deleted=false")
    FootballAgencyUserHasCount findAgencyUserHasCountByUser(User user);
}
