//package com.microteam.base.agency.dao.impl;
//
//import com.microteam.base.agency.dao.FootballAgencyApplicantDao;
//import com.microteam.base.entity.agency.FootballAgencyApplicant;
//import com.microteam.base.common.base.AbstractHibernateDao;
//import org.apache.log4j.Logger;
//import org.hibernate.query.Query;
//import org.springframework.stereotype.Repository;
//
//import java.util.List;
//
//@Repository("footballAgencyApplicantDao")
//public class FootballAgencyApplicantDaoImpl extends AbstractHibernateDao<FootballAgencyApplicant> implements FootballAgencyApplicantDao {
//    static Logger logger = Logger.getLogger(FootballAgencyApplicantDaoImpl.class.getName());
//
//    public FootballAgencyApplicantDaoImpl() {
//        super();
//        setClazz(FootballAgencyApplicant.class);
//    }
//
//    @Override
//    public FootballAgencyApplicant findByAgencyIdAndApplicantId(Long agencyId, Long applicantId) {
//        String hql = "from FootballAgencyApplicant as agencyApplicant " +
//                " left join fetch agencyApplicant.footballAgency as agency " +
//                " left join fetch agencyApplicant.user as user " +
//                " where agency.id = (:agencyId) " +
//                " and user.id = (:applicantId) " +
//                " and agencyApplicant.deleted = false " +
//                " and agency.deleted = false " +
//                " and user.deleted = false ";
//        Query query = getCurrentSession().createQuery(hql)
//                .setParameter("agencyId", agencyId)
//                .setParameter("applicantId", applicantId)
//                .setMaxResults(1);
//        return (FootballAgencyApplicant) query.uniqueResult();
//    }
//
//    @Override
//    public FootballAgencyApplicant findById(long id) {
//        String hql = "from FootballAgencyApplicant as agencyApplicant " +
//                " left join fetch agencyApplicant.footballAgency as agency " +
//                " left join fetch agencyApplicant.user as user " +
//                " where agencyApplicant.id = (:id) " +
//                " and agencyApplicant.deleted = false ";
//        Query query = getCurrentSession().createQuery(hql)
//                .setParameter("id", id)
//                .setMaxResults(1);
//        return (FootballAgencyApplicant) query.uniqueResult();
//    }
//
//    @Override
//    public List<FootballAgencyApplicant> findByAgencyIdForPageOrderByCreateTimeAndAudit(Long agencyId, int page, int pageSize) {
//        String hql = "from FootballAgencyApplicant as agencyApplicant " +
//                "left join fetch agencyApplicant.footballAgency as agency " +
//                "left join fetch agencyApplicant.user as user " +
//                "where agency.id = (:agencyId) " +
//                "and agency.deleted = false " +
//                "and agencyApplicant.deleted = false " +
//                "order by INSTR('312',agencyApplicant.audit) ";
//        Query query = getCurrentSession().createQuery(hql)
//                .setParameter("agencyId", agencyId)
//                .setFirstResult((page - 1) * pageSize)
//                .setMaxResults(pageSize);
//        return query.list();
//    }
//}
