package com.microteam.base.grounds.dao;

import com.microteam.base.entity.grounds.FootballGround;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

public interface FootballGroundDao extends JpaRepository<FootballGround, Long>, JpaSpecificationExecutor<FootballGround> {
    //同时得到FootballGround的内容；
    @Query("from FootballGround as grounds " +
            "left join fetch grounds.user as user " +
            "left join fetch grounds.footballGroundType as footballGroundsType " +
            "left join fetch grounds.groups as groups " +
            "where grounds.groundName=(:groundName) " +
            "and grounds.deleted=false " +
            "and grounds.enabled=true")
    FootballGround findByGroundName(@Param("groundName") String groundName);

    //同时得到FootballGrounds的内容；
    @Query("from FootballGround as grounds " +
            "left join fetch grounds.user as user " +
            "left join fetch grounds.footballGroundType as footballGroundsType " +
            "left join fetch grounds.groups as groups " +
            "where grounds.id=(:id) " +
            "and grounds.deleted=false " +
            "and grounds.enabled=true")
    FootballGround findById(@Param("id") long id);

    //gerald 根据code查询所有场地
//    List<FootballGround> findByCodeForPage(String cityCode, String countryCode, int page, int pageSize);

    //模糊关键字查询场地
//    List<FootballGround> findByCodeAndSearchForPage(String cityCode, String countryCode, int page, int pageSize, String search);

    //同时得到USER的FootballGround的内容；
    @Query("from FootballGround as grounds " +
            "left join fetch grounds.footballGroundType as footballGroundsType " +
            "where grounds.user.id = (:userId) " +
            "and grounds.deleted=false " +
            "and grounds.enabled=true")
    List<FootballGround> findByUserIdForPage(@Param("userId") Long userId, Pageable pageable);

    @Query("from FootballGround as grounds " +
            "left join fetch grounds.footballGroundType as footballGroundsType " +
            "left join fetch grounds.groups as groups " +
            "left join fetch grounds.user as user " +
            "where grounds.countryCode=(:countryCode) " +
            "and grounds.deleted=false " +
            "order by grounds.createTime desc")
    List<FootballGround> findByCountryCodeForPage(@Param("countryCode") String countryCode, Pageable pageable);

}