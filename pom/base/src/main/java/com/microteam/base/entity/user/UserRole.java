package com.microteam.base.entity.user;


import com.microteam.base.common.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;
import java.io.Serializable;

@Entity
@Table(name = "user_role")
@Getter
@Setter
public class UserRole extends BaseEntity implements Serializable {

    @ManyToOne
    @JoinColumn(name = "rolesId", nullable = false)
    private Roles roles;
    @ManyToOne
    @JoinColumn(name = "userId", nullable = false)
    private User user;
    @Column(name = "creator", nullable = false)
    private long creator;

}
