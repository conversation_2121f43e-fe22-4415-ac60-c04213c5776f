package com.microteam.base.common.pojo;

import com.microteam.base.common.pojo.team.KickStatePojo;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

@Getter
@Setter
public class UserHardWarePojo implements Serializable {
    private static final long serialVersionUID = 1L;
    private long teamId;
    private long userHardwareId;
    private long userHardwareDataId;
    private long teamGameId;
    private long hardwarePracticeId;
    private int isTrainning;
    private String identification;  //
    private String rightIdentification;
    private String hardwareName;
    private String versionNumber;
    private String hardwareMac;
    private long kickBallStartTime;
    private long rightKickBallStartTime;
    private int[] kickBallData;
    private int[] rightKickBallData;
    private List<KickStatePojo> kickState;
    private List<KickStatePojo> rightKickState;
    private String practiceName;
    private long practiceTime;
    private long velocity;
    private long velocityTime;
    private Integer energyValue;


    private List<UserHardWareStepPojo> highMoveData;
    private List<UserHardWareStepPojo> midMoveData;
    private List<UserHardWareStepPojo> lowMoveData;
    private List<UserHardWareStepPojo> normalMoveData;

    private List<UserHardWareStepPojo> rightHighMoveData;
    private List<UserHardWareStepPojo> rightMidMoveData;
    private List<UserHardWareStepPojo> rightLowMoveData;
    private List<UserHardWareStepPojo> rightNormalMoveData;

    private int highMoveCount;//左脚高速运动次数
    private int midMoveCount;//左脚中速运动次数
    private int lowMoveCount;//左脚低速运动次数
    private int normalMoveCount;//左脚步行运动次数

    private int rightHighMoveCount;//右脚高速运动次数
    private int rightMidMoveCount;//右脚中速运动次数
    private int rightLowMoveCount;//右脚低速运动次数
    private int rightNormalMoveCount;//右脚步行运动次数

    private int exteriorData;//外脚背数据
    private int instepKickingData;//正脚背数据
    private int archData;//脚弓数据
    private int tiptoeData;//脚尖数据
    private int heelData;//脚后跟数据
    private int soleFootData;//脚底数据

    private int rightExteriorData;    //右脚外脚背数据
    private int rightInstepKickingData;   //右脚正脚背数据
    private int rightArchData;  //右脚脚弓数据
    private int rightTiptoeData;   //右脚脚尖数据
    private int rightHeelData;  //右脚脚后跟数据
    private int rightSoleFootData;  //右脚脚底数据
}
