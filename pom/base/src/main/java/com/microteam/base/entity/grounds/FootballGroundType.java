package com.microteam.base.entity.grounds;


import com.microteam.base.common.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.io.Serializable;

@Entity
@Table(name = "football_ground_type")
@Getter
@Setter
public class FootballGroundType extends BaseEntity implements Serializable {

    @Column(name = "typeName", nullable = false, length = 50)
    private String typeName;
    @Column(name = "description")
    private String description;

}
