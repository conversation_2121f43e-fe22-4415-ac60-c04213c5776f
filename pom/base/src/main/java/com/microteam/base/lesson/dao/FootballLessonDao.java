package com.microteam.base.lesson.dao;


import com.microteam.base.entity.lesson.FootballLesson;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.Date;
import java.util.List;

public interface FootballLessonDao extends JpaRepository<FootballLesson, Long>, JpaSpecificationExecutor<FootballLesson> {

    @Query("from FootballLesson as lesson " +
            "where lesson.userId = (:userId) " +
            "and lesson.deleted = false " +
            "order by lesson.publishTime desc")
    List<FootballLesson> findByUserIdForPage(@Param("userId") Long userId, Pageable pageable);

    @Query("from FootballLesson as lesson " +
            "where lesson.id = (:id) " +
            "and lesson.deleted=false ")
    FootballLesson findById(@Param("id") long id);

    @Query("from FootballLesson as lesson " +
            "where lesson.id = (:lessonId) " +
            "and lesson.userId = (:userId) " +
            "and lesson.deleted=false ")
    List<FootballLesson> findByLessonIdAndUserId(@Param("lessonId") Long lessonId, @Param("userId") Long userId);

    @Query("from FootballLesson as lesson " +
            "where lesson.openness=1 " +
            "and lesson.deleted=false")
    List<FootballLesson> findOpenness();

    //查询未完成的课程(当前时间-课程开始时间)绝对值  asc排序
    @Query("from FootballLesson as lesson " +
            "where lesson.userId=:userId " +
            "and lesson.status <> 3 " +
            "and lesson.status <> 4 " +
            "and lesson.status <> 5 " +
            "and lesson.deleted=false " +
            "order by ABS(lesson.startTime-CURRENT_TIMESTAMP) asc")
    List<FootballLesson> findUnfinishedByUserId(@Param("userId") Long userId);

//    List<FootballLesson> findByUserIdAndStatusAndTimeForPage(Long userId, String status, String sortTime, int page, int pageSize);

    @Query("from FootballLesson as lesson " +
            "where lesson.userId = (:userId) " +
            "and lesson.deleted = false " +
            "and (lesson.status=3 " +
            "or lesson.status=4) " +
            "order by lesson.startTime desc")
    List<FootballLesson> findFinishedByUserIdForPage(@Param("userId") Long userId, Pageable pageable);

    @Query("from FootballLesson as lesson " +
            "where lesson.userId = (:userId) " +
            "and lesson.deleted = false " +
            "and lesson.status<>3 " +
            "and lesson.status<>4 " +
            "and lesson.status<>5 " +
            "order by lesson.startTime")
    List<FootballLesson> findUnfinishedByUserIdForPage(@Param("userId") Long userId, Pageable pageable);

    @Query(value = "SELECT lesson.* " +
            "FROM football_lesson as lesson " +
            "RIGHT JOIN football_lesson_enroll as enroll " +
            "ON enroll.lessonId = lesson.id " +
            "WHERE enroll.userId = :userId  " +
            "AND lesson.status != 3 " +
            "AND lesson.status != 4 " +
            "AND lesson.startTime>(:date) " +
            "AND lesson.startTime<(:lastDay) " +
            "AND lesson.deleted = 0 " +
            "ORDER BY lesson.createTime ", nativeQuery = true)
    List<FootballLesson> findUnfinishedByUserIdAndStartTimeForDay(@Param("userId") Long userId, @Param("date") Date date, @Param("lastDay") Date lastDay);

    @Query(value = "SELECT lesson.* "
            + "FROM football_lesson AS lesson "
            + "RIGHT JOIN football_team_lesson AS teamLesson ON lesson.id = teamLesson.lessonId "
            + "WHERE teamLesson.teamId IN (:teamIdList) "
            + "AND DATE_FORMAT(lesson.startTime,'%Y-%m-%d') >= DATE_FORMAT(NOW(),'%Y-%m-%d') "
            + "AND lesson.deleted = 0 "
            + "order by lesson.startTime "
            + "LIMIT 1", nativeQuery = true)
    FootballLesson findNearestByTeamIdList(@Param("teamIdList") List<Long> teamIdList);

    @Query(value = "SELECT DISTINCT lesson.* " +
            "FROM football_lesson as lesson " +
            "left join football_team_lesson as teamLesson " +
            "on lesson.id = teamLesson.lessonId " +
            "WHERE lesson.userId = :userId " +
            "AND lesson.startTime>=:zero " +
            "AND lesson.startTime<=:lastDay " +
            "AND teamLesson.teamId IN (:teamIdList) " +
            "AND lesson.deleted = 0 " +
            "ORDER BY lesson.startTime ASC", nativeQuery = true)
    List<FootballLesson> findByUserIdAndTeamIdListAndTimeForDay(@Param("teamIdList") Long userId, @Param("teamIdList") List<Long> teamIdList,
                                                                @Param("zero") Date zero, @Param("lastDay") Date lastDay);

    @Query(value = "select count(DISTINCT b.lessonId) " +
            "from agency_team as a " +
            "right join football_team_lesson as b " +
            "on a.teamId = b.teamId " +
            "left join football_lesson as c " +
            "on b.lessonId = c.id " +
            "where c.userId = (:userId) " +
            "and a.agencyId = (:agencyId) ", nativeQuery = true)
    int findCountByUserIdAndAgencyId(@Param("userId") Long userId, @Param("agencyId") Long agencyId);

    @Query("from FootballLesson as lesson " +
            "where lesson.id <> :lessonId " +
            "and lesson.userId = :userId " +
            "and lesson.status < 4 " +
            "and lesson.status > 0 " +
            "and lesson.deleted = false")
    List<FootballLesson> findNotOfLessonByUserId(@Param("lessonId") Long lessonId, @Param("userId") Long userId);

    @Query("from FootballLesson as lesson " +
            " where lesson.id <> :lessonId " +
            " and lesson.userId = :userId " +
            " and lesson.status > 0 " +
            " and lesson.status < 4 " +
            " and lesson.deleted = false ")
    List<FootballLesson> findByUserIdExceptLessonId(@Param("lessonId") Long userId, @Param("lessonId") Long lessonId);

    @Query("from FootballLesson as lesson " +
            "where lesson.id in (:idList) " +
            "and lesson.userId = (:userId) " +
            "and lesson.deleted = false " +
            "order by lesson.publishTime desc")
    List<FootballLesson> findLessonByIdListAndUserId(@Param("idList") List<Long> idList, @Param("userId") Long userId);

    @Query("from FootballLesson as lesson " +
            "where lesson.id in (:idList) " +
            "and lesson.deleted = false " +
            "order by lesson.startTime desc")
    List<FootballLesson> findByIdList(@Param("idList") List<Long> idList);

    @Query(value = "SELECT lesson.* " +
            "FROM football_lesson as lesson " +
            "LEFT JOIN football_lesson_enroll as enroll " +
            "ON enroll.lessonId = lesson.id " +
            "WHERE enroll.userId = :userId " +
            "AND enroll.teamId = :teamId " +
            "AND enroll.deleted = 0 " +
            "AND (YEAR(lesson.startTime) = :year " +
            "AND MONTH(lesson.startTime) = :month)", nativeQuery = true)
    List<FootballLesson> findByUserIdOfTimeSlot(@Param("userId") Long userId, @Param("teamId") Long teamId, @Param("year") int year, @Param("month") int month);

    @Query(" from FootballLesson as lesson " +
            " where lesson.status = 0 " +
            " and lesson.deleted = 0 " +
            " and lesson.startTime < (:date) ")
    List<FootballLesson> findUnStarted(@Param("date") Date date);

    @Query("from FootballLesson as lesson " +
            "where lesson.id in (:idList) " +
            "and lesson.deleted = false " +
            "order by lesson.publishTime desc")
    List<FootballLesson> findByIdListForPage(@Param("idList") List<Long> idList, Pageable pageable);

    @Query("from FootballLesson as lesson " +
            "where lesson.id in (:idList) " +
            "and lesson.deleted = false " +
            "order by lesson.publishTime desc")
    List<FootballLesson> findByIdListOrderByPublishTime(@Param("idList") List<Long> idList);

    @Query("delete from FootballLesson as lesson " +
            "where lesson.id in (:idList) ")
    @Modifying
    int deleteByIdList(@Param("idList") List<Long> idList);

    @Query(value = "SELECT lesson.userId " +
            "FROM football_lesson AS lesson " +
            "WHERE lesson.id IN (:lessonIdList)", nativeQuery = true)
    List<Long> findUserIdListByLessonIdList(@Param("lessonIdList") List<Long> lessonIdList);

    @Query("update FootballLesson as lesson " +
            "set lesson.status=3 " +
            "where lesson.id=(:id) " +
            "and lesson.deleted=false ")
    @Modifying
    int updateStatusById(@Param("id")Long id);

//    @Query(value = "select lesson.*" +
//            " from football_lesson as lesson" +
//            " right join football_team_lesson as teamLesson" +
//            " on lesson.id = teamLesson.lessonId " +
//            " where teamLesson.teamId in (:teamIdList) " +
//            " and lesson.status != 3 " +
//            " and lesson.status != 4 " +
//            " and lesson.deleted = 0 " +
//            " and lesson.startTime>(:firstDate) " +
//            " and lesson.startTime<(:secondDate) " +
//            " order by lesson.createTime ", nativeQuery = true)
//    List<FootballLesson> findUnfinishedByTeamIdListAndStartTimeForDay(@Param("teamIdList") List<Long> teamIdList, @Param("firstDate") Date firstDate, @Param("secondDate") Date secondDate);
}
