package com.microteam.base.agency.service;

import com.microteam.base.entity.agency.FootballAgency;
import com.microteam.base.entity.agency.FootballAgencyClass;
import com.microteam.base.entity.agency.FootballAgencyClassCourse;
import com.microteam.base.entity.agency.FootballAgencyStudent;
import com.microteam.base.entity.user.User;

import java.util.List;

public interface FootballAgencyStudentDaoService {
    //查询学生
    FootballAgencyStudent findStudentByAgencyAndAgencyClassAndAgencyClassCourseAndUser(FootballAgency agency, FootballAgencyClass agencyClass, FootballAgencyClassCourse agencyClassCourse, User user);

    List<FootballAgencyStudent> findStudentByAgencyAndAgencyClassAndUser(FootballAgency agency, FootballAgencyClass agencyClass, User user);

    //查询课程学员名单列表
    List<FootballAgencyStudent> findCourseStudentList(FootballAgency agency, FootballAgencyClass agencyClass, FootballAgencyClassCourse agencyClassCourse, int pageSize, int page);

    //查询机构下的学生
    List<FootballAgencyStudent> findStudentByAgency(FootballAgency agency);

//    List<FootballAgencyStudent> findStudentByUser(User user, String cityCode, String countyCode, String search);

    //判断是否是机构下的学生
    List<FootballAgencyStudent> findStudentByAgencyAndUser(FootballAgency agency, User user);

    //查询机构下的学生
    List<FootballAgencyStudent> findStudentByAgencyAndSearch(FootballAgency agency, int pageSize, int page, String studentName);

    List<FootballAgencyStudent> findStudentByAgencyForPage(FootballAgency agency, int pageSize, int page);

    //根据Id查询机构下的学员信息
    FootballAgencyStudent findStudentById(long id);

    //查询机构班级学生数量
    List<FootballAgencyStudent> findStudentByAgencyAndClass(FootballAgency agency, FootballAgencyClass agencyClass);

    List<FootballAgencyStudent> findStudentByAgencyAndClassForPage(FootballAgency agency, FootballAgencyClass agencyClass, int page, int pageSize);
}
