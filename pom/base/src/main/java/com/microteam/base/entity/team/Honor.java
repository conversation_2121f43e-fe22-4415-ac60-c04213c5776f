package com.microteam.base.entity.team;

import com.microteam.base.common.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

@Entity
@Table(name = "honor")
@Getter
@Setter
public class Honor extends BaseEntity implements Serializable {

    @Basic
    @Column(name = "teamId", nullable = false)
    private Long teamId;
    @Basic
    @Column(name = "level")
    private Integer level;
    @Basic
    @Column(name = "ranking")
    private Integer ranking;
    @Basic
    @Column(name = "name")
    private String name;
    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "time")
    private Date time;
    @Basic
    @Column(name = "description")
    private String description;

    public Date getTime() {
        if (this.time == null) {
            return null;
        }
        return (Date) time.clone();
    }

    public void setTime(Date time) {
        this.time = (Date) time.clone();
    }


}
