package com.microteam.base.common.util.common;


import com.microteam.base.entity.match.FootballTeamMatch;
import com.microteam.base.entity.team.*;
import com.microteam.base.entity.user.User;
import com.microteam.base.entity.user.UserHardwareData;
import com.microteam.base.entity.user.UserHardwarePractice;
import com.microteam.base.entity.user.UserHeadimg;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.*;

public class TranslatorUtil {

    private TranslatorUtil() {

    }

    public static List<Long> getIdList(List list, Class<?> clz) {
        if (list == null) {
            return new ArrayList<>();
        }
        List<Long> result = new ArrayList<>();
        try {
            Method method = clz.getMethod("getId");
            for (Object object : list) {
                Long id = (Long) method.invoke(object);
                result.add(id);
            }
        } catch (NoSuchMethodException | IllegalAccessException | InvocationTargetException e) {
            e.printStackTrace();
        }
        return result;
    }

    public static Map<String, List<UserHardwareData>> listToMap(List<UserHardwareData> list) {
        Map<String, List<UserHardwareData>> map = new HashMap<>();
        if (list == null || list.isEmpty()) {
            return map;
        }
        for (UserHardwareData data : list) {
            putListMap(map, data.getUserHardware().getHardwareMac(), data);
        }
        return map;
    }

    /**
     * @param key  字段名
     * @param list 需要打包的list
     * @param type 打包类型，1：Map<Object,Object> 2:Map<Object,List<Object>>
     * @return 结果集
     */
    public static <T> Map<?, ?> listToMap(String key, List<T> list, int type) throws Exception {
//        Map<Integer, Apple> appleMap = appleList.stream().collect(Collectors.toMap(Apple::getId, a -> a,(k1, k2)->k1));
        if (list == null || list.isEmpty()) {
            return new HashMap<>();
        }
        //getter方法
        key = "get" + getGetter(key);
        /*Method getter = list.get(0).getClass().getMethod(key);*/
        //结果集
        Map<Object, Object> map = new LinkedHashMap<>();
        //遍历list生成map
        for (Object value : list) {
            Method getter = value.getClass().getMethod(key);
            Object rowKey = getter.invoke(value);
            if (type == 1) {
                map.put(rowKey, value);
            } else if (type == 2) {
                putListMap(map, rowKey, value);
            }
        }
        return map;
    }

    public static Map<Long, FootballTeamUser> teamUserListToMap(List<FootballTeamUser> list) {
        Map<Long, FootballTeamUser> map = new HashMap<Long, FootballTeamUser>();
        for (FootballTeamUser teamUser : list) {
            map.put(teamUser.getUser().getId(), teamUser);
        }
        return map;
    }

    public static String getGetter(String fieldName) {
        fieldName = fieldName.substring(0, 1).toUpperCase() + fieldName.substring(1);
        return fieldName;
    }

    public static Map<Object, List<Object>> listToMap(List<Object> keyList, List<Object> valueList) throws NoSuchMethodException, InvocationTargetException, IllegalAccessException {
        //fieldName
        StringBuffer sbFieldName = new StringBuffer(keyList.get(0).getClass().getName());
        //首字母大写并加上"get"
        String fieldName = "get" + sbFieldName.substring(0, 1).toUpperCase().substring(1);
        //getter方法
        Method method = valueList.get(0).getClass().getMethod(fieldName);
        //结果集
        Map<Object, List<Object>> map = new HashMap<>();
        //遍历keyList，根据key来生成结果
        for (Object key : keyList) {
            for (Object value : valueList) {
                //默认key为value的字段之一,使用反射调用getter方法
                Object keyInValue = method.invoke(value);
                //如果匹配，则进入插入结果集的逻辑
                if (key == keyInValue) {
                    putListMap(map, key, value);
                }
            }
        }
        return map;
    }

    public static Map<?, ?> listToMap(List<?> valueList, String fieldName) throws NoSuchMethodException, InvocationTargetException, IllegalAccessException {
        //fieldName
        fieldName = "get" + fieldName.substring(0, 1).toUpperCase().substring(1);
        //class
        Class clz = valueList.get(0).getClass();
        //getter方法
        Method method = clz.getMethod(fieldName);
        //List去重
        Set set = new HashSet<>(valueList);
        //结果集
        Map<Object, Object> map = new HashMap<>();
        for (Object value : set) {
            Object key = method.invoke(value);
            map.put(key, value);
        }
        return map;
    }

    private static void putListMap(Map map, Object key, Object value) {
        if (map.containsKey(key)) {
            ((List) map.get(key)).add(value);
        } else {
            List<Object> tmpList = new ArrayList<>();
            tmpList.add(value);
            map.put(key, tmpList);
        }
    }

    public static Map<FootballTeam, List<FootballTeamGame>> translateListToMap(List<FootballTeam> teamList, List<FootballTeamGame> teamGameList) {
        Map<FootballTeam, List<FootballTeamGame>> map = new HashMap<>();
        for (FootballTeam team : teamList) {
            for (FootballTeamGame teamGame : teamGameList) {
                if (teamGame.getFootballTeam().getTeamName().equals(team.getTeamName()) || teamGame.getOpponent().equals(team.getTeamName())) {
                    putListMap(map, team, teamGame);
                }
            }
        }
        return map;
    }

    public static Map<FootballTeamGame, Long> isHostTeam(List<FootballTeam> teamList, List<FootballTeamGame> teamGameList) {
        Map<FootballTeamGame, Long> resultMap = new HashMap<>();
        for (FootballTeamGame teamGame : teamGameList) {
            for (FootballTeam team : teamList) {
                //1代表主队，2代表客队，3代表同时参加两个队
                if (teamGame.getFootballTeam().getTeamName().equals(team.getTeamName())) {
                    putMap(resultMap, teamGame, 1L);
                }
                if (teamGame.getOpponent().equals(team.getTeamName())) {
                    putMap(resultMap, teamGame, 2L);
                }
            }
        }
        return resultMap;
    }

    private static void putMap(Map<FootballTeamGame, Long> map, FootballTeamGame key, Long value) {
        if (map.containsKey(key)) {
            if (!map.get(key).equals(value) && map.get(key) != 3L) {
                map.put(key, 3L);
            }
        } else {
            map.put(key, value);
        }
    }

    public static Map<String, FootballTeam> teamListToMap(List<FootballTeam> teamList) {
        Map<String, FootballTeam> teamMap = new HashMap<>();
        for (FootballTeam team : teamList) {
            String teamName = team.getTeamName();
            teamMap.put(teamName, team);
        }
        return teamMap;
    }

    public static Map<FootballTeamGame, List<UserHardwareData>> dataListToMap(List<UserHardwareData> dataList) {
        Map<FootballTeamGame, List<UserHardwareData>> dataMap = new HashMap<>();
        for (UserHardwareData data : dataList) {
            FootballTeamGame key = data.getFootballTeamGame();
            putListMap(dataMap, key, data);
        }
        return dataMap;
    }

    public static Map<FootballTeam, PoloShirt> poloShirtListToMap(List<PoloShirt> poloShirtList) {
        Map<FootballTeam, PoloShirt> map = new HashMap<>();
        for (PoloShirt poloShirt : poloShirtList) {
            map.put(poloShirt.getFootballTeam(), poloShirt);
        }
        return map;
    }

    public static Map<FootballTeamGame, FootballTeamGameStatisticsPlayer> statisticsToMap(List<FootballTeamGameStatisticsPlayer> statisticsPlayerList) {
        Map<FootballTeamGame, FootballTeamGameStatisticsPlayer> map = new HashMap<>();
        for (FootballTeamGameStatisticsPlayer statisticsPlayer : statisticsPlayerList) {
            map.put(statisticsPlayer.getFootballTeamGame(), statisticsPlayer);
        }
        return map;
    }

    public static Map<FootballTeam, FootballTeamGameStatisticsPlayer> statisticsToTeamMap(List<FootballTeamGameStatisticsPlayer> statisticsPlayerList) {
        Map<FootballTeam, FootballTeamGameStatisticsPlayer> map = new HashMap<>();
        for (FootballTeamGameStatisticsPlayer statisticsPlayer : statisticsPlayerList) {
            map.put(statisticsPlayer.getFootballTeam(), statisticsPlayer);
        }
        return map;
    }

    public static Map<FootballTeamGame, List<FootballTeamGameStatisticsPlayer>> statisticsListToMap(List<FootballTeamGameStatisticsPlayer> statisticsPlayerList) {
        Map<FootballTeamGame, List<FootballTeamGameStatisticsPlayer>> map = new HashMap<>();
        for (FootballTeamGameStatisticsPlayer tmpObj : statisticsPlayerList) {
            FootballTeamGame key = tmpObj.getFootballTeamGame();
            putListMap(map, key, tmpObj);
        }
        return map;
    }

    public static Map<FootballTeamTakeNode, List<FootballTeamTakeNode>> takeNodeListToMap(List<FootballTeamTakeNode> takeNodeList) {
        Map<FootballTeamTakeNode, List<FootballTeamTakeNode>> map = new HashMap<>();
        for (FootballTeamTakeNode takeNode : takeNodeList) {
            FootballTeamTakeNode key = new FootballTeamTakeNode();
            key.setFootballTeam(takeNode.getFootballTeam());
            key.setFootballTeamGame(takeNode.getFootballTeamGame());
            key.setUser(takeNode.getUser());
            putListMap(map, key, takeNode);
        }
        return map;
    }

    public static Map<UserHardwarePractice, List<UserHardwareData>> practiceDataListToMap(List<UserHardwareData> dataList) {
        Map<UserHardwarePractice, List<UserHardwareData>> map = new HashMap<>();
        for (UserHardwareData data : dataList) {
            UserHardwarePractice key = data.getUserHardwarePractice();
            putListMap(map, key, data);
        }
        return map;
    }

    public static Map<UserHardwarePractice, FootballTeamGameStatisticsPlayer> practiceStatisticsToMap(List<FootballTeamGameStatisticsPlayer> statisticsList) {
        Map<UserHardwarePractice, FootballTeamGameStatisticsPlayer> map = new HashMap<>();
        for (FootballTeamGameStatisticsPlayer tmp : statisticsList) {
            map.put(tmp.getUserHardwarePractice(), tmp);
        }
        return map;
    }

    public static List<Long> teamGamesToIdList(List<FootballTeamGame> gameList, List<Long> matchIdList) {
        if (gameList != null && gameList.size() > 0) {
            for (FootballTeamGame game : gameList) {
                if (game.getMatchId() != null) {
                    matchIdList.add(game.getMatchId());
                }
            }
        }
        return matchIdList;
    }

    public static Map<Long, FootballTeamMatch> matchListToMap(List<FootballTeamMatch> matchList, Map<Long, FootballTeamMatch> matchMap) {
        if (matchList != null && matchList.size() > 0) {
            for (FootballTeamMatch match : matchList) {
                matchMap.put(match.getId(), match);
            }
        }
        return matchMap;
    }

    public static Map<String, List<FootballTeamGame>> gameListToMap(List<FootballTeamGame> gameList) {
        Map<String, List<FootballTeamGame>> resultMap = new HashMap<>();
        if (gameList == null) {
            gameList = new ArrayList<>();
        }
        for (FootballTeamGame game : gameList) {
            putListMap(resultMap, game.getFootballTeam().getTeamName(), game);
            putListMap(resultMap, game.getOpponent(), game);
        }
        return resultMap;
    }

    public static Map<Long, FootballTeamGameEnroll> enrollListToMap(List<FootballTeamGameEnroll> enrollList) {
        Map<Long, FootballTeamGameEnroll> result = new HashMap<>();
        if (enrollList == null) {
            return result;
        }
        for (FootballTeamGameEnroll enroll : enrollList) {
            User user = enroll.getUser();
            result.put(user.getId(), enroll);
        }
        return result;
    }

    public static Map<Long, Long> teamIdMap(List<FootballTeamGameStatisticsPlayer> statisticsPlayerList) {
        Map<Long, Long> map = new HashMap<>();
        for (FootballTeamGameStatisticsPlayer statisticsPlayer : statisticsPlayerList) {
            map.put(statisticsPlayer.getFootballTeamGame().getId(), statisticsPlayer.getFootballTeam().getId());
        }
        return map;
    }

    public static Map<Long, UserHeadimg> userHeadListToMap(List<UserHeadimg> list) {
        Map<Long, UserHeadimg> userHeadMap = new HashMap<>();
        for (UserHeadimg userHeadimg : list) {
            userHeadMap.put(userHeadimg.getUser().getId(), userHeadimg);
        }
        return userHeadMap;
    }

    public static List<User> teamUserToUsers(List<FootballTeamUser> teamUsers) {
        List<User> users = new ArrayList<>();
        if (teamUsers != null && teamUsers.size() > 0) {
            for (FootballTeamUser teamUser : teamUsers) {
                users.add(teamUser.getUser());
            }
        }
        return users;
    }
}
