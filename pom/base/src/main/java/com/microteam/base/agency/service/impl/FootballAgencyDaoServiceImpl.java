package com.microteam.base.agency.service.impl;

import com.microteam.base.agency.dao.FootballAgencyDao;
import com.microteam.base.agency.service.AgencyUserDaoService;
import com.microteam.base.agency.service.FootballAgencyDaoService;
import com.microteam.base.entity.agency.AgencyUser;
import com.microteam.base.entity.agency.FootballAgency;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service("footballAgencyDaoService")
public class FootballAgencyDaoServiceImpl implements FootballAgencyDaoService {
    static Logger logger = Logger.getLogger(FootballAgencyDaoServiceImpl.class.getName());
    @Autowired
    private FootballAgencyDao dao;
    @Autowired
    private AgencyUserDaoService agencyUserDaoService;

    public FootballAgencyDaoServiceImpl() {
        super();
    }

    //根据Id查询机构
    @Override
    public FootballAgency findById(long id) {
        return dao.findById(id);
    }

//    @Override
//    public List<FootballAgency> findByCodeAndSearchForPage(String cityCode, String countyCode, int page, int pageSize, String search) {
//        return dao.findByCodeAndSearchForPage(cityCode, countyCode, page, pageSize, search);
//    }

//    @Override
//    public List<FootballAgency> findAgencyListByPageAndMy(String cityCode, String countyCode, int page, int pageSize, String search, User user) {
//        return dao.findAgencyListByPageAndMy(cityCode, countyCode, page, pageSize, search, user);
//    }

//    @Override
//    public List<FootballAgency> findAgencyListByPageAndMyInfo(String cityCode, String countyCode, int page, int pageSize, String search, String audit, User user) {
//        return this.dao.findAgencyListByPageAndMyInfo(cityCode, countyCode, page, pageSize, search, audit, user);
//    }

//    @Override
//    public List<FootballAgency> findAgencyListByPageAndMyInfoAndType(String cityCode, String countyCode, int page, int pageSize, String search, String audit, User user, Integer type) {
//        return this.dao.findAgencyListByPageAndMyInfoAndType(cityCode, countyCode, page, pageSize, search, audit, user, type);
//    }

//    @Override
//    public List<FootballAgency> findAgencyListByPageAndNotMy(String cityCode, String countyCode, int page, int pageSize, String search, User user) {
//        return dao.findAgencyListByPageAndNotMy(cityCode, countyCode, page, pageSize, search, user);
//    }

    @Override
    public List<FootballAgency> findYouthAgencyListByIdList(List<Long> idList) {
        return dao.findYouthAgencyListByIdList(idList);
    }

    @Override
    public List<FootballAgency> findAgencyListUserId(int page, int pageSize,Long userId) {
        Pageable pageable = PageRequest.of((page - 1) * pageSize, pageSize);
        List<FootballAgency> list = dao.findAgencyListUserId(userId,pageable);
        if(list !=null && list.size()>0){
            return list;
        }
        return new ArrayList<>();
    }

    @Override
    public List<FootballAgency> findAgencyListNoUserId(int page, int pageSize,Long userId) {
        Pageable pageable = PageRequest.of((page - 1) * pageSize, pageSize);
        List<FootballAgency> list = dao.findAgencyListNoUserId(userId,pageable);
        if(list !=null && list.size()>0){
            return list;
        }
        return new ArrayList<>();
    }

    @Override
    public List<FootballAgency> findAgencyListAll(int page, int pageSize, String cityCode, String countyCode, String search, Integer audit, Integer type) {
       //List<FootballAgency> list = dao.findAgencyListAll(page, pageSize, cityCode, countyCode, search, audit, type);
//        Sort sort = Sort.by(Sort.Direction.DESC, "createTime");

        Sort sort = Sort.by(Sort.Direction.DESC);
        Pageable pageable = PageRequest.of((page - 1) * pageSize, pageSize, sort);
        List<FootballAgency> list = dao.findAgencyListAll(search,pageable);
        if(list !=null && list.size()>0){
            return list;
        }
        return new ArrayList<>();
    }

//    @Override
//    public List<FootballAgency> findAgencyListAboutMeOrNot(Long userId, int page, int pageSize, String cityCode, String countyCode, String search, Integer audit, Integer type,boolean isAboutMe) {
//        List<FootballAgency> list = dao.findAgencyListAboutMeOrNot(userId, page, pageSize, cityCode, countyCode, search, audit, type,isAboutMe);
//        if (list != null && list.size() > 0) {
//            return list;
//        }
//        return new ArrayList<>();
//    }

//    @Override
//    public List<FootballAgency> findAgencyListAll(int page, int pageSize, String cityCode, String countyCode, String search, Integer audit, Integer type) {
//        List<FootballAgency> list = dao.findAgencyListAll(page, pageSize, cityCode, countyCode, search, audit, type);
//        if (list != null && list.size() > 0) {
//            return list;
//        }
//        return new ArrayList<>();
//    }


    @Override
    public List<FootballAgency> findJoinedByUserId(Long userId) {
        List<FootballAgency> list = new ArrayList<>();
        List<AgencyUser> agencyUserList = agencyUserDaoService.findByUserIdOrderByRoleId(userId);
        for (AgencyUser agencyUser : agencyUserList) {
            list.add(agencyUser.getAgency());
        }
        return list;
    }

    @Override
    public FootballAgency findLastAgency() {
        int page = 1;
        int pageSize = 1;
        Sort sort = Sort.by(Sort.Direction.DESC, "createTime");
        Pageable pageable = PageRequest.of((page - 1) * pageSize, pageSize, sort);
        List<FootballAgency> list = dao.findFootballAgency(pageable);
        if (list != null && !list.isEmpty()) {
            return list.get(0);
        }
        return null;
    }

}
