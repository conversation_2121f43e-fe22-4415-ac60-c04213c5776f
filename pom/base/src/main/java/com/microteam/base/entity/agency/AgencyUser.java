package com.microteam.base.entity.agency;

import com.microteam.base.common.base.BaseEntity;
import com.microteam.base.entity.user.User;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Entity;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import java.io.Serializable;

@Entity
@Table(name = "agency_user")
@Getter
@Setter
public class AgencyUser extends BaseEntity implements Serializable {

    @ManyToOne
    @JoinColumn(name = "agencyId", nullable = false)
    private FootballAgency agency;
    @ManyToOne
    @JoinColumn(name = "userId", nullable = false)
    private User user;
    @ManyToOne
    @JoinColumn(name = "roleId", nullable = false)
    private AgencyRole role;

}
