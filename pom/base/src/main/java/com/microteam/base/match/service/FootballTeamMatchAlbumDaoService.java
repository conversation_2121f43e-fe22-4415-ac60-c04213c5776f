package com.microteam.base.match.service;


import com.microteam.base.entity.match.FootballTeamMatchAlbum;

import java.util.List;

public interface FootballTeamMatchAlbumDaoService {
    //查询赛事相册是否存在
    FootballTeamMatchAlbum findByMatchIdAndAlbumName(Long matchId, String albumName);

    //分页查询赛事相册
    List<FootballTeamMatchAlbum> findAlbumByMatchAndPage(Long matchId, int page, int pageSize);

    FootballTeamMatchAlbum findById(long id);
}
