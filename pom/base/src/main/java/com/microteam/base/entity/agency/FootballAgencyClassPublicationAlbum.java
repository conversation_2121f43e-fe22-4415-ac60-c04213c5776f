package com.microteam.base.entity.agency;


import com.microteam.base.common.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;
import java.io.Serializable;

@Entity
@Table(name = "football_agency_class_publication_album")
@Getter
@Setter
public class FootballAgencyClassPublicationAlbum extends BaseEntity implements Serializable {

    @ManyToOne
    @JoinColumn(name = "publicationId", nullable = false)
    private FootballAgencyClassPublication footballAgencyClassPublication;
    @Column(name = "imgNetUrl", length = 250)
    private String imgNetUrl;
    @Column(name = "imgUrl", length = 250)
    private String imgUrl;
    @Column(name = "imgName", length = 50)
    private String imgName;
    @Column(name = "imgLength")
    private Long imgLength;

}
