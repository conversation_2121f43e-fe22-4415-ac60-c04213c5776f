package com.microteam.base.grounds.service.impl;

import com.microteam.base.grounds.dao.FootballGroundAlbumDao;
import com.microteam.base.entity.grounds.FootballGroundAlbum;
import com.microteam.base.grounds.service.FootballGroundAlbumDaoService;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;


@Service("footballGroundAlbumDaoService")
public class FootballGroundAlbumDaoServiceImpl implements FootballGroundAlbumDaoService {
    static Logger logger = Logger.getLogger(FootballGroundAlbumDaoServiceImpl.class.getName());

    @Resource(name = "footballGroundAlbumDao")
    private FootballGroundAlbumDao dao;

    @Override
    public List<FootballGroundAlbum> findByUserId(Long userId) {
        return dao.findByUserId(userId);
    }

    @Override
    public List<FootballGroundAlbum> findByGroundId(Long groundId) {
        return dao.findByGroundId(groundId);
    }

    @Override
    public FootballGroundAlbum save(FootballGroundAlbum footballGroundAlbum) {
        return dao.save(footballGroundAlbum);
    }
}
