package com.microteam.base.entity.user;


import com.microteam.base.common.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;
import java.io.Serializable;

@Entity
@Table(name = "user_hardware_practice")
@Getter
@Setter
public class UserHardwarePractice extends BaseEntity implements Serializable {

    @ManyToOne
    @JoinColumn(name = "userId")
    private User user;
    @Column(name = "practiceName", length = 100)
    private String practiceName;
    @Column(name = "hardwareIdentification", length = 100)
    private String hardwareIdentification;

}
