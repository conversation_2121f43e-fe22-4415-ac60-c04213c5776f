package com.microteam.base.lesson.service;


import com.microteam.base.entity.lesson.FootballDataType;

import java.util.List;

public interface FootballDataTypeDaoService {

    //查询所有的数据类型
    List<FootballDataType> findAllDataType();

    List<FootballDataType> findByIdList(List<Long> idList);

    List<FootballDataType> findByLessonId(Long lessonId);

    List<FootballDataType> findByLessonIdOrderByType(Long lessonId);

    List<FootballDataType> findByTypeList(List<Integer> type);

    List<FootballDataType> findByCoursewareId(Long coursewareId);

    FootballDataType findByTypeIntro(String typeIntro);
}
