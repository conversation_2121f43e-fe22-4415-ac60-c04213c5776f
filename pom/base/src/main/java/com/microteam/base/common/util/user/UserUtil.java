package com.microteam.base.common.util.user;

import com.microteam.base.common.base.FinalList;
import com.microteam.base.common.constant.ConstantUserManage;
import com.microteam.base.common.pojo.MyAuthenticator;
import com.microteam.base.common.pojo.user.AppUserRegisterRequestBodyPojo;
import com.microteam.base.common.util.imServerManage.service.ImServerService;
import com.microteam.base.common.util.security.GenerateToken;
import com.microteam.base.common.util.security.PasswordMD5Encoder;
import com.microteam.base.entity.telMailValiManage.TelMailValiInfo;
import com.microteam.base.entity.user.*;
import com.microteam.base.telMailValiManage.service.MobileMessageDaoService;
import com.microteam.base.user.service.*;
import com.sun.mail.util.MailSSLSocketFactory;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.mail.Session;
import javax.mail.Transport;
import javax.mail.internet.InternetAddress;
import javax.mail.internet.MimeMessage;
import java.sql.Timestamp;
import java.util.Calendar;
import java.util.List;
import java.util.Properties;
import java.util.Random;

@Component
public class UserUtil {

    static Logger logger = Logger.getLogger(UserUtil.class.getName());
    @Autowired
    private UserDaoService userDaoService;
    @Autowired
    private MobileMessageDaoService mobileMessageDaoService;
    @Autowired
    private TokensDaoService tokensDaoService;
    @Autowired
    private UserHeadimgDaoService userHeadimgDaoService;
    @Autowired
    private UserHeadimgRandomDaoService userHeadimgRandomDaoService;
    @Autowired
    private ImServerService imServerService;
    @Autowired
    private RolesDaoService rolesDaoService;
    @Autowired
    private UserRoleDaoService userRoleDaoService;
    @Autowired
    private DeviceInfoDaoService deviceInfoDaoService;
    @Autowired
    private UserRegisterDaoService userRegisterDaoService;
    @Autowired
    private UserHeadImgUtil userHeadImgUtil;

    // 检查，用户名不空，唯一名称，是否已经注册，密码长度是否>=6）
    public String checkUsernameIsValid(String username, String password) {
        if (password.length() < 6)
            return "PasswordFormatError";
        //国际版的用户名格式是：区号-手机号，区号长不同，故作户名长度是不等长的。这里取消用取消用户长度限际。Modified by zhongxin.lin 2018-10-31
        //if (username.length() != 11)
        //    return "UsernameFormatError";
        if (userDaoService.findIncludeEnabledByUserName(username) != null) {// 已同名处理；包含禁止使用的用户名也在查找内；
            return "UsernameIsNotEmpty";
        }
        return "UsernameIsEmpty";
    }

    public boolean phoneValidation(String userName, long localTime, String validateMess) {
        try {
            TelMailValiInfo mobileMessage = mobileMessageDaoService.findByValiAccount(userName);
            // 手机没有验证过
            if (mobileMessage == null) {
                return false;
            }
            long endTime = mobileMessage.getValiEndTime();
            if (endTime < localTime) {
                // 2分钟失效
                return false;
            }
            String message = mobileMessage.getValiMessage();
            return message.equals(validateMess);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }

    public Boolean registerRepition(AppUserRegisterRequestBodyPojo pojo) {
        if (pojo == null) {
            return false;
        }
        try {
            User user = userDaoService.findByUserName(pojo.getUserName());
            // gerald 新增第三方注册绑定
            if (user != null) {
                if ("wechat".equals(pojo.getClientMode())) {
                    if (null != user.getBindWeixin() && !"".equals(user.getBindWeixin())) {
                        return false;
                    }
                }
                if ("qq".equals(pojo.getClientMode())) {
                    if (null != user.getBindQq() && !"".equals(user.getBindQq())) {
                        return false;
                    }
                }
                if ("weibo".equals(pojo.getClientMode())) {
                    if (null != user.getBindWeibo() && !"".equals(user.getBindWeibo())) {
                        return false;
                    }
                }
                if ("wechatsubscription".equals(pojo.getClientMode())) {
                    if (null != user.getBindWeChatSubscripUser() && !"".equals(user.getBindWeChatSubscripUser())) {
                        return false;
                    }
                }
            }
        } catch (Exception e) {
            return false;
        }
        return true;
    }

    public Boolean register(AppUserRegisterRequestBodyPojo pojo) {
        if (pojo == null) {
            return false;
        }
        try {
            // md5+salt 加密
            String password = pojo.getPassWord();
            String salt = ConstantUserManage.MD5Salt;
            String algorithm = ConstantUserManage.MD5Algorithm;
            PasswordMD5Encoder encoderMd5 = new PasswordMD5Encoder(salt, algorithm);
            String encodePassword = encoderMd5.encode(password);
            User user = userDaoService.findByUserName(pojo.getUserName());
            if (user == null) {
                Tokens tokens = new Tokens();
                Timestamp timestamp = new Timestamp(System.currentTimeMillis());
                tokens.setCreateTime(timestamp);
                tokens.setTokensStartTime(timestamp);
                Timestamp timestampEnd = new Timestamp(System.currentTimeMillis() + ConstantUserManage.TokenTenYears);// 10年时间
                tokens.setTokensEndTime(timestampEnd);
                // TOKEN generate start
                String accessToken = GenerateToken.getInstance().generateToken(encodePassword, true); // =true;生成的token随时间变化；
                // String token2 = new
                // TokenProcessor().generateToken("password",false);
                // //=false;生成的token不随时间变化；
                tokens.setAccessToken(accessToken);
                // TOKEN generate end
                tokens.setDeleted(false);
                tokens.setEnabled(true);
                tokens.setUpdateTime(new Timestamp(System.currentTimeMillis()));
                tokensDaoService.save(tokens);
                // 先写table user表；后写注册表table user_Register和device_info；
                // User user=new user();
                user = new User();
                user.setTokens(tokens);
                user.setUserName(pojo.getUserName());
                timestamp = new Timestamp(System.currentTimeMillis());
                // string now=
                // java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(now)
                user.setCreateTime(timestamp);
                if (pojo.getNickName() != null && !pojo.getNickName().equals("")) {
                    user.setNickName(pojo.getNickName());
                }
                user.setPassWordMd5(encodePassword);// md5+salt
                // 环信IM 服务器密码生成，im password 6位随机数
                Random random = new Random();
                StringBuilder imPassWord = new StringBuilder();
                for (int i = 0; i < 6; i++) {// 6位随机数；
                    imPassWord.append(random.nextInt(10));
                }
                user.setImPassWord(imPassWord.toString());
                user.setDeleted(false);
                user.setEnabled(true);
                user.setImEnabled(false); // 注意，检查IM SERVER注册是否成功；然后回写此处为true；
                if (pojo.getTeamsType() != null && !pojo.getTeamsType().equals("")) {
                    user.setTeamsType(pojo.getTeamsType());
                } else {
                    user.setTeamsType("footballTeams");
                }
                if ("wechat".equals(pojo.getClientMode())) {
                    user.setBindWeixin(pojo.getBindAccount());
                }
                if ("qq".equals(pojo.getClientMode())) {
                    user.setBindQq(pojo.getBindAccount());
                }
                if ("weibo".equals(pojo.getClientMode())) {
                    user.setBindWeibo(pojo.getBindAccount());
                }
                if ("wechatsubscription".equals(pojo.getClientMode())) {
                    user.setBindWeChatSubscripUser(pojo.getBindAccount());
                }
                userDaoService.save(user);
                if (!"".equals(pojo.getHeadImgNetUrl())
                        && pojo.getHeadImgNetUrl() != null) {
                    UserHeadimg userHeadimg = new UserHeadimg();
                    userHeadimg.setUser(user);
                    userHeadimg.setCreateTime(new Timestamp(System
                            .currentTimeMillis()));
                    userHeadimg.setDeleted(false);
                    userHeadimg.setEnabled(true);
                    userHeadimg.setHeadImgNetUrl(pojo.getHeadImgNetUrl());
                    userHeadimg = userHeadimgDaoService.save(userHeadimg);
                    if (userHeadimg.getId() == null) {// 写 userHeadimg表 失败 ；
                        logger.error("userHeadimgDaoService.save(userHeadimg) error");
                        return false;
                    }
                } else {
                    UserHeadimgRandom userHeadimgRandom = null;
                    while (userHeadimgRandom == null) {
                        List<UserHeadimgRandom> allRandom = userHeadimgRandomDaoService.findAllImgRan();
                        int j = (new Random().nextInt(allRandom.size()));
                        userHeadimgRandom = allRandom.get(j);
                    }
                    String imgurl_Random = userHeadimgRandom.getHeadImgNetUrl();
                    String filePath_Random = userHeadimgRandom.getHeadImgUrl();
                    Long fileLength_Random = userHeadimgRandom.getLength();
                    String fileName_Random = userHeadimgRandom.getFileName();
                    UserHeadimg userHeadimg = userHeadImgUtil.saveImgToDatabaseForUserImg(user, imgurl_Random, filePath_Random, fileName_Random, fileLength_Random);
                    if (userHeadimg.getId() == null) {// 写 userHeadimg表 失败 ；
                        logger.error("userHeadimgDaoService.save(userHeadimg) error");
                        return false;
                    }
                }
                // gerald 新增第三方注册绑定
                userDaoService.save(user); // userDao
                // 注册IM SERVER///
                // java Long类型转换成String ， 不足10位 在前面补0
                // String imUserid=Constant.getLongTenLetter(user.getId());
                // String imUsername=imUserid;//imUsername格式；为10位数，“0000000001"
//                if (!imServerService.registerUserImServer(user)) {
//                    // 要删除
//                    userDaoService.delete(user);
//                    tokensDaoService.delete(tokens);
//                    return false;
//                }
                // 设置user积分；
                user.setCredits(0);
                user.setCreditsRank("");
                // 写user角色记录表table user_role；
                // 设置user role角色
                UserRole userRole = new UserRole();
                timestamp = new Timestamp(System.currentTimeMillis());
                userRole.setCreateTime(timestamp);
                userRole.setDeleted(false);
                // 查询用户注册的角色
                Roles roles = rolesDaoService.findByRoleName("visitor");// 为访客注册角色；
                userRole.setRoles(roles);
                userRole.setUser(user);
                userRole.setUpdateTime(timestamp);
                userRole.setCreator(user.getId());
                userRoleDaoService.save(userRole); // userRoleDao
                /* 写入手机硬件信息表 */
                DeviceInfo deviceInfo = new DeviceInfo();
                deviceInfo.setIdentifierNumber(pojo.getIdentifierNumber());
                deviceInfo.setUser(user);
                deviceInfo.setDeviceName(pojo.getDeviceName());
                deviceInfo.setPhoneVersion(pojo.getPhoneVersion());
                deviceInfo.setPhoneModel(pojo.getPhoneModel());
                deviceInfo.setAppCurVersion(pojo.getAppCurVersion());
                timestamp = new Timestamp(System.currentTimeMillis());
                deviceInfo.setCreateTime(timestamp);
                deviceInfo.setUpdateTime(timestamp);
                deviceInfo.setDeleted(false);
                deviceInfoDaoService.save(deviceInfo);
                // 写注册记录表table user_Register；
                UserRegister userRegister = new UserRegister();
                userRegister.setUser(user);
                userRegister.setDeviceInfo(deviceInfo);
                userRegister.setAudit((short) 1);
                timestamp = new Timestamp(System.currentTimeMillis());
                userRegister.setAudiTime(timestamp);
                userRegister.setLongitude(pojo.getLongitude());
                userRegister.setLatitude(pojo.getLatitude());
                userRegister.setCreateTime(timestamp);
                userRegister.setRegisterMode(pojo.getTerminalMode());
                userRegister.setClientMode(pojo.getClientMode());
                userRegister.setDeleted(false);
                userRegisterDaoService.save(userRegister);
            } else {
                // gerald 新增第三方注册绑定
                if ("wechat".equals(pojo.getClientMode())) {
                    if (null != user.getBindWeixin() && !"".equals(user.getBindWeixin())) {
                        return false;
                    }
                    user.setBindWeixin(pojo.getBindAccount());
                }
                if ("qq".equals(pojo.getClientMode())) {
                    if (null != user.getBindQq() && !"".equals(user.getBindQq())) {
                        return false;
                    }
                    user.setBindQq(pojo.getBindAccount());
                }
                if ("weibo".equals(pojo.getClientMode())) {
                    if (null != user.getBindWeibo() && !"".equals(user.getBindWeibo())) {
                        return false;
                    }
                    user.setBindWeibo(pojo.getBindAccount());
                }
                if ("wechatsubscription".equals(pojo.getClientMode())) {
                    if (null != user.getBindWeChatSubscripUser() && !"".equals(user.getBindWeChatSubscripUser())) {
                        return false;
                    }
                    user.setBindWeChatSubscripUser(pojo.getBindAccount());
                }
                userDaoService.save(user);
            }
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
        return true;
    }

    public boolean sendMessage(String smtpHost, String from,
                               String fromUserPassword, String to, String subject,
                               String messageText, String messageType) {
        // 第一步：配置javax.mail.Session对象
        try {
            Properties props = new Properties();
            props.put("mail.smtp.host", smtpHost);
            /* props.put("mail.smtp.starttls.enable", "true"); */
            // 使用 STARTTLS安全连接
            // props.put("mail.smtp.port", "25"); //google使用465或587端口
            props.put("mail.smtp.auth", "true"); // 使用验证
            // props.put("mail.debug", "true");
            // 发送邮件协议名称
            props.setProperty("mail.transport.protocol", "smtp");

            MailSSLSocketFactory sf = new MailSSLSocketFactory();
            sf.setTrustAllHosts(true);
            props.put("mail.smtp.ssl.enable", "true");
            props.put("mail.smtp.ssl.socketFactory", sf);
            Session mailSession = Session.getInstance(props,
                    new MyAuthenticator(from, fromUserPassword));

            // 第二步：编写消息
            InternetAddress fromAddress = new InternetAddress(from);
            InternetAddress toAddress = new InternetAddress(to);

            MimeMessage message = new MimeMessage(mailSession);

            message.setFrom(fromAddress);
            message.addRecipient(MimeMessage.RecipientType.TO, toAddress);

            message.setSentDate(Calendar.getInstance().getTime());
            message.setSubject(subject);
            message.setContent(messageText, messageType);

            // 第三步：发送消息
            Transport transport = mailSession.getTransport("smtp");
            transport.connect(smtpHost, "<EMAIL>", fromUserPassword);
            transport.sendMessage(message, message.getRecipients(MimeMessage.RecipientType.TO));
            // send(message, message.getRecipients(RecipientType.TO));
            return true;
        } catch (Exception e) {
            logger.error("mailRegister property error." + e.getMessage());
        }
        return false;
    }

    public static String getCountryName(String userName, FinalList<Integer> codeListOfCAN) {
        String countryName = "CN";
        if (userName.contains("-")) {
            userName = userName.replace(" ", "");
            String[] s = userName.split("-");
            String countryCode = s[0];
            String phoneNumber = s[1];

            switch (Integer.parseInt(countryCode)) {
                case 86:
                    countryName = "CN";
                    break;//中国
                case 61:
                    countryName = "AU";
                    break;//澳大利亚
                case 44:
                    countryName = "UK";
                    break;//英国
                case 1:
                    countryName = getUSAOrCAN(phoneNumber, codeListOfCAN);
                    break;//美国 或 加拿大
                case 852:
                    countryName = "HK";
                    break;//香港
                default:
                    countryName = "CN";
                    break;//中国
            }
        }
        return countryName;
    }

    private static String getUSAOrCAN(String phoneNumber, FinalList<Integer> codeListOfCAN) {
        String countryName = "CN";
        if (phoneNumber.length() >= 3) {
            String countyCode = phoneNumber.substring(0, 3);
            if (codeListOfCAN.getList().contains(Integer.parseInt(countyCode))) {
                countryName = "CAN";
            } else {
                countryName = "USA";
            }
        }
        return countryName;
    }
}
