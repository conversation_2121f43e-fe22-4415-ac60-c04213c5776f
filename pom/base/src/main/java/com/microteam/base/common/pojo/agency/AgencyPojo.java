package com.microteam.base.common.pojo.agency;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class AgencyPojo {
    private String provinceCode;
    private String cityCode;
    private String agencyAbbreviation;
    private Long agencyId;
    private Integer type;
    private Integer isMatching;
    private String agencyName;
    private String logoUrl;
    private String creditsRank;
    private String webSite;
    private String imGroupId;
    private String countyCode;
    private Integer credits;
    private Long createTime;
    private String countryCode;
    private String registerAddr;
    private String location;
    private String userRole;
    private String introduction;
    private Integer coachCount;
    private Integer studentCount;
    private Integer teamCount;
}
