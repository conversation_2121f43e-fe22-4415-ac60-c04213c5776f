package com.microteam.base.lesson.service;


import com.microteam.base.entity.lesson.FootballLessonHardwareData;

import java.util.List;

public interface FootballLessonHardwareDataDaoService {

    int shutDownByUserIdAndHardwareId(Long userId, Long hardwareId);

    int shutDownOtherLessonByUserIdAndLessonId(Long userId, Long lessonId);

    FootballLessonHardwareData findByHardwareIdAndLessonIdAndUserId(Long hardwareId, Long lessonId, Long userId);

    List<FootballLessonHardwareData> findStartedByLessonIdAndUserId(Long lessonId, Long userId);

    List<FootballLessonHardwareData> findUploadedByLessonIdAndUserId(Long lessonId, Long userId);

    List<FootballLessonHardwareData> findByLessonIdListAndUserId(List<Long> lessonIdList, Long userId);

    void deleteByLessonIdAndUserId(Long lessonId, Long userId);

    int delByUserId(Long userId);

    List<FootballLessonHardwareData> findStartedByHardwareIdAndLessonId(Long hardwareId, Long lessonId);

    List<FootballLessonHardwareData> findByLessonIdAndUserId(Long lessonId, Long userId);

    List<FootballLessonHardwareData> findStartedByLessonIdAndUserIdOrderByHardwareId(Long lessonId, Long userId);

    List<FootballLessonHardwareData> findStartedByUserId(Long userId);

    List<FootballLessonHardwareData> findUploadedByLessonIdAndUserIdList(Long lessonId, List<Long> userIdList);

    List<FootballLessonHardwareData> findStartedByLessonIdAndUserIdOrderByKickBallStartTime(Long lessonId, Long userId);

    List<FootballLessonHardwareData> findUploadedByLessonIdAndUserIdListOrderByKickBallStartTime(Long lessonId, List<Long> userIdList);

    int deleteByLessonIdList(List<Long> lessonIdList);

}
