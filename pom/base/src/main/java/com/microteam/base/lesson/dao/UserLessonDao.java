package com.microteam.base.lesson.dao;


import com.microteam.base.entity.lesson.UserLesson;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.Date;
import java.util.List;

public interface UserLessonDao extends JpaRepository<UserLesson, Long>, JpaSpecificationExecutor<UserLesson> {

    @Query("from UserLesson " +
            "where lessonId=(:lessonId) " +
            "and userId=(:userId) " +
            "and deleted=false")
    UserLesson findByUserIdAndLessonId(@Param("userId") long userId, @Param("lessonId") long lessonId);

    @Query("from UserLesson " +
            "where userId=(:userId) " +
            "and deleted=false")
    List<UserLesson> findByUserId(@Param("userId") long userId);

    @Query("from UserLesson " +
            "where userId=(:userId) " +
            "and status=1 " +
            "and deleted=false " +
            "order by updateTime desc")
    List<UserLesson> findChosenByUserId(@Param("userId") long userId);

    @Query("from UserLesson as userLesson " +
            "left join fetch userLesson.lesson as lesson " +
            "where userLesson.userId=(:userId) " +
            "and userLesson.status=1 " +
            "and userLesson.deleted=false " +
            "and lesson.startTime>(:date) " +
            "order by userLesson.updateTime desc")
    List<UserLesson> findChosenByUserIdForToday(@Param("userId") Long userId, @Param("date") Date date);
}
