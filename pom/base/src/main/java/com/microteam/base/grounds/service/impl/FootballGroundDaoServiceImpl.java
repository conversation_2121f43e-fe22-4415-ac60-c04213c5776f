package com.microteam.base.grounds.service.impl;

import com.microteam.base.grounds.dao.FootballGroundDao;
import com.microteam.base.entity.grounds.FootballGround;
import com.microteam.base.grounds.service.FootballGroundDaoService;
import org.apache.log4j.Logger;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;


@Service("footballGroundDaoService")
public class FootballGroundDaoServiceImpl implements FootballGroundDaoService {
    static Logger logger = Logger.getLogger(FootballGroundDaoServiceImpl.class.getName());
    @Resource(name = "footballGroundDao")
    private FootballGroundDao dao;

    //同时得到FootballGround的内容；
    @Override
    public FootballGround findByGroundName(String groundName) {
        return dao.findByGroundName(groundName);
    }

    @Override
    public FootballGround findById(long id) {
        return dao.findById(id);
    }

//    @Override
//    public List<FootballGround> findByCodeForPage(String cityCode, String countryCode, int page, int pageSize) {
//        return dao.findByCodeForPage(cityCode, countryCode, page, pageSize);
//    }

//    @Override
//    public List<FootballGround> findByCodeAndSearchForPage(String cityCode, String countryCode,
//                                                           int page, int pageSize, String search) {
//        return dao.findByCodeAndSearchForPage(cityCode, countryCode, page, pageSize, search);
//    }

    @Override
    public List<FootballGround> findByUserIdForPage(Long userId, int page, int pageSize) {
        if (page > 0) {
            page--;
        }
        Pageable pageable = PageRequest.of(page, pageSize);
        return dao.findByUserIdForPage(userId, pageable);
    }

    @Override
    public List<FootballGround> findByCountryCodeForPage(String countryCode, int page, int pageSize) {
        if (page > 0) {
            page--;
        }
        Pageable pageable = PageRequest.of(page, pageSize);
        return dao.findByCountryCodeForPage(countryCode, pageable);
    }

    @Override
    public FootballGround save(FootballGround footballGround) {
        return dao.save(footballGround);
    }
}
