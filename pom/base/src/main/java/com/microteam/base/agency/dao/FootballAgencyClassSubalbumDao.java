package com.microteam.base.agency.dao;

import com.microteam.base.entity.agency.FootballAgencyClassSubalbum;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

public interface FootballAgencyClassSubalbumDao extends JpaRepository<FootballAgencyClassSubalbum, Long>, JpaSpecificationExecutor<FootballAgencyClassSubalbum> {
    //查询相册的图片列表
    @Query("from FootballAgencyClassSubalbum as footballAgencyClassSubalbum " +
            "left join fetch footballAgencyClassSubalbum.user as user " +
            "where footballAgencyClassSubalbum.footballAgencyClassAlbum.id = (:agencyClassAlbumId) " +
            "and footballAgencyClassSubalbum.deleted=false " +
            "order by footballAgencyClassSubalbum.createTime desc ")
    List<FootballAgencyClassSubalbum> findByAgencyClassAlbumId(@Param("agencyClassAlbumId") Long agencyClassAlbumId);
}
