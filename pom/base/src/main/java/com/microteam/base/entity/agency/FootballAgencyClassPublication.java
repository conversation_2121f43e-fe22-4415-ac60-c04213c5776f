package com.microteam.base.entity.agency;


import com.microteam.base.common.base.BaseEntity;
import com.microteam.base.entity.user.User;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;
import java.io.Serializable;

@Entity
@Table(name = "football_agency_class_publication")
@Getter
@Setter
public class FootballAgencyClassPublication extends BaseEntity implements Serializable {

    @ManyToOne
    @JoinColumn(name = "agencyId", nullable = false)
    private FootballAgency footballAgency;
    @ManyToOne
    @JoinColumn(name = "classId")
    private FootballAgencyClass footballAgencyClass;
    @ManyToOne
    @JoinColumn(name = "courseId")
    private FootballAgencyClassCourse footballAgencyClassCourse;
    @ManyToOne
    @JoinColumn(name = "userId", nullable = false)
    private User user;
    @Column(name = "title", length = 200)
    private String title;
    @Column(name = "content", length = 65535)
    private String content;
    @Column(name = "webSite", length = 100)
    private String webSite;
    @Column(name = "publishResouce")
    private Short publishResouce;
    @Column(name = "judge")
    private Long judge;

}
