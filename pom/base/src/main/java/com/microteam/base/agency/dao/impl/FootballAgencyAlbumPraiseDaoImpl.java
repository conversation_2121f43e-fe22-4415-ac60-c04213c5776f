//package com.microteam.base.agency.dao.impl;
//
//import com.microteam.base.agency.dao.FootballAgencyAlbumPraiseDao;
//import com.microteam.base.entity.agency.FootballAgencyAlbumPraise;
//import com.microteam.base.common.base.AbstractHibernateDao;
//import org.apache.log4j.Logger;
//import org.hibernate.query.Query;
//import org.springframework.stereotype.Repository;
//
//import java.util.List;
//
//@Repository("footballAgencyAlbumPraiseDao")
//public class FootballAgencyAlbumPraiseDaoImpl extends AbstractHibernateDao<FootballAgencyAlbumPraise> implements FootballAgencyAlbumPraiseDao {
//    static Logger logger = Logger.getLogger(FootballAgencyAlbumPraiseDaoImpl.class.getName());
//
//    public FootballAgencyAlbumPraiseDaoImpl() {
//        super();
//        setClazz(FootballAgencyAlbumPraise.class);
//    }
//
//    @Override
//    public List<FootballAgencyAlbumPraise> findByAgencyAlbumId(Long agencyAlbumId) {
//        String hql = "from FootballAgencyAlbumPraise as footballAgencyAlbumPraise " +
//                "where footballAgencyAlbumPraise.footballAgencyAlbum.id = (:agencyAlbumId) " +
//                "and footballAgencyAlbumPraise.isPraised=true " +
//                "and footballAgencyAlbumPraise.deleted=false ";
//        Query query = getCurrentSession().createQuery(hql)
//                .setParameter("agencyAlbumId", agencyAlbumId);
//        List<FootballAgencyAlbumPraise> list = query.list();
//        return list;
//    }
//
//    @Override
//    public FootballAgencyAlbumPraise findByAgencyAlbumIdAndUserId(Long agencyAlbumId, Long userId) {
//        String hql = "from FootballAgencyAlbumPraise as footballAgencyAlbumPraise " +
//                "where footballAgencyAlbumPraise.footballAgencyAlbum.id = (:agencyAlbumId) " +
//                "and footballAgencyAlbumPraise.user.id = (:userId) " +
//                "and footballAgencyAlbumPraise.deleted=false ";
//        Query query = getCurrentSession().createQuery(hql)
//                .setParameter("agencyAlbumId", agencyAlbumId)
//                .setParameter("userId", userId);
//        List<FootballAgencyAlbumPraise> list = query.list();
//        if (list != null && list.size() > 0) {
//            return list.get(0);
//        }
//        return null;
//    }
//
//}
