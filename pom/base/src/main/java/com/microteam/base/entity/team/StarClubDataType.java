package com.microteam.base.entity.team;

import com.microteam.base.common.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.io.Serializable;

@Entity
@Table(name = "starclub_datatype")
@Getter
@Setter
public class StarClubDataType extends BaseEntity implements Serializable {

    @Column(name = "starClubId")
    private Long starClubId;
    @Column(name = "dataTypeId")
    private Long dataTypeId;
    @Column(name = "value")
    private Double value;

}
