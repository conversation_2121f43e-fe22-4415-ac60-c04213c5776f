package com.microteam.base.agency.service;

import com.microteam.base.entity.agency.FootballAgencyApplicant;

import java.util.List;

public interface FootballAgencyApplicantDaoService {

    FootballAgencyApplicant findByAgencyIdAndApplicantId(Long agencyId, Long applicantId);

    FootballAgencyApplicant findById(long id);

    List<FootballAgencyApplicant> findByAgencyIdForPageOrderByCreateTimeAndAudit(Long agencyId, int page, int pageSize);

}
