package com.microteam.base.courseware.service.impl;

import com.microteam.base.courseware.dao.CourseWareImgDao;
import com.microteam.base.courseware.service.CourseWareImgDaoService;
import com.microteam.base.entity.courseware.CourseWareImg;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class CourseWareImgDaoServiceImpl implements CourseWareImgDaoService {

    @Autowired
    private CourseWareImgDao dao;

    @Override
    public List<CourseWareImg> findByCourseWareId(Long coursewareId) {
        return dao.findByCourseWareId(coursewareId);
    }

    @Override
    public List<CourseWareImg> saveAll(List<CourseWareImg> list) {
        return dao.saveAll(list);
    }

    @Override
    public CourseWareImg save(CourseWareImg courseWareImg) {
        return dao.save(courseWareImg);
    }
}
