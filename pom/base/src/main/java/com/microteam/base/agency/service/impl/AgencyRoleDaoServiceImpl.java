package com.microteam.base.agency.service.impl;

import com.microteam.base.agency.dao.AgencyRoleDao;
import com.microteam.base.agency.service.AgencyRoleDaoService;
import com.microteam.base.entity.agency.AgencyRole;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class AgencyRoleDaoServiceImpl implements AgencyRoleDaoService {

    @Autowired
    private AgencyRoleDao dao;

    @Override
    public AgencyRole findById(long id) {
        return dao.findById(id);
    }

}
