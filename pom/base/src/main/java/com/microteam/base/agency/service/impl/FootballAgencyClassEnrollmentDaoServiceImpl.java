package com.microteam.base.agency.service.impl;

import com.microteam.base.agency.dao.FootballAgencyClassEnrollmentDao;
import com.microteam.base.agency.service.FootballAgencyClassEnrollmentDaoService;
import com.microteam.base.entity.agency.FootballAgencyClassEnrollment;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.List;

@Service("footballAgencyClassEnrollmentDaoService")
public class FootballAgencyClassEnrollmentDaoServiceImpl implements FootballAgencyClassEnrollmentDaoService {
    static Logger logger = Logger.getLogger(FootballAgencyClassEnrollmentDaoServiceImpl.class.getName());
    @Autowired
    private FootballAgencyClassEnrollmentDao dao;

    //根据Id查询
    @Override
    public FootballAgencyClassEnrollment findById(long id) {
        return dao.findById(id);
    }

    //查询机构下的报名情况
    @Override
    public List<FootballAgencyClassEnrollment> findByAgencyId(Long agencyId) {
        return dao.findByAgencyId(agencyId);
    }

    //分页查询机构下的报名情况
    @Override
    public List<FootballAgencyClassEnrollment> findByAgencyIdForPage(Long agencyId, int page, int pageSize) {
        if (page > 0) {
            page--;
        }
        Pageable pageable = PageRequest.of(page, pageSize);
        return dao.findByAgencyIdForPage(agencyId, pageable);
    }

    @Override
    public List<FootballAgencyClassEnrollment> findByAgencyIdAndAgencyClassId(Long agencyId, Long agencyClassId) {
        return dao.findByAgencyIdAndAgencyClassId(agencyId, agencyClassId);
    }

    @Override
    public List<FootballAgencyClassEnrollment> findByAgencyIdAndAgencyClassIdForPage(Long agencyId, Long agencyClassId, int page, int pageSize) {
        if (page > 0) {
            page--;
        }
        Pageable pageable = PageRequest.of(page, pageSize);
        return dao.findByAgencyIdAndAgencyClassIdForPage(agencyId, agencyClassId, pageable);
    }
}
