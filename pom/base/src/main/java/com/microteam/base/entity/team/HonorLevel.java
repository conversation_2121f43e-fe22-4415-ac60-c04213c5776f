package com.microteam.base.entity.team;

import com.microteam.base.common.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.io.Serializable;

@Entity
@Table(name = "honor_level")
@Getter
@Setter
public class HonorLevel extends BaseEntity implements Serializable {

    @Basic
    @Column(name = "levelName")
    private String levelName;


}
