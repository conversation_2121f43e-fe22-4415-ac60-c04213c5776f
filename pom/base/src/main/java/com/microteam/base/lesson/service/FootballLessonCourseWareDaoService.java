package com.microteam.base.lesson.service;


import com.microteam.base.entity.lesson.FootballLessonCourseWare;

import java.util.List;

public interface FootballLessonCourseWareDaoService {

    //根据课程id删除记录
    int deleteByLessonId(Long lessonId);

    //根据课程id查询课件id
    List<Long> findByLessonId(Long lessonId);

    //根据课程idList查询课件
    List<FootballLessonCourseWare> findByLessonIdList(List<Long> lessonIdList);

    FootballLessonCourseWare save(FootballLessonCourseWare lessonCourseware);
}
