package com.microteam.base.common.util.user;

import com.microteam.base.entity.user.User;
import com.microteam.base.entity.user.UserHeadimg;
import com.microteam.base.entity.user.UserHeadimgRandom;
import com.microteam.base.user.service.UserHeadimgDaoService;
import com.microteam.base.user.service.UserHeadimgRandomDaoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.sql.Timestamp;
import java.util.List;
import java.util.Random;

@Component
public class UserHeadImgUtil {

    @Autowired
    private UserHeadimgDaoService userHeadimgDaoService;
    @Autowired
    private UserHeadimgRandomDaoService userHeadimgRandomDaoService;

    public UserHeadimg saveFreedomUserImg(User user) {
        UserHeadimg userImg = userHeadimgDaoService.findLastByUserId(user.getId());
        if (userImg == null) {
            UserHeadimgRandom userHeadimgRandom = null;
            List<UserHeadimgRandom> allRandom = userHeadimgRandomDaoService.findAllImgRan();
            while (userHeadimgRandom == null) {
                int j = (new Random().nextInt(allRandom.size()));
                userHeadimgRandom = allRandom.get(j);
            }
            String imgurl_Random = userHeadimgRandom.getHeadImgNetUrl();
            String filePath_Random = userHeadimgRandom.getHeadImgUrl();
            Long fileLength_Random = userHeadimgRandom.getLength();
            String fileName_Random = userHeadimgRandom.getFileName();
            return saveImgToDatabaseForUserImg(user, imgurl_Random, filePath_Random, fileName_Random, fileLength_Random);
        }
        return userImg;
    }

    public UserHeadimg saveImgToDatabaseForUserImg(User user, String imgurl, String filePath, String fileName, Long fileLength) {
        UserHeadimg userHeadimg = new UserHeadimg();
        userHeadimg.setCreateTime(new Timestamp(System.currentTimeMillis()));
        userHeadimg.setDeleted(false);
        userHeadimg.setEnabled(true);
        userHeadimg.setFileName(fileName);
        userHeadimg.setHeadImgUrl(filePath);
        userHeadimg.setHeadImgNetUrl(imgurl);
        userHeadimg.setLength(fileLength);
        userHeadimg.setUser(user);
        userHeadimg = userHeadimgDaoService.save(userHeadimg);
        return userHeadimg;
    }
}
