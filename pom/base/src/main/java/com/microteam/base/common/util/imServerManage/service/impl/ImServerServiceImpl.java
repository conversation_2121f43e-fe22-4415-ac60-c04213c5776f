package com.microteam.base.common.util.imServerManage.service.impl;


import com.microteam.base.common.constant.ConstantUserManage;
import com.microteam.base.common.util.imServerManage.pojo.ImGroupRegisterRequestBodyPojo;
import com.microteam.base.common.util.imServerManage.pojo.ImResponseDatas;
import com.microteam.base.common.util.imServerManage.pojo.ImResponseEntities;
import com.microteam.base.common.util.imServerManage.service.ImServerService;
import com.microteam.base.common.util.imServerManage.service.huanxinService.ImServerHuanxinService;
import com.microteam.base.entity.user.Groups;
import com.microteam.base.entity.user.User;
import com.microteam.base.user.service.GroupRoleDaoService;
import com.microteam.base.user.service.GroupsDaoService;
import com.microteam.base.user.service.UserDaoService;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.sql.Timestamp;

@Service("imServerService")
public class ImServerServiceImpl implements ImServerService {
    static Logger logger = Logger.getLogger(ImServerServiceImpl.class.getName());
    @Autowired
    private UserDaoService userDaoService; //for  table
    @Autowired
    private GroupRoleDaoService groupRoleDaoService; //for  table
    @Autowired
    private GroupsDaoService groupsDaoService; //for  table
    @Autowired
    private ImServerHuanxinService imServerHuanxinService; //for  IM SERVER

    @Override
    public boolean registerUserImServer(User user) {
        ImResponseEntities[] entitiesArray = imServerHuanxinService.registerUserImServer(user);
        if ((entitiesArray != null) && (entitiesArray.length > 0)) {
            ImResponseEntities entities = entitiesArray[0];
            //java Long类型转换成String ， 不足10位 在前面补0
            String imUsername = ConstantUserManage.getLongTenLetter(user.getId());
            if (entities.getUsername().equals(imUsername)) {
                //写回user表的imEnabled=true;
                user.setUpdateTime(new Timestamp(System.currentTimeMillis()));
                user.setImEnabled(true);
                user.setImUserUuid(entities.getUuid());
                user.setModifier(user.getId());
                userDaoService.save(user);
                return true;
            }
        }
        return false;
    }

    @Override
    public Groups registerGroupImServer(Groups groups, ImGroupRegisterRequestBodyPojo imGroupRegisterRequestBodyPojo) {
        ImResponseDatas datas = imServerHuanxinService.registerGroupImServer(imGroupRegisterRequestBodyPojo);
        if (datas != null) {
            if (datas.getGroupid() != null) {
                //写回group表的imEnabled=true;
                groups.setImGroupId(datas.getGroupid());
                groups.setImEnabled(true);
                return groups;
            }
        }
        return null;
    }

}