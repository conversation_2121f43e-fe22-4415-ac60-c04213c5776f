package com.microteam.base.agency.service.impl;

import com.microteam.base.agency.dao.AgencyUserDao;
import com.microteam.base.agency.service.AgencyUserDaoService;
import com.microteam.base.entity.agency.AgencyUser;
import com.microteam.base.entity.agency.FootballAgency;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
public class AgencyUserDaoServiceImpl implements AgencyUserDaoService {

    @Autowired
    private AgencyUserDao dao;

    @Override
    public AgencyUser findById(long id) {
        return dao.findById(id);
    }

    @Override
    public AgencyUser findByAgencyIdAndUserIdAndRoleId(Long agencyId, Long userId, Long roleId) {
        return dao.findByAgencyIdAndUserIdAndRoleId(agencyId, userId, roleId);
    }

    @Override
    public AgencyUser save(AgencyUser agencyUser) {
        return dao.save(agencyUser);
    }

    @Override
    public Integer findCountByAgencyIdAndRoleId(Long agencyId, Long roleId) {
        return dao.findCountByAgencyIdAndRoleId(agencyId, roleId);
    }

    @Override
    public Integer findCountOfStuByAgencyId(Long agencyId) {
        return dao.findCountOfStuByAgencyId(agencyId);
    }

    @Override
    public AgencyUser findOneByAgencyIdAndUserId(Long agencyId, Long userId) {
        return dao.findOneByAgencyIdAndUserId(agencyId, userId);
    }

    @Override
    public List<AgencyUser> findByAgencyIdAndUserId(Long agencyId, Long userId) {
        return dao.findByAgencyIdAndUserId(agencyId, userId);
    }

    @Override
    public List<AgencyUser> findByAgencyIdAndRoleId(Long agencyId, Long roleId) {
        List<AgencyUser> list = dao.findByAgencyIdAndRoleId(agencyId, roleId);
        if (list == null) {
            return new ArrayList<>();
        }
        return list;
    }

    @Override
    public List<AgencyUser> findByAgencyIdAndRoleIdForPage(Long agencyId, Long roleId, int page, int pageSize) {
        if (page > 0) {
            page--;
        }
        Pageable pageable = PageRequest.of(page, pageSize);
        List<AgencyUser> list = dao.findByAgencyIdAndRoleIdForPage(agencyId, roleId, pageable);
        if (list == null) {
            return new ArrayList<>();
        }
        return list;
    }

    @Override
    public List<AgencyUser> findByUserId(Long userId) {
        List<AgencyUser> list = dao.findByUserId(userId);
        if (list == null) {
            return new ArrayList<>();
        }
        return list;
    }

    @Override
    public List<AgencyUser> findByUserIdOrderByRoleId(Long userId) {
        List<AgencyUser> list = dao.findByUserIdOrderByRoleId(userId);
        if (list == null) {
            return new ArrayList<>();
        }
        return list;
    }

    @Override
    public List<AgencyUser> findYouthAgencyByUserIdAndRoleIdList(Long userId, List<Long> roleIdList) {
        List<AgencyUser> list = dao.findYouthAgencyByUserIdAndRoleIdList(userId, roleIdList);
        if (list == null) {
            return new ArrayList<>();
        }
        return list;
    }

    @Override
    public boolean delById(Long id) {
        return dao.delById(id);
    }

    @Override
    public List<Long> findUserIdListByTeamId(Long teamId) {
        List<Long> list = dao.findUserIdListByTeamId(teamId);
        if (list == null) {
            return new ArrayList<>();
        }
        return list;
    }

    @Override
    public List<AgencyUser> findByAgencyId(Long agencyId) {
        return dao.findByAgencyId(agencyId);
    }

}
