package com.microteam.base.entity.agency;


import com.microteam.base.common.base.BaseEntity;
import com.microteam.base.entity.user.User;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

@Entity
@Table(name = "football_agency_student")
@Getter
@Setter
public class FootballAgencyStudent extends BaseEntity implements Serializable {

    @ManyToOne
    @JoinColumn(name = "agencyId", nullable = false)
    private FootballAgency footballAgency;
    @ManyToOne
    @JoinColumn(name = "courseId")
    private FootballAgencyClassCourse footballAgencyClassCourse;
    @ManyToOne
    @JoinColumn(name = "userId", nullable = false)
    private User user;
    @ManyToOne
    @JoinColumn(name = "classId")
    private FootballAgencyClass footballAgencyClass;
    @Column(name = "studentName", length = 100)
    private String studentName;
    @Column(name = "headImgNetUrl", length = 250)
    private String headImgNetUrl;
    @Column(name = "headImgUrl", length = 250)
    private String headImgUrl;
    @Column(name = "headImgName", length = 50)
    private String headImgName;
    @Column(name = "headImgLength")
    private Long headImgLength;
    @Column(name = "birthDay", length = 100)
    private String birthDay;
    @Column(name = "myPhone", length = 50)
    private String myPhone;
    @Column(name = "parentPhone", length = 50)
    private String parentPhone;
    @Column(name = "homeAddr", length = 200)
    private String homeAddr;
    @Column(name = "posInTeam")
    private Short posInTeam;
    @Column(name = "habitFoot")
    private Short habitFoot;
    @Column(name = "courseScore")
    private Integer courseScore;
    @Column(name = "courseComment", length = 65535)
    private String courseComment;
    @Column(name = "scoreTeacherId")
    private Long scoreTeacherId;

    public FootballAgencyStudent() {
    }

    public FootballAgencyStudent(FootballAgency footballAgency, User user,
                                 Date createTime, boolean deleted) {
        this.footballAgency = footballAgency;
        this.user = user;
        this.createTime = createTime;
        this.deleted = deleted;
    }

    public FootballAgencyStudent(FootballAgency footballAgency, User user) {
        this.footballAgency = footballAgency;
        this.user = user;
    }


    public FootballAgencyStudent(FootballAgency footballAgency,
                                 FootballAgencyClassCourse footballAgencyClassCourse, User user,
                                 FootballAgencyClass footballAgencyClass, String studentName,
                                 String headImgNetUrl, String headImgUrl, String headImgName,
                                 Long headImgLength, String birthDay, String myPhone,
                                 String parentPhone, String homeAddr, Short posInTeam,
                                 Short habitFoot, Integer courseScore, String courseComment,
                                 Long scoreTeacherId, Date createTime, Date updateTime,
                                 boolean deleted) {
        this.footballAgency = footballAgency;
        this.footballAgencyClassCourse = footballAgencyClassCourse;
        this.user = user;
        this.footballAgencyClass = footballAgencyClass;
        this.studentName = studentName;
        this.headImgNetUrl = headImgNetUrl;
        this.headImgUrl = headImgUrl;
        this.headImgName = headImgName;
        this.headImgLength = headImgLength;
        this.birthDay = birthDay;
        this.myPhone = myPhone;
        this.parentPhone = parentPhone;
        this.homeAddr = homeAddr;
        this.posInTeam = posInTeam;
        this.habitFoot = habitFoot;
        this.courseScore = courseScore;
        this.courseComment = courseComment;
        this.scoreTeacherId = scoreTeacherId;
        this.createTime = createTime;
        this.updateTime = updateTime;
        this.deleted = deleted;
    }
}
