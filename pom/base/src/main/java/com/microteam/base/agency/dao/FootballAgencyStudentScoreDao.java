package com.microteam.base.agency.dao;

import com.microteam.base.entity.agency.FootballAgencyClassCourse;
import com.microteam.base.entity.agency.FootballAgencyStudentScore;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

public interface FootballAgencyStudentScoreDao extends JpaRepository<FootballAgencyStudentScore, Long>, JpaSpecificationExecutor<FootballAgencyStudentScore> {
    //查询课程下所有学员的分数
    @Query("from FootballAgencyStudentScore as agencyStudentScore " +
            "left join fetch agencyStudentScore.footballAgencyStudent as footballAgencyStudent " +
            "where agencyStudentScore.footballAgencyClassCourse=?1 " +
            "and agencyStudentScore.deleted=false ")
    List<FootballAgencyStudentScore> findScoreByCourse(FootballAgencyClassCourse agencyClassCourse);

    @Query("from FootballAgencyStudentScore as agencyStudentScore " +
            "left join fetch agencyStudentScore.footballAgencyStudent as footballAgencyStudent " +
            "where agencyStudentScore.id=?1 " +
            "and agencyStudentScore.deleted=false ")
    FootballAgencyStudentScore findScoreById(long id);
}
