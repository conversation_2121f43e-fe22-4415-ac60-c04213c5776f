//package com.microteam.base.grounds.dao.impl;
//
//import com.microteam.base.common.base.AbstractHibernateDao;
//import com.microteam.base.entity.grounds.FootballGround;
//import com.microteam.base.grounds.dao.FootballGroundDao;
//import org.apache.log4j.Logger;
//import org.hibernate.Hibernate;
//import org.hibernate.query.Query;
//import org.springframework.stereotype.Repository;
//
//import java.util.ArrayList;
//import java.util.List;
//
//@Repository("footballGroundDao")
//public class FootballGroundDaoImpl extends AbstractHibernateDao<FootballGround> implements FootballGroundDao {
//    static Logger logger = Logger.getLogger(FootballGroundDaoImpl.class.getName());
//
//    public FootballGroundDaoImpl() {
//        super();
//        setClazz(FootballGround.class);
//    }
//
//    @Override
//    public FootballGround findByGroundName(String groundName) {
//        String hql = "from FootballGround as grounds " +
//                "left join fetch grounds.user as user " +
//                "left join fetch grounds.footballGroundType as footballGroundsType " +
//                "left join fetch grounds.groups as groups " +
//                "where grounds.groundName=? " +
//                "and grounds.deleted=false " +
//                "and grounds.enabled=true";
//        Query query = getCurrentSession().createQuery(hql)
//                .setParameter(0, groundName);
//        List<FootballGround> list = query.list();
//        if (list.size() == 1)
//            return list.get(0);
//        return null;
//    }
//
//    @Override
//    public FootballGround findById(long id) {
//        String hql = "from FootballGround as grounds " +
//                "left join fetch grounds.user as user " +
//                "left join fetch grounds.footballGroundType as footballGroundsType " +
//                "left join fetch grounds.groups as groups " +
//                "where grounds.id=? " +
//                "and grounds.deleted=false " +
//                "and grounds.enabled=true";
//        Query query = getCurrentSession().createQuery(hql)
//                .setParameter(0, id);
//        List<FootballGround> list = query.list();
//        if (list.size() == 1)
//            return list.get(0);
//        return null;
//    }
//
//    @Override
//    public List<FootballGround> findByCodeForPage(String cityCode, String countryCode, int page, int pageSize) {
//        String hql = "from FootballGround as grounds " +
//                "left join grounds.user as user " +
//                "left join grounds.groups as groups " +
//                "left join grounds.footballGroundType as footballGroundsType " +
//                "with footballGroundsType.deleted=false";
//        Query query = null;
//        if ("".equals(countryCode) || countryCode == null
//                || countryCode.equals("null")) {
//            if ("".equals(cityCode) || cityCode == null || cityCode.equals("null")) {
//                hql += " where  grounds.countryCode='1000000'   and grounds.deleted=false and grounds.enabled=true order by grounds.credits desc";
//                query = getCurrentSession().createQuery(hql);
//                query.setFirstResult((page - 1) * pageSize);
//                query.setMaxResults(pageSize);
//            } else {
//                // 四个直辖市和香港、澳门、台湾特别行政区除外
//                // 北京市
//                if ("110000".equals(cityCode) || "120000".equals(cityCode)
//                        || "310000".equals(cityCode)
//                        || "500000".equals(cityCode)
//                        || "710000".equals(cityCode)
//                        || "810000".equals(cityCode)
//                        || "820000".equals(cityCode)) {
//                    hql += " where grounds.countryCode='1000000' and grounds.provinceCode=?   and grounds.deleted=false and grounds.enabled=true order by grounds.credits desc";
//                    query = getCurrentSession().createQuery(hql).setParameter(
//                            0, cityCode);
//                    query.setFirstResult((page - 1) * pageSize);
//                    query.setMaxResults(pageSize);
//                } else {
//                    hql += " where grounds.countryCode='1000000' and grounds.cityCode=?   and grounds.deleted=false and grounds.enabled=true order by grounds.credits desc";
//                    query = getCurrentSession().createQuery(hql).setParameter(
//                            0, cityCode);
//                    query.setFirstResult((page - 1) * pageSize);
//                    query.setMaxResults(pageSize);
//                }
//            }
//        } else {
//            if ("".equals(cityCode) || cityCode == null || cityCode == "null") {
//                hql += " where  grounds.countryCode=?   and grounds.deleted=false and grounds.enabled=true order by grounds.credits desc";
//                query = getCurrentSession().createQuery(hql).setParameter(0, countryCode);
//                query.setFirstResult((page - 1) * pageSize);
//                query.setMaxResults(pageSize);
//            } else {
//                // 四个直辖市和香港、澳门、台湾特别行政区除外
//                // 北京市
//                if ("110000".equals(cityCode) || "120000".equals(cityCode)
//                        || "310000".equals(cityCode)
//                        || "500000".equals(cityCode)
//                        || "710000".equals(cityCode)
//                        || "810000".equals(cityCode)
//                        || "820000".equals(cityCode)) {
//                    hql += " where grounds.countryCode=? and grounds.provinceCode=?   and grounds.deleted=false and grounds.enabled=true order by grounds.credits desc";
//                    query = getCurrentSession().createQuery(hql).setParameter(
//                            0, countryCode).setParameter(1, cityCode);
//                    query.setFirstResult((page - 1) * pageSize);
//                    query.setMaxResults(pageSize);
//                } else {
//                    hql += " where grounds.countryCode=? and grounds.cityCode=?   and grounds.deleted=false and grounds.enabled=true order by grounds.credits desc";
//                    query = getCurrentSession().createQuery(hql).setParameter(
//                            0, countryCode).setParameter(1, cityCode);
//                    query.setFirstResult((page - 1) * pageSize);
//                    query.setMaxResults(pageSize);
//                }
//            }
//        }
//        List<Object[]> list = query.list();
//
//        if (list.size() > 0) {
//            List<FootballGround> listnew = new ArrayList<FootballGround>();
//            for (int i = 0; i < list.size(); i++) {
//                Object[] obj = list.get(i);
//                FootballGround grounds = (FootballGround) obj[0];
//                Hibernate.initialize(grounds);// 获取赖加载的集合内容；
//                Hibernate.initialize(grounds.getUser());// 获取赖加载的集合内容；
//                Hibernate.initialize(grounds.getFootballGroundType());// 获取赖加载的集合内容；
//                Hibernate.initialize(grounds.getGroups());// 获取赖加载的集合内容；
//                listnew.add(i, grounds);
//
//            }
//
//            return listnew;
//        }
//        return null;
//    }
//
//    @Override
//    public List<FootballGround> findByCodeAndSearchForPage(String cityCode, String countryCode, int page, int pageSize, String search) {
//        String hql = "from FootballGround as grounds " +
//                "left join grounds.user as user " +
//                "left join  grounds.footballGroundType as footballGroundsType " +
//                "with footballGroundsType.deleted=false";
//        Query query = null;
//        if ("".equals(countryCode) || countryCode == null || "null".equals(countryCode)) {
//            if ("".equals(cityCode) || cityCode == null || "null".equals(cityCode)) {
//                hql += " where  grounds.groundName like ? and grounds.countryCode='1000000'   and grounds.deleted=false and grounds.enabled=true order by grounds.credits desc";
//                query = getCurrentSession().createQuery(hql).setParameter(0,
//                        "%" + search + "%");
//                query.setFirstResult((page - 1) * pageSize);
//                query.setMaxResults(pageSize);
//            } else {
//                if ("110000".equals(cityCode) || "120000".equals(cityCode)
//                        || "310000".equals(cityCode) || "500000".equals(cityCode)
//                        || "710000".equals(cityCode) || "810000".equals(cityCode)
//                        || "820000".equals(cityCode)) {
//                    hql += " where grounds.groundName like ? and grounds.provinceCode=? and grounds.countryCode='1000000'   and grounds.deleted=false and grounds.enabled=true order by grounds.credits desc";
//                    query = getCurrentSession().createQuery(hql)
//                            .setParameter(0, "%" + search + "%")
//                            .setParameter(1, cityCode);
//                    query.setFirstResult((page - 1) * pageSize);
//                    query.setMaxResults(pageSize);
//                } else {
//                    hql += " where grounds.groundName like ? and grounds.cityCode=? and grounds.countryCode='1000000'   and grounds.deleted=false and grounds.enabled=true order by grounds.credits desc";
//                    query = getCurrentSession().createQuery(hql)
//                            .setParameter(0, "%" + search + "%")
//                            .setParameter(1, cityCode);
//                    query.setFirstResult((page - 1) * pageSize);
//                    query.setMaxResults(pageSize);
//                }
//            }
//        } else {
//            if ("".equals(cityCode) || cityCode == null || "null".equals(cityCode)) {
//                hql += " where  grounds.groundName like ? and grounds.countryCode=?   and grounds.deleted=false and grounds.enabled=true order by grounds.credits desc";
//                query = getCurrentSession().createQuery(hql).setParameter(0,
//                        "%" + search + "%").setParameter(1, countryCode);
//                query.setFirstResult((page - 1) * pageSize);
//                query.setMaxResults(pageSize);
//            } else {
//                if ("110000".equals(cityCode) || "120000".equals(cityCode)
//                        || "310000".equals(cityCode) || "500000".equals(cityCode)
//                        || "710000".equals(cityCode) || "810000".equals(cityCode)
//                        || "820000".equals(cityCode)) {
//                    hql += " where grounds.groundName like ? and grounds.provinceCode=? and grounds.countryCode=?   and grounds.deleted=false and grounds.enabled=true order by grounds.credits desc";
//                    query = getCurrentSession().createQuery(hql)
//                            .setParameter(0, "%" + search + "%")
//                            .setParameter(1, cityCode).setParameter(2, countryCode);
//                    query.setFirstResult((page - 1) * pageSize);
//                    query.setMaxResults(pageSize);
//                } else {
//                    hql += " where grounds.groundName like ? and grounds.cityCode=? and grounds.countryCode=?   and grounds.deleted=false and grounds.enabled=true order by grounds.credits desc";
//                    query = getCurrentSession().createQuery(hql)
//                            .setParameter(0, "%" + search + "%")
//                            .setParameter(1, cityCode).setParameter(2, countryCode);
//                    query.setFirstResult((page - 1) * pageSize);
//                    query.setMaxResults(pageSize);
//                }
//            }
//        }
//        List<Object[]> list = query.list();
//
//        if (list.size() > 0) {
//            List<FootballGround> listnew = new ArrayList<FootballGround>();
//
//            for (int i = 0; i < list.size(); i++) {
//
//                Object[] obj = list.get(i);
//
//                FootballGround grounds = (FootballGround) obj[0];
//
//                Hibernate.initialize(grounds);// 获取赖加载的集合内容；
//                Hibernate.initialize(grounds.getUser());// 获取赖加载的集合内容；
//                Hibernate.initialize(grounds.getFootballGroundType());// 获取赖加载的集合内容；
//
//                listnew.add(i, grounds);
//
//            }
//
//            return listnew;
//        }
//        return null;
//    }
//
//    @Override
//    public List<FootballGround> findByUserIdForPage(Long userId, int page, int pageSize) {
//        String hql = "from FootballGround as grounds " +
//                "left join fetch grounds.footballGroundType as footballGroundsType " +
//                "where grounds.user.id = (:userId) " +
//                "and grounds.deleted=false " +
//                "and grounds.enabled=true";
//        Query query = getCurrentSession().createQuery(hql)
//                .setParameter("userId", userId);
//        query.setFirstResult((page - 1) * pageSize);
//        query.setMaxResults(pageSize);
//        List<FootballGround> list = query.list();
//        if (list.size() > 0)
//            return list;
//        return null;
//    }
//
//    @Override
//    public List<FootballGround> findByCountryCodeForPage(String countryCode, int page, int pageSize) {
//        String hql = "from FootballGround as grounds " +
//                "left join fetch grounds.footballGroundType as footballGroundsType " +
//                "left join fetch grounds.groups as groups " +
//                "left join fetch grounds.user as user " +
//                "where grounds.countryCode=? " +
//                "and grounds.deleted=false " +
//                "order by grounds.createTime desc";
//        Query query = getCurrentSession().createQuery(hql)
//                .setParameter(0, countryCode);
//        query.setFirstResult((page - 1) * pageSize);
//        query.setMaxResults(pageSize);
//        List<FootballGround> list = query.list();
//        if (list != null && list.size() > 0)
//            return list;
//
//        return null;
//    }
//}
