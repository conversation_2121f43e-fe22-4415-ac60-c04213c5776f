package com.microteam.base.agency.service.impl;

import com.microteam.base.agency.dao.FootballAgencyClassAbsenceDao;
import com.microteam.base.agency.service.FootballAgencyClassAbsenceDaoService;
import com.microteam.base.entity.agency.FootballAgencyClassAbsence;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.List;

@Service("footballAgencyClassAbsenceDaoService")
public class FootballAgencyClassAbsenceDaoServiceImpl implements FootballAgencyClassAbsenceDaoService {

    static Logger logger = Logger.getLogger(FootballAgencyClassAbsenceDaoServiceImpl.class.getName());
    @Autowired
    private FootballAgencyClassAbsenceDao dao;

    @Override
    public FootballAgencyClassAbsence findAbsenceById(long id) {
        return dao.findAbsenceById(id);
    }

    @Override
    public List<FootballAgencyClassAbsence> findByAgencyIdAndAgencyClassIdForPage(Long agencyId, Long agencyClassId, int pageSize, int page) {
        if (page > 0) {
            page--;
        }
        Pageable pageable = PageRequest.of(page, pageSize);
        return dao.findByAgencyIdAndAgencyClassIdForPage(agencyId, agencyClassId, pageable);
    }

    //查询班级课程请假名单
    @Override
    public List<FootballAgencyClassAbsence> findByAgencyIdAndAgencyClassIdAndAgencyClassCourseId(Long agencyId, Long agencyClassId, Long agencyClassCourseId) {
        return dao.findByAgencyIdAndAgencyClassIdAndAgencyClassCourseId(agencyId, agencyClassId, agencyClassCourseId);
    }


}
