package com.microteam.base.lesson.service;


import com.microteam.base.entity.lesson.FootballLesson;

import java.util.Date;
import java.util.List;

public interface FootballLessonDaoService {

    //查询用户创建的课程
    List<FootballLesson> findByUserIdForPage(Long userId, int page, int pageSize);

    //查询所属球队创建额课程
    List<FootballLesson> findByTeamIdListForPage(List<Long> teamIdList, int page, int pageSize);

    List<FootballLesson> findByTeamIdList(List<Long> teamIdList);

    FootballLesson findById(long id);

    List<FootballLesson> findByLessonIdAndUserId(Long lessonId, Long userId);

    List<FootballLesson> findOpenness();

    //查询用户创建的距离当前时间最近的课程
    FootballLesson findNearestByUserId(Long userId);

    //按照(未完成/已完成 )(创建时间排序/开始时间排序)查询用户创建课程
//    List<FootballLesson> findByUserIdAndStatusAndTimeForPage(Long userId, String status, String sortTime, int page, int pageSize);

    List<FootballLesson> findFinishedByUserIdForPage(Long userId, int page, int pageSize);

    List<FootballLesson> findUnfinishedByUserIdForPage(Long userId, int page, int pageSize);

    List<FootballLesson> findUnfinishedByUserIdAndStartTimeForDay(Long userId, Date startTime);

    FootballLesson findNearestByTeamIdList(List<Long> teamIdList);

    List<FootballLesson> findByUserIdAndTeamIdListAndTimeForDay(Long userId, List<Long> teamIdList, Date time);

    int findCountByUserIdAndAgencyId(Long userId, Long agencyId);

    //查询该教练之前有没有没有上传数据的课程
    List<FootballLesson> findNotOfLessonByUserId(Long lessonId, Long userId);

    List<FootballLesson> findByUserIdExceptLessonId(Long userId, Long lessonId);

    List<FootballLesson> findByTeamIdListAndUserId(List<Long> teamIdList, Long userId);

    List<FootballLesson> findByIdList(List<Long> idList);

    List<FootballLesson> findByUserIdOfTimeSlot(Long userId, Long teamId, int year, int month);

    List<FootballLesson> findUnStarted();

    void deleteById(Long id);

    int deleteByIdList(List<Long> idList);

    List<Long> findUserIdListByLessonIdList(List<Long> lessonIdList);

    FootballLesson save(FootballLesson lesson);

    int updateStatusById(Long id);

//    List<FootballLesson> findUnfinishedByTeamIdListAndStartTimeForDay(List<Long> teamIdList, Date startTime);
}
