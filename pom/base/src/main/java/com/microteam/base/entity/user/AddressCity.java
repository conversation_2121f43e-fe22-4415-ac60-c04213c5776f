package com.microteam.base.entity.user;

import com.microteam.base.common.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.io.Serializable;

@Entity
@Table(name = "address_city")
@Getter
@Setter
public class AddressCity extends BaseEntity implements Serializable {

    @Column(name = "code", nullable = false, length = 15)
    private String code;
    @Column(name = "city", nullable = false, length = 30)
    private String city;

}
