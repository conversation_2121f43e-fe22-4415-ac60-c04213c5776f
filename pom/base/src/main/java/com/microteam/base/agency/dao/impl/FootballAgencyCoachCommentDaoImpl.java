//package com.microteam.base.agency.dao.impl;
//
//import com.microteam.base.agency.dao.FootballAgencyCoachCommentDao;
//import com.microteam.base.entity.agency.FootballAgencyCoachComment;
//import com.microteam.base.common.base.AbstractHibernateDao;
//import org.apache.log4j.Logger;
//import org.hibernate.query.Query;
//import org.springframework.stereotype.Repository;
//
//import java.util.List;
//
//@Repository("footballAgencyCoachCommentDao")
//public class FootballAgencyCoachCommentDaoImpl extends AbstractHibernateDao<FootballAgencyCoachComment> implements FootballAgencyCoachCommentDao {
//    static Logger logger = Logger.getLogger(FootballAgencyCoachCommentDaoImpl.class.getName());
//
//    public FootballAgencyCoachCommentDaoImpl() {
//        super();
//        setClazz(FootballAgencyCoachComment.class);
//    }
//
//    @Override
//    public List<FootballAgencyCoachComment> findByAgencyCoachIdForPage(Long agencyCoachId, int page, int pageSize) {
//        String hql = "from FootballAgencyCoachComment as agencyCoachComment " +
//                "left join fetch agencyCoachComment.user as user " +
//                "where agencyCoachComment.footballAgencyCoach.id = (:agencyCoachId) " +
//                "and agencyCoachComment.deleted=false ";
//        Query query = getCurrentSession().createQuery(hql)
//                .setParameter("agencyCoachId", agencyCoachId);
//        query.setFirstResult((page - 1) * pageSize);
//        query.setMaxResults(pageSize);
//        return (List<FootballAgencyCoachComment>) query.list();
//    }
//
//}
