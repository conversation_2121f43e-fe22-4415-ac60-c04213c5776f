package com.microteam.base.telMailValiManage.dao;


import com.microteam.base.entity.telMailValiManage.TelMailValiInfo;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;

public interface MobileMessageDao extends JpaRepository<TelMailValiInfo, Long>, JpaSpecificationExecutor<TelMailValiInfo> {

    //根据手机号查询MobileMessage--gerald
    @Query("from TelMailValiInfo as mobileMessage " +
            "where mobileMessage.valiAccount=?1 " +
            "and mobileMessage.enabled=true " +
            "and mobileMessage.deleted=false " +
            "order by mobileMessage.valiStartTime desc")
    TelMailValiInfo findByValiAccount(String phoneNumber);

    //删除MobileMessage--gerald
    @Query("update TelMailValiInfo as mm " +
            "set mm.deleted=true " +
            "where mm.valiAccount=?1 " +
            "and mm.enabled=true")
    @Modifying
    int delByValiAccount(String phoneNumber);
}
