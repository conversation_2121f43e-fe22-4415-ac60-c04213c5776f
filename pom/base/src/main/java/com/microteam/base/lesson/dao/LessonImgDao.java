package com.microteam.base.lesson.dao;

import com.microteam.base.entity.lesson.LessonImg;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

public interface LessonImgDao extends JpaRepository<LessonImg, Long>, JpaSpecificationExecutor<LessonImg> {

    @Query("from LessonImg as lessonImg " +
            "where lessonImg.lessonId = (:lessonId) " +
            "and lessonImg.deleted = false ")
    List<LessonImg> findByLessonId(@Param("lessonId") Long lessonId);
}
