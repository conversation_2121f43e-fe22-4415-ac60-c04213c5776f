package com.microteam.base.lesson.service;


import com.microteam.base.entity.lesson.FootballLessonTakeNodes;

import java.util.List;

public interface FootballLessonTakeNodesDaoService {

    List<FootballLessonTakeNodes> findByLessonId(Long lessonId);

    FootballLessonTakeNodes findByLessonIdAndTeamIdAndUserId(Long lessonId, Long teamId, Long userId);

//    int saveNodeList(List<LessonTakeNodePojo> pojoList);

    List<FootballLessonTakeNodes> findByLessonIdAndTeamId(Long lessonId, Long teamId);

    int deleteByTeamId(Long teamId);
}
