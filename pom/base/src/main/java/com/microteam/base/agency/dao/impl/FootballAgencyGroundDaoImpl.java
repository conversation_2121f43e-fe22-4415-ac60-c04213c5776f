//package com.microteam.base.agency.dao.impl;
//
//import com.microteam.base.agency.dao.FootballAgencyGroundDao;
//import com.microteam.base.entity.agency.FootballAgency;
//import com.microteam.base.entity.agency.FootballAgencyGround;
//import com.microteam.base.common.base.AbstractHibernateDao;
//import org.apache.log4j.Logger;
//import org.hibernate.query.Query;
//import org.springframework.stereotype.Repository;
//
//import java.util.List;
//
//@Repository("footballAgencyGroundDao")
//public class FootballAgencyGroundDaoImpl extends
//        AbstractHibernateDao<FootballAgencyGround> implements FootballAgencyGroundDao {
//    static Logger logger = Logger.getLogger(FootballAgencyGroundDaoImpl.class.getName());
//
//    public FootballAgencyGroundDaoImpl() {
//        super();
//
//        setClazz(FootballAgencyGround.class);
//    }
//
//    //分页查询机构场地
//    @Override
//    public List<FootballAgencyGround> findAgencyGroundList(
//            FootballAgency agency, int page, int pageSize) {
//        String hql = "from FootballAgencyGround as  agencyGround where agencyGround.footballAgency=? and agencyGround.deleted=false order by agencyGround.createTime desc ";
//        Query query = getCurrentSession().createQuery(hql).setParameter(0, agency);
//        query.setFirstResult((page - 1) * pageSize);
//        query.setMaxResults(pageSize);
//        List<FootballAgencyGround> list = query.list();
//        return list;
//    }
//
//    @Override
//    public FootballAgencyGround findById(long id) {
//        String hql = "from FootballAgencyGround as agencyGround " +
//                "where agencyGround.id=? " +
//                "and agencyGround.deleted=false ";
//        Query query = getCurrentSession().createQuery(hql)
//                .setParameter("id", id);
//        query.setMaxResults(1);
//        FootballAgencyGround result = (FootballAgencyGround) query.uniqueResult();
//        return result;
//    }
//}
