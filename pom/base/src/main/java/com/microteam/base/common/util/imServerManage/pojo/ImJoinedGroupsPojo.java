package com.microteam.base.common.util.imServerManage.pojo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import java.util.Date;
import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
public class ImJoinedGroupsPojo implements java.io.Serializable {
    private String action;
    private String application;
    private ImResponseParams params;
    private String path;
    private String uri;
    private ImResponseEntities[] entities;
    private List<ImJoinedGroupsDataPojo> data;
    private Date timestamp;
    private Long duration;
    private String organization;
    private String applicationName;

    public String getAction() {
        return action;
    }

    public void setAction(String action) {
        this.action = action;
    }

    public String getApplication() {
        return application;
    }

    public void setApplication(String application) {
        this.application = application;
    }

    public ImResponseParams getParams() {
        return params;
    }

    public void setParams(ImResponseParams params) {
        this.params = params;
    }

    public String getPath() {
        return path;
    }

    public void setPath(String path) {
        this.path = path;
    }

    public String getUri() {
        return uri;
    }

    public void setUri(String uri) {
        this.uri = uri;
    }

    public ImResponseEntities[] getEntities() {
        return entities;
    }

    public void setEntities(ImResponseEntities[] entities) {
        this.entities = entities;
    }

    public List<ImJoinedGroupsDataPojo> getData() {
        return data;
    }

    public void setData(List<ImJoinedGroupsDataPojo> data) {
        this.data = data;
    }

    public Date getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(Date timestamp) {
        this.timestamp = timestamp;
    }

    public Long getDuration() {
        return duration;
    }

    public void setDuration(Long duration) {
        this.duration = duration;
    }

    public String getOrganization() {
        return organization;
    }

    public void setOrganization(String organization) {
        this.organization = organization;
    }

    public String getApplicationName() {
        return applicationName;
    }

    public void setApplicationName(String applicationName) {
        this.applicationName = applicationName;
    }
}
