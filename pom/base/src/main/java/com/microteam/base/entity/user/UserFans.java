package com.microteam.base.entity.user;


import com.microteam.base.common.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;
import java.io.Serializable;

@Entity
@Table(name = "user_fans")
@Getter
@Setter
public class UserFans extends BaseEntity implements Serializable {

    @ManyToOne
    @JoinColumn(name = "userId", nullable = false)
    private User user;
    @Column(name = "fansId", nullable = false)
    private long fansId;

}
