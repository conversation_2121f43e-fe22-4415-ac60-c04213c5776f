package com.microteam.base.common.constant;

import org.springframework.beans.factory.annotation.Value;

/**
 * userManage项目的常量；；
 */
public final class ConstantUserManage {

    public static final String MD5Salt = "VSteam20150115";
    public static final String MD5Algorithm = "MD5"; //'MD5"或"SHA"选一个；
    public static final Long TokenTenYears = 315360000000L; //10年(yr)=315360000000毫秒(ms)
    public static final int ImUsernameLength = 10;//目前，10位数，不足补0；
    public static final String ChatBackUpForToken = "ImAccessToken";
    public static final String ChatBackUpForInfo = "InfoBackUp";
    public static final Long checkTimeMsec = 60000L;//时间段来截取数据

    public static String getLongTenLetter(Long data) {

        StringBuilder str = new StringBuilder(Long.toString(data));
        while (str.length() < ImUsernameLength) {
            str.insert(0, "0");
        }
        return str.toString();
    }

    //截取时间段
    public static final Long tenMsec = 600000L;//十分钟
    public static final Long twentyMsec = 1200000L;//二十分钟
    public static final Long thirtyMsec = 1800000L;//三十分钟
    public static final Long fortyMsec = 2400000L;//四十分钟
    public static final Long fortyFiveMsec = 2700000L;//四十五分钟
    public static final Long fiftyFiveMsec = 3300000L;//五十五分钟
    public static final Long sixtyFiveMsec = 3900000L;//六十五分钟
    public static final Long sevenTyFiveMsec = 4500000L;//七十五分钟
    public static final Long eightyFiveMsec = 5100000L;//八十五分钟
    public static final Long ninetyMsec = 5400000L;//九十分钟

    @Value("${custom.umengProductionMode}")
    public static boolean UMENGPRODUCTIONMODE;
}
