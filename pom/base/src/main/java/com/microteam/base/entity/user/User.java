package com.microteam.base.entity.user;


import com.microteam.base.common.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Calendar;
import java.util.Date;

@Entity
@Table(name = "user", uniqueConstraints = @UniqueConstraint(columnNames = "userName"))
@Getter
@Setter
public class User extends BaseEntity implements Serializable {

    @ManyToOne
    @JoinColumn(name = "tokenId", nullable = false)
    private Tokens tokens;
    @Column(name = "userName", unique = true, length = 50)
    private String userName;
    @Column(name = "nickName", length = 50)
    private String nickName;
    @Column(name = "passWordMD5", length = 70)
    private String passWordMd5;
    @Column(name = "teamsType", length = 50)
    private String teamsType;
    @Column(name = "signature", length = 100)
    private String signature;
    @Column(name = "bust")
    private Integer bust;
    @Column(name = "waist")
    private Integer waist;
    @Column(name = "hip")
    private Integer hip;
    @Column(name = "sportsDescription", length = 250)
    private String sportsDescription;
    @Column(name = "description")
    private String description;
    @Column(name = "imPassWord", length = 30)
    private String imPassWord;
    @Column(name = "emailAddress", length = 50)
    private String emailAddress;
    @Column(name = "sex")
    private Short sex;
    @Column(name = "wxUnionId", length = 70)
    private String wxUnionId;
    @Column(name = "imUserUUID", length = 70)
    private String imUserUuid;
    @Column(name = "ages")
    private Short ages;
    @Column(name = "age_time")
    private Long ageTime;//时间戳
    @Column(name = "height")
    private Short height;
    @Column(name = "credits")
    private Integer credits;
    @Column(name = "creditsRank")
    private String creditsRank;
    @Column(name = "weight")
    private Short weight;
    @Column(name = "modifier")
    private Long modifier;
    @Column(name = "imEnabled", nullable = false)
    private boolean imEnabled;
    @Column(name = "enabled", nullable = false)
    private boolean enabled;
    @Column(name = "deviceTokens", length = 200)
    private String deviceTokens;
    @Column(name = "bindWeixin", length = 60)
    private String bindWeixin;
    @Column(name = "bindWeixinName")
    private String bindWeixinName;
    @Column(name = "bindQq", length = 60)
    private String bindQq;
    @Column(name = "bindQqName")
    private String bindQqName;
    @Column(name = "bindWeibo", length = 60)
    private String bindWeibo;
    @Column(name = "bindWeChatSubscripUser", length = 60)
    private String bindWeChatSubscripUser;
    @Column(name = "appleUserId")
    private String appleUserId;
    @Column(name = "appleUserName")
    private String appleUserName;
    @Column(name = "school", length = 65535)
    private String school;
    @Column(name = "curResidence", length = 65535)
    private String curResidence;
    @Column(name = "homeLand", length = 65535)
    private String homeLand;
    @Column(name = "yearsPro")
    private Integer yearPro; //球龄
    @Column(name = "location")
    private Integer location; //场上位置
    @Column(name = "isfilledin")
    private Short isFilledIn;
    @Column(name = "role")
    private Short role;
    @Column(name = "countryName")
    private String countryName;

    public Short getAges() {
        if (ageTime == null) {
            ageTime = 0L;
        }
        Date birthDay = new Date(ageTime);
        Calendar cal = Calendar.getInstance();
        if (cal.before(birthDay)) {
            throw new IllegalArgumentException(
                    "The birthDay is before Now.It's unbelievable!");
        }
        int yearNow = cal.get(Calendar.YEAR);
        int monthNow = cal.get(Calendar.MONTH);
        int dayOfMonthNow = cal.get(Calendar.DAY_OF_MONTH);
        cal.setTime(birthDay);
        int yearBirth = cal.get(Calendar.YEAR);
        int monthBirth = cal.get(Calendar.MONTH);
        int dayOfMonthBirth = cal.get(Calendar.DAY_OF_MONTH);
        int age = yearNow - yearBirth;
        if (monthNow <= monthBirth) {
            if (monthNow == monthBirth) {
                if (dayOfMonthNow < dayOfMonthBirth) age--;
            } else {
                age--;
            }
        }
        return (short) age;
    }

}
