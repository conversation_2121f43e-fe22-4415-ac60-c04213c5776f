package com.microteam.base.agency.dao;

import com.microteam.base.entity.agency.FootballAgency;
import com.microteam.base.entity.agency.FootballAgencyAlbum;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

public interface FootballAgencyAlbumDao extends JpaRepository<FootballAgencyAlbum, Long>, JpaSpecificationExecutor<FootballAgencyAlbum> {
    //查询机构相册是否存在
    @Query("from  FootballAgencyAlbum as footballAgencyAlbum " +
            "where footballAgencyAlbum.footballAgency=?1 " +
            "and footballAgencyAlbum.albumName=?2 " +
            "and footballAgencyAlbum.deleted=false ")
    FootballAgencyAlbum findAlbumByAgency(FootballAgency footballAgency, String name);

    @Query("from  FootballAgencyAlbum as footballAgencyAlbum " +
            "where footballAgencyAlbum.footballAgency=?1 " +
            "and footballAgencyAlbum.deleted=false ")
    List<FootballAgencyAlbum> findAlbumByAgencyAndPage(FootballAgency footballAgency, Pageable pageable);

    @Query("from  FootballAgencyAlbum as footballAgencyAlbum " +
            "left join fetch footballAgencyAlbum.user as user " +
            "where footballAgencyAlbum.id=?1 " +
            "and footballAgencyAlbum.deleted=false ")
    FootballAgencyAlbum findAlbumById(long id);
}
