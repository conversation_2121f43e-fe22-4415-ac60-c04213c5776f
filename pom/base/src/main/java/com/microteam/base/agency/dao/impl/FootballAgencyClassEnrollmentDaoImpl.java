//package com.microteam.base.agency.dao.impl;
//
//import com.microteam.base.agency.dao.FootballAgencyClassEnrollmentDao;
//import com.microteam.base.entity.agency.FootballAgencyClassEnrollment;
//import com.microteam.base.common.base.AbstractHibernateDao;
//import org.apache.log4j.Logger;
//import org.hibernate.query.Query;
//import org.springframework.stereotype.Repository;
//
//import java.util.List;
//
//@Repository("footballAgencyClassEnrollmentDao")
//public class FootballAgencyClassEnrollmentDaoImpl extends AbstractHibernateDao<FootballAgencyClassEnrollment> implements FootballAgencyClassEnrollmentDao {
//    static Logger logger = Logger.getLogger(FootballAgencyClassEnrollmentDaoImpl.class.getName());
//
//    public FootballAgencyClassEnrollmentDaoImpl() {
//        super();
//        setClazz(FootballAgencyClassEnrollment.class);
//    }
//
//    //根据Id查询
//    @Override
//    public FootballAgencyClassEnrollment findById(long id) {
//        String hql = "from FootballAgencyClassEnrollment as agencyClassEnrollment " +
//                "left join fetch agencyClassEnrollment.footballAgency as footballAgency " +
//                "left join fetch footballAgency.groups as groups " +
//                "left join fetch agencyClassEnrollment.footballAgencyClassCourse as footballAgencyClassCourse " +
//                "left join fetch agencyClassEnrollment.footballAgencyClass as footballAgencyClass " +
//                "left join fetch footballAgencyClass.user as user " +
//                "where agencyClassEnrollment.id=? " +
//                "and agencyClassEnrollment.deleted=false ";
//        Query query = getCurrentSession().createQuery(hql)
//                .setParameter(0, id);
//        List<FootballAgencyClassEnrollment> list = query.list();
//        if (list != null && list.size() > 0) {
//            return list.get(0);
//        }
//        return null;
//    }
//
//    //查询机构下的报名情况
//    @Override
//    public List<FootballAgencyClassEnrollment> findByAgencyId(Long agencyId) {
//        String hql = "from FootballAgencyClassEnrollment as agencyClassEnrollment " +
//                "where agencyClassEnrollment.footballAgency.id = (:agencyId) " +
//                "and agencyClassEnrollment.deleted=false ";
//        Query query = getCurrentSession().createQuery(hql)
//                .setParameter("agencyId", agencyId);
//        List<FootballAgencyClassEnrollment> list = query.list();
//        return list;
//    }
//
//    //分页查询机构下的报名情况
//    @Override
//    public List<FootballAgencyClassEnrollment> findByAgencyIdForPage(Long agencyId, int page, int pageSize) {
//        String hql = "from FootballAgencyClassEnrollment as agencyClassEnrollment " +
//                "left join fetch agencyClassEnrollment.footballAgencyClass as footballAgencyClass " +
//                "where agencyClassEnrollment.footballAgency.id = (:agencyId) " +
//                "and agencyClassEnrollment.deleted=false ";
//        Query query = getCurrentSession().createQuery(hql)
//                .setParameter("agencyId", agencyId);
//        query.setFirstResult((page - 1) * pageSize);
//        query.setMaxResults(pageSize);
//        List<FootballAgencyClassEnrollment> list = query.list();
//        return list;
//    }
//
//    @Override
//    public List<FootballAgencyClassEnrollment> findByAgencyIdAndAgencyClassId(Long agencyId, Long agencyClassId) {
//        String hql = "from FootballAgencyClassEnrollment as agencyClassEnrollment " +
//                "left join fetch agencyClassEnrollment.footballAgencyClass as footballAgencyClass " +
//                "where agencyClassEnrollment.footballAgency.id = (:agencyId) " +
//                "and agencyClassEnrollment.footballAgencyClass.id = (:agencyClassId) " +
//                "and agencyClassEnrollment.deleted=false ";
//        Query query = getCurrentSession().createQuery(hql)
//                .setParameter("agencyId", agencyId)
//                .setParameter("agencyClassId", agencyClassId);
//        List<FootballAgencyClassEnrollment> list = query.list();
//        return list;
//    }
//
//    @Override
//    public List<FootballAgencyClassEnrollment> findByAgencyIdAndAgencyClassIdForPage(Long agencyId, Long agencyClassId, int page, int pageSize) {
//        String hql = "from FootballAgencyClassEnrollment as agencyClassEnrollment " +
//                "left join fetch agencyClassEnrollment.footballAgencyClass as footballAgencyClass " +
//                "where agencyClassEnrollment.footballAgency.id = (:agencyId) " +
//                "and agencyClassEnrollment.footballAgencyClass.id = (:agencyClassId) " +
//                "and agencyClassEnrollment.deleted=false ";
//        Query query = getCurrentSession().createQuery(hql)
//                .setParameter("agencyId", agencyId)
//                .setParameter("agencyClassId", agencyClassId);
//        query.setFirstResult((page - 1) * pageSize);
//        query.setMaxResults(pageSize);
//        List<FootballAgencyClassEnrollment> list = query.list();
//        return list;
//    }
//
//
//}
