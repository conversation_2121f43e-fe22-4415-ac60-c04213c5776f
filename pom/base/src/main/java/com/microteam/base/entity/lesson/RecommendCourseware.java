package com.microteam.base.entity.lesson;

import com.microteam.base.common.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;
import java.io.Serializable;

@Entity
@Table(name = "recommend_courseware")
@Getter
@Setter
public class RecommendCourseware extends BaseEntity implements Serializable {

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "coursewareId", nullable = false)
    private FootballCourseWare courseware;
    //1、热门推荐 2、本周推荐
    @Column
    private Integer type;
    @Column
    private Integer order;
}
