package com.microteam.base.agency.service.impl;

import com.microteam.base.agency.dao.FootballAgencyClassEnrollmentMemberDao;
import com.microteam.base.agency.service.FootballAgencyClassEnrollmentMemberDaoService;
import com.microteam.base.entity.agency.FootballAgencyClassEnrollmentMember;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.List;

@Service("footballAgencyClassEnrollmentMemberDaoService")
public class FootballAgencyClassEnrollmentMemberDaoServiceImpl implements FootballAgencyClassEnrollmentMemberDaoService {
    static Logger logger = Logger.getLogger(FootballAgencyClassEnrollmentMemberDaoServiceImpl.class.getName());
    @Autowired
    private FootballAgencyClassEnrollmentMemberDao dao;

    //分页查询用户报名列表
    @Override
    public List<FootballAgencyClassEnrollmentMember> findByEnrollmentIdAndRoleAndNickNameForPage(Long enrollmentId, short userRole, int page, int pageSize, String search) {
        if (page > 0) {
            page--;
        }
        Pageable pageable = PageRequest.of(page, pageSize);
        if (!"".equals(search)) {
            search = '%' + search + '%';
        }
        return dao.findByEnrollmentIdAndRoleAndNickNameForPage(enrollmentId, userRole, search, pageable);
    }

    @Override
    public FootballAgencyClassEnrollmentMember findById(long id) {
        return dao.findById(id);
    }

    //分页查询用户报名列表
    @Override
    public List<FootballAgencyClassEnrollmentMember> findByEnrollmentIdAndRoleForPage(Long enrollmentId, short userRole, int page, int pageSize) {
        if (page > 0) {
            page--;
        }
        Pageable pageable = PageRequest.of(page, pageSize);
        return dao.findByEnrollmentIdAndRoleForPage(enrollmentId, userRole, pageable);
    }

    //查询学员申请列表
    @Override
    public List<FootballAgencyClassEnrollmentMember> findByEnrollmentId(Long enrollmentId) {
        return dao.findByEnrollmentId(enrollmentId);
    }


}
