package com.microteam.base.entity.agency;


import com.microteam.base.common.base.BaseEntity;
import com.microteam.base.entity.user.User;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;
import java.io.Serializable;

@Entity
@Table(name = "football_agency_coach_comment")
@Getter
@Setter
public class FootballAgencyCoachComment extends BaseEntity implements Serializable {

    @ManyToOne
    @JoinColumn(name = "coachId", nullable = false)
    private FootballAgencyCoach footballAgencyCoach;
    @ManyToOne
    @JoinColumn(name = "userId", nullable = false)
    private User user;
    @Column(name = "comment", length = 65535)
    private String comment;
    @Column(name = "coachScore")
    private Integer coachScore;

}
