package com.microteam.base.agency.dao;

import com.microteam.base.entity.agency.FootballAgency;
import com.microteam.base.entity.agency.FootballAgencyMessageBoard;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

public interface FootballAgencyMessageBoardDao extends JpaRepository<FootballAgencyMessageBoard, Long>, JpaSpecificationExecutor<FootballAgencyMessageBoard> {

    //分页查询机构下的留言板
    @Query("from FootballAgencyMessageBoard as agencyMessageBoard " +
            "where agencyMessageBoard.footballAgency=?1 " +
            "and agencyMessageBoard.deleted=false " +
            "order by agencyMessageBoard.createTime desc ")
    List<FootballAgencyMessageBoard> findMessBoardByPage(FootballAgency agency, Pageable pageable);
}
