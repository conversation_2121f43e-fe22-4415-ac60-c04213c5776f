package com.microteam.base.entity.user;


import com.microteam.base.common.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;
import java.io.Serializable;

@Entity
@Table(name = "user_log")
@Getter
@Setter
public class UserLog extends BaseEntity implements Serializable {

    @ManyToOne
    @JoinColumn(name = "userId", nullable = false)
    private User user;
    @Column(name = "action", length = 20)
    private String action;
    @Column(name = "method", length = 20)
    private String method;
    @Column(name = "uri", length = 250)
    private String uri;
    @Column(name = "accessToken", length = 64)
    private String accessToken;
    @Column(name = "data")
    private String data;
    @Column(name = "result", length = 250)
    private String result;
    @Column(name = "duration")
    private Long duration;

}
