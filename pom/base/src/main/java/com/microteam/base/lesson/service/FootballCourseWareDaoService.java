package com.microteam.base.lesson.service;


import com.microteam.base.entity.lesson.FootballCourseWare;

import java.util.List;

public interface FootballCourseWareDaoService {
    //查询所有课件
    List<FootballCourseWare> findOpennessForPage(int page, int pageSize);

    //根据idList查询
    List<FootballCourseWare> findByIdList(List<Long> idList);

    FootballCourseWare findByLessonId(long lessonId);

    List<FootballCourseWare> findByGradeAndMotionIdAndLevelAndOrder(int grade, Long motionId, int level, int order, int page, int pageSize);

    FootballCourseWare findById(long id);

    FootballCourseWare findByCourseName(String courseName);

    List<FootballCourseWare> findHotTop(int page, int pageSize);

    List<FootballCourseWare> findNewTop(int page, int pageSize);

    List<FootballCourseWare> findByType(Integer type, int page, int pageSize);

    List<FootballCourseWare> searchByNameAndType(String searchName, int type, int page, int pageSize);

    List<FootballCourseWare> findByChoice(Integer choice,Long userId);

    List<FootballCourseWare> findByVsteam();

    List<FootballCourseWare> findBynoVsteam(Long userId);

    List<FootballCourseWare> findByOpen(Integer open);

    FootballCourseWare save(FootballCourseWare courseWare);

    int updateImg(Long id,String headImg);

    int updateVideo(Long id,String video);

    int saveFoundIsOpen(Long id,Integer isOpen);

    List<FootballCourseWare> findByDivide(Integer divide);

    int updateQuote(Long id,Integer quote);

    List<FootballCourseWare> remCourseware(Integer actionClassification,Integer level);
}
