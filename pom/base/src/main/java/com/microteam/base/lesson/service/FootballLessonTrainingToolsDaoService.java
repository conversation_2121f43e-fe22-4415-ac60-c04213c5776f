package com.microteam.base.lesson.service;


import com.microteam.base.entity.lesson.FootballLessonTrainingTools;

import java.util.List;

public interface FootballLessonTrainingToolsDaoService {

    List<FootballLessonTrainingTools> findByLessonId(Long lessonId);

    //保存课程选择的训练习工具
//    boolean saveSelectedLessonTrainingTools(Long lessonId, List<Long> tools);

    //保存课程选择的训练习人员
//    boolean saveSelectedLessonTrainingUsers(Long lessonId, Long teamId, List<Long> userIdList);

    int deleteByLessonIdList(List<Long> lessonIdList);

    FootballLessonTrainingTools save(FootballLessonTrainingTools footballLessonTrainingTools);

    List<FootballLessonTrainingTools> fingByCourwareId(Long courwareId);
}
