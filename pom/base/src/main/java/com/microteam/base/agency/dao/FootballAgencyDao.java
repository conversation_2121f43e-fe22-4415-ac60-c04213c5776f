package com.microteam.base.agency.dao;

import com.microteam.base.entity.agency.FootballAgency;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

public interface FootballAgencyDao extends JpaRepository<FootballAgency, Long>, JpaSpecificationExecutor<FootballAgency> {
    //根据Id查询机构
    @Query("from FootballAgency as footballAgency " +
            "left join fetch footballAgency.user as user " +
            "left join fetch footballAgency.groups as groups " +
            "where footballAgency.id=?1 " +
            "and footballAgency.deleted=false ")
    FootballAgency findById(long id);

//    //查询培训机构列表
//    List<FootballAgency> findByCodeAndSearchForPage(String cityCode, String countyCode, int page, int pageSize, String search);

    //查询我创建的机构列表
//    List<FootballAgency> findAgencyListByPageAndMy(String cityCode, String countyCode, int page, int pageSize, String search, User user);

//    List<FootballAgency> findAgencyListByPageAndMyInfo(String cityCode, String countyCode, int page, int pageSize, String search, String audit, User user);

//    List<FootballAgency> findAgencyListByPageAndMyInfoAndType(String cityCode, String countyCode, int page, int pageSize, String search, String audit, User user, Integer type);

    //查询不是我创建的机构列表
//    List<FootballAgency> findAgencyListByPageAndNotMy(String cityCode, String countyCode, int page, int pageSize, String search, User user);

    @Query("from FootballAgency as footballAgency " +
            "left join fetch footballAgency.user as user " +
            "left join fetch footballAgency.groups as groups " +
            "where footballAgency.id in (:idList) " +
            "and footballAgency.deleted=false ")
    List<FootballAgency> findYouthAgencyListByIdList(@Param("idList") List<Long> idList);

    @Query("from FootballAgency as footballAgency " +
            "left join fetch footballAgency.user as user " +
            "where user.id = (:userId) " +
            "and footballAgency.deleted = false")
    List<FootballAgency> findAgencyListUserId(@Param("userId")Long userId, Pageable pageable);

    @Query("from FootballAgency as footballAgency " +
            "left join fetch footballAgency.user as user " +
            "where user.id <> (:userId) " +
            "and footballAgency.deleted = false")
    List<FootballAgency> findAgencyListNoUserId(@Param("userId")Long userId, Pageable pageable);

//    @Query("from FootballAgency as footballAgency " +
//           "where footballAgency.deleted = false ")
//    List<FootballAgency> findAgencyListAll(@Param("page") int page,@Param("pageSize") int pageSize,@Param("cityCode") String cityCode,@Param("countyCode") String countyCode, @Param("search")String search, @Param("audit")Integer audit, @Param("type")Integer type);

/*    @Query("from FootballAgency as footballAgency " +
            "where footballAgency.agencyName like CONCAT('%',?1,'%')" +
            "and footballAgency.deleted = false")*/
    @Query(value = "select * from football_agency as footballAgency " +
        "where footballAgency.agencyName like CONCAT('%',:search,'%')" +
        " and footballAgency.deleted = false"
            ,nativeQuery = true)
    List<FootballAgency> findAgencyListAll(@Param("search")String search,Pageable pageable);

    @Query("from FootballAgency as footballAgency " +
            "left join fetch footballAgency.user as user " +
            "left join fetch footballAgency.groups as groups " +
            "where footballAgency.deleted=false ")
    List<FootballAgency> findFootballAgency(Pageable pageable);
}
