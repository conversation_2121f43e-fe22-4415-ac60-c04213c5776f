package com.microteam.base.entity.lesson;

import com.microteam.base.common.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;
import java.io.Serializable;

@Entity
@Table(name = "football_courseware")
@Getter
@Setter
public class FootballCourseWare extends BaseEntity implements Serializable {

    @Column(name = "courseName", length = 100)
    private String courseName;
    @Column(name = "courseIntro", length = 250)
    private String courseIntro;
    @Column(name = "courseNetUrl", length = 250)
    private String courseNetUrl;
    @Column(name = "openness")
    private Integer openness;
    @Column(name = "vsteam", nullable = false)
    private boolean vsteam;
    @Column(name = "choice")
    private Integer choice;
    @Column(name = "founderId")
    private Long founderId;
    @Column(name = "open")
    private Integer open;
    @Column(name = "divide")
    private Integer divide;
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "motionId", nullable = false)
    private Motion motion;
    @Column
    private Integer level;
    @Column
    private Integer difficulyt;
    @Column
    private Integer grade;
    @Column
    private Integer coursewareDuration;
    @Column
    private Integer applyAge;
    @Column
    private Integer actionClassification;
    @Column
    private Integer attention;
    @Column(name = "quote",nullable = false)
    private Integer quote;
    @Column
    private String area;
    @Column
    private String norm;
    @Column
    private String areaContent;
    @Column
    private String headImg;
    @Column
    private String video;
    //type 1:个人 2:团体
    @Column
    private Integer type;
    @Column
    private Integer isVideo;
    @Column
    private Integer english;
}
