package com.microteam.base.demoManager.dao;


import com.microteam.base.entity.demoManager.MacQRCode;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

public interface MacQRCodeDao extends JpaRepository<MacQRCode, Long>, JpaSpecificationExecutor<MacQRCode> {

    @Query("from MacQRCode as code " +
            "where code.deleted = false")
    List<MacQRCode> findAllCode();

    @Query("from MacQRCode as code where " +
            "code.mac = :mac " +
            "and code.deleted = false")
    MacQRCode findMacQRCode(@Param("mac") String mac);

    @Query("from MacQRCode as code " +
            "where code.qrCode = :qrCode " +
            "and code.deleted = false")
    MacQRCode findMacQRCodeByCode(@Param("qrCode") String qrCode);
}
