package com.microteam.base.integral.service.impl;

import com.microteam.base.common.util.common.TranslatorUtil;
import com.microteam.base.common.util.team.IntegralUtil;
import com.microteam.base.entity.integral.FootballIntegralHistory;
import com.microteam.base.entity.integral.FootballIntegralTeamHardwaremac;
import com.microteam.base.entity.integral.FootballIntegralType;
import com.microteam.base.entity.team.FootballTeam;
import com.microteam.base.entity.team.FootballTeamGame;
import com.microteam.base.entity.team.FootballTeamGameStatisticsPlayer;
import com.microteam.base.entity.team.FootballTeamUser;
import com.microteam.base.entity.user.User;
import com.microteam.base.entity.user.UserHardware;
import com.microteam.base.integral.dao.FootballIntegralHistoryDao;
import com.microteam.base.integral.service.FootballIntegralHistoryDaoService;
import com.microteam.base.integral.service.FootballIntegralTeamHardwaremacDaoService;
import com.microteam.base.integral.service.FootballIntegralTypeDaoService;
import com.microteam.base.team.service.FootballTeamDaoService;
import com.microteam.base.team.service.FootballTeamGameDaoService;
import com.microteam.base.team.service.FootballTeamGameStatisticsPlayerDaoService;
import com.microteam.base.team.service.FootballTeamUserDaoService;
import com.microteam.base.user.service.UserHardwareDaoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Service(value = "footballIntegralHistoryDaoService")
public class FootballIntegralHistoryDaoServiceImpl implements FootballIntegralHistoryDaoService {

    @Autowired
    FootballIntegralHistoryDao footballIntegralHistoryDao;
    @Autowired
    FootballIntegralTypeDaoService footballIntegralTypeDaoService;
    @Autowired
    FootballIntegralTeamHardwaremacDaoService footballIntegralTeamHardwaremacDaoService;
    @Autowired
    private FootballTeamDaoService footballTeamDaoService;
    @Autowired
    private UserHardwareDaoService userHardwareDaoService;
    @Autowired
    private FootballTeamGameDaoService footballTeamGameDaoService;
    @Autowired
    private FootballTeamGameStatisticsPlayerDaoService footballTeamGameStatisticsPlayerDaoService;
    @Autowired
    private FootballTeamUserDaoService footballTeamUserDaoService;

    @Override
    public List<FootballIntegralHistory> findAll() {
        return footballIntegralHistoryDao.findAll();
    }

    @Override
    public List<FootballIntegralHistory> findByTeamId(Long teamId) throws Exception {
        List<FootballIntegralHistory> integralHistoryList = footballIntegralHistoryDao.findByTeamId(teamId);
        if (integralHistoryList == null || integralHistoryList.isEmpty()) {
            return generalTeamData(teamId);
        }
        return integralHistoryList;
    }

    @Override
    public List<FootballIntegralHistory> findByTeamIdAndTypeId(Long teamId, Long typeId) {
        return footballIntegralHistoryDao.findByTeamIdAndTypeId(teamId, typeId);
    }

    @Override
    public List<FootballIntegralHistory> generalTeamData(Long teamId) throws Exception {
        FootballTeam team = footballTeamDaoService.findTeamsById(teamId);
        List<FootballIntegralType> footballIntegralTypeList = footballIntegralTypeDaoService.findAll();
        List<FootballIntegralHistory> integralHistoryList = new ArrayList<>();
        //球队创立成功
        FootballIntegralType integralType = footballIntegralTypeList.get(0);
        FootballIntegralHistory integralHistory = new FootballIntegralHistory();
        integralHistory.setTypeId(integralType.getId());
        integralHistory.setTeamId(teamId);
        integralHistory.setUserId(team.getUser().getId());
        integralHistory.setDeleted(false);
//        footballIntegralHistoryDao.save(integralHistory);
        integralHistoryList.add(integralHistory);
        //基本信息补充完整
        if (IntegralUtil.isFilledInTeam(team)) {
            FootballIntegralType integralType1 = footballIntegralTypeList.get(1);
            FootballIntegralHistory integralHistory1 = new FootballIntegralHistory();
            integralHistory1.setTypeId(integralType1.getId());
            integralHistory1.setTeamId(teamId);
            integralHistory1.setUserId(team.getUser().getId());
            integralHistory1.setDeleted(false);
//            footballIntegralHistoryDao.save(integralHistory1);
            integralHistoryList.add(integralHistory1);
        }
        //每加入一个成员
        List<FootballTeamUser> teamUserList = footballTeamUserDaoService.findByTeamId(team.getId());
//        List<UserHardwareTeam> userHardwareTeamList = userHardwareTeamDaoService.findByTeamForPage(team, 1, Integer.MAX_VALUE);
//        List<UserHardwareTeam> userHardwareTeamList = userHardwareTeamDaoService.findAllByTeam(team);
        List<FootballIntegralHistory> integralHistoryList1 = footballIntegralHistoryDao.findByTeamIdAndTypeId(teamId, 3L);
        List<Long> userIdList = new ArrayList<>();
        for (FootballIntegralHistory integralHistory1 : integralHistoryList1) {
            userIdList.add(integralHistory1.getUserId());
        }
        if (teamUserList != null) {
            for (FootballTeamUser teamUser : teamUserList) {
                if (!userIdList.contains(teamUser.getUser().getId())) {
                    FootballIntegralType integralType2 = footballIntegralTypeList.get(2);
                    FootballIntegralHistory integralHistory2 = new FootballIntegralHistory();
                    integralHistory2.setTypeId(integralType2.getId());
                    integralHistory2.setTeamId(teamId);
                    integralHistory2.setUserId(teamUser.getUser().getId());
                    integralHistory2.setDeleted(false);
//                footballIntegralHistoryDao.save(integralHistory2);
                    integralHistoryList.add(integralHistory2);
                }
            }
        }
        //成员每绑定一个设备
        if (teamUserList != null) {
            for (FootballTeamUser teamUser : teamUserList) {
                List<FootballIntegralTeamHardwaremac> hardwaremacList = footballIntegralTeamHardwaremacDaoService.findByTeamId(teamUser.getFootballTeam().getId());
                List<String> macList = new ArrayList<>();
                for (FootballIntegralTeamHardwaremac hardwaremac : hardwaremacList) {
                    macList.add(hardwaremac.getHardwareMac());
                }
                User user = teamUser.getUser();
                List<UserHardware> hardwareList = userHardwareDaoService.findByUserId(user.getId());
                if (hardwareList != null) {
                    for (UserHardware userHardware : hardwareList) {
                        if (!macList.contains(userHardware.getHardwareMac())) {
                            FootballIntegralType integralType3 = footballIntegralTypeList.get(3);
                            FootballIntegralHistory integralHistory3 = new FootballIntegralHistory();
                            integralHistory3.setTypeId(integralType3.getId());
                            integralHistory3.setTeamId(teamId);
                            integralHistory3.setUserId(teamUser.getUser().getId());
                            integralHistory3.setDeleted(false);
//                    footballIntegralHistoryDao.save(integralHistory3);
                            integralHistoryList.add(integralHistory3);
                            FootballIntegralTeamHardwaremac footballIntegralTeamHardwaremac = new FootballIntegralTeamHardwaremac();
                            footballIntegralTeamHardwaremac.setTeamId(teamId);
                            footballIntegralTeamHardwaremac.setHardwareMac(userHardware.getHardwareMac());
                            footballIntegralTeamHardwaremac.setDeleted(false);
                            footballIntegralTeamHardwaremac.setCreateTime(new Timestamp(System.currentTimeMillis()));
                            footballIntegralTeamHardwaremacDaoService.save(footballIntegralTeamHardwaremac);
                            macList.add(userHardware.getHardwareMac());
                        }
                    }
                }
            }
        }
        //球队每完成一场比赛并且获取数据
        List<FootballTeamGame> gameList = footballTeamGameDaoService.findByTeamName(team.getTeamName());
        List<Long> gameIdList = TranslatorUtil.getIdList(gameList, FootballTeamGame.class);
        List<FootballTeamGameStatisticsPlayer> statisticsPlayerList = footballTeamGameStatisticsPlayerDaoService.findByGameIdList(gameIdList);
        Map<FootballTeamGame, List<FootballTeamGameStatisticsPlayer>> map = (Map<FootballTeamGame, List<FootballTeamGameStatisticsPlayer>>) TranslatorUtil.listToMap("footballTeamGame", statisticsPlayerList, 2);
        for (FootballTeamGame game : gameList) {
            List<FootballTeamGameStatisticsPlayer> list = map.get(game);
            if (list != null && !list.isEmpty()) {
                FootballIntegralType integralType4 = footballIntegralTypeList.get(4);
                FootballIntegralHistory integralHistory4 = new FootballIntegralHistory();
                integralHistory4.setTypeId(integralType4.getId());
                integralHistory4.setTeamId(teamId);
                integralHistory4.setUserId(list.get(0).getUser().getId());
                integralHistory4.setDeleted(false);
//                footballIntegralHistoryDao.save(integralHistory4);
                integralHistoryList.add(integralHistory4);
            }
        }
//        footballIntegralHistoryDao.saveList(integralHistoryList);
        for (FootballIntegralHistory obj : integralHistoryList) {
            footballIntegralHistoryDao.save(obj);
        }
        return integralHistoryList;
    }

    @Override
    public List<FootballIntegralHistory> findNewByTeamId(Long teamId, Long lastAdd) {
        return footballIntegralHistoryDao.findNewByTeamId(teamId, lastAdd);
    }

    @Override
    public FootballIntegralHistory save(FootballIntegralHistory footballIntegralHistory) {
        return footballIntegralHistoryDao.save(footballIntegralHistory);
    }
}
