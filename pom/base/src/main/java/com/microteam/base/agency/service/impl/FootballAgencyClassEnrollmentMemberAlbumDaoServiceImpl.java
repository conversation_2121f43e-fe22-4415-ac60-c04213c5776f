package com.microteam.base.agency.service.impl;

import com.microteam.base.agency.dao.FootballAgencyClassEnrollmentMemberAlbumDao;
import com.microteam.base.entity.agency.FootballAgencyClassEnrollmentMemberAlbum;
import com.microteam.base.agency.service.FootballAgencyClassEnrollmentMemberAlbumDaoService;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service("footballAgencyClassEnrollmentMemberAlbumDaoService")
public class FootballAgencyClassEnrollmentMemberAlbumDaoServiceImpl implements FootballAgencyClassEnrollmentMemberAlbumDaoService {
    static Logger logger = Logger.getLogger(FootballAgencyClassEnrollmentMemberAlbumDaoServiceImpl.class.getName());
    @Autowired
    private FootballAgencyClassEnrollmentMemberAlbumDao dao;

    @Override
    public List<FootballAgencyClassEnrollmentMemberAlbum> findByEnrollmentMemberId(Long enrollmentMemberId) {
        return dao.findByEnrollmentMemberId(enrollmentMemberId);
    }


}
