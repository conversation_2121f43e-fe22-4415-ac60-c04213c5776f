package com.microteam.base.common.util.imServerManage.pojo;



import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

/**
*  IM response body 里面的data pojo类；
* 
* @param 
* @param 
* @return
*/
@JsonIgnoreProperties(ignoreUnknown = true)
public class ImResponseDataForAddMemberOne implements java.io.Serializable{
/*
	"action" : "add_member",
    "result" : true,
    "groupid" : "1411816013089",
    "user" : "q4xpsfjfvf"
*/	
	

		/**
		 * 
		 */
		
		private String action;
		private boolean result;
		private String groupid;
		private String user;
		
		public ImResponseDataForAddMemberOne() {
		}

		
		

		
		public ImResponseDataForAddMemberOne(String action, boolean result,
				String groupid, String user) {
			super();
			this.action = action;
			this.result = result;
			this.groupid = groupid;
			this.user = user;
		}





		public String getGroupid() {
			return this.groupid;
		}

		public void setGroupid(String groupid) {
			this.groupid = groupid;
		}





		public String getAction() {
			return action;
		}





		public void setAction(String action) {
			this.action = action;
		}





		public boolean isResult() {
			return result;
		}





		public void setResult(boolean result) {
			this.result = result;
		}





		public String getUser() {
			return user;
		}





		public void setUser(String user) {
			this.user = user;
		}


		
		
}