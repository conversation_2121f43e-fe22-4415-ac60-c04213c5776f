package com.microteam.base.lesson.service.impl;

import com.microteam.base.entity.lesson.FootballLessonSecondEnroll;
import com.microteam.base.lesson.dao.FootballLessonSecondEnrollDao;
import com.microteam.base.lesson.service.FootballLessonSecondEnrollDaoService;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service("lessonSecondEnrollDaoService")
public class FootballLessonSecondEnrollDaoServiceImpl implements FootballLessonSecondEnrollDaoService {
    static Logger logger = Logger.getLogger(FootballLessonSecondEnrollDaoServiceImpl.class.getName());

    @Autowired
    FootballLessonSecondEnrollDao dao;

    @Override
    public List<FootballLessonSecondEnroll> findByLessonIdAndIsGroup(Long lessonId, Integer isGroup) {
        return dao.findByLessonIdAndIsGroup(lessonId, isGroup);
    }

    @Override
    public FootballLessonSecondEnroll findByUserIdAndTeamIdAndLessonId(Long userId, Long teamId, Long lessonId) {
        return dao.findByUserIdAndTeamIdAndLessonId(userId, teamId, lessonId);
    }

    @Override
    public FootballLessonSecondEnroll findUserIdAndLessonId(Long userId, Long lessonId) {
        return dao.findUserIdAndLessonId(userId,lessonId);
    }

    @Override
    public FootballLessonSecondEnroll findByUserIdAndLessonId(Long userId, Long lessonId) {
        return dao.findByUserIdAndLessonId(userId,lessonId);
    }

    @Override
    public List<FootballLessonSecondEnroll> findNotOfUser(List<Long> userList, Long teamId, Long lessonId) {
        if (userList != null && userList.size() > 0) {
            return dao.findNotOfUser(userList, teamId, lessonId);
        }
        return new ArrayList<>();
    }

    @Override
    public FootballLessonSecondEnroll save(FootballLessonSecondEnroll enroll) {
        return dao.save(enroll);
    }

    @Override
    public List<FootballLessonSecondEnroll> findByTeamIdAndLessonId(Long teamId, Long lessonId) {
        return dao.findByTeamIdAndLessonId(teamId, lessonId);
    }

    @Override
    public List<FootballLessonSecondEnroll> findByLessonId(Long lessonId) {
        List<FootballLessonSecondEnroll> list = dao.findByLessonId(lessonId);
        if (list == null) {
            return new ArrayList<>();
        }
        return list;
    }

    @Override
    public int deleteByTeamId(Long teamId) {
        return dao.deleteByTeamId(teamId);
    }
}
