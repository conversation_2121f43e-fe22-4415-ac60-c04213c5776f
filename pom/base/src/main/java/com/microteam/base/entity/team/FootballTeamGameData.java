package com.microteam.base.entity.team;

import com.microteam.base.common.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.io.Serializable;

@Entity
@Table(name = "football_team_game_data")
@Getter
@Setter
public class FootballTeamGameData extends BaseEntity implements Serializable {

    @Column(name = "gameId")
    private Long gameId;
    @Column(name = "teamId")
    private Long teamId;
    @Column(name = "maxConsecutivePasses")
    private Integer maxConsecutivePasses;
    @Column(name = "averConsecutivePasses")
    private Double averConsecutivePasses;

}
