//package com.microteam.base.agency.dao.impl;
//
//import com.microteam.base.agency.dao.FootballAgencyEvaluateDao;
//import com.microteam.base.entity.agency.FootballAgency;
//import com.microteam.base.entity.agency.FootballAgencyEvaluate;
//import com.microteam.base.common.base.AbstractHibernateDao;
//import org.apache.log4j.Logger;
//import org.hibernate.Hibernate;
//import org.hibernate.query.Query;
//import org.springframework.stereotype.Repository;
//
//import java.util.ArrayList;
//import java.util.List;
//
//@Repository("footballAgencyEvaluateDao")
//public class FootballAgencyEvaluateDaoImpl extends
//        AbstractHibernateDao<FootballAgencyEvaluate> implements FootballAgencyEvaluateDao {
//    static Logger logger = Logger.getLogger(FootballAgencyEvaluateDaoImpl.class.getName());
//
//    public FootballAgencyEvaluateDaoImpl() {
//        super();
//        setClazz(FootballAgencyEvaluate.class);
//    }
//
//    //分页查询机构评价
//    @SuppressWarnings("unchecked")
//    @Override
//    public List<FootballAgencyEvaluate> findEvaluateByAgency(
//            FootballAgency agency, int page, int pageSize) {
//        String hql = "from FootballAgencyEvaluate as agencyEvaluate left join fetch agencyEvaluate.user as user  where agencyEvaluate.footballAgency=? and agencyEvaluate.deleted=false ";
//        Query query = getCurrentSession().createQuery(hql).setParameter(0, agency);
//        query.setFirstResult((page - 1) * pageSize);
//        query.setMaxResults(pageSize);
//        List<FootballAgencyEvaluate> list = query.list();
//        if (list.size() > 0) {
//            List<FootballAgencyEvaluate> listnew = new ArrayList<FootballAgencyEvaluate>();
//            for (int i = 0; i < list.size(); i++) {
//                FootballAgencyEvaluate agencyEvaluate = list.get(i);
//                Hibernate.initialize(agencyEvaluate);// 获取赖加载的集合内容；
//                Hibernate.initialize(agencyEvaluate.getUser());
//                listnew.add(i, agencyEvaluate);
//            }//for
//            return listnew;
//        }//if
//        return null;
//    }
//
//
//}
