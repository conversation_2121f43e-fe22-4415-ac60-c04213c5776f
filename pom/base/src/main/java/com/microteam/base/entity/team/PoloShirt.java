package com.microteam.base.entity.team;


import com.microteam.base.entity.user.User;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

@Entity
@Table(name = "polo_shirt")
@Getter
@Setter
public class PoloShirt implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false)
    protected Long id;
    @ManyToOne
    @JoinColumn(name = "userId", nullable = false)
    private User user;
    @Column(name = "number", nullable = false)
    private Long number;
    @ManyToOne
    @JoinColumn(name = "team_Id", nullable = false)
    private FootballTeam footballTeam;
    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "create_Time", nullable = false, length = 19)
    protected Date createTime;
    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "update_Time", nullable = false, length = 19)
    protected Date updateTime;
    @Column(name = "deleted", nullable = false)
    protected boolean deleted;
}
