package com.microteam.base.agency.service.impl;

import com.microteam.base.agency.dao.FootballAgencyManagerDao;
import com.microteam.base.agency.service.FootballAgencyManagerDaoService;
import com.microteam.base.entity.agency.FootballAgency;
import com.microteam.base.entity.agency.FootballAgencyManager;
import com.microteam.base.entity.user.User;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.List;

@Service("footballAgencyManagerDaoService")
public class FootballAgencyManagerDaoServiceImpl implements FootballAgencyManagerDaoService {
    static Logger logger = Logger.getLogger(FootballAgencyManagerDaoServiceImpl.class.getName());
    @Autowired
    private FootballAgencyManagerDao dao;

    public FootballAgencyManagerDaoServiceImpl() {
        super();

    }

    //查询用户是不是机构下的管理员
    @Override
    public FootballAgencyManager findAgencyManagerByAgencyAndUser(
            FootballAgency agency, User user) {

        return dao.findAgencyManagerByAgencyAndUser(agency, user);
    }

    //分页查询机构管理员
    @Override
    public List<FootballAgencyManager> findAgencyManagerByAgencyForPage(FootballAgency agency, int page, int pageSize) {
        if (page > 0) {
            page--;
        }
        Pageable pageable = PageRequest.of(page, pageSize);
        return dao.findAgencyManagerByAgencyForPage(agency, pageable);
    }

    //根据Id查询
    @Override
    public FootballAgencyManager findAgencyManagerById(long id) {

        return dao.findAgencyManagerById(id);
    }

    //根据机构查询
    @Override
    public List<FootballAgencyManager> findAgencyManagerByAgency(
            FootballAgency agency) {

        return dao.findAgencyManagerByAgency(agency);
    }

    //分页模糊查询机构下的管理员
    @Override
    public List<FootballAgencyManager> findAgencyManagerByAgencyAndManagerNameForPage(FootballAgency agency, int page, int pageSize, String managerName) {
        if (managerName != null && !"".equals(managerName)) {
            managerName = '%' + managerName + '%';
        } else {
            managerName = "";
        }
        if (page > 0) {
            page--;
        }
        Pageable pageable = PageRequest.of(page, pageSize);
        return dao.findAgencyManagerByAgencyAndManagerNameForPage(agency, managerName, pageable);
    }

    @Override
    public List<FootballAgencyManager> findAgencyManagerByAgencyAndSearch(FootballAgency agency, String managerName) {
        if (managerName != null && !"".equals(managerName)) {
            managerName = '%' + managerName + '%';
        } else {
            managerName = "";
        }
        return dao.findAgencyManagerByAgencyAndSearch(agency, managerName);
    }

//    @Override
//    public List<FootballAgencyManager> findAgencyManagerByUser(User user, String cityCode, String countyCode, String search) {
//
//        return dao.findAgencyManagerByUser(user, cityCode, countyCode, search);
//    }

}
