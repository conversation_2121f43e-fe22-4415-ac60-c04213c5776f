//package com.microteam.base.agency.dao.impl;
//
//import com.microteam.base.agency.dao.FootballAgencyClassAlbumCommentDao;
//import com.microteam.base.entity.agency.FootballAgencyClassAlbumComment;
//import com.microteam.base.common.base.AbstractHibernateDao;
//import org.apache.log4j.Logger;
//import org.hibernate.Hibernate;
//import org.hibernate.query.Query;
//import org.springframework.stereotype.Repository;
//
//import java.util.ArrayList;
//import java.util.List;
//
//@Repository("footballAgencyClassAlbumCommentDao")
//public class FootballAgencyClassAlbumCommentDaoImpl extends AbstractHibernateDao<FootballAgencyClassAlbumComment> implements FootballAgencyClassAlbumCommentDao {
//    static Logger logger = Logger.getLogger(FootballAgencyClassAlbumCommentDaoImpl.class.getName());
//
//    public FootballAgencyClassAlbumCommentDaoImpl() {
//        super();
//        setClazz(FootballAgencyClassAlbumComment.class);
//    }
//
//    //查询相册评论
//    @Override
//    public List<FootballAgencyClassAlbumComment> findByAgencyClassAlbumId(Long agencyClassAlbumId) {
//        String hql = "from FootballAgencyClassAlbumComment as footballAgencyClassAlbumComment " +
//                "where footballAgencyClassAlbumComment.footballAgencyClassAlbum.id = (:agencyClassAlbumId) " +
//                "and footballAgencyClassAlbumComment.deleted=false ";
//        Query query = getCurrentSession().createQuery(hql)
//                .setParameter("agencyClassAlbumId", agencyClassAlbumId);
//        List<FootballAgencyClassAlbumComment> list = query.list();
//        return list;
//    }
//
//    //分页查询
//    @Override
//    public List<FootballAgencyClassAlbumComment> findByAgencyClassAlbumIdForPage(Long agencyClassAlbumId, int page, int pageSize) {
//        String hql = "from FootballAgencyClassAlbumComment as footballAgencyClassAlbumComment " +
//                "left join fetch footballAgencyClassAlbumComment.user as user " +
//                "where footballAgencyClassAlbumComment.footballAgencyClassAlbum.id = (:agencyClassAlbumId) " +
//                "and footballAgencyClassAlbumComment.deleted=false ";
//        Query query = getCurrentSession().createQuery(hql).setParameter("agencyClassAlbumId", agencyClassAlbumId);
//        query.setFirstResult((page - 1) * pageSize);
//        query.setMaxResults(pageSize);
//        List<FootballAgencyClassAlbumComment> list = query.list();
//        if (list.size() > 0) {
//            List<FootballAgencyClassAlbumComment> listnew = new ArrayList<>();
//            for (int i = 0; i < list.size(); i++) {
//                FootballAgencyClassAlbumComment agencyClassAlbumComment = list.get(i);
//                Hibernate.initialize(agencyClassAlbumComment);// 获取赖加载的集合内容；
//                Hibernate.initialize(agencyClassAlbumComment.getUser());// 获取赖加载的集合内容；
//                listnew.add(i, agencyClassAlbumComment);
//            }
//            return listnew;
//        }
//        return null;
//    }
//
//}
