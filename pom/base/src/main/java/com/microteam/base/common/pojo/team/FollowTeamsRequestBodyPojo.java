package com.microteam.base.common.pojo.team;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

@Getter
@Setter
public class FollowTeamsRequestBodyPojo implements Serializable {

    // {"teamsMemberRole":"cheerTeam","joinMessage":"需要加入","member":["imUserid1","imUserid2"]}
    private String teamsMemberRole;
    private String joinMessage;
    private int joinMode;//1:autojoin applicant;2:autojoin invitee;3:approvedjoin applicant ;4:approvedjoin invitee;
    private String leavedMessage;//离开理由
    private String kickMessage;//踢人理由footballTeamGameExtraDaoService
    private String joinedMatch;//参加赛事：例如：2015-2016年度中国足协会室内五人制甲级联赛
    private int teamPosition;//擅长位置:1:forward（前锋）2:centerforward(中场)3:guard（后卫）4:goalkeeper（守门员）
    private int teamNumber;//球队号码
    private String[] members; //群组成员；
    private String nickName;
    private int age;
    private int height;
    private int weight;
    private int bust;
    private int waist;
    private int hip;
    private String remark;

}
