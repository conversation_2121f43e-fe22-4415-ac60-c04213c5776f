//package com.microteam.base.agency.dao.impl;
//
//import com.microteam.base.agency.dao.FootballAgencyClassDao;
//import com.microteam.base.entity.agency.FootballAgencyClass;
//import com.microteam.base.common.base.AbstractHibernateDao;
//import org.apache.log4j.Logger;
//import org.hibernate.query.Query;
//import org.springframework.stereotype.Repository;
//
//import java.util.List;
//
//@Repository("footballAgencyClassDao")
//public class FootballAgencyClassDaoImpl extends AbstractHibernateDao<FootballAgencyClass> implements FootballAgencyClassDao {
//    static Logger logger = Logger.getLogger(FootballAgencyClassDaoImpl.class.getName());
//
//    public FootballAgencyClassDaoImpl() {
//        super();
//        setClazz(FootballAgencyClass.class);
//    }
//
//    @Override
//    public List<FootballAgencyClass> findByAgencyId(Long agencyId) {
//        String hql = "from FootballAgencyClass as footballAgencyClass " +
//                "where footballAgencyClass.footballAgency.id = (:agencyId) " +
//                "and footballAgencyClass.deleted=false ";
//        Query query = getCurrentSession().createQuery(hql)
//                .setParameter("agencyId", agencyId);
//        List<FootballAgencyClass> list = query.list();
//        return list;
//    }
//
//    @Override
//    public List<FootballAgencyClass> findHistoryByAgencyId(Long agencyId, int page, int pageSize, String searchType) {
//        String hql;
//        if ("historyClass".equals(searchType)) {
//            hql = "from FootballAgencyClass as footballAgencyClass " +
//                    "left join fetch footballAgencyClass.groups as groups " +
//                    "left join fetch footballAgencyClass.user as user " +
//                    "where footballAgencyClass.footballAgency.id = (:agencyId) " +
//                    "and footballAgencyClass.deleted=false " +
//                    "and footballAgencyClass.isOver=true ";
//        } else {
//            hql = "from FootballAgencyClass as footballAgencyClass " +
//                    "left join fetch footballAgencyClass.groups as groups " +
//                    "left join fetch footballAgencyClass.user as user " +
//                    "where footballAgencyClass.footballAgency.id = (:agencyId) " +
//                    "and footballAgencyClass.deleted=false " +
//                    "and footballAgencyClass.isOver=false ";
//        }
//        Query query = getCurrentSession().createQuery(hql)
//                .setParameter("agencyId", agencyId);
//        query.setFirstResult((page - 1) * pageSize);
//        query.setMaxResults(pageSize);
//        List<FootballAgencyClass> list = query.list();
//        return list;
//    }
//
//    @Override
//    public FootballAgencyClass findById(long id) {
//        String hql = "from FootballAgencyClass as footballAgencyClass " +
//                "left join fetch footballAgencyClass.user as user " +
//                "left join fetch footballAgencyClass.groups as groups " +
//                "where footballAgencyClass.id=? " +
//                "and footballAgencyClass.deleted=false ";
//        Query query = getCurrentSession().createQuery(hql)
//                .setParameter(0, id);
//        List<FootballAgencyClass> list = query.list();
//        if (list != null && list.size() > 0) {
//            return list.get(0);
//        }
//        return null;
//    }
//
//}
