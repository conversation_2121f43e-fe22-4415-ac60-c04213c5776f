package com.microteam.base.common.pojo.team;

//个人职业生涯的平均传球
public class FootTeamballPassballAverage {
    //平均传球（职业生涯）
    double avgOneFootPassCount;   //一脚传球--平均值
    double avgTwoFootPassCount;   //两脚传球--平均值
    double avgLongPass;           //长传次数--平均值
    double avgShortPass;          //短传次数--平均值
    double avgMaxSprintSpeed;     //最高冲刺速度--平均值

    double avgPassBallError;      //传球失误--平均值
    double avgPassBallCounts;     //传球成功--平均值   
    double avgSuccessRate;        //传球成功率--平均值(百分数) = 传球成功/(传球成功+传球失误)

    public FootTeamballPassballAverage() {
        super();
    }

    public FootTeamballPassballAverage(double avgOneFootPassCount, double avgTwoFootPassCount, double avgLongPass, double avgShortPass, double avgMaxSprintSpeed, double avgPassBallCounts, double avgPassBallError) {
        this.avgOneFootPassCount = avgOneFootPassCount;

        this.avgTwoFootPassCount = avgTwoFootPassCount;
        this.avgLongPass = avgLongPass;
        this.avgShortPass = avgShortPass;
        this.avgMaxSprintSpeed = avgMaxSprintSpeed;

        this.avgPassBallCounts = avgPassBallCounts;
        this.avgPassBallError = avgPassBallError;
        this.avgSuccessRate = avgPassBallCounts / (avgPassBallCounts + avgPassBallError);
    }


    public double getAvgOneFootPassCount() {
        return avgOneFootPassCount;
    }


    public void setAvgOneFootPassCount(double avgOneFootPassCount) {
        this.avgOneFootPassCount = avgOneFootPassCount;
    }


    public double getAvgTwoFootPassCount() {
        return avgTwoFootPassCount;
    }

    public void setAvgTwoFootPassCount(double avgTwoFootPassCount) {
        this.avgTwoFootPassCount = avgTwoFootPassCount;
    }

    public double getAvgLongPass() {
        return avgLongPass;
    }

    public void setAvgLongPass(double avgLongPass) {
        this.avgLongPass = avgLongPass;
    }

    public double getAvgShortPass() {
        return avgShortPass;
    }

    public void setAvgShortPass(double avgShortPass) {
        this.avgShortPass = avgShortPass;
    }

    public double getAvgPassBallError() {
        return avgPassBallError;
    }

    public void setAvgPassBallError(double avgPassBallError) {
        this.avgPassBallError = avgPassBallError;
    }

    public double getAvgPassBallCounts() {
        return avgPassBallCounts;
    }

    public void setAvgPassBallCounts(double avgPassBallCounts) {
        this.avgPassBallCounts = avgPassBallCounts;
    }

    public double getAvgMaxSprintSpeed() {
        return avgMaxSprintSpeed;
    }

    public void setAvgMaxSprintSpeed(double avgMaxSprintSpeed) {
        this.avgMaxSprintSpeed = avgMaxSprintSpeed;
    }

    public double getAvgSuccessRate() {
        return avgSuccessRate;
    }

    public void setAvgSuccessRate(double avgSuccessRate) {
        this.avgSuccessRate = avgSuccessRate;
    }

}
