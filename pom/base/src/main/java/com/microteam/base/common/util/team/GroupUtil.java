package com.microteam.base.common.util.team;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.microteam.base.common.constant.ConstantUserManage;
import com.microteam.base.common.pojo.MtJavaServerResponseBodyPojo;
import com.microteam.base.common.pojo.MtJavaServerResponseBodyResultPojo;
import com.microteam.base.common.pojo.im.AppGroupsRegisterRequestBodyPojo;
import com.microteam.base.common.util.common.CommonUtil;
import com.microteam.base.common.util.imServerManage.common.ConstantImServerManage;
import com.microteam.base.common.util.imServerManage.pojo.ImGroupRegisterRequestBodyPojo;
import com.microteam.base.common.util.imServerManage.service.ImServerService;
import com.microteam.base.entity.user.GroupRole;
import com.microteam.base.entity.user.Groups;
import com.microteam.base.entity.user.User;
import com.microteam.base.user.service.GroupRoleDaoService;
import com.microteam.base.user.service.GroupsDaoService;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.sql.Timestamp;
import java.util.HashMap;
import java.util.Map;

@Component
public class GroupUtil {
    static Logger logger = Logger.getLogger(GroupUtil.class.getName());
    @Autowired
    private GroupsDaoService groupsDaoService;
    @Autowired
    private ImServerService imServerService;
    @Autowired
    private GroupRoleDaoService groupRoleDaoService;

    public MtJavaServerResponseBodyPojo groupsRegisterImServerForApp(MtJavaServerResponseBodyPojo responsebodypojoId, AppGroupsRegisterRequestBodyPojo registerrequestbodypojoId, User user, boolean isRegisterByImServer) {
        MtJavaServerResponseBodyResultPojo responseBodyResultPojo = new MtJavaServerResponseBodyResultPojo();
        try {
            Groups groups = new Groups();
            groups.setUser(user);

            String groupname = registerrequestbodypojoId.getGroupname();

            boolean resultCheck = checkGroupnameIsValid(groupname);//检查，用户名不空，唯一名称，是否已经注册，密码长度是否>=6）

            if (!resultCheck) {//检查，用名格式错误，）;
                responseBodyResultPojo.setCode("2016");
                responseBodyResultPojo.setMessage("The Same Groupname ERROR.");
                String resultString = CommonUtil.changeResultToString(responseBodyResultPojo);
                responsebodypojoId.setResult(resultString);
                return responsebodypojoId;

            }
            // 给注册环信群组对象赋值
            ImGroupRegisterRequestBodyPojo imGroupRegisterRequestBodyPojo = new ImGroupRegisterRequestBodyPojo();
            groups.setGroupName(groupname);
            imGroupRegisterRequestBodyPojo.setGroupname(groupname);

            if (!registerrequestbodypojoId.getDesc().equals("")) {
                imGroupRegisterRequestBodyPojo.setDesc(registerrequestbodypojoId.getDesc());

            }

            imGroupRegisterRequestBodyPojo.setOwner(ConstantUserManage.getLongTenLetter(user.getId()));
            int maxusers = registerrequestbodypojoId.getMaxusers();
            if (maxusers > ConstantImServerManage.Maxusers)
                maxusers = ConstantImServerManage.Maxusers;

            groups.setMaxusers(maxusers);


            imGroupRegisterRequestBodyPojo.setMaxusers(maxusers);

            short groupStyle = registerrequestbodypojoId.getGroupStyle();
            groups.setGroupStyle(groupStyle);

					/* groupStyle=
					 //私有群组，不能被非群组成员看到
					  eGroupStyle_PrivateOnlyownerInvite = 0,  // 只有创建者可以邀请非成员进群
					  eGroupStyle_PrivateMemberCanInvite=1,	// 所有群成员都可以邀请非成员进群

					 //共有群组，可通过查看所有共有群组得到
					  eGroupStyle_PublicJoinNeedApproval=2,	// 需要创建者同意才能进入(创建者可以邀请非成员进群)
					  eGroupStyle_PublicOpenJoin=3,		// 不需要同意可以直接进入()
					 */
            if (groupStyle == 0) {
                imGroupRegisterRequestBodyPojo.setApproval(true);

                imGroupRegisterRequestBodyPojo.setPUBLIC(false);
                groups.setMembersOnly(true);
                groups.setIsPublic(false);
            } else if (groupStyle == 1) {
                imGroupRegisterRequestBodyPojo.setApproval(false);

                imGroupRegisterRequestBodyPojo.setPUBLIC(false);
                groups.setMembersOnly(false);
                groups.setIsPublic(false);
            } else if (groupStyle == 2) {
                imGroupRegisterRequestBodyPojo.setApproval(true);

                imGroupRegisterRequestBodyPojo.setPUBLIC(true);
                groups.setMembersOnly(true);
                groups.setIsPublic(true);
            } else if (groupStyle == 3) {
                imGroupRegisterRequestBodyPojo.setApproval(false);

                imGroupRegisterRequestBodyPojo.setPUBLIC(true);
                groups.setMembersOnly(false);
                groups.setIsPublic(true);
            }
            if (registerrequestbodypojoId.getMembers() == null) {
                String[] myMember = {""};
                imGroupRegisterRequestBodyPojo.setMembers(myMember);
            } else if (registerrequestbodypojoId.getMembers().length > 0) {
                imGroupRegisterRequestBodyPojo.setMembers(registerrequestbodypojoId.getMembers());
            } else {
                responseBodyResultPojo.setCode("2017");
                responseBodyResultPojo.setMessage("Group Register getMembers Error.");
                String resultString = CommonUtil.changeResultToString(responseBodyResultPojo);
                responsebodypojoId.setResult(resultString);
                return responsebodypojoId;
            }
            groups.setImEnabled(false);
            if (isRegisterByImServer) {//注册处理；
                groups = imServerService.registerGroupImServer(groups, imGroupRegisterRequestBodyPojo);
                if (groups == null) {//IM SERVER注册 group失败；
                    responseBodyResultPojo.setCode("2018");
                    responseBodyResultPojo.setMessage("Group Register Huanxin IM Server Error.");
                    String resultString = CommonUtil.changeResultToString(responseBodyResultPojo);
                    responsebodypojoId.setResult(resultString);
                    return responsebodypojoId;
                }
                groups.setImEnabled(true);
            } else { //不注册处理；
                groups.setImEnabled(false);
            }

            String groupRolename = registerrequestbodypojoId.getGroupRole();
            GroupRole groupRole = groupRoleDaoService.findByRoleName(groupRolename);
            groups.setGroupRole(groupRole);
            groups.setAllowinvites(true);//需要根据groupStyle进行判断,暂时没有写。
            groups.setDeleted(false);
            groups.setEnabled(true);
            Timestamp timestamp = new Timestamp(System.currentTimeMillis());
            groups.setRegisterTime(timestamp);

            groups = groupsDaoService.save(groups);
            if (groups.getId() == null) {
                responseBodyResultPojo.setCode("2019");
                responseBodyResultPojo.setMessage("Group Register save groups table Error.");
                String resultString = CommonUtil.changeResultToString(responseBodyResultPojo);
                responsebodypojoId.setResult(resultString);
                return responsebodypojoId;

            }
            //存储redis
            // "data":[{"groupid":1234,"imGroupid":"1234","imGroupname":"1234","registerTime":23345666}],
            Map<String, Object> map = new HashMap<String, Object>();
            map.put("groupsid", groups.getId());
            map.put("imGroupid", groups.getImGroupId());
            map.put("imGroupname", groupname);
            map.put("registerTime", groups.getRegisterTime().getTime());
            JSONObject json = new JSONObject();
            json.put("groups", map);

            JSONArray jArray = new JSONArray();

            jArray.set(0, json);
            responsebodypojoId.setData(jArray.toString());  //gandy_data????
            responseBodyResultPojo.setCode("0000");
            responseBodyResultPojo.setMessage("sucess.");
            String resultString = CommonUtil.changeResultToString(responseBodyResultPojo);
            responsebodypojoId.setResult(resultString);
        } catch (Exception e) {
            e.printStackTrace();
            logger.debug("GROUP Register Exception Error." + e.getMessage());
            responseBodyResultPojo.setCode("2020");
            responseBodyResultPojo.setMessage("Group Register Exception Error.");
            String resultString = CommonUtil.changeResultToString(responseBodyResultPojo);
            responsebodypojoId.setResult(resultString);
        }
        return responsebodypojoId;
    }

    public boolean checkGroupnameIsValid(String groupname) {
        if (groupname.equals(""))
            return false;
        Groups group = groupsDaoService.findByGroupNameExceptEnabled(groupname);//包含禁止的群组名；用于注册同名检查使用；
        //gerald add redis
        return group == null;
    }
}
