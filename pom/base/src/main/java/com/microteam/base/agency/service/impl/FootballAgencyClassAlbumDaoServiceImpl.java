package com.microteam.base.agency.service.impl;

import com.microteam.base.agency.dao.FootballAgencyClassAlbumDao;
import com.microteam.base.agency.service.FootballAgencyClassAlbumDaoService;
import com.microteam.base.entity.agency.FootballAgencyClass;
import com.microteam.base.entity.agency.FootballAgencyClassAlbum;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.List;

@Service("footballAgencyClassAlbumDaoService")
public class FootballAgencyClassAlbumDaoServiceImpl implements FootballAgencyClassAlbumDaoService {
    static Logger logger = Logger.getLogger(FootballAgencyClassAlbumDaoServiceImpl.class.getName());
    @Autowired
    private FootballAgencyClassAlbumDao dao;

    //查看班级相册是否存在
    @Override
    public FootballAgencyClassAlbum findByAgencyClassIdAndAlbumName(FootballAgencyClass agencyClassId, String albumName) {
        return dao.findByAgencyClassIdAndAlbumName(agencyClassId, albumName);
    }

    @Override
    public FootballAgencyClassAlbum findById(long id) {
        return dao.findById(id);
    }

    //分页查询班级相册
    @Override
    public List<FootballAgencyClassAlbum> findByAgencyClassIdForPage(FootballAgencyClass agencyClassId, int page, int pageSize) {
        if (page > 0) {
            page--;
        }
        Pageable pageable = PageRequest.of(page, pageSize);
        return dao.findByAgencyClassIdForPage(agencyClassId, pageable);
    }


}
