//package com.microteam.base.integral.dao.impl;
//
//import com.microteam.base.common.base.AbstractHibernateDao;
//import com.microteam.base.integral.dao.FootballIntegralTeamHardwaremacDao;
//import com.microteam.base.entity.integral.FootballIntegralTeamHardwaremac;
//import org.apache.log4j.Logger;
//import org.hibernate.query.Query;
//import org.springframework.stereotype.Service;
//
//import java.util.List;
//
//@Service(value = "footballIntegralTeamHardwaremacDao")
//public class FootballIntegralTeamHardwaremacDaoImpl extends AbstractHibernateDao<FootballIntegralTeamHardwaremac> implements FootballIntegralTeamHardwaremacDao {
//
//    static Logger logger = Logger.getLogger(FootballIntegralTeamHardwaremacDaoImpl.class.getName());
//
//    public FootballIntegralTeamHardwaremacDaoImpl() {
//        super();
//        setClazz(FootballIntegralTeamHardwaremac.class);
//    }
//
//    @Override
//    public List<FootballIntegralTeamHardwaremac> findByTeamId(Long teamId) {
//        String hql = "from FootballIntegralTeamHardwaremac as integralTeamMac " +
//                "where integralTeamMac.teamId = (:teamId) " +
//                "and integralTeamMac.deleted=0 " +
//                "order by integralTeamMac.updateTime desc";
//        Query query = getCurrentSession().createQuery(hql)
//                .setParameter("teamId", teamId);
//        return query.list();
//    }
//
//    @Override
//    public List<FootballIntegralTeamHardwaremac> findByTeamIdAndMac(Long teamId, String mac) {
//        String hql = "from FootballIntegralTeamHardwaremac as integralTeamMac " +
//                "where integralTeamMac.teamId = (:teamId) " +
//                "and integralTeamMac.hardwareMac = (:mac) " +
//                "and integralTeamMac.deleted=0 " +
//                "order by integralTeamMac.updateTime desc";
//        Query query = getCurrentSession().createQuery(hql)
//                .setParameter("teamId", teamId)
//                .setParameter("mac", mac);
//        return query.list();
//    }
//}
