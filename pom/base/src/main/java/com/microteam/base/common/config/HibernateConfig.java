//package com.microteam.base.common.config;
//
//import org.hibernate.SessionFactory;
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.annotation.Configuration;
//import org.springframework.transaction.annotation.EnableTransactionManagement;
//import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;
//
//import javax.persistence.EntityManagerFactory;
//
//@Configuration
//@EnableTransactionManagement
//public class HibernateConfig implements WebMvcConfigurer {
//
//    @Bean
//    public SessionFactory getSessionFactory(EntityManagerFactory factory) {
//        if (factory.unwrap(SessionFactory.class) == null) {
//            throw new NullPointerException("factory is not a hibernate factory");
//        }
//        return factory.unwrap(SessionFactory.class);
//    }
//
//
//}
//
