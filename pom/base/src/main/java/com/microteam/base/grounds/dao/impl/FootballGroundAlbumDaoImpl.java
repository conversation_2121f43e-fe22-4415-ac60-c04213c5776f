//package com.microteam.base.grounds.dao.impl;
//
//import com.microteam.base.common.base.AbstractHibernateDao;
//import com.microteam.base.grounds.dao.FootballGroundAlbumDao;
//import com.microteam.base.entity.grounds.FootballGroundAlbum;
//import org.apache.log4j.Logger;
//import org.hibernate.query.Query;
//import org.springframework.stereotype.Repository;
//
//import java.util.List;
//
//@Repository("footballGroundAlbumDao")
//public class FootballGroundAlbumDaoImpl extends AbstractHibernateDao<FootballGroundAlbum> implements FootballGroundAlbumDao {
//    static Logger logger = Logger.getLogger(FootballGroundAlbumDaoImpl.class.getName());
//
//
//    public FootballGroundAlbumDaoImpl() {
//        super();
//        setClazz(FootballGroundAlbum.class);
//    }
//
//    @Override
//    public List<FootballGroundAlbum> findByUserId(Long userId) {
//        String hql = "from FootballGroundAlbum as groundsAlbum " +
//                "where groundsAlbum.user.id = (:userId) " +
//                "and groundsAlbum.deleted=false " +
//                "order by groundsAlbum.createTime desc";
//        Query query = getCurrentSession().createQuery(hql)
//                .setParameter("userId", userId);
//        List<FootballGroundAlbum> list = query.list();
//        if (list.size() > 0)
//            return list;
//        return null;
//    }
//
//    @Override
//    public List<FootballGroundAlbum> findByGroundId(Long groundId) {
//        String hql = "from FootballGroundAlbum as groundsAlbum " +
//                "where groundsAlbum.footballGround.id = (:groundId) " +
//                "and groundsAlbum.deleted=false " +
//                "order by groundsAlbum.createTime desc";
//        Query query = getCurrentSession().createQuery(hql)
//                .setParameter("groundId", groundId);
//        List<FootballGroundAlbum> list = query.list();
//        if (list.size() > 0)
//            return list;
//        return null;
//    }
//
//}
