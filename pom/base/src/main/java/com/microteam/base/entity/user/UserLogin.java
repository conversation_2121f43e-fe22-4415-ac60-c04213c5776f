package com.microteam.base.entity.user;


import com.microteam.base.common.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

@Entity
@Table(name = "user_login")
@Getter
@Setter
public class UserLogin extends BaseEntity implements Serializable {

    @ManyToOne
    @JoinColumn(name = "userId")
    private User user;
    @ManyToOne
    @JoinColumn(name = "deviceId")
    private DeviceInfo deviceInfo;
    @Column(name = "loginMode", length = 30)
    private String loginMode;
    @Column(name = "clientMode", length = 30)
    private String clientMode;
    @Column(name = "longitude")
    private String longitude;
    @Column(name = "latitude")
    private String latitude;
    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "loginTime", nullable = false, length = 19)
    private Date loginTime;

    public Date getLoginTime() {
        if (this.loginTime == null) {
            return null;
        }
        return (Date) this.loginTime.clone();
    }

    public void setLoginTime(Date loginTime) {
        this.loginTime = (Date) loginTime.clone();
    }


}
