package com.microteam.base.entity.match;


import com.microteam.base.common.base.BaseEntity;
import com.microteam.base.entity.user.User;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;
import java.io.Serializable;

@Entity
@Table(name = "football_team_match_manager")
@Getter
@Setter
public class FootballTeamMatchManager extends BaseEntity implements Serializable {

    @ManyToOne
    @JoinColumn(name = "userId", nullable = false)
    private User user;
    @ManyToOne
    @JoinColumn(name = "matchId", nullable = false)
    private FootballTeamMatch footballTeamMatch;
    @Column(name = "userRole", length = 50)
    private String userRole;
    @Column(name = "audit")
    private Short audit;

}
