package com.microteam.base.entity.demoManager;

import com.microteam.base.common.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;
import java.io.Serializable;

@Entity
@Table(name = "demo_mac_qrcode")
@Getter
@Setter
public class MacQRCode extends BaseEntity implements Serializable {

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "demoUserId", nullable = false)
    private DemoUser demoUser;
    @Column(name = "mac", nullable = false)
    private String mac;
    @Column(name = "qrCode", nullable = false)
    private String qrCode;

}
