package com.microteam.base.entity.team;


import com.microteam.base.common.base.BaseEntity;
import com.microteam.base.entity.user.User;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

@Entity
@Table(name = "football_team_violence")
@Getter
@Setter
public class FootballTeamViolence extends BaseEntity implements Serializable {
    @ManyToOne
    @JoinColumn(name = "publisherId", nullable = false)
    private User user;
    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "publishTime", nullable = false, length = 19)
    private Date publishTime;
    @Column(name = "countryCode")
    private String countryCode;
    @Column(name = "provinceCode")
    private String provinceCode;
    @Column(name = "cityCode")
    private String cityCode;
    @Column(name = "countyCode")
    private String countyCode;
    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "violenceTime", length = 19)
    private Date violenceTime;
    @Column(name = "detailLocation", length = 200)
    private String detailLocation;
    @Column(name = "title", length = 100)
    private String title;
    @Column(name = "content", length = 65535)
    private String content;

    public Date getPublishTime() {
        if (this.publishTime == null) {
            return null;
        }
        return (Date) this.publishTime.clone();
    }

    public void setPublishTime(Date publishTime) {
        this.publishTime = (Date) publishTime.clone();
    }

    public Date getViolenceTime() {
        if (this.violenceTime == null) {
            return null;
        }
        return (Date) this.violenceTime.clone();
    }

    public void setViolenceTime(Date violenceTime) {
        this.violenceTime = (Date) violenceTime.clone();
    }

}
