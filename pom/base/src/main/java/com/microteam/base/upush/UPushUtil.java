package com.microteam.base.upush;

import com.microteam.base.upush.android.AndroidListcast;
import com.microteam.base.upush.android.AndroidUnicast;
import com.microteam.base.upush.ios.IOSCustomizedcast;
import com.microteam.base.upush.ios.IOSListcast;
import org.springframework.stereotype.Service;

import java.util.Map;

import static com.microteam.base.common.constant.ConstantUserManage.UMENGPRODUCTIONMODE;

@Service
public class UPushUtil {

    private String appkey = null;
    private String appMasterSecret = null;
    private String appkeyIOS = null;
    private String appMasterSecretIOS = null;
    private String timestamp = null;
    private PushClient client = new PushClient();

    public UPushUtil() {
        appkey = "55974e3567e58ea378005894";
        appMasterSecret = "cxbywwtddldjqc3qktvmxga0flpgxyp6";
        appkeyIOS = "557e52f667e58e5f89000d17";
        appMasterSecretIOS = "xnuypo74otruri5e3vx2tufrmr5o1aln";
    }

    public void sendAndroidUnicast(String deviceToken, String ticker, String title, String text, AndroidNotification.DisplayType displayType) throws Exception {
        AndroidUnicast unicast = new AndroidUnicast(appkey, appMasterSecret);
        // TODO Set your device token
        unicast.setDeviceToken(deviceToken);
        // 通知栏提示文字
        unicast.setTicker(ticker);
        // 通知标题
        unicast.setTitle(title);
        // 通知文字描述
        unicast.setText(text);
        unicast.goAppAfterOpen();
        // 通知-Android(notification)：消息送达到用户设备后，由友盟SDK接管处理并在通知栏上显示通知内容。
        // 消息-Android(message)：消息送达到用户设备后，消息内容透传给应用自身进行解析处理。
        unicast.setDisplayType(displayType);
        unicast.setProductionMode();
        client.send(unicast);
    }

    public void sendAndroidListcast(String deviceTokens, String ticker, String title, String text, AndroidNotification.DisplayType displayType, Map<String, String> map) throws Exception {
        AndroidListcast listcast = new AndroidListcast(appkey, appMasterSecret);
        // TODO Set your device token
        listcast.setDeviceToken(deviceTokens);
        // 通知栏提示文字
        listcast.setTicker(ticker);
        // 通知标题
        listcast.setTitle(title);
        // 通知文字描述
        listcast.setText(text);
        listcast.goAppAfterOpen();
        // 通知-Android(notification)：消息送达到用户设备后，由友盟SDK接管处理并在通知栏上显示通知内容。
        // 消息-Android(message)：消息送达到用户设备后，消息内容透传给应用自身进行解析处理。
        listcast.setDisplayType(displayType);
        listcast.setProductionMode(UMENGPRODUCTIONMODE);
        for (Map.Entry<String, String> entry : map.entrySet()) {
            listcast.setExtraField(entry.getKey(), entry.getValue());
        }
        client.send(listcast);
    }

    public void sendAndroidListcast(String deviceTokens, String text) throws Exception {
        AndroidListcast listcast = new AndroidListcast(appkey, appMasterSecret);
        listcast.setDeviceToken(deviceTokens);
        listcast.setDisplayType(AndroidNotification.DisplayType.MESSAGE);
        listcast.setProductionMode(UMENGPRODUCTIONMODE);
        listcast.setCustomField(text);
        client.send(listcast);
    }

    public void sendIOSCustomizedcast(String deviceTokens, String text) throws Exception {
        IOSCustomizedcast customizedcast = new IOSCustomizedcast(appkeyIOS, appMasterSecretIOS);

        customizedcast.setCustomizedField("message", text);
        client.send(customizedcast);
    }

    public void sendIOSListcast(String deviceTokens, String text) throws Exception {
        IOSListcast unicast = new IOSListcast(appkeyIOS, appMasterSecretIOS);
        unicast.setDeviceToken(deviceTokens);
        unicast.setProductionMode(UMENGPRODUCTIONMODE);
        unicast.setCustomizedField("message", text);
        unicast.setCustomizedField("display_type", "1");
        unicast.setContentAvailable(1);
        client.send(unicast);
    }
}
