package com.microteam.base.agency.service.impl;

import com.microteam.base.agency.dao.FootballAgencyStudentScoreAlbumDao;
import com.microteam.base.entity.agency.FootballAgencyStudentScore;
import com.microteam.base.entity.agency.FootballAgencyStudentScoreAlbum;
import com.microteam.base.agency.service.FootballAgencyStudentScoreAlbumDaoService;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service("footballAgencyStudentScoreAlbumDaoService")
public class FootballAgencyStudentScoreAlbumDaoServiceImpl implements FootballAgencyStudentScoreAlbumDaoService {
    static Logger logger = Logger.getLogger(FootballAgencyStudentScoreAlbumDaoServiceImpl.class.getName());
    @Autowired
    private FootballAgencyStudentScoreAlbumDao dao;

    public FootballAgencyStudentScoreAlbumDaoServiceImpl() {
        super();

    }

    //查询学员分数相册
    @Override
    public List<FootballAgencyStudentScoreAlbum> findStudentScoreAlbumByScore(
            FootballAgencyStudentScore agencyStudentScore) {

        return dao.findStudentScoreAlbumByScore(agencyStudentScore);
    }


}
