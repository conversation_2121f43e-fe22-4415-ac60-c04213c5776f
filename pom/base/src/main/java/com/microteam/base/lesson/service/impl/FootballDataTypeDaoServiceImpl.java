package com.microteam.base.lesson.service.impl;


import com.microteam.base.common.util.lesson.FootballLessonUtils;
import com.microteam.base.entity.lesson.CoursewareDataType;
import com.microteam.base.entity.lesson.FootballDataType;
import com.microteam.base.entity.lesson.FootballLessonDataType;
import com.microteam.base.lesson.dao.CoursewareDataTypeDao;
import com.microteam.base.lesson.dao.FootballDataTypeDao;
import com.microteam.base.lesson.dao.FootballLessonDataTypeDao;
import com.microteam.base.lesson.service.FootballDataTypeDaoService;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Service("footballDataTypeDaoService")
public class FootballDataTypeDaoServiceImpl implements FootballDataTypeDaoService {
    static Logger logger = Logger.getLogger(FootballDataTypeDaoServiceImpl.class.getName());

    @Autowired
    private FootballDataTypeDao dao;
    @Autowired
    private FootballLessonDataTypeDao footballLessonDatatypeDao;
    @Autowired
    private CoursewareDataTypeDao coursewareDataTypeDao;

    @Override
    public List<FootballDataType> findAllDataType() {
        return dao.findAllDataType();
    }


    @Override
    public List<FootballDataType> findByIdList(List<Long> idList) {
        if (idList != null && idList.size() > 0) {
            return dao.findByIdList(idList);
        }
        return new ArrayList<>();
    }

    @Override
    public List<FootballDataType> findByLessonId(Long lessonId) {
        List<FootballLessonDataType> list = footballLessonDatatypeDao.findByLessonId(lessonId);
        if (list != null && list.size() > 0) {
            List<Long> dataTypeId = FootballLessonUtils.getDataTypeIdList(list);
            return dao.findByIdList(dataTypeId);
        }
        return new ArrayList<>();
    }

    @Override
    public List<FootballDataType> findByLessonIdOrderByType(Long lessonId) {
        List<FootballLessonDataType> list = footballLessonDatatypeDao.findByLessonId(lessonId);
        if (list != null && list.size() > 0) {
            List<Long> dataTypeId = FootballLessonUtils.getDataTypeIdList(list);
            return dao.findByIdListOrderByType(dataTypeId);
        }
        return new ArrayList<>();
    }

    @Override
    public List<FootballDataType> findByTypeList(List<Integer> type) {
        if (type != null && type.size() > 0) {
            return dao.findByTypeList(type);
        }
        return new ArrayList<>();
    }

    @Override
    public List<FootballDataType> findByCoursewareId(Long coursewareId) {
        List<FootballDataType> list = new ArrayList<>();
        List<CoursewareDataType> coursewareDataTypeList = coursewareDataTypeDao.findByCoursewareId(coursewareId);
        for (CoursewareDataType coursewareDataType : Optional.ofNullable(coursewareDataTypeList).orElse(new ArrayList<>())) {
            list.add(coursewareDataType.getDataType());
        }
        return list;
    }

    @Override
    public FootballDataType findByTypeIntro(String typeIntro) {
        return dao.findByTypeIntro(typeIntro);
    }
}
