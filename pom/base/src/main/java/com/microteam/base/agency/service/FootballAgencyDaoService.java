package com.microteam.base.agency.service;

import com.microteam.base.entity.agency.FootballAgency;

import java.util.List;

public interface FootballAgencyDaoService {
    //根据Id查询机构
    FootballAgency findById(long id);

    //查询培训机构列表
//    List<FootballAgency> findByCodeAndSearchForPage(String cityCode, String countyCode, int page, int pageSize, String search);

    //查询我创建的机构列表
//    List<FootballAgency> findAgencyListByPageAndMy(String cityCode, String countyCode, int page, int pageSize, String search, User user);

    //查询我创建的机构列表
//    List<FootballAgency> findAgencyListByPageAndMyInfo(String cityCode, String countyCode, int page, int pageSize, String search, String audit, User user);

//    List<FootballAgency> findAgencyListByPageAndMyInfoAndType(String cityCode, String countyCode, int page, int pageSize, String search, String audit, User user, Integer type);

    //查询不是我创建的机构列表
//    List<FootballAgency> findAgencyListByPageAndNotMy(String cityCode, String countyCode, int page, int pageSize, String search, User user);

    List<FootballAgency> findYouthAgencyListByIdList(List<Long> idList);

    List<FootballAgency> findAgencyListUserId(int page, int pageSize,Long userId);

    List<FootballAgency> findAgencyListNoUserId(int page, int pageSize,Long userId);

    List<FootballAgency> findAgencyListAll(int page, int pageSize, String cityCode, String countyCode, String search, Integer audit, Integer type);

    List<FootballAgency> findJoinedByUserId(Long userId);

    FootballAgency findLastAgency();

}
