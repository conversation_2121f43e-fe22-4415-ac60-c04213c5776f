//package com.microteam.base.telMailValiManage.dao.impl;
//
//
//import com.microteam.base.common.base.AbstractHibernateDao;
//import com.microteam.base.telMailValiManage.dao.MobileMessageDao;
//import com.microteam.base.entity.telMailValiManage.TelMailValiInfo;
//import org.apache.log4j.Logger;
//import org.hibernate.query.Query;
//import org.springframework.stereotype.Repository;
//
//import java.util.List;
//
//@Repository("mobileMessageDao")
//public class MobileMessageDaoImpl extends AbstractHibernateDao<TelMailValiInfo> implements MobileMessageDao {
//    static Logger logger = Logger.getLogger(MobileMessageDaoImpl.class.getName());
//
//    public MobileMessageDaoImpl() {
//        super();
//        setClazz(TelMailValiInfo.class);
//    }
//
//    //根据手机号查询MobileMessage--gerald
//    @Override
//    public TelMailValiInfo findByValiAccount(String phoneNumber) {
//        String hql = "from TelMailValiInfo as mobileMessage " +
//                "where mobileMessage.valiAccount=? " +
//                "and mobileMessage.enabled=true " +
//                "and mobileMessage.deleted=false " +
//                "order by mobileMessage.valiStartTime desc";
//        Query query = getCurrentSession().createQuery(hql)
//                .setParameter(0, phoneNumber);
//        List<TelMailValiInfo> list = query.list();
//        if (list != null && list.size() > 0) {
//            return list.get(0);
//        }
//        return null;
//    }
//
//    //删除MobileMessage--gerald
//    @Override
//    public void delByValiAccount(String phoneNumber) {
//        String hql = "update MobileMessage as mm " +
//                "set mm.deleted=false " +
//                "where mm.valiAccount=? " +
//                "and mm.enabled=true";
//        Query query = getCurrentSession().createQuery(hql)
//                .setParameter(0, phoneNumber);
//        query.executeUpdate();
//    }
//
//}
