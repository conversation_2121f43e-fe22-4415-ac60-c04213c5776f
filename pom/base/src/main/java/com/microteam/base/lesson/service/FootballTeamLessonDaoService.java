package com.microteam.base.lesson.service;


import com.microteam.base.entity.lesson.FootballTeamLesson;

import java.util.List;

public interface FootballTeamLessonDaoService {
    List<FootballTeamLesson> findByLessonId(Long lessonId);

    List<FootballTeamLesson> findByTeamIdList(List<Long> teamIdList);

    int deleteByLessonIdListAndTeamId(List<Long> lessonIdList, Long teamId);

    List<FootballTeamLesson> findByTeamId(Long teamId);

    int deleteByTeamId(Long teamId);

    List<Long> findAloneLessonIdListByTeamId(Long teamId);

    List<FootballTeamLesson> saveAll(List<FootballTeamLesson> list);
}
