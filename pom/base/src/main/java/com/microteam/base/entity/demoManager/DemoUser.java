package com.microteam.base.entity.demoManager;

import com.microteam.base.common.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.io.Serializable;

@Entity
@Table(name = "demo_user")
@Getter
@Setter
public class DemoUser extends BaseEntity implements Serializable {

    @Column(name = "userName", nullable = false)
    private String userName;
    @Column(name = "passWord", nullable = false)
    private String password;
    @Column(name = "nickName")
    private String nickName;

}
