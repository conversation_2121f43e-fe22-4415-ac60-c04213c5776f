package com.microteam.base.common.util;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONException;
import com.microteam.base.common.pojo.team.LostAndFreeRateInfo;
import com.microteam.base.common.util.team.FootballTeamUtils;
import com.microteam.base.common.util.user.DataUtil;
import com.microteam.base.entity.team.*;
import com.microteam.base.entity.user.User;
import com.microteam.base.entity.user.UserHardwareData;
import com.microteam.base.team.service.FootballTeamGameEnrollDaoService;
import com.microteam.base.team.service.FootballTeamGameStatisticsPlayerDaoService;
import com.microteam.base.team.service.FootballTeamGameTakeNodeDaoService;
import com.microteam.base.user.service.UserHardwareDataDaoService;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;

@Service
public class TeamLostAndFreeUtil {
    private static Logger logger = Logger.getLogger(TeamLostAndFreeUtil.class.getName());

    @Autowired
    private FootballTeamGameEnrollDaoService footballTeamGameEnrollDaoService;
    @Autowired
    private UserHardwareDataDaoService userHardwareDataDaoService;
    @Autowired
    private FootballTeamGameTakeNodeDaoService footballTeamGameTakeNodeDaoService;
    @Autowired
    private FootballTeamGameStatisticsPlayerDaoService footballTeamGameStatisticsPlayerDaoService;

    /**
     * 计算得球与失球
     * 返回得球与失球的集合便于计算
     *
     * @Description:TODO
     * @author:zk
     * @time:2017年6月2日 下午2:38:52
     */
    public Map<String, Object> lostAndFree(FootballTeam team, FootballTeamGame teamGame, long userId) {
        String Rank;
        double rate = 0;
        List<Map<String, Object>> mapLostAndFree;
        Map<String, Object> mapLAF = new HashMap<>();
//        try {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        List<FootballTeamGameEnroll> footballTeamEnroll_list = footballTeamGameEnrollDaoService.findByTeamIdAndGameId(team.getId(), teamGame.getId());
        JSONArray touchBall_Array = new JSONArray(); //触球时间和对应的用户id（个人）
        JSONArray teamTouch_timeArray = new JSONArray(); //触球时间和对应的用户id（全队）
        if (footballTeamEnroll_list != null && footballTeamEnroll_list.size() > 1) {
            //这场比赛中报名球员的触球时间
            for (FootballTeamGameEnroll footballTeamGameEnroll : footballTeamEnroll_list) {
                //查询这场比赛中报名某个队员硬件的数据
                List<UserHardwareData> userHardwareData_list_user = userHardwareDataDaoService.findByGameIdAndUserId(teamGame.getId(), footballTeamGameEnroll.getUser().getId());
                if (userHardwareData_list_user != null && userHardwareData_list_user.size() > 0) {
                    for (UserHardwareData userHardwareData_one : userHardwareData_list_user) {
                        if (userHardwareData_one.getIsStartUp() == 3) {
                            if (userHardwareData_one.getKickBallData() != null && !userHardwareData_one.getKickBallData().equals("")) {
                                Date kickBallDataStartTime = userHardwareData_one.getKickBallStartTime();
                                long kickBallDataStartTime_long = kickBallDataStartTime.getTime();
                                JSONArray kickBallData_array = JSON.parseArray(userHardwareData_one.getKickBallData());
                                for (int y = 0; y < kickBallData_array.size(); y++) {
                                    long touchBall_time = kickBallDataStartTime_long + kickBallData_array.getInteger(y) * 1000;
                                    Map<String, Object> touchBall_time_user_map = new HashMap<>();
                                    touchBall_time_user_map.put("touchTime", touchBall_time);
                                    touchBall_time_user_map.put("userId", userHardwareData_one.getUser().getId());
                                    touchBall_time_user_map.put("touchTimeDate", sdf.format(new Date(touchBall_time)));
                                    touchBall_Array.add(touchBall_time_user_map);
                                }
                            }

                        }
                    }
                }// if
            }
            //把全队每个人的的触球时间按时间排序合并到一起
            if (touchBall_Array.size() > 1) {
                for (int u = 0; u < touchBall_Array.size(); u++) {
                    teamTouch_timeArray.add(touchBall_Array.getJSONObject(u));
                }
            }
            //排序
            DataUtil.sortArrayByLongValue(teamTouch_timeArray, "touchTime");
//
            /*
             * 全部得失球的集合
             */
            mapLostAndFree = FootballTeamUtils.lostAndfreeNum(teamTouch_timeArray);
            /*
             * 计算得球与失球人
             * 计算每一个人的得失球情况
             */
            List<LostAndFreeRateInfo> listLostAndFreeRate = new ArrayList<LostAndFreeRateInfo>();
            for (FootballTeamGameEnroll footballTeamGameEnroll : footballTeamEnroll_list) {
                int userIdLost = 0;
                int userIdFree = 0;
                for (Map<String, Object> map : mapLostAndFree) {
                    String lost;
                    String free;
                    lost = (String) map.get("userIdLost");
                    free = (String) map.get("userIdFree");
                    if (lost != null && !lost.equals("")) {
                        if (String.valueOf(footballTeamGameEnroll.getUser().getId()).equals(lost) ||
                                lost.equals(String.valueOf(footballTeamGameEnroll.getUser().getId()))) {
                            userIdLost++;
                        }
                    }
                    if (free != null && !free.equals("")) {
                        if (String.valueOf(userId).equals(free) || free.equals(String.valueOf(userId))) {
                            userIdFree++;
                        }
                    }
                }
                if (userIdFree != 0) {
                    rate = (double) userIdLost / userIdFree;
                }
                LostAndFreeRateInfo LAFrate = new LostAndFreeRateInfo();
                LAFrate.setLostAndFreeRate(rate);
                LAFrate.setUserId(footballTeamGameEnroll.getUser().getId());
                listLostAndFreeRate.add(LAFrate);
            }
            /*
             * int compare(Student o1, Student o2) 返回一个基本类型的整型，
             * 返回负数表示：o1 小于o2，
             * 返回0 表示：o1和o2相等，
             * 返回正数表示：o1大于o2。
             */
            listLostAndFreeRate.sort((o1, o2) -> {
                if (o1 != null && o2 != null) {
                    if (o1.getLostAndFreeRate() < o2.getLostAndFreeRate()) {
                        return 1;
                    }
                    if (o1.getLostAndFreeRate() == o2.getLostAndFreeRate()) {
                        return 0;
                    }
                }
                return -1;
            });
            //转化为map，根据userid获取排名
            Map<String, String> RankMap = new HashMap<>();
            for (int l = 0; l < listLostAndFreeRate.size(); l++) {
                RankMap.put(listLostAndFreeRate.get(l).getUserId().toString(), String.valueOf(l + 1));
            }
            for (LostAndFreeRateInfo lostAndFreeRateInfo : listLostAndFreeRate) {
                if (lostAndFreeRateInfo.getUserId() == userId) {
                    rate = lostAndFreeRateInfo.getLostAndFreeRate();
                }
            }
            /*
             * 排名
             * 丢球率
             */
            if (RankMap.get(String.valueOf(userId)) != null) {
                Rank = RankMap.get(String.valueOf(userId));
            } else {
                Rank = "-1";
            }
        } else {
            Rank = "1";
        }
        int lostAndFreeRates = (int) (rate * 100);
        mapLAF.put("Rank", Integer.parseInt(Rank));
        mapLAF.put("lostAndFreeRate", lostAndFreeRates);
//        } catch (Exception e) {
//            logger.debug(e.getMessage());
//        }
        return mapLAF;
    }


    public Map<String, Object> maxVelocity(FootballTeam team, FootballTeamGame teamGame, User user) {
        FootballTeamGameStatisticsPlayer teamGameStatisticsPlayer = footballTeamGameStatisticsPlayerDaoService.findByTeamIdAndGameIdAndUserId(team.getId(), teamGame.getId(), user.getId());
        Map<String, Object> map = new HashMap<>();
        if (teamGameStatisticsPlayer != null) {
            //最大球速
            Long velocity = 0L;
            List<UserHardwareData> userHardwareDataList = userHardwareDataDaoService.findByGameIdAndUserId(teamGame.getId(), user.getId());
            for (UserHardwareData data : userHardwareDataList) {
                if (data.getVelocity() != null) {
                    long tempVelocity = data.getVelocity();
                    if (tempVelocity > velocity) {
                        velocity = tempVelocity;
                    }
                }
            }

            //最大球速与历史平均值比较
            Long velocitySum = 0L;
            Map<Long, Long> velocityMap = new HashMap<>();
            List<UserHardwareData> historyHardwareData = userHardwareDataDaoService.findStartedByUserIdBeforeGameId(user.getId(), teamGame.getId());
            for (UserHardwareData data : historyHardwareData) {

                long tempVelocity = data.getVelocity() == null ? 0 : data.getVelocity();
                if (velocityMap.containsKey(data.getFootballTeamGame().getId())) {
                    if (tempVelocity > velocityMap.get(data.getFootballTeamGame().getId())) {
                        velocityMap.put(data.getFootballTeamGame().getId(), tempVelocity);
                    }
                } else {
                    velocityMap.put(data.getFootballTeamGame().getId(), tempVelocity);
                }


            }
            for (Map.Entry<Long, Long> entry : velocityMap.entrySet()) {
                velocitySum += entry.getValue();
            }
            double avgVelocity = 0.0;
            if (velocitySum > 0) {
                avgVelocity = ((double) velocitySum / (double) velocityMap.size());
            }
            if (velocitySum > 0) {
                avgVelocity = new BigDecimal(velocitySum / velocityMap.size()).setScale(1, BigDecimal.ROUND_DOWN).doubleValue();
            }

            //排名
            int velocitySpeedRank = 1;
            Map<Long, Long> userVelocityMap = new HashMap<>();
            List<FootballTeamGameEnroll> enrollList = footballTeamGameEnrollDaoService.findByTeamIdAndGameId(team.getId(), teamGame.getId());
            List<Long> userIdList = new ArrayList<>();
            for (FootballTeamGameEnroll enroll : enrollList) {
                userIdList.add(enroll.getUser().getId());
            }
            List<UserHardwareData> teamHardwareDataList = userHardwareDataDaoService.findStartedByGameIdAndUserList(teamGame.getId(), userIdList);
            for (UserHardwareData data : teamHardwareDataList) {
                if (data.getFootballTeamGame().getId().equals(teamGame.getId()) && data.getVelocity() != null) {
                    userVelocityMap.put(data.getUser().getId(), data.getVelocity());
                }
            }
            for (Map.Entry<Long, Long> entry : userVelocityMap.entrySet()) {
                if (entry.getValue() > velocity) {
                    velocitySpeedRank++;
                }
            }
            map.put("velocity", velocity.doubleValue());
            /*map.put("velocityTime", velocityTime);*/
            map.put("velocitySpeedUpOrDown", ((Double) map.get("velocity")).compareTo(avgVelocity) + 1);
            map.put("velocitySpeedRank", velocitySpeedRank);
        } else {
            map.put("velocity", -1);
            map.put("velocitySpeedUpOrDown", 1);
            map.put("velocitySpeedRank", -1);
        }
        return map;
    }


    /**
     * 是否录入数据，是否同步数据
     */
    public Map<String, Object> whetherInput(FootballTeam team,
                                            FootballTeamGame teamGame) {
        Map<String, Object> map = new HashMap<>();
        /*
         * 没有数据表示全部设备启动
         */
        List<UserHardwareData> userHardwaredate_list = userHardwareDataDaoService.isUploadGame(teamGame.getId());
        if (userHardwaredate_list == null || userHardwaredate_list.size() == 0) {
            /*
             * 1：同步  2未同步
             */
            map.put("upload", 1);
        } else {
            map.put("upload", 2);
        }
        List<FootballTeamTakeNode> ListFootballTeamTakeNode = footballTeamGameTakeNodeDaoService.findByGameIdAndTeamId(teamGame.getId(), team.getId());
        if (ListFootballTeamTakeNode != null && ListFootballTeamTakeNode.size() > 0) {
            /*
             * 1：录入 2：未录入
             */
            map.put("enter", 1);
        } else {
            map.put("enter", 2);
        }
        return map;
    }

    public JSONArray packagingFootData(List<UserHardwareData> userHardwaredate_list) {
        JSONArray json = new JSONArray();
        if (userHardwaredate_list != null && userHardwaredate_list.size() > 0) {
            try {

                for (UserHardwareData userHardware : userHardwaredate_list) {
                    if (userHardware.getIsTrainning()) {  //练习赛
                        String jArrayString = userHardware.getFootBallData();
                        if (jArrayString != null && !jArrayString.equals("")) {
                            JSONArray jarray = JSON.parseArray(jArrayString);
                            if (jarray != null && jarray.size() > 0) {
                                for (int g = 0; g < jarray.size(); g++) {
                                    Map<String, Object> map = new HashMap<>();
                                    map.put("foot", jarray.getJSONObject(g).getInteger("foot"));
                                    map.put("exteriorData", jarray.getJSONObject(g).getInteger("exteriorData"));
                                    map.put("soleFootData", jarray.getJSONObject(g).getInteger("soleFootData"));
                                    map.put("heelData", jarray.getJSONObject(g).getInteger("heelData"));
                                    map.put("archData", jarray.getJSONObject(g).getInteger("archData"));
                                    map.put("tiptoeData", jarray.getJSONObject(g).getInteger("tiptoeData"));
                                    map.put("instepKickingData", jarray.getJSONObject(g).getInteger("instepKickingData"));
                                    json.add(map);
                                }
                            }

                        }
                    } else {
                        //比赛
                        String jArrayString = userHardware.getFootBallData();
                        if (jArrayString != null && !jArrayString.equals("")) {
                            JSONArray jsonArray = JSON.parseArray(jArrayString);
                            Map<String, Object> map = new HashMap<>();
                            map.put("foot", userHardware.getUserHardware().getHardwareType());
                            map.put("exteriorData", jsonArray.getJSONObject(0).getInteger("exteriorData"));
                            map.put("soleFootData", jsonArray.getJSONObject(0).getInteger("soleFootData"));
                            map.put("heelData", jsonArray.getJSONObject(0).getInteger("heelData"));
                            map.put("archData", jsonArray.getJSONObject(0).getInteger("archData"));
                            map.put("tiptoeData", jsonArray.getJSONObject(0).getInteger("tiptoeData"));
                            map.put("instepKickingData", jsonArray.getJSONObject(0).getInteger("instepKickingData"));
                            json.add(map);
                        }

                    }
                }
            } catch (JSONException e) {
                logger.error("packagingFootData", e);
            }
        }
        return json;
    }


}

