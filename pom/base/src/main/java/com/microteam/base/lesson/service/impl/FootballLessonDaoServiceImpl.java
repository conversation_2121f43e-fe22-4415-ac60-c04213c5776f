package com.microteam.base.lesson.service.impl;

import com.microteam.base.common.util.lesson.FootballLessonUtils;
import com.microteam.base.entity.lesson.FootballLesson;
import com.microteam.base.entity.lesson.FootballTeamLesson;
import com.microteam.base.lesson.dao.FootballLessonDao;
import com.microteam.base.lesson.service.FootballLessonDaoService;
import com.microteam.base.lesson.service.FootballTeamLessonDaoService;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

@Service("footballLessonDaoService")
public class FootballLessonDaoServiceImpl implements FootballLessonDaoService {
    static Logger logger = Logger.getLogger(FootballLessonDaoServiceImpl.class.getName());

    @Autowired
    private FootballLessonDao dao;

    @Autowired
    private FootballTeamLessonDaoService footballTeamLessonDaoService;

    @Autowired
    private RedisTemplate<String, ?> redisTemplate;


    @Override
    public List<FootballLesson> findByUserIdForPage(Long userId, int page, int pageSize) {
        if (page > 0) {
            page--;
        }
        Pageable pageable = PageRequest.of(page, pageSize);
        return dao.findByUserIdForPage(userId, pageable);
    }

    @Override
    public FootballLesson findById(long id) {
        return dao.findById(id);
    }

    @Override
    public List<FootballLesson> findByLessonIdAndUserId(Long lessonId, Long userId) {
        return dao.findByLessonIdAndUserId(lessonId, userId);
    }

    @Override
    public List<FootballLesson> findOpenness() {
        return dao.findOpenness();
    }

    @Override
    public FootballLesson findNearestByUserId(Long userId) {
        List<FootballLesson> list = dao.findUnfinishedByUserId(userId);
        if (list != null && list.size() > 0) {
            return list.get(0);
        }
        return null;
    }

//    @Override
//    public List<FootballLesson> findByUserIdAndStatusAndTimeForPage(Long userId, String status, String sortTime, int page, int pageSize) {
//        List<FootballLesson> list = dao.findByUserIdAndStatusAndTimeForPage(userId, status, sortTime, page, pageSize);
//        if (list != null && list.size() > 0) {
//            return list;
//        }
//        return new ArrayList<>();
//    }

    @Override
    public List<FootballLesson> findFinishedByUserIdForPage(Long userId, int page, int pageSize) {
        if (page > 0) {
            page--;
        }
        Pageable pageable = PageRequest.of(page, pageSize);
        return dao.findFinishedByUserIdForPage(userId, pageable);
    }

    @Override
    public List<FootballLesson> findUnfinishedByUserIdForPage(Long userId, int page, int pageSize) {
        if (page > 0) {
            page--;
        }
        Pageable pageable = PageRequest.of(page, pageSize);
        return dao.findUnfinishedByUserIdForPage(userId, pageable);
    }

    @Override
    public List<FootballLesson> findUnfinishedByUserIdAndStartTimeForDay(Long userId, Date startTime) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(startTime);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        Date zero = calendar.getTime();
        Calendar calendar2 = Calendar.getInstance();
        calendar2.setTime(startTime);
        calendar2.set(Calendar.HOUR_OF_DAY, 23);
        calendar2.set(Calendar.MINUTE, 59);
        calendar2.set(Calendar.SECOND, 59);
        Date lastDay = calendar2.getTime();
        return dao.findUnfinishedByUserIdAndStartTimeForDay(userId, zero, lastDay);
    }

    @Override
    public FootballLesson findNearestByTeamIdList(List<Long> teamIdList) {
        if (teamIdList != null && teamIdList.size() > 0) {
            return dao.findNearestByTeamIdList(teamIdList);
        }
        return null;
    }

    @Override
    public List<FootballLesson> findByUserIdAndTeamIdListAndTimeForDay(Long userId, List<Long> teamIdList, Date time) {
        List<FootballLesson> list;
        if (teamIdList != null && teamIdList.size() > 0) {
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(time);
            calendar.set(Calendar.HOUR_OF_DAY, 0);
            calendar.set(Calendar.MINUTE, 0);
            calendar.set(Calendar.SECOND, 0);
            Date zero = calendar.getTime();
            Calendar calendar2 = Calendar.getInstance();
            calendar2.setTime(time);
            calendar2.set(Calendar.HOUR_OF_DAY, 23);
            calendar2.set(Calendar.MINUTE, 59);
            calendar2.set(Calendar.SECOND, 59);
            Date lastDay = calendar2.getTime();
            list = dao.findByUserIdAndTeamIdListAndTimeForDay(userId, teamIdList, zero, lastDay);
            if (list != null && list.size() > 0) {
                return list;
            }
        }
        return new ArrayList<>();
    }

    @Override
    public int findCountByUserIdAndAgencyId(Long userId, Long agencyId) {
        return dao.findCountByUserIdAndAgencyId(userId, agencyId);
    }

    @Override
    public List<FootballLesson> findNotOfLessonByUserId(Long lessonId, Long userId) {
        List<FootballLesson> list = dao.findNotOfLessonByUserId(lessonId, userId);
        if (list != null) {
            return list;
        }
        return new ArrayList<>();
    }

    @Override
    public List<FootballLesson> findByUserIdExceptLessonId(Long userId, Long lessonId) {
        List<FootballLesson> list = dao.findByUserIdExceptLessonId(userId, lessonId);
        if (list == null) {
            return new ArrayList<>();
        }
        return list;
    }

    @Override
    public List<FootballLesson> findByTeamIdListAndUserId(List<Long> teamIdList, Long userId) {
        List<FootballTeamLesson> teamLessons = footballTeamLessonDaoService.findByTeamIdList(teamIdList);
        if (teamLessons != null && teamLessons.size() > 0) {
            List<Long> lessonIdList = FootballLessonUtils.getLessonIdListByteamLessons(teamLessons);
            return dao.findLessonByIdListAndUserId(lessonIdList, userId);
        }
        return new ArrayList<>();
    }

    @Override
    public List<FootballLesson> findByIdList(List<Long> idList) {
        if (idList != null && idList.size() > 0) {
            return dao.findByIdList(idList);
        }
        return new ArrayList<>();
    }

    @Override
    public List<FootballLesson> findByUserIdOfTimeSlot(Long userId, Long teamId, int year, int month) {
        List<FootballLesson> lessonList = dao.findByUserIdOfTimeSlot(userId, teamId, year, month);
        if (lessonList != null) {
            return lessonList;
        }
        return new ArrayList<>();
    }

    @Override
    public List<FootballLesson> findUnStarted() {
        List<FootballLesson> list = dao.findUnStarted(new Date());
        if (list == null) {
            return new ArrayList<>();
        }
        return list;
    }

    @Override
    public List<FootballLesson> findByTeamIdListForPage(List<Long> teamIdList, int page, int pageSize) {
        List<FootballTeamLesson> teamLessons = footballTeamLessonDaoService.findByTeamIdList(teamIdList);
        if (teamLessons != null && teamLessons.size() > 0) {
            List<Long> lessonIdList = FootballLessonUtils.getLessonIdListByteamLessons(teamLessons);
            if (page > 0) {
                page--;
            }
            Pageable pageable = PageRequest.of(page, pageSize);
            return dao.findByIdListForPage(lessonIdList, pageable);
        }
        return new ArrayList<>();
    }

    @Override
    public List<FootballLesson> findByTeamIdList(List<Long> teamIdList) {
        List<FootballTeamLesson> teamLessons = footballTeamLessonDaoService.findByTeamIdList(teamIdList);
        if (teamLessons != null && teamLessons.size() > 0) {
            List<Long> lessonIdList = FootballLessonUtils.getLessonIdListByteamLessons(teamLessons);
            return dao.findByIdListOrderByPublishTime(lessonIdList);
        }
        return new ArrayList<>();
    }

    @Override
    public void deleteById(Long id) {
        dao.deleteById(id);
    }

    @Override
    public int deleteByIdList(List<Long> idList) {
        if (idList != null && idList.size() > 0) {
            return dao.deleteByIdList(idList);
        }
        return 0;
    }

    @Override
    public List<Long> findUserIdListByLessonIdList(List<Long> lessonIdList) {
        List<Long> list = new ArrayList<>();
        if (lessonIdList != null && lessonIdList.size() > 0) {
            list = dao.findUserIdListByLessonIdList(lessonIdList);
            if (list == null) {
                return new ArrayList<>();
            }
        }
        return list;
    }

    @Override
    public FootballLesson save(FootballLesson lesson) {
        return dao.save(lesson);
    }

    @Override
    public int updateStatusById(Long id) {
        return dao.updateStatusById(id);
    }

//    @Override
//    public List<FootballLesson> findUnfinishedByTeamIdListAndStartTimeForDay(List<Long> teamIdList, Date startTime) {
//        Calendar calendar = Calendar.getInstance();
//        calendar.setTime(startTime);
//        calendar.set(Calendar.HOUR_OF_DAY, 0);
//        calendar.set(Calendar.MINUTE, 0);
//        calendar.set(Calendar.SECOND, 0);
//        Date zero = calendar.getTime();
//        Calendar calendar2 = Calendar.getInstance();
//        calendar2.setTime(startTime);
//        calendar2.set(Calendar.HOUR_OF_DAY, 23);
//        calendar2.set(Calendar.MINUTE, 59);
//        calendar2.set(Calendar.SECOND, 59);
//        Date lastDay = calendar2.getTime();
//        return dao.findUnfinishedByTeamIdListAndStartTimeForDay(teamIdList, zero, lastDay);
//    }
}
