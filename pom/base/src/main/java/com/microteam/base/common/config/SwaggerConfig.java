package com.microteam.base.common.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;
import springfox.documentation.builders.ApiInfoBuilder;
import springfox.documentation.builders.ParameterBuilder;
import springfox.documentation.builders.PathSelectors;
import springfox.documentation.builders.RequestHandlerSelectors;
import springfox.documentation.schema.ModelRef;
import springfox.documentation.service.ApiInfo;
import springfox.documentation.service.Parameter;
import springfox.documentation.spi.DocumentationType;
import springfox.documentation.spring.web.plugins.Docket;
import springfox.documentation.swagger2.annotations.EnableSwagger2;

import java.util.ArrayList;
import java.util.List;

@Component
@Configuration
@EnableSwagger2
public class SwaggerConfig implements WebMvcConfigurer {

    /**
     * 创建API应用     * apiInfo() 增加API相关信息     * 通过select()函数返回一个ApiSelectorBuilder实例,用来控制哪些接口暴露给Swagger来展现，     * 本例采用指定扫描的包路径来定义指定要建立API的目录。     *      * @return
     */
    @Bean
    public Docket createRestApi() {
        ParameterBuilder authorization = new ParameterBuilder();
        authorization.name("authorization")
                .description("authorization")
                .modelRef(new ModelRef("string"))
                .parameterType("header")
                .required(false)
                .defaultValue("679220204d24bc8a673ff9c9b52c9653")
                .build(); //header中的ticket参数非必填，传空也可以
        ParameterBuilder contentType = new ParameterBuilder();
        contentType.name("Content-Type")
                .description("Content-Type")
                .modelRef(new ModelRef("string"))
                .parameterType("header")
                .required(true)
                .defaultValue("application/json")
                .build();
        ParameterBuilder pageSize = new ParameterBuilder();
        pageSize.name("pageSize")
                .description("pageSize")
                .modelRef(new ModelRef("int"))
                .parameterType("header")
                .required(false)
                .defaultValue("10")
                .build(); //header中的ticket参数非必填，传空也可以
        ParameterBuilder cursor = new ParameterBuilder();
        cursor.name("cursor")
                .description("cursor")
                .modelRef(new ModelRef("int"))
                .parameterType("header")
                .required(false)
                .defaultValue("1")
                .build(); //header中的ticket参数非必填，传空也可以
        List<Parameter> pars = new ArrayList<>();
        pars.add(authorization.build());    //根据每个方法名也知道当前方法在设置什么参数
        pars.add(contentType.build());
        pars.add(pageSize.build());
        pars.add(cursor.build());

        return new Docket(DocumentationType.SWAGGER_2)
                .apiInfo(apiInfo())
                .select()
                .apis(RequestHandlerSelectors.basePackage("com.microteam"))
                .paths(PathSelectors.any())
                .build()
                .globalOperationParameters(pars);
    }

    /**
     * 创建该API的基本信息（这些基本信息会展现在文档页面中）     * 访问地址：http://项目实际地址/swagger-ui.html     * @return
     */
    private ApiInfo apiInfo() {
        return new ApiInfoBuilder()
                .title("MicroService APIs")
                .description("更多请关注https://www.google.com/")
                .termsOfServiceUrl("https://www.google.com/")
                .version("2.0")
                .build();
    }


}