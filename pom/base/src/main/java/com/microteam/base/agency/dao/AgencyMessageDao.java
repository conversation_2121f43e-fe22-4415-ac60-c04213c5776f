package com.microteam.base.agency.dao;

import com.microteam.base.entity.agency.AgencyMessage;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

public interface AgencyMessageDao extends JpaRepository<AgencyMessage, Long>, JpaSpecificationExecutor<AgencyMessage> {

    @Query("from AgencyMessage as agencyMessage " +
            "left join fetch agencyMessage.agency as agency " +
            "left join fetch agencyMessage.user as user " +
            "where agency.id = (:agencyId) " +
            "and agencyMessage.deleted = false " +
            "order by agencyMessage.createTime desc ")
    List<AgencyMessage> findByAgencyIdOrderByCreateTimeForPage(@Param("agencyId") Long agencyId, Pageable pageable);
}
