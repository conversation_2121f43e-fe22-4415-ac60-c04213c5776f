package com.microteam.base.common.pojo;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

@Getter
@Setter
public class UserHardWareStepPojo implements Serializable {
    private static final long serialVersionUID = 1L;
    private long startTime;
    private int intervalTime;
    private long endTime;
    private int stepCount;
    private int isHaveBall;    //有球、无球标识(1：有球   0：无球)

}
