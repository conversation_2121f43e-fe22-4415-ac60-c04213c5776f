package com.microteam.base.entity.match;

import com.microteam.base.common.base.BaseEntity;
import com.microteam.base.entity.user.User;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

@Entity
@Table(name = "football_team_match_new")
@Getter
@Setter
public class FootballTeamMatchNew extends BaseEntity implements Serializable {

    @ManyToOne
    @JoinColumn(name = "publisherId", nullable = false)
    private User publisher;
    @ManyToOne
    @JoinColumn(name = "matchId", nullable = false)
    private FootballTeamMatch teamMatch;
    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "publishTime", nullable = false, length = 19)
    private Date publishTime;
    @Column(name = "countryCode")
    private String countryCode;
    @Column(name = "provinceCode")
    private String provinceCode;
    @Column(name = "cityCode")
    private String cityCode;
    @Column(name = "countyCode")
    private String countyCode;
    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "matchNewTime", length = 19)
    private Date matchNewTime; //快报发生时间
    @Column(name = "detailLocation", length = 200)
    private String detailLocation;
    @Column(name = "title", length = 100)
    private String title;
    @Column(name = "content", length = 65535)
    private String content;


    public Date getPublishTime() {
        if (this.publishTime == null) {
            return null;
        }
        return (Date) publishTime.clone();
    }

    public void setPublishTime(Date publishTime) {
        this.publishTime = (Date) publishTime.clone();
    }


    public Date getMatchNewTime() {
        if (this.matchNewTime == null) {
            return null;
        }
        return (Date) matchNewTime.clone();
    }

    public void setMatchNewTime(Date matchNewTime) {
        this.matchNewTime = (Date) matchNewTime.clone();
    }


}
