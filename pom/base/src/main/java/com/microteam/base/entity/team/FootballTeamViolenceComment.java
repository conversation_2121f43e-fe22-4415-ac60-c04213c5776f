package com.microteam.base.entity.team;


import com.microteam.base.common.base.BaseEntity;
import com.microteam.base.entity.user.User;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;
import java.io.Serializable;

@Entity
@Table(name = "football_team_violence_comment")
@Getter
@Setter
public class FootballTeamViolenceComment extends BaseEntity implements Serializable {

    @ManyToOne
    @JoinColumn(name = "userId", nullable = false)
    private User user;
    @ManyToOne
    @JoinColumn(name = "violenceId", nullable = false)
    private FootballTeamViolence footballTeamViolence;
    @Column(name = "comment", length = 65535)
    private String comment;

}
