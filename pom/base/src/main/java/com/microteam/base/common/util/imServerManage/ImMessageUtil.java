package com.microteam.base.common.util.imServerManage;

import com.alibaba.fastjson.JSONObject;
import com.microteam.base.common.constant.ConstantUserManage;
import com.microteam.base.common.constant.FootballContants;
import com.microteam.base.common.util.common.ArithmeticUtil;
import com.microteam.base.common.util.imServerManage.service.huanxinService.ImServerHuanxinService;
import com.microteam.base.entity.team.FootballTeam;
import com.microteam.base.entity.team.FootballTeamGame;
import com.microteam.base.entity.team.FootballTeamGameEnroll;
import com.microteam.base.entity.team.FootballTeamUser;
import com.microteam.base.entity.user.User;
import com.microteam.base.team.service.FootballTeamGameEnrollDaoService;
import com.microteam.base.team.service.FootballTeamUserDaoService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Component
public class ImMessageUtil {
    static Logger logger = LoggerFactory.getLogger(ArithmeticUtil.class.getName());
    @Autowired
    private FootballTeamUserDaoService footballTeamUserDaoService;
    @Autowired
    private FootballTeamGameEnrollDaoService footballTeamGameEnrollDaoService;
    @Autowired
    private ImServerHuanxinService imServerHuanxinService;

    //当比赛提前启动或结束时，发送比赛启动或结束通知（透传消息）
    //footballTeamGame -- 比赛对象
    //user             -- 用户对象
    //dateTime         -- 时间,比赛启动或结束时间
    //strZoned         -- 时区
    //notifyType       -- 通知类型 （开始或结束）
    public void sendGameStartupOrEndNotify(FootballTeamGame footballTeamGame, User user, Date dateTime, String strZoned, String notifyType) {
        try {
            //透传消息
            //如果比赛提前开始（即比赛开始时间被修改），则发透传消息给球队的每个队员（除自已外）
            FootballTeam team = footballTeamGame.getFootballTeam();
            List<FootballTeamUser> footballTeamUserList = footballTeamUserDaoService.findByTeamId(team.getId());

            long teamId = team.getId(); //球队ID
            long gameId = footballTeamGame.getId(); //比赛ID
            String message;
            String mode = "users";

            //修改球衣发消息规则：如果当前录用户与修改目标用户不是同一个人（UserId不同），则发发透传消息;
            //如果是登录自己修改自己的球衣，则不需要给自己发透传消息。
            //选出发送球员名单（即接收信息的人）
            String tmpDeviceToken = user.getDeviceTokens();
            List<String> memberArray = new ArrayList<>();
            for (FootballTeamUser teamUser : footballTeamUserList) {
                String deviceToken = teamUser.getUser().getDeviceTokens();
                if (!deviceToken.equals(tmpDeviceToken)) {
                    memberArray.add(deviceToken);
                }
            }

            if (memberArray.size() > 0) {
                JSONObject object = new JSONObject();
                object.put("action", notifyType + "_ahead");    //通知功能值：teamGameStart_ahead(比赛提前开始)或 teamGameEnd_ahead(比赛提前结束)
                object.put("dateTime", dateTime);    //比赛开始或结束时间
                object.put("zoned", strZoned);       //时区
                object.put("teamId", teamId);        //球队ID
                object.put("teamGameId", gameId);    //比赛ID

                String fromImUsername = ConstantUserManage.getLongTenLetter(user.getId());
                message = object.toString();
                boolean result = imServerHuanxinService.sendUMengMessage(message, memberArray);
            }

        } catch (Exception e) {
            e.printStackTrace();
            logger.debug("sendGameNotify Exception Error." + e.getMessage());
        }
    }

    public boolean sendImOfUploadedGameData(Long userId, Long gameId, Long teamId) {
        String mode = "users";
        String fromImUsername = ConstantUserManage.getLongTenLetter(userId);
        List<String> memberList = new ArrayList<>();
        /* List<FootballTeamGameStatisticsPlayer> playerList = footballTeamGameStatisticsPlayerDaoService.findByGameIdAndTeamId(gameId,teamId);*/

        List<FootballTeamUser> teamUserList = footballTeamUserDaoService.findPlayerAndOwnerByTeamId(teamId);
        List<Long> userIdList = new ArrayList<>();
        for (FootballTeamUser teamUser : teamUserList) {
            Long tmpId = teamUser.getUser().getId();
            if (!tmpId.equals(userId)) {
                userIdList.add(tmpId);
            }

        }

        List<FootballTeamGameEnroll> enrollList = footballTeamGameEnrollDaoService.findByUserListAndTeamAndGame(userIdList, teamId, gameId);
        for (FootballTeamGameEnroll enroll : enrollList) {
            String deviceToken = enroll.getUser().getDeviceTokens();
            if (!memberList.contains(deviceToken)) {
                memberList.add(deviceToken);
            }
        }

       /* List<UserHardwareData> dataList = userHardwareDataDaoService.findStartedByGameIdAndUserList(gameId, enrollUserIdList);
        for (UserHardwareData data : dataList) {
            String memberUserName = ConstantUserManage.getLongTenLetter(data.getUser().getId());
            if (!memberList.contains(memberUserName) && !memberUserName.equals(fromImUsername)) {
                memberList.add(memberUserName);
            }
        }*/
        if (memberList.size() > 0) {
            JSONObject transportMap = new JSONObject();
            transportMap.put("action", FootballContants.UPLOADED);
            transportMap.put("teamId", teamId);
            transportMap.put("gameId", gameId);
            transportMap.put("userId", userId);
            String message = transportMap.toString();
            boolean r = imServerHuanxinService.sendUMengMessage(message, memberList);
        }
        return true;
    }
}
