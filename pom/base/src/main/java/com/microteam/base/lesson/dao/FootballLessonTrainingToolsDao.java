package com.microteam.base.lesson.dao;


import com.microteam.base.entity.lesson.FootballLessonTrainingTools;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

public interface FootballLessonTrainingToolsDao extends JpaRepository<FootballLessonTrainingTools, Long>, JpaSpecificationExecutor<FootballLessonTrainingTools> {

//    List<TrainingTools> findByIdList(List<Long> idList);
//
//    //通过课程ID获取训练工具
//    List<TrainingTools> findAllTrainingTools();

    @Query("from FootballLessonTrainingTools as seltools " +
            "where seltools.lessonId = (:lessonId) " +
            "and seltools.deleted = 0 " +
            "order by seltools.id asc ")
    List<FootballLessonTrainingTools> findByLessonId(@Param("lessonId") Long lessonId);

    //保存选择的课程训练工具
//    boolean saveSelectedLessonTrainingTools(Long lessonId, List<Long> tools);

    @Query("delete from FootballLessonTrainingTools as lessonTrainingTools " +
            "where lessonTrainingTools.lessonId in (:lessonIdList) ")
    int deleteByLessonIdList(@Param("lessonIdList") List<Long> lessonIdList);

    @Query("from FootballLessonTrainingTools as seltools " +
            "where seltools.courwareId = (:courwareId) " +
            "and seltools.deleted = 0 " +
            "order by seltools.id asc ")
    List<FootballLessonTrainingTools> fingByCourwareId(@Param("courwareId") Long courwareId);
}
