package com.microteam.base.agency.dao;

import com.microteam.base.entity.agency.AgencyUser;
import com.microteam.base.entity.agency.FootballAgency;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

public interface AgencyUserDao extends JpaRepository<AgencyUser, Long>, JpaSpecificationExecutor<AgencyUser> {

    @Query("from AgencyUser as agencyUser " +
            " left join fetch agencyUser.agency as agency " +
            " left join fetch agencyUser.user as user " +
            " left join fetch agencyUser.role as role " +
            " where agency.id = (:id) " +
            " and agencyUser.deleted = false ")
    AgencyUser findById(@Param("id") long id);

    @Query("from AgencyUser as agencyUser " +
            " left join fetch agencyUser.agency as agency " +
            " left join fetch agencyUser.user as user " +
            " left join fetch agencyUser.role as role " +
            " where agency.id = (:agencyId) " +
            " and user.id = (:userId) " +
            " and role.id = (:roleId) " +
            " and agencyUser.deleted = false " +
            " and agency.deleted = false " +
            " and user.deleted = false ")
    AgencyUser findByAgencyIdAndUserIdAndRoleId(@Param("agencyId") Long agencyId, @Param("userId") Long userId, @Param("roleId") Long roleId);

    @Query(value = "select count(1) from agency_user as agencyUser " +
            " where agencyUser.agencyId = (:agencyId) " +
            " and agencyUser.roleId = (:roleId) " +
            " and agencyUser.deleted = 0 ", nativeQuery = true)
    Integer findCountByAgencyIdAndRoleId(@Param("agencyId") Long agencyId, @Param("roleId") Long roleId);

    @Query(value = "SELECT COUNT(DISTINCT au.userId) AS count " +
            "FROM agency_user AS au " +
            "WHERE agencyId = :agencyId " +
            "AND au.roleId in (1,5) " +
            "AND au.deleted = 0", nativeQuery = true)
    Integer findCountOfStuByAgencyId(@Param("agencyId") Long agencyId);

    @Query("from AgencyUser as agencyUser " +
            " left join fetch agencyUser.agency as agency " +
            " left join fetch agencyUser.user as user " +
            " left join fetch agencyUser.role as role " +
            " where agency.id = (:agencyId) " +
            " and user.id = (:userId) " +
            " and agencyUser.deleted = false " +
            " order by INSTR('1765234',role.id) ")
    AgencyUser findOneByAgencyIdAndUserId(@Param("agencyId") Long agencyId, @Param("userId") Long userId);

    @Query("from AgencyUser as agencyUser " +
            " left join fetch agencyUser.agency as agency " +
            " left join fetch agencyUser.user as user " +
            " left join fetch agencyUser.role as role " +
            " where agency.id = (:agencyId) " +
            " and user.id = (:userId) " +
            " and agencyUser.deleted = false ")
    List<AgencyUser> findByAgencyIdAndUserId(@Param("agencyId") Long agencyId, @Param("userId") Long userId);

    @Query("from AgencyUser as agencyUser " +
            " left join fetch agencyUser.agency as agency " +
            " left join fetch agencyUser.user as user " +
            " left join fetch agencyUser.role as role " +
            " where agency.id = (:agencyId) " +
            " and role.id = (:roleId) " +
            " and agencyUser.deleted = false ")
    List<AgencyUser> findByAgencyIdAndRoleId(@Param("agencyId") Long agencyId, @Param("roleId") Long roleId);

    @Query("from AgencyUser as agencyUser " +
            " left join fetch agencyUser.agency as agency " +
            " left join fetch agencyUser.user as user " +
            " left join fetch agencyUser.role as role " +
            " where agency.id = (:agencyId) " +
            " and role.id = (:roleId) " +
            " and agencyUser.deleted = false ")
    List<AgencyUser> findByAgencyIdAndRoleIdForPage(@Param("agencyId") Long agencyId, @Param("roleId") Long roleId, Pageable pageable);

    @Query("from AgencyUser as agencyUser " +
            " left join fetch agencyUser.agency as agency " +
            " left join fetch agencyUser.user as user " +
            " left join fetch agencyUser.role as role " +
            " where user.id = (:userId) " +
            " and agencyUser.deleted = false " +
            " order by agencyUser.createTime desc ")
    List<AgencyUser> findByUserId(@Param("userId") Long userId);

    @Query("from AgencyUser as agencyUser " +
            " left join fetch agencyUser.agency as agency " +
            " left join fetch agencyUser.user as user " +
            " left join fetch agencyUser.role as role " +
            " where user.id = (:userId) " +
            " and agencyUser.deleted = false " +
            "and agency.deleted = false " +
            " ORDER BY CASE role.id WHEN 1 THEN 0 WHEN 5 THEN 1 WHEN 6 THEN 2 WHEN 2 THEN 3 ELSE 4 END,agencyUser.createTime DESC ")
    List<AgencyUser> findByUserIdOrderByRoleId(@Param("userId")Long userId);

    @Query("from AgencyUser as agencyUser " +
            " left join fetch agencyUser.agency as agency " +
            " left join fetch agencyUser.user as user " +
            " left join fetch agencyUser.role as role " +
            " where user.id = (:userId) " +
            " and agencyUser.deleted = false " +
            " and role.id in (:roleIdList) " +
            " and agency.type = 2 " +
            " order by agencyUser.createTime desc ")
    List<AgencyUser> findYouthAgencyByUserIdAndRoleIdList(@Param("userId") Long userId, @Param("roleIdList") List<Long> roleIdList);

    @Query("update AgencyUser as agencyUser " +
            "set agencyUser.deleted = 1 " +
            "where agencyUser.id = (:id) " +
            "and agencyUser.deleted = 0")
    @Modifying
    boolean delById(@Param("id") Long id);

    @Query(value = "SELECT aUser.userId " +
            "FROM agency_user AS aUser " +
            "WHERE EXISTS (" +
            "SELECT * " +
            "FROM agency_team AS aTeam " +
            "WHERE aTeam.agencyId = aUser.agencyId " +
            "AND aTeam.teamId = :teamId)", nativeQuery = true)
    List<Long> findUserIdListByTeamId(@Param("teamId") Long teamId);

    @Query("from AgencyUser as agencyUser " +
            "left join fetch agencyUser.agency as agency " +
            "left join fetch agencyUser.user as user " +
            "where agency.id = (:agencyId) " +
            "and agencyUser.deleted = false ")
    List<AgencyUser> findByAgencyId(Long agencyId);

}
