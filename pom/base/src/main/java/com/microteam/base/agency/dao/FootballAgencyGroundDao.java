package com.microteam.base.agency.dao;

import com.microteam.base.entity.agency.FootballAgency;
import com.microteam.base.entity.agency.FootballAgencyGround;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

public interface FootballAgencyGroundDao extends JpaRepository<FootballAgencyGround, Long>, JpaSpecificationExecutor<FootballAgencyGround> {

    //分页查询机构场地
    @Query("from FootballAgencyGround as agencyGround " +
            "where agencyGround.footballAgency=?1 " +
            "and agencyGround.deleted=false " +
            "order by agencyGround.createTime desc ")
    List<FootballAgencyGround> findAgencyGroundList(FootballAgency agency, Pageable pageable);

    @Query("from FootballAgencyGround as agencyGround " +
            "where agencyGround.id=?1 " +
            "and agencyGround.deleted=false ")
    FootballAgencyGround findById(long id);
}
