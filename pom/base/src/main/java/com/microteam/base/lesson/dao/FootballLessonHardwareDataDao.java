package com.microteam.base.lesson.dao;


import com.microteam.base.entity.lesson.FootballLessonHardwareData;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

public interface FootballLessonHardwareDataDao extends JpaRepository<FootballLessonHardwareData, Long>, JpaSpecificationExecutor<FootballLessonHardwareData> {

    @Query("update FootballLessonHardwareData as lessonHardwareData " +
            "set lessonHardwareData.isStartUp=1 " +
            "where lessonHardwareData.hardwareId = (:hardwareId) " +
            "and lessonHardwareData.userId = (:userId) " +
            "and lessonHardwareData.isStartUp=2 " +
            "and lessonHardwareData.deleted=false ")
    @Modifying
    int shutDownByUserIdAndHardwareId(@Param("userId") Long userId, @Param("hardwareId") Long hardwareId);

    @Query("update FootballLessonHardwareData as lessonHardwareData " +
            "set lessonHardwareData.isStartUp=1 " +
            "where lessonHardwareData.userId = (:userId) " +
            "and lessonHardwareData.isStartUp=2 " +
            "and lessonHardwareData.lessonId<>(:lessonId) " +
            "and lessonHardwareData.deleted=false ")
    @Modifying
    int shutDownOtherLessonByUserIdAndLessonId(@Param("userId") Long userId, @Param("lessonId") Long lessonId);

    @Query("from FootballLessonHardwareData as lessonHardwareData " +
            "where lessonHardwareData.hardwareId=(:hardwareId) " +
            "and lessonHardwareData.lessonId=(:lessonId) " +
            "and lessonHardwareData.userId=(:userId) " +
            "and lessonHardwareData.deleted=false " +
            "and lessonHardwareData.isStartUp<>1")
    FootballLessonHardwareData findByHardwareIdAndLessonIdAndUserId(@Param("hardwareId") Long hardwareId, @Param("lessonId") Long lessonId, @Param("userId") Long userId);

    @Query("from FootballLessonHardwareData as lessonHardwareData " +
            "where lessonHardwareData.lessonId=(:lessonId) " +
            "and lessonHardwareData.userId=(:userId) " +
            "and lessonHardwareData.isStartUp=2 " +
            "and lessonHardwareData.deleted=false")
    List<FootballLessonHardwareData> findStartedByLessonIdAndUserId(@Param("lessonId") Long lessonId, @Param("userId") Long userId);

    @Query("from FootballLessonHardwareData as lessonHardwareData " +
            "where lessonHardwareData.lessonId=(:lessonId) " +
            "and lessonHardwareData.userId=(:userId) " +
            "and lessonHardwareData.isStartUp=3 " +
            "and lessonHardwareData.deleted=false")
    List<FootballLessonHardwareData> findUploadedByLessonIdAndUserId(@Param("lessonId") Long lessonId, @Param("userId") Long userId);

    @Query("from FootballLessonHardwareData as lessonHardwareData " +
            "where lessonHardwareData.lessonId in (:lessonIdList) " +
            "and lessonHardwareData.userId = (:userId) " +
            "and lessonHardwareData.deleted=false " +
            "and lessonHardwareData.isStartUp<>1")
    List<FootballLessonHardwareData> findByLessonIdListAndUserId(@Param("lessonIdList") List<Long> lessonIdList, @Param("userId") Long userId);

//    @Query("delete from FootballLessonHardwareData as lessonHardwareData " +
//            "where lessonHardwareData.lessonId = (:lessonId) " +
//            "and lessonHardwareData.userId = (:userId) " +
//            "and lessonHardwareData.deleted=false")
//    int delByUserIdAndLessonId(@Param("userId") Long userId, @Param("lessonId") Long lessonId);

    void deleteByLessonIdAndUserId(Long lessonId, Long userId);

    @Query("update FootballLessonHardwareData as lessonHardwareData " +
            "set lessonHardwareData.deleted = true " +
            "where lessonHardwareData.userId = (:userId) " +
            "and lessonHardwareData.deleted = false")
    @Modifying
    int delByUserId(@Param("userId") Long userId);

    @Query("from FootballLessonHardwareData as lessonHardwareData " +
            "where lessonHardwareData.hardwareId = (:hardwareId) " +
            "and lessonHardwareData.lessonId = (:lessonId) " +
            "and lessonHardwareData.deleted=false " +
            "and lessonHardwareData.isStartUp=2")
    List<FootballLessonHardwareData> findStartedByHardwareIdAndLessonId(@Param("hardwareId") Long hardwareId, @Param("lessonId") Long lessonId);

    @Query("from FootballLessonHardwareData as lessonHardwareData " +
            "where lessonHardwareData.lessonId = (:lessonId) " +
            "and lessonHardwareData.userId = (:userId) " +
            "and lessonHardwareData.deleted=false " +
            "and lessonHardwareData.isStartUp<>1")
    List<FootballLessonHardwareData> findByLessonIdAndUserId(@Param("lessonId") Long lessonId, @Param("userId") Long userId);

    @Query("from FootballLessonHardwareData as data " +
            "where data.lessonId = :lessonId " +
            "and data.userId = :userId " +
            "and data.isStartUp = 2 " +
            "and data.deleted = false " +
            "order by data.hardwareId ")
    List<FootballLessonHardwareData> findStartedByLessonIdAndUserIdOrderByHardwareId(@Param("lessonId") Long lessonId, @Param("userId") Long userId);

    @Query("from FootballLessonHardwareData as lessonHardwareData " +
            "where lessonHardwareData.userId=(:userId) " +
            "and lessonHardwareData.isStartUp=2 " +
            "and lessonHardwareData.deleted=false")
    List<FootballLessonHardwareData> findStartedByUserId(@Param("userId") Long userId);

    @Query("from FootballLessonHardwareData as lessonHardwareData " +
            "where lessonHardwareData.lessonId = :lessonId " +
            "and lessonHardwareData.userId in (:userIdList) " +
            "and lessonHardwareData.isStartUp = 3 " +
            "and lessonHardwareData.deleted=false")
    List<FootballLessonHardwareData> findUploadedByLessonIdAndUserIdList(@Param("lessonId") Long lessonId, @Param("userIdList") List<Long> userIdList);

    //获取用户的硬件课程个人的硬件数据
    @Query(value = "select * from football_lesson_hardware_data as lessonHardwareData "
            + " where lessonHardwareData.lessonId=(:lessonId) "
            + " and lessonHardwareData.userId = (:userId) "
            + " and lessonHardwareData.isStartUp=3 "
            + " and lessonHardwareData.deleted=false "
            + " order by kickBallStartTime ", nativeQuery = true)
    List<FootballLessonHardwareData> findStartedByLessonIdAndUserIdOrderByKickBallStartTime(@Param("lessonId") Long lessonId, @Param("userId") Long userId);

    //获取课程训练报名的所有用户的硬件数据
    @Query("from FootballLessonHardwareData as lessonHardwareData " +
            "where lessonHardwareData.lessonId = :lessonId " +
            "and lessonHardwareData.userId in (:userIdList) " +
            "and lessonHardwareData.isStartUp = 3 " +
            "and lessonHardwareData.deleted=false " +
            "order by kickBallStartTime asc ")
    List<FootballLessonHardwareData> findUploadedByLessonIdAndUserIdListOrderByKickBallStartTime(@Param("lessonId") Long lessonId, @Param("userIdList") List<Long> userIdList);

    @Query("delete from FootballLessonHardwareData as lessonHardwareData " +
            "where lessonHardwareData.lessonId in (:lessonIdList) ")
    @Modifying
    int deleteByLessonIdList(@Param("lessonIdList") List<Long> lessonIdList);
}
