package com.microteam.base.lesson.service.impl;

import com.microteam.base.entity.lesson.FootballLessonTakeNodes;
import com.microteam.base.lesson.dao.FootballLessonTakeNodesDao;
import com.microteam.base.lesson.service.FootballLessonTakeNodesDaoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service("footballLessonTakeNodesDaoService")
public class FootballLessonTakeNodesDaoServiceImpl implements FootballLessonTakeNodesDaoService {

    @Autowired
    FootballLessonTakeNodesDao dao;

    @Override
    public List<FootballLessonTakeNodes> findByLessonId(Long lessonId) {
        return dao.findByLessonId(lessonId);
    }

    @Override
    public FootballLessonTakeNodes findByLessonIdAndTeamIdAndUserId(Long lessonId, Long teamId, Long userId) {
        return dao.findByLessonIdAndTeamIdAndUserId(lessonId, teamId, userId);
    }

//    @Override
//    public int saveNodeList(List<LessonTakeNodePojo> pojoList) {
//        int result = 0;
//        if (pojoList != null) {
//            for (LessonTakeNodePojo pojo : pojoList) {
//                if (dao.saveNode(pojo)) {
//                    result++;
//                }
//            }
//        }
//        return result;
//    }

    @Override
    public List<FootballLessonTakeNodes> findByLessonIdAndTeamId(Long lessonId, Long teamId) {
        return dao.findByLessonIdAndTeamId(lessonId, teamId);
    }

    @Override
    public int deleteByTeamId(Long teamId) {
        return dao.deleteByTeamId(teamId);
    }
}
