//package com.microteam.base.agency.dao.impl;
//
//import com.microteam.base.agency.dao.FootballAgencyClassNoticeDao;
//import com.microteam.base.entity.agency.FootballAgencyClassNotice;
//import com.microteam.base.common.base.AbstractHibernateDao;
//import org.apache.log4j.Logger;
//import org.hibernate.query.Query;
//import org.springframework.stereotype.Repository;
//
//import java.util.List;
//
//@Repository("footballAgencyClassNoticeDao")
//public class FootballAgencyClassNoticeDaoImpl extends AbstractHibernateDao<FootballAgencyClassNotice> implements FootballAgencyClassNoticeDao {
//    static Logger logger = Logger.getLogger(FootballAgencyClassNoticeDaoImpl.class.getName());
//
//    public FootballAgencyClassNoticeDaoImpl() {
//        super();
//        setClazz(FootballAgencyClassNotice.class);
//    }
//
//    //分页查询通知
//    @Override
//    public List<FootballAgencyClassNotice> findByAgencyIdAndAgencyClassIdForPage(Long agencyId, Long agencyClassId, int pageSize, int page) {
//        String hql = "from FootballAgencyClassNotice as footballAgencyClassNotice " +
//                "left join fetch footballAgencyClassNotice.user as user " +
//                "where footballAgencyClassNotice.footballAgency.id = (:agencyId) " +
//                "and footballAgencyClassNotice.footballAgencyClass.id = (:agencyClassId) " +
//                "and footballAgencyClassNotice.deleted=false ";
//        Query query = getCurrentSession().createQuery(hql)
//                .setParameter("agencyId", agencyId)
//                .setParameter("agencyClassId", agencyClassId);
//        query.setFirstResult((page - 1) * pageSize);
//        query.setMaxResults(pageSize);
//        List<FootballAgencyClassNotice> list = query.list();
//        return list;
//    }
//
//    @Override
//    public FootballAgencyClassNotice findById(long id) {
//        String hql = "from FootballAgencyClassNotice as footballAgencyClassNotice " +
//                "left join fetch footballAgencyClassNotice.user as user " +
//                "left join fetch footballAgencyClassNotice.footballAgency as footballAgency " +
//                "left join fetch footballAgencyClassNotice.footballAgencyClass as footballAgencyClass " +
//                "where footballAgencyClassNotice.id=? " +
//                "and footballAgencyClassNotice.deleted=false ";
//        Query query = getCurrentSession().createQuery(hql).setParameter(0, id);
//        List<FootballAgencyClassNotice> list = query.list();
//        if (list != null && list.size() > 0) {
//            return list.get(0);
//        }
//        return null;
//    }
//
//}
