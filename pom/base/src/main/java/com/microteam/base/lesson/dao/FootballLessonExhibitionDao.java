package com.microteam.base.lesson.dao;


import com.microteam.base.entity.lesson.FootballLessonExhibition;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;


public interface FootballLessonExhibitionDao extends JpaRepository<FootballLessonExhibition, Long>, JpaSpecificationExecutor<FootballLessonExhibition> {

    @Query("from FootballLessonExhibition as exhibition " +
            "where exhibition.lessonId=(:lessonId) " +
            "and exhibition.userId=(:userId) " +
            "and exhibition.deleted=false ")
    FootballLessonExhibition findByLessonId(@Param("lessonId")Long lessonId,@Param("userId")Long userId);

    @Query("from FootballLessonExhibition as exhibition " +
            "where exhibition.lessonId=(:lessonId) " +
            "and exhibition.deleted=false ")
    List<FootballLessonExhibition> findByExLessonId(@Param("lessonId")Long lessonId);
}
