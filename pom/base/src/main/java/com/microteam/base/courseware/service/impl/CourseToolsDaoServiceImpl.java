package com.microteam.base.courseware.service.impl;

import com.microteam.base.courseware.dao.CourseToolsDao;
import com.microteam.base.courseware.service.CourseToolsDaoService;
import com.microteam.base.entity.courseware.CourseWareTools;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class CourseToolsDaoServiceImpl implements CourseToolsDaoService {
    @Autowired
    private CourseToolsDao dao;

    @Override
    public List<CourseWareTools> findByCoursewareId(long courseWareId) {
        return dao.findByCoursewareId(courseWareId);
    }

    @Override
    public CourseWareTools save(CourseWareTools courseWareTools) {
        return dao.save(courseWareTools);
    }
}
