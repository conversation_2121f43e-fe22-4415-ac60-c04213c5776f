package com.microteam.base.entity.agency;


import com.microteam.base.common.base.BaseEntity;
import com.microteam.base.entity.user.User;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

@Entity
@Table(name = "football_agency_class_course")
@Getter
@Setter
public class FootballAgencyClassCourse extends BaseEntity implements Serializable {

    @ManyToOne
    @JoinColumn(name = "creator", nullable = false)
    private User user;
    @ManyToOne
    @JoinColumn(name = "classId", nullable = false)
    private FootballAgencyClass footballAgencyClass;
    @Column(name = "courseName", nullable = false, length = 100)
    private String courseName;
    @Column(name = "courseContent", length = 65535)
    private String courseContent;
    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "courseStartTime", length = 19)
    private Date courseStartTime;
    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "courseEndTime", length = 19)
    private Date courseEndTime;
    @Column(name = "countryCode", length = 15)
    private String countryCode;
    @Column(name = "provinceCode", length = 15)
    private String provinceCode;
    @Column(name = "cityCode", length = 15)
    private String cityCode;
    @Column(name = "countyCode", length = 15)
    private String countyCode;
    @Column(name = "location", length = 200)
    private String location;
    @Column(name = "courStatus")
    private Short courStatus;
    @Column(name = "remark", length = 65535)
    private String remark;
    @Column(name = "memberCount")
    private Integer memberCount;

    public Date getCourseStartTime() {
        if (this.courseStartTime == null) {
            return null;
        }
        return (Date) this.courseStartTime.clone();
    }

    public void setCourseStartTime(Date courseStartTime) {
        this.courseStartTime = (Date) courseStartTime.clone();
    }

    public Date getCourseEndTime() {
        if (this.courseEndTime == null) {
            return null;
        }
        return (Date) this.courseEndTime.clone();
    }

    public void setCourseEndTime(Date courseEndTime) {
        this.courseEndTime = (Date) courseEndTime.clone();
    }

}
