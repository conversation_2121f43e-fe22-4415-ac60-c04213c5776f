package com.microteam.base.common.pojo;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class UserInformationPojo implements java.io.Serializable {

    private String nickname;//昵称
    private String signature;//个性签名
    private String description;//个人说明
    private String sportsDescription;//个人运动经历描述
    private String emailAddress;//邮箱
    private Short sex;//性别 1:male,2:female
    private Short ages;//年龄
    private Long ageTime;//时间戳
    private Short height;//身高
    private Short weight;//体重
    private String school;  //学校格式: [{"province":"code","city":"code","county":"code","schoolType":"middleSchool","schoolName":"abc school"}];//小学，中学，大学，数组；
    private String homeLand; //家乡地址 // CITY {"province":"code","city":"code","county":"code"}
    private String curResidence; //用户所在城市； {"province":"code","city":"code","county":"code"}
    private String teamsType;
    private int bust;//胸围
    private int waist;//腰围
    private int hip;//臀围
    private int yearPro;//球龄
    private int location;//位置     0.中锋ST  1.守门员GK  2.左后卫LB   3.中后卫CB  4.右后卫RB  5.后腰CDM  6.中场CM  7.前腰CAM8.左前锋LF  9.右前锋RF

}
