package com.microteam.base.lesson.service;


import com.microteam.base.entity.lesson.TrainingTools;

import java.util.List;

public interface TrainingToolsDaoService {
    List<TrainingTools> findByIdList(List<Long> idList);

    //通过课程ID获取训练习工具
    List<TrainingTools> findAllTrainingTools();

    List<TrainingTools> findByCourwareId(Long courwareId);

    List<TrainingTools> findByLessonId(Long lessonId);

    TrainingTools findByName(String name);

    TrainingTools findById(Long id);
}
