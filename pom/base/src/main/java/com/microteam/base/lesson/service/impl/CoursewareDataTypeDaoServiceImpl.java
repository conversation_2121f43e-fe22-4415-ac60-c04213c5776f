package com.microteam.base.lesson.service.impl;

import com.microteam.base.entity.lesson.CoursewareDataType;
import com.microteam.base.lesson.dao.CoursewareDataTypeDao;
import com.microteam.base.lesson.service.CoursewareDataTypeDaoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class CoursewareDataTypeDaoServiceImpl implements CoursewareDataTypeDaoService {

    @Autowired
    private CoursewareDataTypeDao dao;

    @Override
    public List<CoursewareDataType> findByCoursewareId(Long coursewareId) {
        return dao.findByCoursewareId(coursewareId);
    }

    @Override
    public CoursewareDataType save(CoursewareDataType coursewareDataType) {
        return dao.save(coursewareDataType);
    }

}
