package com.microteam.base.entity.agency;


import com.microteam.base.common.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

@Entity
@Table(name = "football_agency_class_enrollment")
@Getter
@Setter
public class FootballAgencyClassEnrollment extends BaseEntity implements Serializable {

    @ManyToOne
    @JoinColumn(name = "agencyId", nullable = false)
    private FootballAgency footballAgency;
    @ManyToOne
    @JoinColumn(name = "courseId")
    private FootballAgencyClassCourse footballAgencyClassCourse;
    @ManyToOne
    @JoinColumn(name = "classId", nullable = false)
    private FootballAgencyClass footballAgencyClass;
    @Column(name = "trainContent", length = 65535)
    private String trainContent;
    @Column(name = "trainNumber")
    private Integer trainNumber;
    @Column(name = "trainTime", length = 200)
    private String trainTime;
    @Column(name = "trainFee", length = 200)
    private String trainFee;
    @Column(name = "countryCode", length = 15)
    private String countryCode;
    @Column(name = "provinceCode", length = 15)
    private String provinceCode;
    @Column(name = "cityCode", length = 15)
    private String cityCode;
    @Column(name = "countyCode", length = 15)
    private String countyCode;
    @Column(name = "trainLocation", length = 200)
    private String trainLocation;
    @Column(name = "studentAge", length = 100)
    private String studentAge;
    @Column(name = "phone", length = 100)
    private String phone;
    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "trainStartTime", length = 19)
    private Date trainStartTime;
    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "trainEndTime", length = 19)
    private Date trainEndTime;
    @Column(name = "website", length = 200)
    private String website;
    @Column(name = "demand", length = 65535)
    private String demand;
    @Column(name = "publishResouce")
    private Short publishResouce;

    public Date getTrainStartTime() {
        if (this.trainStartTime == null) {
            return null;
        }
        return (Date) this.trainStartTime.clone();
    }

    public void setTrainStartTime(Date trainStartTime) {
        this.trainStartTime = (Date) trainStartTime.clone();
    }


    public Date getTrainEndTime() {
        if (this.trainEndTime == null) {
            return null;
        }
        return (Date) this.trainEndTime.clone();
    }

    public void setTrainEndTime(Date trainEndTime) {
        this.trainEndTime = (Date) trainEndTime.clone();
    }

}
