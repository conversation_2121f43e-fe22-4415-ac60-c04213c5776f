package com.microteam.base.common.util.team;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONException;
import com.alibaba.fastjson.JSONObject;
import com.microteam.base.agency.service.AgencyTeamDaoService;
import com.microteam.base.common.constant.FootballContants;
import com.microteam.base.common.constant.TeamMemberRoleEnum;
import com.microteam.base.common.pojo.CalorieCurvePojo;
import com.microteam.base.common.pojo.CalorieCurveSumPojo;
import com.microteam.base.common.pojo.team.CarryCountPojo;
import com.microteam.base.common.pojo.team.FootballTeamTakeNodePojo;
import com.microteam.base.common.pojo.team.KickStatePojo;
import com.microteam.base.common.pojo.team.StepWidthPojo;
import com.microteam.base.common.util.common.TranslatorUtil;
import com.microteam.base.common.util.jdbc.JDBCUtil;
import com.microteam.base.common.util.share.ShareUtils;
import com.microteam.base.common.util.user.UserDataUtil;
import com.microteam.base.entity.match.FootballTeamMatch;
import com.microteam.base.entity.team.*;
import com.microteam.base.entity.user.*;
import com.microteam.base.team.service.*;
import com.microteam.base.user.service.UserHardwareDaoService;
import com.microteam.base.user.service.UserHardwareDataDaoService;
import com.microteam.base.user.service.UserHeadimgDaoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.beans.PropertyDescriptor;
import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.Timestamp;
import java.util.*;

@Component
public class FootballTeamUtils {

    @Autowired
    private FootballTeamGameStatisticsPlayerDaoService footballTeamGameStatisticsPlayerDaoService;
    @Autowired
    private FootballTeamGameEnrollDaoService teamGameEnrollDaoService;
    @Autowired
    private UserHardwareDaoService userHardwareDaoService;
    @Autowired
    private UserHardwareDataDaoService userHardwareDataDaoService;
    @Autowired
    FootballTeamGameTakeNodeDaoService takeNodeDaoService;
    @Autowired
    private FootballTeamGameEnrollDaoService footballTeamGameEnrollDaoService;
    //    @Autowired
//    private FootballTeamMemberCountDaoService footballTeamMemberCountDaoService;
    @Autowired
    private FootballTeamUserDaoService footballTeamUserDaoService;
    @Autowired
    private FootballTeamGameTakeNodeDaoService teamGameTakeNodeDaoService;
    @Autowired
    private UserHeadimgDaoService userHeadimgDaoService;
    @Autowired
    private PoloShirtDaoService poloShirtDaoService;
    @Autowired
    private ActionDaoService actionDaoService;
    @Autowired
    private FootballTeamGameOnceNodeDaoService footballTeamGameOnceNodeDaoService;
    @Autowired
    private FootballTeamDaoService footballTeamDaoService;
    @Autowired
    private FootballTeamHeadimgRandomDaoService footballTeamHeadimgRandomDaoService;
    @Autowired
    private FootballTeamJoinApplicantDaoService footballTeamJoinApplicantDaoService;
    @Autowired
    private FootballTeamInvitationDaoService footballTeamInvitationDaoService;
    @Autowired
    private AgencyTeamDaoService agencyTeamDaoService;

    public CalorieCurveSumPojo teamGameCalorieCurveData(Long gameId, Long teamId) throws JSONException {
        List<FootballTeamGameStatisticsPlayer> list = footballTeamGameStatisticsPlayerDaoService.findByGameIdAndTeamId(gameId, teamId);
        if (list == null) {
            list = new ArrayList<>();
        }
        Integer one = 0;
        Integer two = 0;
        Integer three = 0;
        Integer four = 0;
        Integer five = 0;
        Integer six = 0;
        Integer seven = 0;
        Integer size = list.size();
        for (FootballTeamGameStatisticsPlayer obj : list) {
            String calorieCurveData = obj.getCalorieCurveData();
            JSONObject jsonObject = JSON.parseObject(calorieCurveData);
            if (jsonObject.containsKey("one")) {
                one += (Integer) jsonObject.get("one");
            }
            if (jsonObject.containsKey("two")) {
                two += (Integer) jsonObject.get("two");
            }
            if (jsonObject.containsKey("three")) {
                three += (Integer) jsonObject.get("three");
            }
            if (jsonObject.containsKey("four")) {
                four += (Integer) jsonObject.get("four");
            }
            if (jsonObject.containsKey("five")) {
                five += (Integer) jsonObject.get("five");
            }
            if (jsonObject.containsKey("six")) {
                six += (Integer) jsonObject.get("six");
            }
            if (jsonObject.containsKey("seven")) {
                seven += (Integer) jsonObject.get("seven");
            }
        }
        CalorieCurveSumPojo calorieCurveSumPojo = new CalorieCurveSumPojo();
        CalorieCurvePojo calorieCurvePojo = new CalorieCurvePojo();
        if (size == 0) {
            calorieCurvePojo.setOne(0);
            calorieCurvePojo.setTwo(0);
            calorieCurvePojo.setThree(0);
            calorieCurvePojo.setFour(0);
            calorieCurvePojo.setFive(0);
            calorieCurvePojo.setSix(0);
            calorieCurvePojo.setSeven(0);
            calorieCurveSumPojo.setCalorieCurvePojo(calorieCurvePojo);
            calorieCurveSumPojo.setSum(0);
            return calorieCurveSumPojo;
        }
        calorieCurvePojo.setOne(one / size);
        two += one;
        calorieCurvePojo.setTwo(two / size);
        three += two;
        calorieCurvePojo.setThree(three / size);
        four += three;
        calorieCurvePojo.setFour(four / size);
        five += four;
        calorieCurvePojo.setFive(five / size);
        six += five;
        calorieCurvePojo.setSix(six / size);
        seven += six;
        calorieCurvePojo.setSeven(seven / size);
        calorieCurveSumPojo.setCalorieCurvePojo(calorieCurvePojo);
        Integer sum = seven / size;
        calorieCurveSumPojo.setSum(sum);
        return calorieCurveSumPojo;
    }

    public CalorieCurveSumPojo teamHistoryCalorieCurveData(Long teamId) throws JSONException {
        List<FootballTeamGameStatisticsPlayer> list = footballTeamGameStatisticsPlayerDaoService.findByTeamId(teamId);
        if (list == null) {
            list = new ArrayList<>();
        }
        Integer one = 0;
        Integer two = 0;
        Integer three = 0;
        Integer four = 0;
        Integer five = 0;
        Integer six = 0;
        Integer seven = 0;
        Integer size = list.size();
        for (FootballTeamGameStatisticsPlayer obj : list) {
            String calorieCurveData = obj.getCalorieCurveData();
            JSONObject jsonObject = JSON.parseObject(calorieCurveData);
            if (jsonObject.containsKey("one")) {
                one += (Integer) jsonObject.get("one");
            }
            if (jsonObject.containsKey("two")) {
                two += (Integer) jsonObject.get("two");
            }
            if (jsonObject.containsKey("three")) {
                three += (Integer) jsonObject.get("three");
            }
            if (jsonObject.containsKey("four")) {
                four += (Integer) jsonObject.get("four");
            }
            if (jsonObject.containsKey("five")) {
                five += (Integer) jsonObject.get("five");
            }
            if (jsonObject.containsKey("six")) {
                six += (Integer) jsonObject.get("six");
            }
            if (jsonObject.containsKey("seven")) {
                seven += (Integer) jsonObject.get("seven");
            }
        }
        CalorieCurveSumPojo calorieCurveSumPojo = new CalorieCurveSumPojo();
        CalorieCurvePojo calorieCurvePojo = new CalorieCurvePojo();
        if (size == 0) {
            calorieCurvePojo.setOne(0);
            calorieCurvePojo.setTwo(0);
            calorieCurvePojo.setThree(0);
            calorieCurvePojo.setFour(0);
            calorieCurvePojo.setFive(0);
            calorieCurvePojo.setSix(0);
            calorieCurvePojo.setSeven(0);
            calorieCurveSumPojo.setCalorieCurvePojo(calorieCurvePojo);
            calorieCurveSumPojo.setSum(0);
            return calorieCurveSumPojo;
        }
        calorieCurvePojo.setOne(one / size);
        two += one;
        calorieCurvePojo.setTwo(two / size);
        three += two;
        calorieCurvePojo.setThree(three / size);
        four += three;
        calorieCurvePojo.setFour(four / size);
        five += four;
        calorieCurvePojo.setFive(five / size);
        six += five;
        calorieCurvePojo.setSix(six / size);
        seven += six;
        calorieCurvePojo.setSeven(seven / size);
        calorieCurveSumPojo.setCalorieCurvePojo(calorieCurvePojo);
        Integer sum = seven / size;
        calorieCurveSumPojo.setSum(sum);
        return calorieCurveSumPojo;
    }

    public static List<Integer> sortArray(long leftFootStartTime,
                                          long rightFootStartTime, List<Integer> leftFootData,
                                          List<Integer> rightFootData) {
        List<Integer> resultArray = new ArrayList<Integer>();
        if (leftFootStartTime != 0 && rightFootStartTime != 0) {
            if (leftFootStartTime > rightFootStartTime) {
                // 左脚比右脚后绑定的硬件
                int intervalTime = (int) ((leftFootStartTime - rightFootStartTime) / 1000);
                if (leftFootData != null && leftFootData.size() > 0 && rightFootData != null && rightFootData.size() > 0) {
                    // 左右脚都有数据
                    resultArray.addAll(leftFootData);
                    for (Integer rightFootDatum : rightFootData) {
                        resultArray.add(rightFootDatum + intervalTime);
                    }
                    // 排序
                    subQuickSort(resultArray, 0, resultArray.size() - 1);
                } else {
                    if (leftFootData == null || leftFootData.size() < 1) {
                        resultArray.addAll(rightFootData);
                        // 排序
                        subQuickSort(resultArray, 0, resultArray.size() - 1);
                    } else if (rightFootData == null || rightFootData.size() < 1) {
                        resultArray.addAll(leftFootData);
                        // 排序
                        subQuickSort(resultArray, 0, resultArray.size() - 1);
                    }// else if
                }// else
            } else {
                // 右脚比左脚后绑定的硬件leftFootStartTime
                int intervalTime = (int) ((rightFootStartTime - leftFootStartTime) / 1000);
                if (leftFootData != null && leftFootData.size() > 0
                        && rightFootData != null && rightFootData.size() > 0) {
                    // 左右脚都有数据
                    for (Integer leftFootDatum : leftFootData) {
                        resultArray.add(leftFootDatum + intervalTime);
                    }
                    resultArray.addAll(rightFootData);
                    // 排序
                    subQuickSort(resultArray, 0, resultArray.size() - 1);
                } else {
                    if (leftFootData == null || leftFootData.size() < 1) {
                        resultArray.addAll(rightFootData);
                        // 排序
                        subQuickSort(resultArray, 0, resultArray.size() - 1);
                    } else if (rightFootData == null || rightFootData.size() < 1) {
                        resultArray.addAll(leftFootData);
                        // 排序
                        subQuickSort(resultArray, 0, resultArray.size() - 1);
                    }// else if
                }// else
            }// else
        } else {
            if (leftFootData != null && leftFootData.size() > 0) {
                resultArray.addAll(leftFootData);
                // 排序
                subQuickSort(resultArray, 0, resultArray.size() - 1);
            } else if (rightFootData != null && rightFootData.size() > 0) {
                resultArray.addAll(rightFootData);
                // 排序
                subQuickSort(resultArray, 0, resultArray.size() - 1);
            }
        }// else
        return resultArray;
    }

    public static void subQuickSort(List<Integer> array, int start, int end) {
        if (array == null || (end - start + 1) < 2) {
            return;
        }

        int part = partition(array, start, end);

        if (part == start) {
            subQuickSort(array, part + 1, end);
        } else if (part == end) {
            subQuickSort(array, start, part - 1);
        } else {
            subQuickSort(array, start, part - 1);
            subQuickSort(array, part + 1, end);
        }
    }

    public static int partition(List<Integer> array, int start, int end) {
        int value = array.get(end);// 从最后开始
        int index = start - 1;
        for (int i = start; i < end; i++) {
            if (array.get(i) < value) {
                index++;
                if (index != i) {
                    /*
                     * 换位排序
                     */
                    exchangeElements(array, index, i);
                }
            }
        }

        if ((index + 1) != end) {
            exchangeElements(array, index + 1, end);
        }

        return index + 1;
    }

    public static void exchangeElements(List<Integer> array, int index1,
                                        int index2) {
        int temp = array.get(index1);
        array.set(index1, array.get(index2));
        array.set(index2, temp);
    }

    // 计算带球次数
    public static int calculateCarryCount(List<Integer> array, int passInterval) {
        int count = 0;
        if (array != null && array.size() > 0) {
            for (int i = 0; i < array.size() - 1; i++) {
                // 计算带球次数
                if ((array.get(i + 1) - array.get(i)) <= passInterval) {
                    if (i == 0) {
                        count++;
                    } else {
                        if ((array.get(i) - array.get(i - 1)) > passInterval) {
                            count++;
                        }// if
                    }// else

                }// if
            }// for
        }// if
        return count;
    }

    //	//统计带球时间
//	public static int calculateCarryTime(List<Integer> array, int passInterval) {
//		int carryTime = 0;
//		if (array != null && array.size() > 0) {
//			for (int i = 0; i < array.size() - 1; i++) {
//				// 计算带球次数
//				if ((array.get(i + 1) - array.get(i)) <= passInterval) {
//					if (i == 0) {
//						carryTime+=array.get(i);
//					} else {
//						if ((array.get(i) - array.get(i - 1)) > passInterval) {
//							carryTime+=array.get(i) - array.get(i - 1);
//						}// if
//					}// else
//
//				}// if
//			}// for
//		}// if
//		return carryTime;
//	}
    //统计带球时间
    public static int calculateCarryTime(List<Integer> array, int passInterval) {
        int carryTime = 0;
        if (array != null && array.size() > 0) {
            for (int i = 0; i < array.size() - 1; i++) {
                // 计算带球次数
                if ((array.get(i + 1) - array.get(i)) <= passInterval) {

                    carryTime += array.get(i + 1) - array.get(i);


                }// if
            }// for
        }// if
        return carryTime;
    }

    // 计算带球距离
    public static int calculateCarryDistance(List<Integer> array, int passInterval, JSONArray highSpeedMoveDataArray, JSONArray midSpeedMoveDataArray, JSONArray lowSpeedMoveDataArray, JSONArray normalSpeedMoveDataArray, long kickballTime, double stepWidth) throws JSONException {
        int moveDistance = 0;
        if (array != null && array.size() > 0) {
            for (int i = 0; i < array.size() - 1; i++) {
                // 计算带球距离
                if ((array.get(i + 1) - array.get(i)) <= passInterval) {
                    if (i == 0) {
                        if (highSpeedMoveDataArray != null && highSpeedMoveDataArray.size() > 0) {
                            for (int k = 0; k < highSpeedMoveDataArray.size(); k++) {
                                JSONObject tempObject = highSpeedMoveDataArray.getJSONObject(k);
                                long startTime = tempObject.getLong("startTime");
                                long endTime = tempObject.getLong("endTime");
                                int stepCount = tempObject.getInteger("stepCount");
                                int intervalTime = tempObject.getInteger("intervalTime") / 1000;
                                if (startTime <= (kickballTime + array.get(i) * 1000) &&
                                        (kickballTime + array.get(i) * 1000) <= endTime) {
                                    //运动距离（米）=步数*步长
                                    //步长（米）=均速（米/分钟）/步频 2、步频=步数/时间（分钟）
                                    //高速跑默认8m/s
                                        /*int aveSpeend=8*60;//均速（米/分钟）
                                        int moveFrequence=((int)Math.ceil((float)stepCount/intervalTime))*60;
										if(moveFrequence==0){
											moveDistance+=0;
										}else{
											moveDistance+=aveSpeend/moveFrequence*stepCount;
										}*/

                                    moveDistance = (int) Math.ceil((float) stepCount * stepWidth * 1.3);


                                }
                            }
                        }//highSpeedMoveDataArray
                        if (midSpeedMoveDataArray != null && midSpeedMoveDataArray.size() > 0) {
                            for (int k = 0; k < midSpeedMoveDataArray.size(); k++) {
                                JSONObject tempObject = midSpeedMoveDataArray.getJSONObject(k);
                                long startTime = tempObject.getLong("startTime");
                                long endTime = tempObject.getLong("endTime");
                                int stepCount = tempObject.getInteger("stepCount");
                                int intervalTime = tempObject.getInteger("intervalTime") / 1000;
                                if (startTime <= (kickballTime + array.get(i) * 1000) &&
                                        (kickballTime + array.get(i) * 1000) <= endTime) {
                                    //运动距离（米）=步数*步长
                                    //步长（米）=均速（米/分钟）/步频 2、步频=步数/时间（分钟）
                                    //中速跑默认5m/s
                                        /*int aveSpeend=5*60;//均速（米/分钟）
                                        int moveFrequence=((int)Math.ceil((float)stepCount/intervalTime))*60;
										if(moveFrequence==0){
											moveDistance+=0;
										}else{
											moveDistance+=aveSpeend/moveFrequence*stepCount;
										}*/

                                    moveDistance = (int) Math.ceil((float) stepCount * stepWidth * 1.2);

                                }
                            }
                        }//midSpeedMoveDataArray
                        if (lowSpeedMoveDataArray != null && lowSpeedMoveDataArray.size() > 0) {
                            for (int k = 0; k < lowSpeedMoveDataArray.size(); k++) {
                                JSONObject tempObject = lowSpeedMoveDataArray.getJSONObject(k);
                                long startTime = tempObject.getLong("startTime");
                                long endTime = tempObject.getLong("endTime");
                                int stepCount = tempObject.getInteger("stepCount");
                                int intervalTime = tempObject.getInteger("intervalTime") / 1000;
                                if (startTime <= (kickballTime + array.get(i) * 1000) &&
                                        (kickballTime + array.get(i) * 1000) <= endTime) {
                                    //运动距离（米）=步数*步长
                                    //步长（米）=均速（米/分钟）/步频 2、步频=步数/时间（分钟）
                                    //低速跑默认3m/s
                                        /*int aveSpeend=3*60;//均速（米/分钟）
										int moveFrequence=((int)Math.ceil((float)stepCount/intervalTime))*60;
										if(moveFrequence==0){
											moveDistance+=0;
										}else{
											moveDistance+=aveSpeend/moveFrequence*stepCount;
										}*/
                                    moveDistance = (int) Math.ceil((float) stepCount * stepWidth * 1.1);

                                }
                            }
                        }//lowSpeedMoveDataArray
                        if (normalSpeedMoveDataArray != null && normalSpeedMoveDataArray.size() > 0) {
                            for (int k = 0; k < normalSpeedMoveDataArray.size(); k++) {
                                JSONObject tempObject = normalSpeedMoveDataArray.getJSONObject(k);
                                long startTime = tempObject.getLong("startTime");
                                long endTime = tempObject.getLong("endTime");
                                int stepCount = tempObject.getInteger("stepCount");
                                int intervalTime = tempObject.getInteger("intervalTime") / 1000;
                                if (startTime <= (kickballTime + array.get(i) * 1000) &&
                                        (kickballTime + array.get(i) * 1000) <= endTime) {

                                    //运动距离（米）=步数*步长
                                    //步长（米）=均速（米/分钟）/步频 2、步频=步数/时间（分钟）
                                    //走路默认1m/s
										/*int aveSpeend=1*60;//均速（米/分钟）
										int moveFrequence=((int)Math.ceil((float)stepCount/intervalTime))*60;
										if(moveFrequence==0){
											moveDistance+=0;
										}else{
											moveDistance+=aveSpeend/moveFrequence*stepCount;
										}*/
                                    moveDistance = (int) Math.ceil((float) stepCount * stepWidth * 1);

                                }
                            }
                        }//normalSpeedMoveDataArray
                    } else {
                        if ((array.get(i) - array.get(i - 1)) > passInterval) {
                            if (highSpeedMoveDataArray != null && highSpeedMoveDataArray.size() > 0) {
                                for (int k = 0; k < highSpeedMoveDataArray.size(); k++) {
                                    JSONObject tempObject = highSpeedMoveDataArray.getJSONObject(k);
                                    long startTime = tempObject.getLong("startTime");
                                    long endTime = tempObject.getLong("endTime");
                                    int stepCount = tempObject.getInteger("stepCount");
                                    int intervalTime = tempObject.getInteger("intervalTime") / 1000;
                                    if (startTime <= (kickballTime + array.get(i) * 1000) &&
                                            (kickballTime + array.get(i) * 1000) <= endTime) {
                                        //运动距离（米）=步数*步长
                                        //步长（米）=均速（米/分钟）/步频 2、步频=步数/时间（分钟）
                                        //高速跑默认8m/s
											/*int aveSpeend=8*60;//均速（米/分钟）
											int moveFrequence=((int)Math.ceil((float)stepCount/intervalTime))*60;
											if(moveFrequence==0){
												moveDistance+=0;
											}else{
												moveDistance+=aveSpeend/moveFrequence*stepCount;
											}*/
                                        moveDistance = (int) Math.ceil((float) stepCount * stepWidth * 1.3);

                                    }
                                }
                            }//highSpeedMoveDataArray
                            if (midSpeedMoveDataArray != null && midSpeedMoveDataArray.size() > 0) {
                                for (int k = 0; k < midSpeedMoveDataArray.size(); k++) {
                                    JSONObject tempObject = midSpeedMoveDataArray.getJSONObject(k);
                                    long startTime = tempObject.getLong("startTime");
                                    long endTime = tempObject.getLong("endTime");
                                    int stepCount = tempObject.getInteger("stepCount");
                                    int intervalTime = tempObject.getInteger("intervalTime") / 1000;
                                    if (startTime <= (kickballTime + array.get(i) * 1000) &&
                                            (kickballTime + array.get(i) * 1000) <= endTime) {
                                        //运动距离（米）=步数*步长
                                        //步长（米）=均速（米/分钟）/步频 2、步频=步数/时间（分钟）
                                        //中速跑默认5m/s
											/*int aveSpeend=5*60;//均速（米/分钟）
											int moveFrequence=((int)Math.ceil((float)stepCount/intervalTime))*60;
											if(moveFrequence==0){
												moveDistance+=0;
											}else{
												moveDistance+=aveSpeend/moveFrequence*stepCount;
											}*/
                                        moveDistance = (int) Math.ceil((float) stepCount * stepWidth * 1.2);

                                    }
                                }
                            }//midSpeedMoveDataArray
                            if (lowSpeedMoveDataArray != null && lowSpeedMoveDataArray.size() > 0) {
                                for (int k = 0; k < lowSpeedMoveDataArray.size(); k++) {
                                    JSONObject tempObject = lowSpeedMoveDataArray.getJSONObject(k);
                                    long startTime = tempObject.getLong("startTime");
                                    long endTime = tempObject.getLong("endTime");
                                    int stepCount = tempObject.getInteger("stepCount");
                                    int intervalTime = tempObject.getInteger("intervalTime") / 1000;
                                    if (startTime <= (kickballTime + array.get(i) * 1000) &&
                                            (kickballTime + array.get(i) * 1000) <= endTime) {
                                        //运动距离（米）=步数*步长
                                        //步长（米）=均速（米/分钟）/步频 2、步频=步数/时间（分钟）
                                        //低速跑默认3m/s
											/*int aveSpeend=3*60;//均速（米/分钟）
											int moveFrequence=((int)Math.ceil((float)stepCount/intervalTime))*60;
											if(moveFrequence==0){
												moveDistance+=0;
											}else{
												moveDistance+=aveSpeend/moveFrequence*stepCount;
											}*/
                                        moveDistance = (int) Math.ceil((float) stepCount * stepWidth * 1.1);

                                    }
                                }
                            }//lowSpeedMoveDataArray
                            if (normalSpeedMoveDataArray != null && normalSpeedMoveDataArray.size() > 0) {
                                for (int k = 0; k < normalSpeedMoveDataArray.size(); k++) {
                                    JSONObject tempObject = normalSpeedMoveDataArray.getJSONObject(k);
                                    long startTime = tempObject.getLong("startTime");
                                    long endTime = tempObject.getLong("endTime");
                                    int stepCount = tempObject.getInteger("stepCount");
                                    int intervalTime = tempObject.getInteger("intervalTime") / 1000;
                                    if (startTime <= (kickballTime + array.get(i) * 1000) &&
                                            (kickballTime + array.get(i) * 1000) <= endTime) {
                                        //运动距离（米）=步数*步长
                                        //步长（米）=均速（米/分钟）/步频 2、步频=步数/时间（分钟）
                                        //走路默认1m/s
											/*int aveSpeend=1*60;//均速（米/分钟）
											int moveFrequence=((int)Math.ceil((float)stepCount/intervalTime))*60;
											if(moveFrequence==0){
												moveDistance+=0;
											}else{
												moveDistance+=aveSpeend/moveFrequence*stepCount;
											}*/
                                        moveDistance = (int) Math.ceil((float) stepCount * stepWidth * 1);
                                    }
                                }
                            }//normalSpeedMoveDataArray
                        }// if
                    }// else

                }// if
            }// for
        }// if
        return moveDistance;
    }


    // 计算带球距离
    public static int calculateCarryDistance(List<Integer> array, int passInterval, JSONArray highSpeedMoveDataArray, JSONArray midSpeedMoveDataArray, JSONArray lowSpeedMoveDataArray, JSONArray normalSpeedMoveDataArray, long kickballTime, int highSpeedMoveCount, int midSpeedMoveCount, int lowSpeedMoveCount, int normalSpeedMoveCount, double stepWidth) throws JSONException {
        int moveDistance = 0;
        if (array != null && array.size() > 0) {
            for (int i = 0; i < array.size() - 1; i++) {
                // 计算带球距离
                if ((array.get(i + 1) - array.get(i)) <= passInterval) {
                    if (i == 0) {
                        if (highSpeedMoveDataArray != null && highSpeedMoveDataArray.size() > 0) {
                            for (int k = 0; k < highSpeedMoveDataArray.size(); k++) {
                                JSONObject tempObject = highSpeedMoveDataArray.getJSONObject(k);
                                long startTime = tempObject.getLong("startTime");
                                long endTime = tempObject.getLong("endTime");
                                int stepCount = tempObject.getInteger("stepCount");
                                int intervalTime = tempObject.getInteger("intervalTime") / 1000;
                                if (startTime <= (kickballTime + array.get(i) * 1000) &&
                                        (kickballTime + array.get(i) * 1000) <= endTime) {
                                    //运动距离（米）=步数*步长
                                    //步长（米）=均速（米/分钟）/步频 2、步频=步数/时间（分钟）
                                    //高速跑默认8m/s
										/*int aveSpeend=8*60;//均速（米/分钟）
										int moveFrequence=((int)Math.ceil((float)stepCount/intervalTime))*60;
										if(moveFrequence==0){
											moveDistance+=0;
										}else{
											moveDistance+=aveSpeend/moveFrequence*stepCount;
										}*/
                                    double avgstepCount = (double) stepCount / (double) intervalTime;
                                    moveDistance = (int) Math.ceil((float) (endTime - (kickballTime + array.get(i) * 1000)) / 1000 * avgstepCount * stepWidth * 1.3);
                                    //	    moveDistance =(int)Math.ceil( (float)highSpeedMoveCount * stepWidth * 1.3);


                                }
                            }
                        }//highSpeedMoveDataArray
                        if (midSpeedMoveDataArray != null && midSpeedMoveDataArray.size() > 0) {
                            for (int k = 0; k < midSpeedMoveDataArray.size(); k++) {
                                JSONObject tempObject = midSpeedMoveDataArray.getJSONObject(k);
                                long startTime = tempObject.getLong("startTime");
                                long endTime = tempObject.getLong("endTime");
                                int stepCount = tempObject.getInteger("stepCount");
                                int intervalTime = tempObject.getInteger("intervalTime") / 1000;
                                if (startTime <= (kickballTime + array.get(i) * 1000) &&
                                        (kickballTime + array.get(i) * 1000) <= endTime) {
                                    //运动距离（米）=步数*步长
                                    //步长（米）=均速（米/分钟）/步频 2、步频=步数/时间（分钟）
                                    //中速跑默认5m/s
										/*int aveSpeend=5*60;//均速（米/分钟）
										int moveFrequence=((int)Math.ceil((float)stepCount/intervalTime))*60;
										if(moveFrequence==0){
											moveDistance+=0;
										}else{
											moveDistance+=aveSpeend/moveFrequence*stepCount;
										}*/
                                    //   moveDistance = (int)Math.ceil( (float)midSpeedMoveCount * stepWidth * 1.2);
                                    double avgstepCount = (double) stepCount / (double) intervalTime;
                                    moveDistance = (int) Math.ceil((float) (endTime - (kickballTime + array.get(i) * 1000)) / 1000 * avgstepCount * stepWidth * 1.3);
                                }
                            }
                        }//midSpeedMoveDataArray
                        if (lowSpeedMoveDataArray != null && lowSpeedMoveDataArray.size() > 0) {
                            for (int k = 0; k < lowSpeedMoveDataArray.size(); k++) {
                                JSONObject tempObject = lowSpeedMoveDataArray.getJSONObject(k);
                                long startTime = tempObject.getLong("startTime");
                                long endTime = tempObject.getLong("endTime");
                                int stepCount = tempObject.getInteger("stepCount");
                                int intervalTime = tempObject.getInteger("intervalTime") / 1000;
                                if (startTime <= (kickballTime + array.get(i) * 1000) &&
                                        (kickballTime + array.get(i) * 1000) <= endTime) {
                                    //运动距离（米）=步数*步长
                                    //步长（米）=均速（米/分钟）/步频 2、步频=步数/时间（分钟）
                                    //低速跑默认3m/s
										/*int aveSpeend=3*60;//均速（米/分钟）
										int moveFrequence=((int)Math.ceil((float)stepCount/intervalTime))*60;
										if(moveFrequence==0){
											moveDistance+=0;
										}else{
											moveDistance+=aveSpeend/moveFrequence*stepCount;
										}*/
                                    moveDistance = (int) Math.ceil((float) lowSpeedMoveCount * stepWidth * 1.1);

                                }
                            }
                        }//lowSpeedMoveDataArray
                        if (normalSpeedMoveDataArray != null && normalSpeedMoveDataArray.size() > 0) {
                            for (int k = 0; k < normalSpeedMoveDataArray.size(); k++) {
                                JSONObject tempObject = normalSpeedMoveDataArray.getJSONObject(k);
                                long startTime = tempObject.getLong("startTime");
                                long endTime = tempObject.getLong("endTime");
                                int stepCount = tempObject.getInteger("stepCount");
                                int intervalTime = tempObject.getInteger("intervalTime") / 1000;
                                if (startTime <= (kickballTime + array.get(i) * 1000) &&
                                        (kickballTime + array.get(i) * 1000) <= endTime) {


                                    //运动距离（米）=步数*步长
                                    //步长（米）=均速（米/分钟）/步频 2、步频=步数/时间（分钟）
                                    //走路默认1m/s
										/*int aveSpeend=1*60;//均速（米/分钟）
										int moveFrequence=((int)Math.ceil((float)stepCount/intervalTime))*60;
										if(moveFrequence==0){
											moveDistance+=0;
										}else{
											moveDistance+=aveSpeend/moveFrequence*stepCount;
										}*/
                                    moveDistance = (int) Math.ceil((float) normalSpeedMoveCount * stepWidth * 1);

                                }
                            }
                        }//normalSpeedMoveDataArray
                    } else {
                        if (highSpeedMoveDataArray != null && highSpeedMoveDataArray.size() > 0) {
                            for (int k = 0; k < highSpeedMoveDataArray.size(); k++) {
                                JSONObject tempObject = highSpeedMoveDataArray.getJSONObject(k);
                                long startTime = tempObject.getLong("startTime");
                                long endTime = tempObject.getLong("endTime");
                                int stepCount = tempObject.getInteger("stepCount");
                                int intervalTime = tempObject.getInteger("intervalTime") / 1000;
                                if (startTime <= (kickballTime + array.get(i) * 1000) &&
                                        (kickballTime + array.get(i) * 1000) <= endTime) {
                                    //运动距离（米）=步数*步长
                                    //步长（米）=均速（米/分钟）/步频 2、步频=步数/时间（分钟）
                                    //高速跑默认8m/s
											/*int aveSpeend=8*60;//均速（米/分钟）
											int moveFrequence=((int)Math.ceil((float)stepCount/intervalTime))*60;
											if(moveFrequence==0){
												moveDistance+=0;
											}else{
												moveDistance+=aveSpeend/moveFrequence*stepCount;
											}*/
                                    moveDistance = (int) Math.ceil((float) highSpeedMoveCount * stepWidth * 1.3);

                                }
                            }
                        }//highSpeedMoveDataArray
                        if (midSpeedMoveDataArray != null && midSpeedMoveDataArray.size() > 0) {
                            for (int k = 0; k < midSpeedMoveDataArray.size(); k++) {
                                JSONObject tempObject = midSpeedMoveDataArray.getJSONObject(k);
                                long startTime = tempObject.getLong("startTime");
                                long endTime = tempObject.getLong("endTime");
                                int stepCount = tempObject.getInteger("stepCount");
                                int intervalTime = tempObject.getInteger("intervalTime") / 1000;
                                if (startTime <= (kickballTime + array.get(i) * 1000) &&
                                        (kickballTime + array.get(i) * 1000) <= endTime) {
                                    //运动距离（米）=步数*步长
                                    //步长（米）=均速（米/分钟）/步频 2、步频=步数/时间（分钟）
                                    //中速跑默认5m/s
											/*int aveSpeend=5*60;//均速（米/分钟）
											int moveFrequence=((int)Math.ceil((float)stepCount/intervalTime))*60;
											if(moveFrequence==0){
												moveDistance+=0;
											}else{
												moveDistance+=aveSpeend/moveFrequence*stepCount;
											}*/
                                    moveDistance = (int) Math.ceil((float) midSpeedMoveCount * stepWidth * 1.2);

                                }
                            }
                        }//midSpeedMoveDataArray
                        if (lowSpeedMoveDataArray != null && lowSpeedMoveDataArray.size() > 0) {
                            for (int k = 0; k < lowSpeedMoveDataArray.size(); k++) {
                                JSONObject tempObject = lowSpeedMoveDataArray.getJSONObject(k);
                                long startTime = tempObject.getLong("startTime");
                                long endTime = tempObject.getLong("endTime");
                                int stepCount = tempObject.getInteger("stepCount");
                                int intervalTime = tempObject.getInteger("intervalTime") / 1000;
                                if (startTime <= (kickballTime + array.get(i) * 1000) &&
                                        (kickballTime + array.get(i) * 1000) <= endTime) {
                                    //运动距离（米）=步数*步长
                                    //步长（米）=均速（米/分钟）/步频 2、步频=步数/时间（分钟）
                                    //低速跑默认3m/s
											/*int aveSpeend=3*60;//均速（米/分钟）
											int moveFrequence=((int)Math.ceil((float)stepCount/intervalTime))*60;
											if(moveFrequence==0){
												moveDistance+=0;
											}else{
												moveDistance+=aveSpeend/moveFrequence*stepCount;
											}*/
                                    moveDistance = (int) Math.ceil((float) lowSpeedMoveCount * stepWidth * 1.1);

                                }
                            }
                        }//lowSpeedMoveDataArray
                        if (normalSpeedMoveDataArray != null && normalSpeedMoveDataArray.size() > 0) {
                            for (int k = 0; k < normalSpeedMoveDataArray.size(); k++) {
                                JSONObject tempObject = normalSpeedMoveDataArray.getJSONObject(k);
                                long startTime = tempObject.getLong("startTime");
                                long endTime = tempObject.getLong("endTime");
                                int stepCount = tempObject.getInteger("stepCount");
                                int intervalTime = tempObject.getInteger("intervalTime") / 1000;
                                if (startTime <= (kickballTime + array.get(i) * 1000) &&
                                        (kickballTime + array.get(i) * 1000) <= endTime) {
                                    //运动距离（米）=步数*步长
                                    //步长（米）=均速（米/分钟）/步频 2、步频=步数/时间（分钟）
                                    //走路默认1m/s
											/*int aveSpeend=1*60;//均速（米/分钟）
											int moveFrequence=((int)Math.ceil((float)stepCount/intervalTime))*60;
											if(moveFrequence==0){
												moveDistance+=0;
											}else{
												moveDistance+=aveSpeend/moveFrequence*stepCount;
											}*/
                                    moveDistance = (int) Math.ceil((float) normalSpeedMoveCount * stepWidth * 1);
                                }
                            }
                        }//normalSpeedMoveDataArray
                    }// else

                }// if
            }// for
        }// if
        return moveDistance;
    }


    // 一脚传球次数
    public static int calculateOneFootPassCount(List<Integer> array,
                                                int passInterval) {
        int count = 0;
        if (array != null && array.size() > 0) {
            for (int i = 0; i < array.size() - 1; i++) {
                if (i == 0) {
                    if ((array.get(i + 1) - array.get(i)) > passInterval) {
                        count++;
                    }
                } else {
                    if ((array.get(i + 1) - array.get(i)) > passInterval) {
                        if ((array.get(i) - array.get(i - 1)) > passInterval) {
                            count++;
                        }
                    }// if
                }

            }// for
        }// if
        return count;
    }

    // 计算传球次数
    public static int calculatePassCount(List<Integer> array, int passInterval) {
        int count = 0;
        if (array != null && array.size() > 0) {
            for (int i = 0; i < array.size() - 1; i++) {
                if ((array.get(i + 1) - array.get(i)) > passInterval) {
                    count++;
                }// if

            }// for
        }// if
        return count;
    }


    public static JSONObject buildCalorieCurveData(JSONArray array) throws JSONException {
        // 将排过续的第一个时间作为曲线的开始时间
        long curveStartTime = array.getJSONObject(0).getLong("startTime");
        JSONObject resultObject = new JSONObject();
        for (int i = 0; i < array.size(); i++) {
            JSONObject tempObject = array.getJSONObject(i);
            // 根据结束时间来判断
            long endTime = tempObject.getLong("endTime");
            long moveCalarie = tempObject.getLong("moveCalarie");
            // 取最早的时间作为起始时间，默认比赛为2个半小时=9000000毫秒=150m，10分钟=600000ms
            if (endTime <= (curveStartTime + 900000)) {
                if (resultObject.containsKey("one")) {
                    // 继续叠加
                    resultObject.put("one", resultObject.getLong("one") + moveCalarie);
                } else {
                    // 没有就创建
                    resultObject.put("one", moveCalarie);
                }
                // 10分钟
            } else if (endTime <= (curveStartTime + 900000 * 2)) {
                // 20分钟
                if (resultObject.containsKey("two")) {
                    // 继续叠加
                    resultObject.put("two", resultObject.getLong("two") + moveCalarie);
                } else {
                    // 没有就创建
                    resultObject.put("two", moveCalarie);
                }
            } else if (endTime <= (curveStartTime + 900000 * 3)) {
                // 30分钟
                if (resultObject.containsKey("three")) {
                    // 继续叠加
                    resultObject.put("three", resultObject.getLong("three") + moveCalarie);
                } else {
                    // 没有就创建
                    resultObject.put("three", moveCalarie);
                }
            } else if (endTime <= (curveStartTime + 900000 * 4)) {
                // 40分钟
                if (resultObject.containsKey("four")) {
                    // 继续叠加
                    resultObject.put("four", resultObject.getLong("four") + moveCalarie);
                } else {
                    // 没有就创建
                    resultObject.put("four", moveCalarie);
                }
            } else if (endTime <= (curveStartTime + 900000 * 5)) {
                // 50分钟
                if (resultObject.containsKey("five")) {
                    // 继续叠加
                    resultObject.put("five", resultObject.getLong("five") + moveCalarie);
                } else {
                    // 没有就创建
                    resultObject.put("five", moveCalarie);
                }
            } else if (endTime <= (curveStartTime + 900000 * 6)) {
                // 60分钟
                if (resultObject.containsKey("six")) {
                    // 继续叠加
                    resultObject.put("six", resultObject.getLong("six") + moveCalarie);
                } else {
                    // 没有就创建
                    resultObject.put("six", moveCalarie);
                }
            } else if (endTime <= (curveStartTime + 900000 * 7)) {
                // 70分钟
                if (resultObject.containsKey("seven")) {
                    // 继续叠加
                    resultObject.put("seven", resultObject.getLong("seven") + moveCalarie);
                } else {
                    // 没有就创建
                    resultObject.put("seven", moveCalarie);
                }
            }
        }
        return resultObject;
    }


    //球队成员硬件数据排序
    public static JSONArray sortTeamDataArray(List<UserHardwareData> userHardwareDataList) {
        //找出最早时间最为基准时间
        JSONArray sortedArray = new JSONArray();
        for (int i = 0; i < userHardwareDataList.size(); i++) {
            for (int j = i + 1; j < userHardwareDataList.size(); j++) {
                UserHardwareData userHardwareDataI = userHardwareDataList.get(i);
                long intervalTimeI = userHardwareDataI.getKickBallStartTime().getTime();
                UserHardwareData userHardwareDataJ = userHardwareDataList.get(j);
                long intervalTimeJ = userHardwareDataJ.getKickBallStartTime().getTime();
                if (intervalTimeI > intervalTimeJ) {
                    userHardwareDataList.set(i, userHardwareDataJ);
                    userHardwareDataList.set(j, userHardwareDataI);
                }
            }
        }//for
        long standardTime = userHardwareDataList.get(0).getKickBallStartTime().getTime();
        //封装球队数据未排序的数组
        for (UserHardwareData userHardwareData : userHardwareDataList) {
            User user = userHardwareData.getUser();
            long kickBallTime = userHardwareData.getKickBallStartTime().getTime();
            int intervalTime = (int) ((kickBallTime - standardTime) / 1000);
            String kickBallData = userHardwareData.getKickBallData();
            if (!"".equals(kickBallData) && kickBallData != null) {
                try {
                    JSONArray tempArray = JSON.parseArray(kickBallData);
                    if (tempArray != null && tempArray.size() > 0) {
                        for (int k = 0; k < tempArray.size(); k++) {
                            Map<String, Object> map = new HashMap<String, Object>();
                            map.put("data", tempArray.getInteger(k) + intervalTime);
                            map.put("userId", user.getId() + "");
                            sortedArray.add(map);
                        }//for
                    }//tempArray!=null&&tempArray.size()>0
                } catch (JSONException e) {
                    e.printStackTrace();
                }

            }//!"".equals(kickBallData)&&kickBallData!=null

        }//
        return sortedArray;
    }

    //新的，根据球队硬件数据计算个人传球次数，
    public static int calculatePassCount(JSONArray sortedTeamDataArray) {
        int passCount = 0;
        if (sortedTeamDataArray != null && sortedTeamDataArray.size() > 0) {
            for (int i = 0; i < sortedTeamDataArray.size() - 1; i++) {
                //最后一个数据不算传球
                try {
                    JSONObject tempObjectI = sortedTeamDataArray.getJSONObject(i);
                    int dataI = tempObjectI.getInteger("data");
                    String userIdI = tempObjectI.getString("userId");
                    JSONObject tempObjectJ = sortedTeamDataArray.getJSONObject(i + 1);
                    int dataJ = tempObjectJ.getInteger("data");
                    String userIdJ = tempObjectJ.getString("userId");
                    if ((dataJ - dataI) < 10) {
                        //小于10秒，且与下一个数据不是同一个人
                        if (!userIdI.equals(userIdJ)) {
                            passCount++;
                        }
                    }// if
                } catch (JSONException e) {
                    // TODO Auto-generated catch block
                    e.printStackTrace();
                }

            }// for
        }// if
        return passCount;
    }


    //新的，根据球队硬件数据计算个人传球一脚次数，
    public static int calculateOneFootPassCount(JSONArray sortedTeamDataArray) {
        int passCount = 0;
        if (sortedTeamDataArray != null && sortedTeamDataArray.size() > 0) {
            for (int i = 1; i < sortedTeamDataArray.size() - 1; i++) {
                //最后一个和第一个数据不算传球
                try {
                    JSONObject tempObjectK = sortedTeamDataArray.getJSONObject(i - 1);
                    int dataK = tempObjectK.getInteger("data");
                    String userIdK = tempObjectK.getString("userId");
                    JSONObject tempObjectI = sortedTeamDataArray.getJSONObject(i);
                    int dataI = tempObjectI.getInteger("data");
                    String userIdI = tempObjectI.getString("userId");
                    JSONObject tempObjectJ = sortedTeamDataArray.getJSONObject(i + 1);
                    int dataJ = tempObjectJ.getInteger("data");
                    String userIdJ = tempObjectJ.getString("userId");
                    if ((dataJ - dataI) < 10) {
                        //小于10秒，且与前一个数据和后一个数据不是同一个人
                        if (!userIdI.equals(userIdJ) && !userIdI.equals(userIdK)) {
                            passCount++;
                        }
                    }// if
                } catch (JSONException e) {
                    // TODO Auto-generated catch block
                    e.printStackTrace();
                }

            }// for
        }// if
        return passCount;
    }


    //计算高中低走的无球距离
    public static long noBallDistance(JSONArray jAarray, User user, long noballStartTime, long noballEndTime) throws JSONException {
        long NotballDistance = 0;
        for (int g = 0; g < jAarray.size(); g++) {
            JSONObject highMove_Object = jAarray.getJSONObject(g);
            long highStartTime = highMove_Object.getLong("startTime");
            long highEndTime = highMove_Object.getLong("endTime");
            if (noballEndTime >= highStartTime || noballStartTime <= highEndTime) {
                long avgstepCounts = highMove_Object.getInteger("stepCount") /
                        (highMove_Object.getLong("intervalTime") / 1000);

                // 步长计算公式：步长=身高*0.45
                double stepWidth;
                if (user.getHeight() != null && user.getHeight() != 0) {
                    stepWidth = (user.getHeight() * 0.45 / 100.0);
                } else {
                    // 没有身高，默认为175
                    stepWidth = (175 * 0.45 / 100.0);
                }
//					System.out.println(highEndTime-highStartTime);
                long stepWidth_long = Math.round(stepWidth);
                long NotballDistance_temp = (highEndTime - highStartTime) / 1000 * avgstepCounts *
                        stepWidth_long;
                NotballDistance += NotballDistance_temp;
            }// if
        }// for g
        return NotballDistance;
    }


    // 计算高中低走带球距离
    public static Map<String, Object> calculateHaveFootballDistance(List<Integer> array, int passInterval,
                                                                    JSONArray highSpeedMoveDataArray, JSONArray midSpeedMoveDataArray, JSONArray lowSpeedMoveDataArray, JSONArray normalSpeedMoveDataArray,
                                                                    long kickballTime, double stepWidth) throws JSONException {
        //           int moveDistance=0;
        int haveballDistance = 0;
        int haveballhighballDistace = 0;
        int haveballmidDistance = 0;
        int haveballlowDistance = 0;
        int haveballnormalDistance = 0;
        Map<String, Object> haveballMap = new HashMap<String, Object>();
        if (array != null && array.size() > 0) {
            for (int i = 0; i < array.size() - 1; i++) {
                // 计算带球距离
                if ((array.get(i + 1) - array.get(i)) <= passInterval) {
                    if (i == 0) {
                        if (highSpeedMoveDataArray != null && highSpeedMoveDataArray.size() > 0) {
                            for (int k = 0; k < highSpeedMoveDataArray.size(); k++) {
                                JSONObject tempObject = highSpeedMoveDataArray.getJSONObject(k);
                                long startTime = tempObject.getLong("startTime");
                                long endTime = tempObject.getLong("endTime");
                                int stepCount = tempObject.getInteger("stepCount");
                                int intervalTime = tempObject.getInteger("intervalTime") / 1000;
                                if (startTime <= (kickballTime + array.get(i) * 1000) &&
                                        (kickballTime + array.get(i) * 1000) <= endTime) {
                                    //运动距离（米）=步数*步长
                                    //步长（米）=均速（米/分钟）/步频 2、步频=步数/时间（分钟）
                                    //高速跑默认8m/s
                                        /*int aveSpeend=8*60;//均速（米/分钟）
                                        int moveFrequence=((int)Math.ceil((float)stepCount/intervalTime))*60;
                                        if(moveFrequence==0){
                                            moveDistance+=0;
                                        }else{
                                            moveDistance+=aveSpeend/moveFrequence*stepCount;
                                        }*/
                                    double avgstepCount = (double) stepCount / (double) intervalTime;
                                    haveballhighballDistace = (int) Math.ceil((float) endTime - ((double) kickballTime + (double) array.get(i) * 1000) / 1000 * avgstepCount * stepWidth * 1.3);


                                }
                            }
                        }//highSpeedMoveDataArray
                        if (midSpeedMoveDataArray != null && midSpeedMoveDataArray.size() > 0) {
                            for (int k = 0; k < midSpeedMoveDataArray.size(); k++) {

                                JSONObject tempObject = midSpeedMoveDataArray.getJSONObject(k);
                                long startTime = tempObject.getLong("startTime");
                                long endTime = tempObject.getLong("endTime");
                                int stepCount = tempObject.getInteger("stepCount");
                                int intervalTime = tempObject.getInteger("intervalTime") / 1000;
                                if (startTime <= (kickballTime + array.get(i) * 1000) &&
                                        (kickballTime + array.get(i) * 1000) <= endTime) {

                                    //运动距离（米）=步数*步长
                                    //步长（米）=均速（米/分钟）/步频 2、步频=步数/时间（分钟）
                                    //中速跑默认5m/s
                                        /*int aveSpeend=5*60;//均速（米/分钟）
                                        int moveFrequence=((int)Math.ceil((float)stepCount/intervalTime))*60;
                                        if(moveFrequence==0){
                                            moveDistance+=0;
                                        }else{
                                            moveDistance+=aveSpeend/moveFrequence*stepCount;
                                        }*/
                                    double avgstepCount = (double) stepCount / (double) intervalTime;
                                    //                                 System.out.println("--------------"+(int)Math.ceil( endTime-(kickballTime+array.get(i)*1000)*avgstepCount  * stepWidth * 1.2));
                                    haveballmidDistance = (int) Math.ceil((double) endTime - (double) (kickballTime + array.get(i) * 1000) / 1000.0 * avgstepCount * stepWidth * 1.2);

                                }
                            }
                        }//midSpeedMoveDataArray
                        if (lowSpeedMoveDataArray != null && lowSpeedMoveDataArray.size() > 0) {
                            for (int k = 0; k < lowSpeedMoveDataArray.size(); k++) {
                                JSONObject tempObject = lowSpeedMoveDataArray.getJSONObject(k);
                                long startTime = tempObject.getLong("startTime");
                                long endTime = tempObject.getLong("endTime");
                                int stepCount = tempObject.getInteger("stepCount");
                                int intervalTime = tempObject.getInteger("intervalTime") / 1000;
                                if (startTime <= (kickballTime + array.get(i) * 1000) &&
                                        (kickballTime + array.get(i) * 1000) <= endTime) {
                                    //运动距离（米）=步数*步长
                                    //步长（米）=均速（米/分钟）/步频 2、步频=步数/时间（分钟）
                                    //低速跑默认3m/s
                                        /*int aveSpeend=3*60;//均速（米/分钟）
                                        int moveFrequence=((int)Math.ceil((float)stepCount/intervalTime))*60;
                                        if(moveFrequence==0){
                                            moveDistance+=0;
                                        }else{
                                            moveDistance+=aveSpeend/moveFrequence*stepCount;
                                        }*/
                                    double avgstepCount = (double) stepCount / (double) intervalTime;
                                    haveballlowDistance = (int) Math.ceil((double) endTime - (kickballTime + array.get(i) * 1000.0) / 1000.0 * avgstepCount * stepWidth * 1.1);

                                }
                            }
                        }//lowSpeedMoveDataArray
                        if (normalSpeedMoveDataArray != null && normalSpeedMoveDataArray.size() > 0) {
                            for (int k = 0; k < normalSpeedMoveDataArray.size(); k++) {
                                JSONObject tempObject = normalSpeedMoveDataArray.getJSONObject(k);
                                long startTime = tempObject.getLong("startTime");
                                long endTime = tempObject.getLong("endTime");
                                int stepCount = tempObject.getInteger("stepCount");
                                int intervalTime = tempObject.getInteger("intervalTime") / 1000;
                                if (startTime <= (kickballTime + array.get(i) * 1000) &&
                                        (kickballTime + array.get(i) * 1000) <= endTime) {

                                    //运动距离（米）=步数*步长
                                    //步长（米）=均速（米/分钟）/步频 2、步频=步数/时间（分钟）
                                    //走路默认1m/s
                                        /*int aveSpeend=1*60;//均速（米/分钟）
                                        int moveFrequence=((int)Math.ceil((float)stepCount/intervalTime))*60;
                                        if(moveFrequence==0){
                                            moveDistance+=0;
                                        }else{
                                            moveDistance+=aveSpeend/moveFrequence*stepCount;
                                        }*/
                                    double avgstepCount = (double) stepCount / (double) intervalTime;
                                    haveballnormalDistance = (int) Math.ceil((double) endTime - (kickballTime + array.get(i) * 1000.0) / 1000.0 * avgstepCount * stepWidth * 1);

                                }
                            }
                        }//normalSpeedMoveDataArray
                    } else {
                        if (highSpeedMoveDataArray != null && highSpeedMoveDataArray.size() > 0) {
                            for (int k = 0; k < highSpeedMoveDataArray.size(); k++) {
                                JSONObject tempObject = highSpeedMoveDataArray.getJSONObject(k);
                                long startTime = tempObject.getLong("startTime");
                                long endTime = tempObject.getLong("endTime");
                                int stepCount = tempObject.getInteger("stepCount");
                                int intervalTime = tempObject.getInteger("intervalTime") / 1000;
                                if (startTime <= (kickballTime + array.get(i) * 1000) &&
                                        (kickballTime + array.get(i) * 1000) <= endTime) {
                                    //运动距离（米）=步数*步长
                                    //步长（米）=均速（米/分钟）/步频 2、步频=步数/时间（分钟）
                                    //高速跑默认8m/s
                                            /*int aveSpeend=8*60;//均速（米/分钟）
                                            int moveFrequence=((int)Math.ceil((float)stepCount/intervalTime))*60;
                                            if(moveFrequence==0){
                                                moveDistance+=0;
                                            }else{
                                                moveDistance+=aveSpeend/moveFrequence*stepCount;
                                            }*/
                                    double avgstepCount = (double) stepCount / (double) intervalTime;
                                    haveballhighballDistace = (int) Math.ceil((endTime - (kickballTime + array.get(i) * 1000.0)) / 1000.0 * avgstepCount * stepWidth * 1.3);

                                }
                            }
                        }//highSpeedMoveDataArray
                        if (midSpeedMoveDataArray != null && midSpeedMoveDataArray.size() > 0) {
                            for (int k = 0; k < midSpeedMoveDataArray.size(); k++) {
                                JSONObject tempObject = midSpeedMoveDataArray.getJSONObject(k);
                                long startTime = tempObject.getLong("startTime");
                                long endTime = tempObject.getLong("endTime");
                                int stepCount = tempObject.getInteger("stepCount");
                                int intervalTime = tempObject.getInteger("intervalTime") / 1000;
                                if (startTime <= (kickballTime + array.get(i) * 1000) &&
                                        (kickballTime + array.get(i) * 1000) <= endTime) {
                                    //运动距离（米）=步数*步长
                                    //步长（米）=均速（米/分钟）/步频 2、步频=步数/时间（分钟）
                                    //中速跑默认5m/s
                                            /*int aveSpeend=5*60;//均速（米/分钟）
                                            int moveFrequence=((int)Math.ceil((float)stepCount/intervalTime))*60;
                                            if(moveFrequence==0){
                                                moveDistance+=0;
                                            }else{
                                                moveDistance+=aveSpeend/moveFrequence*stepCount;
                                            }*/

                                    double avgstepCount = (double) stepCount / (double) intervalTime;
                                    haveballmidDistance = (int) Math.ceil((endTime - (kickballTime + array.get(i) * 1000.0)) / 1000.0 * avgstepCount * stepWidth * 1.2);

                                }
                            }
                        }//midSpeedMoveDataArray
                        if (lowSpeedMoveDataArray != null && lowSpeedMoveDataArray.size() > 0) {
                            for (int k = 0; k < lowSpeedMoveDataArray.size(); k++) {
                                JSONObject tempObject = lowSpeedMoveDataArray.getJSONObject(k);
                                long startTime = tempObject.getLong("startTime");
                                long endTime = tempObject.getLong("endTime");
                                int stepCount = tempObject.getInteger("stepCount");
                                int intervalTime = tempObject.getInteger("intervalTime") / 1000;
                                if (startTime <= (kickballTime + array.get(i) * 1000) &&
                                        (kickballTime + array.get(i) * 1000) <= endTime) {
                                    //运动距离（米）=步数*步长
                                    //步长（米）=均速（米/分钟）/步频 2、步频=步数/时间（分钟）
                                    //低速跑默认3m/s
                                            /*int aveSpeend=3*60;//均速（米/分钟）
                                            int moveFrequence=((int)Math.ceil((float)stepCount/intervalTime))*60;
                                            if(moveFrequence==0){
                                                moveDistance+=0;
                                            }else{
                                                moveDistance+=aveSpeend/moveFrequence*stepCount;
                                            }*/
                                    double avgstepCount = (double) stepCount / (double) intervalTime;
                                    haveballlowDistance = (int) Math.ceil((endTime - (kickballTime + array.get(i) * 1000.0)) / 1000.0 * avgstepCount * stepWidth * 1.1);

                                }
                            }
                        }//lowSpeedMoveDataArray
                        if (normalSpeedMoveDataArray != null && normalSpeedMoveDataArray.size() > 0) {
                            for (int k = 0; k < normalSpeedMoveDataArray.size(); k++) {
                                JSONObject tempObject = normalSpeedMoveDataArray.getJSONObject(k);
                                long startTime = tempObject.getLong("startTime");
                                long endTime = tempObject.getLong("endTime");
                                int stepCount = tempObject.getInteger("stepCount");
                                int intervalTime = tempObject.getInteger("intervalTime") / 1000;
                                if (startTime <= (kickballTime + array.get(i) * 1000) &&
                                        (kickballTime + array.get(i) * 1000) <= endTime) {
                                    //运动距离（米）=步数*步长
                                    //步长（米）=均速（米/分钟）/步频 2、步频=步数/时间（分钟）
                                    //走路默认1m/s
                                            /*int aveSpeend=1*60;//均速（米/分钟）
                                            int moveFrequence=((int)Math.ceil((float)stepCount/intervalTime))*60;
                                            if(moveFrequence==0){
                                                moveDistance+=0;
                                            }else{
                                                moveDistance+=aveSpeend/moveFrequence*stepCount;
                                            }*/
                                    double avgstepCount = (double) stepCount / (double) intervalTime;
                                    haveballnormalDistance = (int) Math.ceil((endTime - (kickballTime + array.get(i) * 1000.0)) / 1000.0 * avgstepCount * stepWidth * 1);
                                }
                            }
                        }//normalSpeedMoveDataArray
                    }// else

                }// if
            }// for
//            haveballDistance = haveballhighballDistace + haveballmidDistance + haveballlowDistance + haveballnormalDistance;
        }// if
        haveballMap.put("haveballhighballDistace", haveballhighballDistace);
        haveballMap.put("haveballmidDistance", haveballmidDistance);
        haveballMap.put("haveballlowDistance", haveballlowDistance);
        haveballMap.put("haveballnormalDistance", haveballnormalDistance);
        return haveballMap;
    }

    //计算一脚传球次数和人员信息
    public static JSONArray calculateOneFootPassCountAndUser(JSONArray teamtouch_timeArray, int passInterval) {
        try {
            if (teamtouch_timeArray.size() > 1 && !"".equals(teamtouch_timeArray.toString())) {

                JSONArray resultjArray = new JSONArray();
                for (int i = 0; i < teamtouch_timeArray.size() - 1; i++) {
                    JSONArray jArray = new JSONArray(); // 一脚传球
                    JSONObject touchObject = teamtouch_timeArray.getJSONObject(i);
                    long touchTime = touchObject.getLong("touchTime");

                    if (i == 0) {
                        JSONObject touchObject_Next = teamtouch_timeArray.getJSONObject(i + 1);
                        long touchNextTime = touchObject_Next.getLong("touchTime");
                        if (!touchObject.getLong("userId").equals(touchObject_Next.getLong("userId")) &&
                                (touchNextTime / 1000) - (touchTime / 1000) <= passInterval && (touchNextTime / 1000) - (touchTime / 1000) >= 0) {
                            jArray.add(touchObject);
                            jArray.add(touchObject_Next);
                            resultjArray.add(jArray);
                        }
                    } else {
                        JSONObject touchObject_Next = teamtouch_timeArray.getJSONObject(i + 1);
                        JSONObject touchObject_Last = teamtouch_timeArray.getJSONObject(i - 1);
                        long touchNextTime = touchObject_Next.getLong("touchTime");
                        long touchLastTime = touchObject_Last.getLong("touchTime");

                        if ((touchNextTime / 1000) - (touchTime / 1000) <= passInterval && (touchNextTime / 1000) - (touchTime / 1000) >= 0 &&
                                !touchObject_Next.getLong("userId").equals(touchObject.getLong("userId"))) {
                            if ((touchTime / 1000) - (touchLastTime / 1000) <= passInterval && (touchTime / 1000) - (touchLastTime / 1000) >= 0 &&
                                    !touchObject.getLong("userId").equals(touchObject_Last.getLong("userId"))) {
                                jArray.add(touchObject_Last);
                                jArray.add(touchObject);
                                jArray.add(touchObject_Next);
                                resultjArray.add(jArray);
                            }
                        }
                    }
                } //for
                return resultjArray;
            } // if
        } catch (JSONException e) {
            //TODO Auto-generated catch block
            e.printStackTrace();
        }
        return null;
    }


    public static List<Long> jArrayToList(JSONArray jArray) {
        List<Long> list = new ArrayList<>();
        if (jArray != null && jArray.size() > 0) {
            for (int i = 0; i < jArray.size(); i++) {
                list.add(Long.parseLong(jArray.getString(i)));
            }
        }
        return list;
    }


    //计算有球无球距离
    public static Map<String, Object> calculateHaveball(List<UserHardwareData> userHardwareDataList, FootballTeamGameStatisticsPlayer teamGameStatisticsData, User user) {
        Map<String, Object> haveballMap = new HashMap<String, Object>(); //有球数据
        //                List<Integer> sortedDataArray = new ArrayList<Integer>(); //左右脚触球秒数
        long leftFootStartTime = 0;
        long rightFootStartTime = 0;

        List<Integer> leftList = new ArrayList<>();
        List<Integer> rightList = new ArrayList<>();
        try {
            for (UserHardwareData userHardwareData : userHardwareDataList) {
                if (userHardwareData != null) {
                    int there = userHardwareData.getUserHardware().getHardwareType();
                    if (there == 1) { //左脚
                        if (userHardwareData.getKickBallStartTime() != null) {
                            leftFootStartTime = userHardwareData.getKickBallStartTime().getTime();
                            String leftData = userHardwareData.getKickBallData();
                            if (leftData != null && !"".equals(leftData)) {
                                JSONArray leftArray = JSON.parseArray(leftData);
                                if (leftArray != null && leftArray.size() > 0) {
                                    for (int l = 0; l < leftArray.size(); l++) {
                                        leftList.add(leftArray.getInteger(l));
                                    }
                                } // leftArray!=null
                            } // if leftData!=null
                        } // if userHardwareData.getKickBallStartTime()!=null
                    } else if (there == 2) { //右脚
                        if (userHardwareData.getKickBallStartTime() != null) {
                            rightFootStartTime = userHardwareData.getKickBallStartTime().getTime();
                            String rightData = userHardwareData.getKickBallData();
                            if (rightData != null && !"".equals(rightData)) {
                                JSONArray rightArray = JSON.parseArray(rightData);
                                if (rightArray != null && rightArray.size() > 0) {
                                    for (int k = 0; k < rightArray.size(); k++) {
                                        rightList.add(rightArray.getInteger(k));
                                    }
                                }
                            } // if rightData!=null
                        } // if userHardwareData.getKickBallStartTime()!=null
                    } //there == 2
                } // if userHardwareData!=null
            } // for p
            //左右脚数组排序
            List<Integer> sortedDataArray = FootballTeamUtils
                    .sortArray(leftFootStartTime,
                            rightFootStartTime, leftList, rightList);
            //计算带球数据
            String HighMoveCaloriestring = null;
            String MidMoveCaloriestring = null;
            String LowMoveCaloriestring = null;
            String NormalMoveCaloriestring = null;

            if (teamGameStatisticsData != null) {
                HighMoveCaloriestring = teamGameStatisticsData.getHighMoveCalorie();
                MidMoveCaloriestring = teamGameStatisticsData.getMidMoveCalorie();
                LowMoveCaloriestring = teamGameStatisticsData.getLowMoveCalorie();
                NormalMoveCaloriestring = teamGameStatisticsData.getNormalMoveCalorie();
            }

            JSONArray highMoveCalorieArray = new JSONArray();
            JSONArray midMoveCalorieArray = new JSONArray();
            JSONArray lowMoveCalorieArray = new JSONArray();
            JSONArray normalMoveCalorieArray = new JSONArray();

            if (HighMoveCaloriestring != null && !"".equals(HighMoveCaloriestring)) {
                highMoveCalorieArray = JSON.parseArray(HighMoveCaloriestring);
            }
            if (MidMoveCaloriestring != null && !"".equals(MidMoveCaloriestring)) {
                midMoveCalorieArray = JSON.parseArray(MidMoveCaloriestring);
            }
            if (LowMoveCaloriestring != null && !"".equals(LowMoveCaloriestring)) {
                lowMoveCalorieArray = JSON.parseArray(LowMoveCaloriestring);
            }
            if (NormalMoveCaloriestring != null && !"".equals(NormalMoveCaloriestring)) {
                normalMoveCalorieArray = new JSONArray();
            }

            // 步长计算公式：步长=身高*0.45
            double stepWidth;
            if (user.getHeight() != null && user.getHeight() != 0) {
                stepWidth = (user.getHeight() * 0.45 / 100.0);
            } else {
                // 没有身高，默认为175
                stepWidth = (175 * 0.45 / 100.0);
            }
            // 计算有球的数据
            if (leftFootStartTime >= rightFootStartTime) {
                if (rightFootStartTime > 0) {
                    haveballMap = FootballTeamUtils.calculateHaveFootballDistance(sortedDataArray, 3,
                            highMoveCalorieArray, midMoveCalorieArray, lowMoveCalorieArray, normalMoveCalorieArray,
                            rightFootStartTime, stepWidth);
                } else {
                    haveballMap = FootballTeamUtils.calculateHaveFootballDistance(sortedDataArray, 3,
                            highMoveCalorieArray, midMoveCalorieArray, lowMoveCalorieArray, normalMoveCalorieArray,
                            leftFootStartTime, stepWidth);
                }
            } else {
                if (leftFootStartTime > 0) {
                    haveballMap = FootballTeamUtils.calculateHaveFootballDistance(sortedDataArray, 3,
                            highMoveCalorieArray, midMoveCalorieArray, lowMoveCalorieArray, normalMoveCalorieArray,
                            leftFootStartTime, stepWidth);
                } else {
                    haveballMap = FootballTeamUtils.calculateHaveFootballDistance(sortedDataArray, 3,
                            highMoveCalorieArray, midMoveCalorieArray, lowMoveCalorieArray, normalMoveCalorieArray,
                            rightFootStartTime, stepWidth);
                }
            }
        } catch (JSONException e) {
            //TODO Auto-generated catch block
            e.printStackTrace();
        }
        return haveballMap;
    }

    public static Map<String, Object> calculateCarryBallDistance(JSONArray highMoveDataArray, JSONArray midMoveDataArray,
                                                                 JSONArray lowMoveDataArray, JSONArray normalMoveArray, Short height) throws JSONException {
        Map<String, Object> map = new HashMap<>();

        Map<String, Object> map1;
        Map<String, Object> map2;
        Map<String, Object> map3;
        Map<String, Object> map4;

        long moveDistance;
        int highCarryDistance;
        int midCarryDistance;
        int lowCarryDistance;
        int normalCarryDistance;

        int highCarryCount;
        int midCarryCount;
        int lowCarryCount;
        int normalCarryCount;

        double highMaxSpeed;

        double highCarryMaxSpeed;
        double midCarryMaxSpeed;
        double lowCarryMaxSpeed;
        double normalCarryMaxSpeed;

        double maxCarrySpeed;

        //计算高速带球
        map1 = calculateMoveData(highMoveDataArray, height, 1);
        highCarryDistance = (int) map1.get("distance");
        highCarryCount = (int) map1.get("carryCount");
        highMaxSpeed = (double) map1.get("maxSpeed");
        highCarryMaxSpeed = (double) map1.get("maxCarrySpeed"); //最高冲刺带球速度

        //计算中速带球
        map2 = calculateMoveData(midMoveDataArray, height, 2);
        midCarryDistance = (int) map2.get("distance");
        midCarryCount = (int) map2.get("carryCount");
        midCarryMaxSpeed = (double) map2.get("maxCarrySpeed");  //最高高速带球速度

        //计算低速带球
        map3 = calculateMoveData(lowMoveDataArray, height, 3);
        lowCarryDistance = (int) map3.get("distance");
        lowCarryCount = (int) map3.get("carryCount");
        lowCarryMaxSpeed = (double) map3.get("maxCarrySpeed");  //最高中速带球速度
        //计算步行带球
        map4 = calculateMoveData(normalMoveArray, height, 4);
        normalCarryDistance = (int) map4.get("distance");
        normalCarryCount = (int) map4.get("carryCount");
        normalCarryMaxSpeed = (double) map4.get("maxCarrySpeed"); //最高低速带球速度

        if (highCarryDistance < 0) {
            highCarryDistance = 0;
        }
        if (midCarryDistance < 0) {
            midCarryDistance = 0;
        }
        if (lowCarryDistance < 0) {
            lowCarryDistance = 0;
        }
        if (normalCarryDistance < 0) {
            normalCarryDistance = 0;
        }

        List<Double> maxSpeedList = new ArrayList<>();
        maxSpeedList.add(highCarryMaxSpeed);
        maxSpeedList.add(midCarryMaxSpeed);
        maxSpeedList.add(lowCarryMaxSpeed);
        maxSpeedList.add(normalCarryMaxSpeed);
        maxCarrySpeed = Collections.max(maxSpeedList);

        moveDistance = highCarryDistance + midCarryDistance + lowCarryDistance + normalCarryDistance;
        map.put("moveDistance", (int) moveDistance);
        map.put("highCarryDistance", highCarryDistance);
        map.put("midCarryDistance", midCarryDistance);
        map.put("lowCarryDistance", lowCarryDistance);
        map.put("normalCarryDistance", normalCarryDistance);

        map.put("highCarryCount", highCarryCount);
        map.put("midCarryCount", midCarryCount);
        map.put("lowCarryCount", lowCarryCount);
        map.put("normalCarryCount", normalCarryCount);

        map.put("highMaxSpeed", highMaxSpeed);
        map.put("maxCarrySpeed", maxCarrySpeed);
        return map;
    }

    public static Map<String, Object> calculateCarryBallDistance(JSONArray highMoveDataArray, JSONArray midMoveDataArray,
                                                                 JSONArray lowMoveDataArray, JSONArray normalMoveArray,
                                                                 List<KickStatePojo> kickStatePojoList, List<KickStatePojo> rightKickStatePojoList,
                                                                 Short height) throws JSONException {
        Map<String, Object> map = new HashMap<>();
        Map<String, Object> map1;
        Map<String, Object> map2;
        Map<String, Object> map3;
        Map<String, Object> map4;
        long moveDistance;
        int highCarryDistance;
        int midCarryDistance;
        int lowCarryDistance;
        int normalCarryDistance;
        int highCarryCount;
        int midCarryCount;
        int lowCarryCount;
        int normalCarryCount;
        double highMaxSpeed;
        double highCarryMaxSpeed;
        double midCarryMaxSpeed;
        double lowCarryMaxSpeed;
        double normalCarryMaxSpeed;
        double maxCarrySpeed;
        StepWidthPojo stepWidthPojo = getStepWidthPojoSum(kickStatePojoList);
        StepWidthPojo rightStepWidthPojo = getStepWidthPojoSum(rightKickStatePojoList);

//        double stepWidthSum = stepWidthPojo.getStepWidthSum() + rightStepWidthPojo.getStepWidthSum();
        double highStepWidthSum = stepWidthPojo.getHighStepWidthSum() + rightStepWidthPojo.getHighStepWidthSum();
        double middleStepWidthSum = stepWidthPojo.getMiddleStepWidthSum() + rightStepWidthPojo.getMiddleStepWidthSum();
        double lowStepWidthSum = stepWidthPojo.getLowStepWidthSum() + rightStepWidthPojo.getLowStepWidthSum();

        if (rightKickStatePojoList != null && !rightKickStatePojoList.isEmpty()) {
            kickStatePojoList.addAll(rightKickStatePojoList);
        }
        kickStatePojoList.sort(Comparator.comparingInt(KickStatePojo::getSec));
        CarryCountPojo carryCountPojo = getCarryCount(kickStatePojoList);


        //计算高速带球
        map1 = calculateMoveData(highMoveDataArray, height, 1);
//        highCarryDistance = (int) map1.get("distance");
//        highCarryCount = (int) map1.get("carryCount");
        highCarryDistance = (int) calculateMoveDistance(highStepWidthSum, height);
        highCarryCount = carryCountPojo.getHighCarryCount();
        highMaxSpeed = (double) map1.get("maxSpeed");
        highCarryMaxSpeed = (double) map1.get("maxCarrySpeed"); //最高冲刺带球速度

        //计算中速带球
        map2 = calculateMoveData(midMoveDataArray, height, 2);
//        midCarryDistance = (int) map2.get("distance");
//        midCarryCount = (int) map2.get("carryCount");
        midCarryDistance = (int) calculateMoveDistance(middleStepWidthSum, height);
        midCarryCount = carryCountPojo.getMiddleCarryCount();
        midCarryMaxSpeed = (double) map2.get("maxCarrySpeed");  //最高高速带球速度

        //计算低速带球
        map3 = calculateMoveData(lowMoveDataArray, height, 3);
//        lowCarryDistance = (int) map3.get("distance");
//        lowCarryCount = (int) map3.get("carryCount");
        lowCarryDistance = (int) calculateMoveDistance(lowStepWidthSum, height);
        lowCarryCount = carryCountPojo.getLowCarryCount();
        lowCarryMaxSpeed = (double) map3.get("maxCarrySpeed");  //最高中速带球速度
        //计算步行带球
        map4 = calculateMoveData(normalMoveArray, height, 4);
//        normalCarryDistance = (int) map4.get("distance");
//        normalCarryCount = (int) map4.get("carryCount");
        normalCarryDistance = (int) calculateMoveDistance(lowStepWidthSum, height);
        normalCarryCount = carryCountPojo.getLowCarryCount();
        normalCarryMaxSpeed = (double) map4.get("maxCarrySpeed"); //最高低速带球速度

        if (highCarryDistance < 0) {
            highCarryDistance = 0;
        }
        if (midCarryDistance < 0) {
            midCarryDistance = 0;
        }
        if (lowCarryDistance < 0) {
            lowCarryDistance = 0;
        }
        if (normalCarryDistance < 0) {
            normalCarryDistance = 0;
        }

        List<Double> maxSpeedList = new ArrayList<>();
        maxSpeedList.add(highCarryMaxSpeed);
        maxSpeedList.add(midCarryMaxSpeed);
        maxSpeedList.add(lowCarryMaxSpeed);
        maxSpeedList.add(normalCarryMaxSpeed);
        maxCarrySpeed = Collections.max(maxSpeedList);

        moveDistance = highCarryDistance + midCarryDistance + lowCarryDistance + normalCarryDistance;
        map.put("moveDistance", (int) moveDistance);
        map.put("highCarryDistance", highCarryDistance);
        map.put("midCarryDistance", midCarryDistance);
        map.put("lowCarryDistance", lowCarryDistance);
        map.put("normalCarryDistance", normalCarryDistance);

        map.put("highCarryCount", highCarryCount);
        map.put("midCarryCount", midCarryCount);
        map.put("lowCarryCount", lowCarryCount);
        map.put("normalCarryCount", normalCarryCount);

        map.put("highMaxSpeed", highMaxSpeed);
        map.put("maxCarrySpeed", maxCarrySpeed);
        return map;
    }

    public static Map<String, Object> calculateMoveData(JSONArray moveDataArray, Short height, int type) throws JSONException {
        int distance = 0;
        int carryCount = 0;
        double maxSpeed = 0;
        double maxCarrySpeed = 0;
        Map<String, Object> map = new HashMap<>();
        if (moveDataArray != null && !"".equals(moveDataArray.toString())) {
            for (int i = 0; i < moveDataArray.size(); i++) {
                JSONObject object = moveDataArray.getJSONObject(i);
                if (object != null) {
                    int isHaveBall = object.getInteger("isHaveBall");
                    long tempDistance = UserDataUtil.calculateMoveDistanceNew(object.getInteger("stepCount"), object.getInteger("intervalTime"), height, type);
                    //计算最大速度
                    double intervalTime = (double) object.getInteger("intervalTime") / 1000.0;
                    double tempSpeed = 0;
                    if (intervalTime > 0) {
                        tempSpeed = tempDistance / intervalTime;    // m/s
                        if (tempSpeed > maxSpeed) {
                            maxSpeed = tempSpeed;
                        }
                    }
                    if (isHaveBall == 1) {  //表示是有球数据
                        //计算带球次数和带球距离
                        distance += tempDistance;
                        if (tempDistance > 0) {
                            carryCount++;
                        }
                        if (tempSpeed > maxCarrySpeed) {
                            maxCarrySpeed = maxSpeed;
                        }
                    }
                }
            }
        }
        maxSpeed *= 3.6;    // km/h
        map.put("maxSpeed", maxSpeed);
        map.put("distance", distance);
        map.put("carryCount", carryCount);
        map.put("maxCarrySpeed", maxCarrySpeed); //最高带球速度，add by zhongxin.lin 2018-9-10
        return map;
    }

    // 计算带球距离
    public static Map<String, Integer> calculateBallDistance(List<Integer> array, int passInterval,
                                                             JSONArray highSpeedMoveDataArray, JSONArray midSpeedMoveDataArray,
                                                             JSONArray lowSpeedMoveDataArray, JSONArray normalSpeedMoveDataArray, long kickballTime, Short height) throws JSONException {
        long moveDistance = 0;
        int highCarryDistance = 0;
        int midCarryDistance = 0;
        int lowCarryDistance = 0;
        int normalCarryDistance = 0;
        Map<String, Integer> map = new HashMap<>();
        List<List<Integer>> list = new ArrayList<>();
        List<Integer> temp = new ArrayList<>();
        if (array != null && array.size() > 0) {
            for (int i = 0; i < array.size() - 1; i++) {
                if ((array.get(i + 1) - array.get(i)) <= passInterval) {
                    if (temp.size() == 0) {
                        temp.add(0, array.get(i));
                        temp.add(1, array.get(i + 1));
                    } else {
                        temp.remove(1);
                        temp.add(1, array.get(i + 1));
                    }
                    if (i == array.size() - 2) {
                        if (temp.size() > 0) {
                            list.add(temp);
                        }
                    }
                } else {
                    List<Integer> g = temp;
                    if (g.size() > 0 && g != null) {
                        list.add(g);
                        temp = new ArrayList<>();
                    }
                }
            } // for
        } // if


//          int highStepCount = 0;
//          int highIntervalTime = 0;
//
//          int midStepCount = 0;
//          int midIntervalTime = 0;
//
//          int lowStepCount = 0;
//          int lowIntervalTime = 0;
//
//          int normalStepCount = 0;
//          int normalIntervalTime = 0;

        if (list.size() > 0) {
            for (List<Integer> t : list) {
                if (highSpeedMoveDataArray != null && highSpeedMoveDataArray.size() > 0) {
                    for (int j = 0; j < highSpeedMoveDataArray.size(); j++) {
                        JSONObject Object = highSpeedMoveDataArray.getJSONObject(j);
                        long b1 = kickballTime + t.get(0) * 1000;
                        long e1 = kickballTime + t.get(1) * 1000;
                        long b2 = Object.getLong("startTime");
                        long e2 = Object.getLong("endTime");
                        if (b1 < e2 && e1 > b2) {
                            if (b1 <= b2 && e1 >= e2) {   //（b1---【b2-----e2】--e1）1包含2
                                highCarryDistance += UserDataUtil.calculateMoveDistance(((Object.getInteger("stepCount"))), Object.getInteger("intervalTime"), height);
                            } else if (b1 >= b2 && e1 <= e2) {     //【b2---（b1-----e1）--e2】2包含1
                                int avgStepCount = (int) (Object.getInteger("stepCount") / (Object.getInteger("intervalTime") / 1000) * ((e1 - b1) / 1000));
                                highCarryDistance += UserDataUtil.calculateMoveDistance(avgStepCount, (int) (e1 - b1), height);
                            } else if (b1 >= b2 && b1 <= e2 && e2 <= e1) {     //（b1---【b2---e1）----e2】 相交1
                                int avgStepCount = (int) (Object.getInteger("stepCount") / (Object.getInteger("intervalTime") / 1000) * ((e1 - b2) / 1000));
                                highCarryDistance += UserDataUtil.calculateMoveDistance(avgStepCount, (int) (e1 - b2), height);
                            } else if (b1 <= b2 && e1 <= e2 && e1 >= b2) {     //【b2---(b1---e2】----e1) 相交2
                                int avgStepCount = (int) (Object.getInteger("stepCount") / (Object.getInteger("intervalTime") / 1000) * ((e2 - b1) / 1000));
                                highCarryDistance += UserDataUtil.calculateMoveDistance(avgStepCount, (int) (e2 - b1), height);
                            } else if (e1 <= b2 || b1 >= e2) {        //（b1-----e1）【b2-----e2】或【b2-----e2】（b1-----e1）不相交
                                highCarryDistance += 0;
                            } else {        //意料之外的组合
                                highCarryDistance += 0;
                            }
                        }
                    } // for
                }


                if (midSpeedMoveDataArray != null && midSpeedMoveDataArray.size() > 0) {
                    for (int j = 0; j < midSpeedMoveDataArray.size(); j++) {
                        JSONObject Object = midSpeedMoveDataArray.getJSONObject(j);
                        long b1 = kickballTime + t.get(0) * 1000;
                        long e1 = kickballTime + t.get(1) * 1000;
                        long b2 = Object.getLong("startTime");
                        long e2 = Object.getLong("endTime");
                        if (b1 < e2 && e1 > b2) {
                            if (b1 <= b2 && e1 >= e2) {   //（b1---【b2-----e2】--e1）1包含2
                                midCarryDistance += UserDataUtil.calculateMoveDistance(((Object.getInteger("stepCount"))), Object.getInteger("intervalTime"), height);
                            } else if (b1 >= b2 && e1 <= e2) {     //【b2---（b1-----e1）--e2】2包含1
                                int avgStepCount = (int) (Object.getInteger("stepCount") / (Object.getInteger("intervalTime") / 1000) * ((e1 - b1) / 1000));
                                midCarryDistance += UserDataUtil.calculateMoveDistance(avgStepCount, (int) (e1 - b1), height);
                            } else if (b1 >= b2 && b1 <= e2 && e2 <= e1) {     //（b1---【b2---e1）----e2】 相交1
                                int avgStepCount = (int) (Object.getInteger("stepCount") / (Object.getInteger("intervalTime") / 1000) * ((e1 - b2) / 1000));
                                midCarryDistance += UserDataUtil.calculateMoveDistance(avgStepCount, (int) (e1 - b2), height);
                            } else if (b1 <= b2 && e1 <= e2 && e1 >= b2) {     //【b2---(b1---e2】----e1) 相交2
                                int avgStepCount = (int) (Object.getInteger("stepCount") / (Object.getInteger("intervalTime") / 1000) * ((e2 - b1) / 1000));
                                midCarryDistance += UserDataUtil.calculateMoveDistance(avgStepCount, (int) (e2 - b1), height);
                            } else if (e1 <= b2 || b1 >= e2) {        //（b1-----e1）【b2-----e2】或【b2-----e2】（b1-----e1）不相交
                                midCarryDistance += 0;
                            } else {        //意料之外的组合
                                midCarryDistance += 0;
                            }
                        }
                    } // for
                }


                if (lowSpeedMoveDataArray != null && lowSpeedMoveDataArray.size() > 0) {
                    for (int j = 0; j < lowSpeedMoveDataArray.size(); j++) {
                        JSONObject Object = lowSpeedMoveDataArray.getJSONObject(j);
                        long b1 = kickballTime + t.get(0) * 1000;
                        long e1 = kickballTime + t.get(1) * 1000;
                        long b2 = Object.getLong("startTime");
                        long e2 = Object.getLong("endTime");
                        if (b1 < e2 && e1 > b2) {
                            if (b1 <= b2 && e1 >= e2) {   //（b1---【b2-----e2】--e1）1包含2
                                lowCarryDistance += UserDataUtil.calculateMoveDistance(((Object.getInteger("stepCount"))), Object.getInteger("intervalTime"), height);
                            } else if (b1 >= b2 && e1 <= e2) {     //【b2---（b1-----e1）--e2】2包含1
                                int avgStepCount = (int) (Object.getInteger("stepCount") / (Object.getInteger("intervalTime") / 1000) * ((e1 - b1) / 1000));
                                lowCarryDistance += UserDataUtil.calculateMoveDistance(avgStepCount, (int) (e1 - b1), height);
                            } else if (b1 >= b2 && b1 <= e2 && e2 <= e1) {     //（b1---【b2---e1）----e2】 相交1
                                int avgStepCount = (int) (Object.getInteger("stepCount") / (Object.getInteger("intervalTime") / 1000) * ((e1 - b2) / 1000));
                                lowCarryDistance += UserDataUtil.calculateMoveDistance(avgStepCount, (int) (e1 - b2), height);
                            } else if (b1 <= b2 && e1 <= e2 && e1 >= b2) {     //【b2---(b1---e2】----e1) 相交2
                                int avgStepCount = (int) (Object.getInteger("stepCount") / (Object.getInteger("intervalTime") / 1000) * ((e2 - b1) / 1000));
                                lowCarryDistance += UserDataUtil.calculateMoveDistance(avgStepCount, (int) (e2 - b1), height);
                            } else if (e1 <= b2 || b1 >= e2) {        //（b1-----e1）【b2-----e2】或【b2-----e2】（b1-----e1）不相交
                                lowCarryDistance += 0;
                            } else {        //意料之外的组合
                                lowCarryDistance += 0;
                            }
                        }
                    } // for
                }


                if (normalSpeedMoveDataArray != null && normalSpeedMoveDataArray.size() > 0) {
                    for (int j = 0; j < normalSpeedMoveDataArray.size(); j++) {
                        JSONObject Object = normalSpeedMoveDataArray.getJSONObject(j);
                        long b1 = kickballTime + t.get(0) * 1000;
                        long e1 = kickballTime + t.get(1) * 1000;
                        long b2 = Object.getLong("startTime");
                        long e2 = Object.getLong("endTime");
                        if (b1 < e2 && e1 > b2) {
                            if (b1 <= b2 && e1 >= e2) {   //（b1---【b2-----e2】--e1）1包含2
                                normalCarryDistance += UserDataUtil.calculateMoveDistance(((Object.getInteger("stepCount"))), Object.getInteger("intervalTime"), height);
                            } else if (b1 >= b2 && e1 <= e2) {     //【b2---（b1-----e1）--e2】2包含1
                                int avgStepCount = (int) (Object.getInteger("stepCount") / (Object.getInteger("intervalTime") / 1000) * ((e1 - b1) / 1000));
                                normalCarryDistance += UserDataUtil.calculateMoveDistance(avgStepCount, (int) (e1 - b1), height);
                            } else if (b1 >= b2 && b1 <= e2 && e2 <= e1) {     //（b1---【b2---e1）----e2】 相交1
                                int avgStepCount = (int) (Object.getInteger("stepCount") / (Object.getInteger("intervalTime") / 1000) * ((e1 - b2) / 1000));
                                normalCarryDistance += UserDataUtil.calculateMoveDistance(avgStepCount, (int) (e1 - b2), height);
                            } else if (b1 <= b2 && e1 <= e2 && e1 >= b2) {     //【b2---(b1---e2】----e1) 相交2
                                int avgStepCount = (int) (Object.getInteger("stepCount") / (Object.getInteger("intervalTime") / 1000) * ((e2 - b1) / 1000));
                                normalCarryDistance += UserDataUtil.calculateMoveDistance(avgStepCount, (int) (e2 - b1), height);
                            } else if (e1 <= b2 || b1 >= e2) {        //（b1-----e1）【b2-----e2】或【b2-----e2】（b1-----e1）不相交
                                normalCarryDistance += 0;
                            } else {        //意料之外的组合
                                normalCarryDistance += 0;
                            }
                        }
                    } // for
                }
            } // for
            if (highCarryDistance < 0) {
                highCarryDistance = 0;
            }
            if (midCarryDistance < 0) {
                midCarryDistance = 0;
            }
            if (lowCarryDistance < 0) {
                lowCarryDistance = 0;
            }
            if (normalCarryDistance < 0) {
                normalCarryDistance = 0;
            }
            moveDistance = highCarryDistance + midCarryDistance + lowCarryDistance + normalCarryDistance;
        } // if
        map.put("highCarryDistance", highCarryDistance);
        map.put("midCarryDistance", midCarryDistance);
        map.put("lowCarryDistance", lowCarryDistance);
        map.put("normalCarryDistance", normalCarryDistance);
        map.put("moveDistance", (int) moveDistance);
        return map;


    }


    @SuppressWarnings("unchecked")
    public static Map<String, Object> packLastNotOverGame(FootballTeamGame teamGame, Map<String, Map<Long, Integer>> gameMap, Map<String, Object> primap) {
        Map<String, Object> map = new HashMap<String, Object>();

        Map<Long, Integer> uploadMap = gameMap.get("upload");
        Map<Long, Integer> isStartMap = gameMap.get("isStart");

        Map<String, String> hostPriMap = (Map<String, String>) primap.get("hostRole");
        Map<String, String> guestPriMap = (Map<String, String>) primap.get("guestRole");

        Map<String, FootballTeam> hostTeam = (Map<String, FootballTeam>) primap.get("hostTeam");
        Map<String, FootballTeam> guestTeam = (Map<String, FootballTeam>) primap.get("guestTeam");

        Map<Long, FootballTeamMatch> matchMap = (Map<Long, FootballTeamMatch>) primap.get("match");
        //封装比赛
        if (teamGame != null) {
            long nowTime = System.currentTimeMillis();
            map.put("systemTime", System.currentTimeMillis());
            map.put("isGame", 1); //1 表示是比赛
            map.put("teamGameId", teamGame.getId());
            map.put("gameTime", teamGame.getCompetitionTime().getTime());
            map.put("hostName", teamGame.getFootballTeam().getTeamName());
            map.put("guestName", teamGame.getOpponent());
            map.put("hostImg", hostTeam.get(teamGame.getFootballTeam().getTeamName()).getTeamHeadImgNetUrl() == null ? "" : hostTeam.get(teamGame.getFootballTeam().getTeamName()).getTeamHeadImgNetUrl());
            map.put("hostId", teamGame.getFootballTeam().getId());
            map.put("hostPri", hostPriMap.get(teamGame.getFootballTeam().getTeamName()));
            map.put("hostScore", teamGame.getHostScore());
            map.put("guestScore", teamGame.getGuestScore());

            map.put("hostAbbreviation", teamGame.getFootballTeam().getTeamAbbreviation());

            FootballTeam opptTeam = null;
            if (guestTeam != null && guestTeam.size() > 0) {
                opptTeam = guestTeam.get(teamGame.getOpponent());
            }

            map.put("fieldLocation", teamGame.getFieldLocation());
            if (opptTeam != null) {
                map.put("opptAbbreviation", opptTeam.getTeamAbbreviation());
            } else {
                map.put("opptAbbreviation", teamGame.getOpponent());
            }

            //判断比赛是否结束
            long teamGameCompeteTime = teamGame.getCompetitionTime()
                    .getTime();
            long gameEndTime = teamGameCompeteTime
                    + FootballContants.GAMEDURATION;
            if (gameEndTime < nowTime) {
                map.put("gameStatus", 1); //已结束
            } else if (teamGameCompeteTime <= nowTime
                    && nowTime <= gameEndTime) {
                map.put("gameStatus", 2);//进行中
            } else if (teamGameCompeteTime > nowTime) {
                map.put("gameStatus", 3);//未开始
            }
            //取客队的头像 ，id，权限
            if (guestTeam != null && guestTeam.size() > 0) {
                if (guestTeam.containsKey(teamGame.getOpponent())) {
                    map.put("guestImg", guestTeam.get(teamGame.getOpponent()).getTeamHeadImgNetUrl() == null ? "" : guestTeam.get(teamGame.getOpponent()).getTeamHeadImgNetUrl());
                    map.put("guestId", guestTeam.get(teamGame.getOpponent()).getId());
                    map.put("guestPri", guestPriMap.get(teamGame.getOpponent()));
                } else {
                    map.put("guestImg", teamGame.getOppoentHeadImg() == null ? "" : teamGame.getOppoentHeadImg());
                    map.put("guestId", -1L);
                    map.put("guestPri", "");
                }
            } else {
                map.put("guestImg", teamGame.getOppoentHeadImg() == null ? "" : teamGame.getOppoentHeadImg());
                map.put("guestId", -1L);
                map.put("guestPri", "");
            }
            //取这场比赛有关赛事的字段
            if (teamGame.isCompetition()) {
                map.put("isMatch", 1);
                map.put("matchName", matchMap.get(teamGame.getMatchId()).getMatchName());
                map.put("gameType", matchMap.get(teamGame.getMatchId()).getMatchType()); // 1联赛  2 杯赛
            } else {
                map.put("isMatch", 0);
                map.put("matchName", "");
                map.put("gameType", (short) 3);   // 3 友谊赛
            }
            //是否上传数据和设备启动
            if (uploadMap != null && uploadMap.size() > 0) {
                if (uploadMap.containsKey(teamGame.getId())) {
                    map.put("isUpload", uploadMap.get(teamGame.getId()));
                    map.put("isStart", isStartMap.get(teamGame.getId()));
                } else {
                    map.put("isUpload", 0);
                    map.put("isStart", 0);
                }
            } else {
                map.put("isUpload", 0);
                map.put("isStart", 0);
            }
        }
        return map;
    }

    public static Map<String, Object> addEnrollTeamId(FootballTeamGameEnroll enroll, Map<String, Object> gameMap) {
        if (gameMap != null && gameMap.size() > 0) {
            if (enroll != null) {
                gameMap.put("enrollTeamId", enroll.getFootballTeam().getId());
            } else {
                gameMap.put("enrollTeamId", -1L);
            }
        }
        return gameMap;
    }

    public static JSONArray getListToTwoJSONArray(List<FootballTeamGame> notFinishGameList, List<UserHardwarePractice> allPracticeList, Map<String, Object> map,
                                                  Map<Long, Integer> practiceMap, Map<String, Map<Long, Integer>> gameMap, List<UserHardwarePractice> userAllPracticeList) throws JSONException {
        JSONArray jsonArray = new JSONArray();
        Map<String, FootballTeam> hostTeam = (Map<String, FootballTeam>) map.get("hostTeam");
        Map<String, FootballTeam> guestTeam = (Map<String, FootballTeam>) map.get("guestTeam");
        Map<String, String> hostPriMap = (Map<String, String>) map.get("hostRole");
        Map<String, String> guestPriMap = (Map<String, String>) map.get("guestRole");
        Map<Long, FootballTeamMatch> matchMap = (Map<Long, FootballTeamMatch>) map.get("match");
        Map<Long, Integer> gameUpload = gameMap.get("upload");
        Map<Long, Integer> gameIsstart = gameMap.get("isStart");
        long nowTime = System.currentTimeMillis();

        //处理练习赛
        if (allPracticeList != null && allPracticeList.size() > 0) {
            for (UserHardwarePractice practice : allPracticeList) {
                int ranking = (userAllPracticeList.indexOf(practice) + 1);
                Map<String, Object> tempMap = new HashMap<String, Object>();
                tempMap.put("practiceId", practice.getId());
                tempMap.put("practiceName", practice.getPracticeName());
                tempMap.put("gameTime", practice.getCreateTime().getTime());
                tempMap.put("ranking", ranking);
                tempMap.put("isGame", 0); //不是比赛
                if (practiceMap != null && practiceMap.size() > 0) {
                    tempMap.put("isUpload", practiceMap.getOrDefault(practice.getId(), 0));
                } else {
                    tempMap.put("isUpload", 0);
                }

                jsonArray.add(tempMap);
            }
        }

        //处理比赛
        if (notFinishGameList != null && notFinishGameList.size() > 0) {
            long sysytemTime = System.currentTimeMillis();
            for (FootballTeamGame teamGame : notFinishGameList) {
                Map<String, Object> tempMap = new HashMap<String, Object>();
                tempMap.put("systemTime", sysytemTime);
                tempMap.put("isGame", 1); // 1 表示是比赛
                tempMap.put("teamGameId", teamGame.getId());
                tempMap.put("gameTime", teamGame.getCompetitionTime().getTime());
                tempMap.put("hostName", teamGame.getFootballTeam().getTeamName());
                tempMap.put("guestName", teamGame.getOpponent());
                tempMap.put("hostImg", hostTeam.get(teamGame.getFootballTeam().getTeamName()).getTeamHeadImgNetUrl() == null ? "" : hostTeam.get(teamGame.getFootballTeam().getTeamName()).getTeamHeadImgNetUrl());
                tempMap.put("hostId", teamGame.getFootballTeam().getId());
                tempMap.put("hostPri", hostPriMap.get(teamGame.getFootballTeam().getTeamName()));
                tempMap.put("hostScore", teamGame.getHostScore());
                tempMap.put("guestScore", teamGame.getGuestScore());
                tempMap.put("fieldLocation", teamGame.getFieldLocation());
                tempMap.put("hostAbbreviation", teamGame.getFootballTeam().getTeamAbbreviation());
                FootballTeam opptTeam = null;
                if (guestTeam != null && guestTeam.size() > 0) {
                    opptTeam = guestTeam.get(teamGame.getOpponent());
                }
                if (opptTeam != null) {
                    tempMap.put("opptAbbreviation", opptTeam.getTeamAbbreviation());
                } else {
                    tempMap.put("opptAbbreviation", teamGame.getOpponent());
                }
                long teamGameCompeteTime = teamGame.getCompetitionTime()
                        .getTime();
                long gameEndTime = teamGameCompeteTime
                        + FootballContants.GAMEDURATION;
                if (gameEndTime < nowTime) {
                    tempMap.put("gameStatus", 1);
                } else if (teamGameCompeteTime <= nowTime
                        && nowTime <= gameEndTime) {
                    tempMap.put("gameStatus", 2);
                } else if (teamGameCompeteTime > nowTime) {
                    tempMap.put("gameStatus", 3);
                }
                if (guestTeam != null && guestTeam.size() > 0) {
                    if (guestTeam.containsKey(teamGame.getOpponent())) {
                        tempMap.put("guestImg", guestTeam.get(teamGame.getOpponent()).getTeamHeadImgNetUrl() == null ? "" : guestTeam.get(teamGame.getOpponent()).getTeamHeadImgNetUrl());
                        tempMap.put("guestId", guestTeam.get(teamGame.getOpponent()).getId());
                        tempMap.put("guestPri", guestPriMap.get(teamGame.getOpponent()));
                    } else {
                        tempMap.put("guestImg", teamGame.getOppoentHeadImg() == null ? "" : teamGame.getOppoentHeadImg());
                        tempMap.put("guestId", -1L);
                        tempMap.put("guestPri", "");
                    }
                } else {
                    tempMap.put("guestImg", teamGame.getOppoentHeadImg() == null ? "" : teamGame.getOppoentHeadImg());
                    tempMap.put("guestId", -1L);
                    tempMap.put("guestPri", "");
                }

                if (teamGame.isCompetition()) {
                    tempMap.put("isMatch", 1);
                    tempMap.put("matchName", matchMap.get(teamGame.getMatchId()).getMatchName());
                    tempMap.put("gameType", matchMap.get(teamGame.getMatchId()).getMatchType()); // 1联赛  2 杯赛
                } else {
                    tempMap.put("isMatch", 0);
                    tempMap.put("matchName", "");
                    tempMap.put("gameType", (short) 3);   // 3 友谊赛
                }
                if (gameUpload != null && gameUpload.size() > 0) {
                    if (gameUpload.containsKey(teamGame.getId())) {
                        tempMap.put("isUpload", gameUpload.get(teamGame.getId()));
                        tempMap.put("isStart", gameIsstart.get(teamGame.getId()));
                    } else {
                        tempMap.put("isUpload", 0);
                        tempMap.put("isStart", 0);
                    }
                } else {
                    tempMap.put("isUpload", 0);
                    tempMap.put("isStart", 0);
                }
                jsonArray.add(tempMap);
            } // for
        }

        //给比赛和练习赛排序
        /*if (jsonArray != null && jsonArray.size() > 0) {
            for (int i = 0; i < jsonArray.size(); i++) {
                for (int j = i + 1; j < jsonArray.size(); j++) {
                    JSONObject iObject = jsonArray.getJSONObject(i);
                    JSONObject jObject = jsonArray.getJSONObject(j);
                    if (iObject.getLong("gameTime") < jObject.getLong("gameTime")) {
                        jsonArray.add(i, jObject);
                        jsonArray.add(j, iObject);
                    }
                }
            }
        }*/
        return jsonArray;
    }

    public static Map<String, String> getPrimap(List<FootballTeamUser> teamUserList) {
        Map<String, String> map = new HashMap<String, String>();
        if (teamUserList != null && teamUserList.size() > 0) {
            for (FootballTeamUser teamUser : teamUserList) {
                map.put(teamUser.getFootballTeam().getTeamName(), teamUser.getFootballTeamMemberRole().getRoleName());
            }
        }
        return map;
    }

    public static Map<String, FootballTeam> getFootballTeamMap(List<FootballTeam> teamList) {
        Map<String, FootballTeam> map = new HashMap<String, FootballTeam>();
        if (teamList != null && teamList.size() > 0) {
            for (FootballTeam team : teamList) {
                map.put(team.getTeamName(), team);
            }
        }
        return map;
    }

    public static Map<Long, Integer> getPracticIsupload(List<UserHardwareData> userHardwareDataList) {
        Map<Long, Integer> map = new HashMap<Long, Integer>();
        if (userHardwareDataList != null && userHardwareDataList.size() > 0) {
            for (UserHardwareData hardWareData : userHardwareDataList) {
                if (hardWareData.getIsStartUp() == 2) {
                    map.put(hardWareData.getUserHardwarePractice().getId(), 0);
                } else if (hardWareData.getIsStartUp() == 3) {
                    map.put(hardWareData.getUserHardwarePractice().getId(), 1);
                }

            }
        }
        return map;
    }

    public static List<UserHardwarePractice> hardWareDataToPractice(List<UserHardwareData> userHardwareDataList, List<UserHardware> userHardWareList) {
        if (userHardwareDataList == null || userHardWareList == null) {
            return new ArrayList<>();
        }
        List<UserHardwarePractice> list = new ArrayList<>();

        //判断用户上传数据对应的硬件和用户绑定的硬件是否相符
        List<UserHardware> tempHardWareList = new ArrayList<>();
        if (userHardWareList.size() > 0 && userHardwareDataList.size() > 0) {
            for (UserHardware userHardware : userHardWareList) {
                for (UserHardwareData userHardwareData : userHardwareDataList) {
                    if (userHardware.getId().equals(userHardwareData.getUserHardware().getId())) {
                        tempHardWareList.add(userHardware);
                    }
                }
            }
        }

        if (tempHardWareList.size() == userHardWareList.size()) {
            if (userHardwareDataList.size() > 0) {
                for (UserHardwareData userHardwareData : userHardwareDataList) {
                    list.add(userHardwareData.getUserHardwarePractice());
                }
                list = new ArrayList<>(new LinkedHashSet<>(list)); //去除重复的练习赛
            }
        }
        return list;
    }

    public static List<UserHardwarePractice> hardWareDataToPractice(List<UserHardwareData> userHardwareDataList) {
        List<UserHardwarePractice> list = new ArrayList<UserHardwarePractice>();
        if (userHardwareDataList != null && userHardwareDataList.size() > 0) {
            for (UserHardwareData userHardwareData : userHardwareDataList) {
                list.add(userHardwareData.getUserHardwarePractice());
            }
            list = new ArrayList<UserHardwarePractice>(new LinkedHashSet<UserHardwarePractice>(list)); //去除重复的练习赛
        }
        return list;
    }

    public static Map<String, Object> getLastPractice(List<UserHardwareData> userHardwareDataList, List<UserHardwarePractice> userAllPracticeList, List<UserHardware> userHardWareList) {
        if (userHardwareDataList == null || userHardWareList == null) {
            return new HashMap<>();
        }
        Map<String, Object> map = new HashMap<>();

        List<UserHardware> tempHardWareList = new ArrayList<>();
        if (userHardwareDataList.size() > 0 && userHardWareList.size() > 0) {
            for (UserHardware userHardware : userHardWareList) {
                for (UserHardwareData userHardwareData : userHardwareDataList) {
                    if (userHardware.getId().equals(userHardwareData.getUserHardware().getId())) {
                        tempHardWareList.add(userHardware);
                    }
                }
            }
        }

        if (tempHardWareList.size() == userHardWareList.size()) {
            if (userHardwareDataList.size() > 0) {
                UserHardwarePractice practice = userHardwareDataList.get(0).getUserHardwarePractice();
                int ranking = (userAllPracticeList.indexOf(practice) + 1);
                map.put("practiceId", practice.getId());
                map.put("practiceName", practice.getPracticeName());
                map.put("gameTime", practice.getCreateTime().getTime());
                map.put("ranking", ranking);
                map.put("isGame", 0); //不是比赛
                boolean b = true;
                for (UserHardwareData userHardwareData : userHardwareDataList) {
                    if (userHardwareData.getIsStartUp() != 3) {
                        b = false;
                    }
                }
                if (b) {
                    map.put("isUpload", 1);
                } else {
                    map.put("isUpload", 0);
                }
            }
        }


        return map;
    }

    public static Map<String, Map<Long, Integer>> userHardwareListToGameUploadMap(List<UserHardwareData> userHardwareDataList,
                                                                                  List<FootballTeamGame> notFinishGameList, List<UserHardware> userHardWareList) {
        Map<String, Map<Long, Integer>> map = new HashMap<String, Map<Long, Integer>>();

        //对userHardwareDataList 按比赛id分组
        if (userHardwareDataList != null && userHardwareDataList.size() > 0) {
            Map<Long, List<UserHardwareData>> gameDataMap = new HashMap<Long, List<UserHardwareData>>();
            for (UserHardwareData data : userHardwareDataList) {
                List<UserHardwareData> tempList = gameDataMap.get(data.getFootballTeamGame().getId());
                /*如果取不到数据,那么直接new一个空的ArrayList**/
                if (tempList == null || tempList.size() == 0) {
                    tempList = new ArrayList<>();
                    tempList.add(data);
                    gameDataMap.put(data.getFootballTeamGame().getId(), tempList);
                } else {  /*如果取到，追加到原来的List里*/
                    tempList.add(data);
                }
            }
            // gameDataMap------   key:比赛id value: 该场比赛的上传数据
            Map<Long, Integer> uploadMap = new HashMap<Long, Integer>();
            Map<Long, Integer> isStartMap = new HashMap<Long, Integer>();
            if (notFinishGameList != null && notFinishGameList.size() > 0) {
                for (FootballTeamGame game : notFinishGameList) {
                    if (gameDataMap.containsKey(game.getId())) {
                        List<UserHardwareData> list = gameDataMap.get(game.getId());
                        isUploadAndIsStartUp(list, userHardWareList, uploadMap, isStartMap, game.getId());
                    } else {  //没有上传数据
                        uploadMap.put(game.getId(), 0);
                        isStartMap.put(game.getId(), 0);
                    }
                }
                map.put("isStart", isStartMap);
                map.put("upload", uploadMap);
            }
        }
        return map;
    }


    public static void isUploadAndIsStartUp(List<UserHardwareData> dataList, List<UserHardware> hardWarelist, Map<Long, Integer> uploadMap, Map<Long, Integer> isStartMap, Long gameId) {

        boolean b = false;   //是否上传
        boolean s = true;   //是否启动
        List<Long> startUpList = new ArrayList<>();
        for (UserHardwareData userHardwareData : dataList) {
            if (userHardwareData.getIsStartUp() == 2) {
                startUpList.add(userHardwareData.getUserHardware().getId());
            } else if (userHardwareData.getIsStartUp() == 3) {
                b = true;
            }

        }

        if (hardWarelist.size() > 0) {
            for (UserHardware userHardware : hardWarelist) {
                if (!startUpList.contains(userHardware.getId())) {
                    s = false;
                    break;
                }
            }
        } else {
            s = false;
        }

        if (b) {
            uploadMap.put(gameId, 1);
        } else {
            uploadMap.put(gameId, 0);
        }
        if (s) {
            isStartMap.put(gameId, 1);
        } else {
            isStartMap.put(gameId, 0);
        }


    }


    public static JSONArray pageForJArray(JSONArray jArray, int page, int pageSize) {
        int pageCount;
        JSONArray resultjArray = new JSONArray();
        try {
            if (jArray.size() % pageSize == 0) {
                pageCount = jArray.size() / pageSize;
            } else {
                pageCount = (jArray.size() / pageSize + 1);
            }
            if (page > pageCount) {
                resultjArray = new JSONArray();
            } else {
                int startIndex = (page - 1) * pageSize;
                if (jArray.size() <= pageSize) {
                    resultjArray = jArray;
                } else {
                    for (int i = startIndex; i < jArray.size(); i++) {
                        resultjArray.add(jArray.getJSONObject(i));
                        if (resultjArray.size() >= pageSize) {
                            break;
                        }
                    }
                }
            }

        } catch (Exception e) {
            e.printStackTrace();
        }
        return resultjArray;

    }

    public static JSONArray addEnrollTeamId(JSONArray jArray, List<FootballTeamGameEnroll> enrollList) throws JSONException {
        Map<Long, Long> enrollMap = new HashMap<Long, Long>();
        if (enrollList != null && enrollList.size() > 0) {
            for (FootballTeamGameEnroll enroll : enrollList) {
                enrollMap.put(enroll.getFootballTeamGame().getId(), enroll.getFootballTeam().getId());
            }
        }
        if (jArray != null && jArray.size() > 0) {
            for (int i = 0; i < jArray.size(); i++) {
                if (jArray.getJSONObject(i).containsKey("teamGameId") && enrollMap.containsKey(jArray.getJSONObject(i).getLong("teamGameId"))) {
                    jArray.getJSONObject(i).put("enrollTeamId", enrollMap.get(jArray.getJSONObject(i).getLong("teamGameId")));
                } else {
                    jArray.getJSONObject(i).put("enrollTeamId", -1L);
                }
            }
        }
        return jArray;
    }

    public static JSONArray packSaveTeam(List<FootballTeamUser> joinList, List<PoloShirt> poloList, JSONArray teamjArray, User user) {

        Map<Long, Long> poloMap = new HashMap<Long, Long>();
        if (poloList != null && poloList.size() > 0) {
            for (PoloShirt polo : poloList) {
                poloMap.put(polo.getFootballTeam().getId(), polo.getNumber());
            }
        }

        if (joinList != null && joinList.size() > 0) {
            for (FootballTeamUser teamUser : joinList) {
                Map<String, Object> teamMap = new HashMap<String, Object>();
                teamMap.put("teamName", teamUser.getFootballTeam().getTeamName());
                teamMap.put("teamAbbreviation", teamUser.getFootballTeam().getTeamAbbreviation());
                teamMap.put("teamId", teamUser.getFootballTeam().getId());
                teamMap.put("teamSite", toTeamSite(teamUser.getTeamPosition()));
                teamMap.put("teamNumber", poloMap.get(teamUser.getFootballTeam().getId()));
                teamMap.put("teamPosition", teamUser.getTeamPosition() == null ? Short.valueOf("1") : teamUser.getTeamPosition());
                teamMap.put("teamImg", teamUser.getFootballTeam().getTeamHeadImgNetUrl() == null ? "" : teamUser.getFootballTeam().getTeamHeadImgNetUrl());
                teamMap.put("teamRole", teamUser.getFootballTeamMemberRole().getRoleName());
                teamjArray.add(teamMap);
                if (teamjArray.size() > 1) {
                    break;
                }
            }
        }
        return teamjArray;
    }

    public static String toTeamSite(Short teamSite) {
        String s = "CF";
        if (teamSite != null) {
            switch (teamSite) {
                case 1:
                    s = "CF";
                    break;
                case 2:
                    s = "CM";
                    break;
                case 3:
                    s = "CB";
                    break;
                case 4:
                    s = "GK";
                    break;
                default:
                    s = "CF";
                    break;
            }
        }
        return s;
    }

    public static JSONArray packHistoryGame(List<FootballTeamTakeNode> takeNodeList, List<UserHardwareData> userHardWareDataList, JSONArray historyjArray,
                                            Map<Long, String> hosttPri, Map<Long, FootballTeamMatch> matchMap, Map<String, FootballTeam> guestTeamMap, Map<Long, String> guestPri,
                                            List<FootballTeamGameStatisticsPlayer> statisList, User user, List<FootballTeamGame> teamGameList, List<UserHardware> userHardWareList) {

        Map<String, Map<Long, Integer>> isUploadAndIsStartMap = FootballTeamUtils.userHardwareListToGameUploadMap(userHardWareDataList, teamGameList, userHardWareList);
        Map<Long, Integer> isUploadMap = isUploadAndIsStartMap.get("upload");
        Map<Long, Integer> isStartMap = isUploadAndIsStartMap.get("isStart");

        Map<String, Object> statisMap = new HashMap<String, Object>();
        Map<String, Object> distanceMap = new HashMap<String, Object>();
        if (statisList != null && statisList.size() > 0) {
            for (FootballTeamGameStatisticsPlayer statis : statisList) {
                statisMap.put(statis.getFootballTeamGame().getId() + "_" + statis.getFootballTeam().getId() + "_" + statis.getUser().getId(), statis.getPassBallCounts());
                distanceMap.put(statis.getFootballTeamGame().getId() + "_" + statis.getFootballTeam().getId() + "_" + statis.getUser().getId(), statis.getWholeMoveDistance());
            }
        }


        if (takeNodeList != null && takeNodeList.size() > 0) {
            long systemTime = System.currentTimeMillis();
            for (FootballTeamTakeNode takeNode : takeNodeList) {
                Map<String, Object> map = new HashMap<>();
                map.put("systemTime", systemTime);
                if (isUploadMap != null && isStartMap != null) {
                    map.put("isUpload", isUploadMap.get(takeNode.getFootballTeamGame().getId()) == null ? Integer.valueOf(0) : isUploadMap.get(takeNode.getFootballTeamGame().getId()));
                    map.put("isStart", isStartMap.get(takeNode.getFootballTeamGame().getId()) == null ? Integer.valueOf(0) : isStartMap.get(takeNode.getFootballTeamGame().getId()));
                } else {
                    map.put("isUpload", 0);
                    map.put("isStart", 0);
                }
                map.put("isGame", 1); //肯定是比赛
                map.put("hostTeamId", takeNode.getFootballTeamGame().getFootballTeam().getId());
                map.put("hostName", takeNode.getFootballTeamGame().getFootballTeam().getTeamName());
                map.put("hostAbbreviation", takeNode.getFootballTeamGame().getFootballTeam().getTeamAbbreviation());
                map.put("teamGameId", takeNode.getFootballTeamGame().getId());
                map.put("goalsfor", takeNode.getGoalsfor());
                map.put("assist", takeNode.getAssist());
                map.put("fault", takeNode.getFault());
                map.put("holdUp", takeNode.getHoldUp());
                map.put("hostScore", takeNode.getFootballTeamGame().getHostScore());
                map.put("guestScore", takeNode.getFootballTeamGame().getGuestScore());
                map.put("hostImg", takeNode.getFootballTeamGame().getFootballTeam().getTeamHeadImgNetUrl());
                map.put("gameTime", takeNode.getFootballTeamGame().getCompetitionTime().getTime());
                map.put("hostPri", hosttPri.get(takeNode.getFootballTeamGame().getFootballTeam().getId()));
                map.put("fieldLocation", takeNode.getFootballTeamGame().getFieldLocation());
                //比赛类型和赛事名称
                if (takeNode.getFootballTeamGame().isCompetition()) {  //是赛事比赛
                    map.put("isMatch", 1);
                    if (matchMap.containsKey(takeNode.getFootballTeamGame().getMatchId())) {
                        map.put("matchName", matchMap.get(takeNode.getFootballTeamGame().getMatchId()).getMatchName());
                        map.put("gameType", matchMap.get(takeNode.getFootballTeamGame().getMatchId()).getMatchType()); // 1:联赛   2:杯赛
                    } else {
                        map.put("matchName", "");
                        map.put("gameType", (short) 3);
                    }
                } else {                  //不是赛事比赛
                    map.put("isMatch", 0);
                    map.put("matchName", "");
                    map.put("gameType", (short) 3);
                }

                if (guestTeamMap != null) {
                    if (guestTeamMap.containsKey(takeNode.getFootballTeamGame().getOpponent())) {
                        map.put("guestImg", guestTeamMap.get(takeNode.getFootballTeamGame().getOpponent()).getTeamHeadImgNetUrl());
                        map.put("guestName", guestTeamMap.get(takeNode.getFootballTeamGame().getOpponent()).getTeamName());
                        map.put("guestTeamId", guestTeamMap.get(takeNode.getFootballTeamGame().getOpponent()).getId());
                        map.put("guestPri", guestPri.get(map.get("guestTeamId")));
                        map.put("opptAbbreviation", guestTeamMap.get(takeNode.getFootballTeamGame().getOpponent()).getTeamAbbreviation());
                    } else {
                        map.put("guestImg", takeNode.getFootballTeamGame().getOppoentHeadImg() == null ? "" : takeNode.getFootballTeamGame().getOppoentHeadImg());
                        map.put("guestName", takeNode.getFootballTeamGame().getOpponent());
                        map.put("guestTeamId", (long) -1);
                        map.put("guestPri", "");
                        map.put("opptAbbreviation", takeNode.getFootballTeamGame().getOpponent());
                    }
                } else {
                    map.put("guestImg", takeNode.getFootballTeamGame().getOppoentHeadImg() == null ? "" : takeNode.getFootballTeamGame().getOppoentHeadImg());
                    map.put("guestName", takeNode.getFootballTeamGame().getOpponent());
                    map.put("guestTeamId", (long) -1);
                    map.put("guestPri", "");
                    map.put("opptAbbreviation", takeNode.getFootballTeamGame().getOpponent());
                }

                long gameStartTime = System.currentTimeMillis();
                long teamGameCompeteTime = takeNode.getFootballTeamGame().getCompetitionTime()
                        .getTime();
                long gameEndTime = teamGameCompeteTime
                        + FootballContants.GAMEDURATION;
                if (gameEndTime < gameStartTime) {
                    map.put("gameStatus", 1);
                } else if (teamGameCompeteTime <= gameStartTime
                        && gameStartTime <= gameEndTime) {
                    map.put("gameStatus", 2);
                } else if (teamGameCompeteTime > gameStartTime) {
                    map.put("gameStatus", 3);
                }

                map = packWords(map, statisMap, distanceMap, takeNode, user);
                historyjArray.add(map);

            }
        }
        return historyjArray;
    }

    public static JSONArray packAllHistoryGame(List<FootballTeamGame> teamGameList, List<FootballTeamTakeNode> takeNodeList, List<UserHardwareData> userHardWareDataList, JSONArray historyjArray,
                                               Map<Long, String> hosttPri, Map<Long, FootballTeamMatch> matchMap, Map<String, FootballTeam> guestTeamMap, Map<Long, String> guestPri,
                                               List<FootballTeamGameStatisticsPlayer> statisList, User user, List<UserHardware> userHardWareList) {

        Map<String, Map<Long, Integer>> isUploadAndIsStartMap = FootballTeamUtils.userHardwareListToGameUploadMap(userHardWareDataList, teamGameList, userHardWareList);
        Map<Long, Integer> isUploadMap = isUploadAndIsStartMap.get("upload");
        Map<Long, Integer> isStartMap = isUploadAndIsStartMap.get("isStart");

        Map<String, Object> statisMap = new HashMap<String, Object>();
        Map<String, Object> distanceMap = new HashMap<String, Object>();
        if (statisList != null && statisList.size() > 0) {
            for (FootballTeamGameStatisticsPlayer statis : statisList) {
                statisMap.put(statis.getFootballTeamGame().getId() + "_" + statis.getFootballTeam().getId() + "_" + statis.getUser().getId(), statis.getPassBallCounts());
                distanceMap.put(statis.getFootballTeamGame().getId() + "_" + statis.getFootballTeam().getId() + "_" + statis.getUser().getId(), statis.getWholeMoveDistance());
            }
        }

        Map<String, FootballTeamTakeNode> nodeMap = new HashMap<String, FootballTeamTakeNode>();
        if (takeNodeList != null && takeNodeList.size() > 0) {
            for (FootballTeamTakeNode node : takeNodeList) {
                nodeMap.put(node.getFootballTeamGame().getId() + "_" + node.getFootballTeam().getId() + "_" + node.getUser().getId(), node);
            }
        }


        if (teamGameList != null && teamGameList.size() > 0) {
            long systemTime = System.currentTimeMillis();
            for (FootballTeamGame game : teamGameList) {
                Map<String, Object> map = new HashMap<String, Object>();
                map.put("systemTime", systemTime);
                if (isUploadMap != null && isStartMap != null) {
                    map.put("isUpload", isUploadMap.get(game.getId()) == null ? Integer.valueOf(0) : isUploadMap.get(game.getId()));
                    map.put("isStart", isStartMap.get(game.getId()) == null ? Integer.valueOf(0) : isStartMap.get(game.getId()));
                } else {
                    map.put("isUpload", 0);
                    map.put("isStart", 0);
                }
                map.put("isGame", 1); //肯定是比赛
                map.put("hostTeamId", game.getFootballTeam().getId());
                map.put("hostName", game.getFootballTeam().getTeamName());
                map.put("hostAbbreviation", game.getFootballTeam().getTeamAbbreviation());
                map.put("fieldLocation", game.getFieldLocation());
                map.put("teamGameId", game.getId());
                map.put("hostScore", game.getHostScore());
                map.put("guestScore", game.getGuestScore());
                map.put("hostImg", game.getFootballTeam().getTeamHeadImgNetUrl());
                map.put("gameTime", game.getCompetitionTime().getTime());
                map.put("hostPri", hosttPri.get(game.getFootballTeam().getId()));
                //比赛类型和赛事名称
                if (game.isCompetition()) {  //是赛事比赛
                    map.put("isMatch", 1);
                    if (matchMap.containsKey(game.getMatchId())) {
                        map.put("matchName", matchMap.get(game.getMatchId()).getMatchName());
                        map.put("gameType", matchMap.get(game.getMatchId()).getMatchType()); // 1:联赛   2:杯赛
                    } else {
                        map.put("matchName", "");
                        map.put("gameType", (short) 3);
                    }
                } else {                  //不是赛事比赛
                    map.put("isMatch", 0);
                    map.put("matchName", "");
                    map.put("gameType", (short) 3);
                }

                if (guestTeamMap != null) {
                    if (guestTeamMap.containsKey(game.getOpponent())) {
                        map.put("guestImg", guestTeamMap.get(game.getOpponent()).getTeamHeadImgNetUrl());
                        map.put("guestName", guestTeamMap.get(game.getOpponent()).getTeamName());
                        map.put("opptAbbreviation", guestTeamMap.get(game.getOpponent()).getTeamAbbreviation());
                        map.put("guestTeamId", guestTeamMap.get(game.getOpponent()).getId());
                        map.put("guestPri", guestPri.get(map.get("guestTeamId")));
                    } else {
                        map.put("guestImg", game.getOppoentHeadImg() == null ? "" : game.getOppoentHeadImg());
                        map.put("guestName", game.getOpponent());
                        map.put("guestTeamId", (long) -1);
                        map.put("guestPri", "");
                        map.put("opptAbbreviation", game.getOpponent());
                    }
                } else {
                    map.put("guestImg", game.getOppoentHeadImg() == null ? "" : game.getOppoentHeadImg());
                    map.put("guestName", game.getOpponent());
                    map.put("guestTeamId", (long) -1);
                    map.put("guestPri", "");
                    map.put("opptAbbreviation", game.getOpponent());
                }

                long gameStartTime = System.currentTimeMillis();
                long teamGameCompeteTime = game.getCompetitionTime()
                        .getTime();
                long gameEndTime = teamGameCompeteTime
                        + FootballContants.GAMEDURATION;
                if (gameEndTime < gameStartTime) {
                    map.put("gameStatus", 1);
                } else if (teamGameCompeteTime <= gameStartTime
                        && gameStartTime <= gameEndTime) {
                    map.put("gameStatus", 2);
                } else if (teamGameCompeteTime > gameStartTime) {
                    map.put("gameStatus", 3);
                }
                if (nodeMap.containsKey(game.getId() + "_" + map.get("hostTeamId") + "_" + user.getId())) {    //判断有没有在主队录数据
                    FootballTeamTakeNode takeNode = nodeMap.get(game.getId() + "_" + map.get("hostTeamId") + "_" + user.getId());
                    Map<String, Object> tempMap = new HashMap<String, Object>(map);
                               /*tempMap.put("goalsfor", takeNode.getGoalsfor());
                               tempMap.put("assist", takeNode.getAssist());
                               tempMap.put("fault", takeNode.getFault());
                               tempMap.put("holdUp", takeNode.getHoldUp());*/
                    tempMap = packWords(tempMap, statisMap, distanceMap, takeNode, user);
                    historyjArray.add(tempMap);
                }

                if (nodeMap.containsKey(game.getId() + "_" + map.get("guestTeamId") + "_" + user.getId())) {   //判断有没有在客队录数据
                    Map<String, Object> tempMap = new HashMap<String, Object>();
                    FootballTeamTakeNode takeNode = nodeMap.get(game.getId() + "_" + map.get("guestTeamId") + "_" + user.getId());
                    tempMap.putAll(map);
                    tempMap = packWords(tempMap, statisMap, distanceMap, takeNode, user);
                    historyjArray.add(tempMap);
                }

                if (!nodeMap.containsKey(game.getId() + "_" + map.get("hostTeamId") + "_" + user.getId()) &&
                        !nodeMap.containsKey(game.getId() + "_" + map.get("guestTeamId") + "_" + user.getId())) { //主客队都没有录数据
                    Map<String, Object> tempMap = new HashMap<String, Object>();
                    tempMap.putAll(map);
                    String key = game.getId() + "_" + game.getFootballTeam().getId() + "_" + user.getId();
                    if (statisMap.containsKey(key) && (int) statisMap.get(key) > 0) {
                        tempMap.put("count", statisMap.get(key));
                        tempMap.put("wordsIndex", 5);
                    } else if (distanceMap.containsKey(key) && (long) distanceMap.get(key) > 0) {
                        Long distance = (Long) distanceMap.get(key);
                        tempMap.put("count", distance.doubleValue() / 1000);
                        tempMap.put("wordsIndex", 6);
                    } else {
                        tempMap.put("count", 0); //没有录入失误数据
                        tempMap.put("wordsIndex", 12);
                    }
                    historyjArray.add(tempMap);
                }
            }
        }
        return historyjArray;
    }


    public static Map<String, Object> packWords(Map<String, Object> map, Map<String, Object> statisMap, Map<String, Object> distanceMap, FootballTeamTakeNode takeNode, User user) {

        int wordsIndex;
        if (takeNode.getGoalsfor() != null && takeNode.getGoalsfor() >= 3) {
            wordsIndex = 1;
            map.put("count", takeNode.getGoalsfor());
        } else if (takeNode.getGoalsfor() != null && takeNode.getGoalsfor() == 2) {
            wordsIndex = 2;
            map.put("count", takeNode.getGoalsfor());
        } else if (takeNode.getAssist() != null && takeNode.getAssist() >= 3) {
            wordsIndex = 3;
            map.put("count", takeNode.getAssist());
        } else if (takeNode.getAssist() != null && takeNode.getAssist() == 2) {
            wordsIndex = 4;
            map.put("count", takeNode.getAssist());

        } else if (statisMap.containsKey(takeNode.getFootballTeamGame().getId() + "_" + takeNode.getFootballTeam().getId() + "_" + user.getId()) && (int) statisMap.get(takeNode.getFootballTeamGame().getId() + "_" + takeNode.getFootballTeam().getId() + "_" + user.getId()) > 0) {
            wordsIndex = 5;
            map.put("count", statisMap.get(takeNode.getFootballTeamGame().getId() + "_" + takeNode.getFootballTeam().getId() + "_" + user.getId()));

        } else if (distanceMap.containsKey(takeNode.getFootballTeamGame().getId() + "_" + takeNode.getFootballTeam().getId() + "_" + user.getId()) && (long) distanceMap.get(takeNode.getFootballTeamGame().getId() + "_" + takeNode.getFootballTeam().getId() + "_" + user.getId()) > 0) {
            wordsIndex = 6;
            Long distance = (Long) distanceMap.get(takeNode.getFootballTeamGame().getId() + "_" + takeNode.getFootballTeam().getId() + "_" + user.getId());
            map.put("count", distance.doubleValue() / 1000);

        } else if (takeNode.getGoalsfor() != null && takeNode.getGoalsfor() == 1) {
            wordsIndex = 7;
            map.put("count", takeNode.getGoalsfor());
        } else if (takeNode.getAssist() != null && takeNode.getAssist() == 1) {
            wordsIndex = 8;
            map.put("count", takeNode.getAssist());
        } else if (takeNode.getHoldUp() != null && takeNode.getHoldUp() >= 1) {
            wordsIndex = 9;
            map.put("count", takeNode.getHoldUp());
        } else if (takeNode.getSave() != null && takeNode.getSave() >= 1) {
            wordsIndex = 10;
            map.put("count", takeNode.getSave());
        } else if (takeNode.getStopFault() != null && takeNode.getDefendFault() != null && takeNode.getPassFault() != null) {
            wordsIndex = 11;
            map.put("count", takeNode.getStopFault() + takeNode.getDefendFault() + takeNode.getPassFault());
        } else {
            wordsIndex = 12;
            map.put("count", 0);
        }
        map.put("wordsIndex", wordsIndex);
        return map;

    }

    /*

        /**
         * JSONArray数据集合为全队触球点的时间点的集合JSon
         *
         * @throws JSONException
         * @Description:TODO
         * @author:Administrator
         * @time:2017年6月2日 下午12:43:23
         */
    public static List<Map<String, Object>> lostAndfreeNum(JSONArray sortedTeamDataArray) throws JSONException {
        List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();
        /*Map<String,Object> map=new HashMap<String, Object>();*/
        int lost = 0;
        int free = 0;
        if (sortedTeamDataArray != null && sortedTeamDataArray.size() > 0) {
            //最后一个数据不算丢球
            try {
                for (int i = 0; i < sortedTeamDataArray.size() - 1; i++) {
                    Map<String, Object> map = new HashMap<String, Object>();
                    JSONObject tempObjectI = sortedTeamDataArray.getJSONObject(i);
                    int dataI = tempObjectI.getInteger("touchTime");
                    String userIdI = tempObjectI.getString("userId");
                    JSONObject tempObjectJ = sortedTeamDataArray.getJSONObject(i + 1);
                    int dataJ = tempObjectJ.getInteger("touchTime");
                    String userIdJ = tempObjectJ.getString("userId");
                    if ((dataJ - dataI) > 3000) {
	    						/*if(i==0){
		    						map.put(userIdI, "userIdFree");
		    						map.put("userIdLost",userIdI);
	    							list.add(map);
		    					}*/
                        //小于3秒，且与下一个数据不是同一个人
                        /*if(!userIdI.equals(userIdJ)){*/
                        map.put("userIdLost", userIdI);
                        //map.put(userIdI,"userIdLost");
                        map.put("userIdFree", userIdJ);
                        //map.put(userIdJ, "userIdFree");
                        list.add(map);
                        /*}*/
                    } else {
                        if (!userIdI.equals(userIdJ)) {
                            map.put("userIdFree", userIdJ);
                            //map.put(userIdJ, "userIdFree");
                            list.add(map);
                        }
                    }
                }
                //第一个算得球
            } catch (JSONException e) {
                // TODO Auto-generated catch block
                e.printStackTrace();
            }
            //map集合判断丢球次数
            //第一个获取数据的人获得一次球权
            String userIdI = sortedTeamDataArray.getJSONObject(0).getString("userId");
            Map<String, Object> mapOne = new HashMap<String, Object>();
            mapOne.put("userIdFree", userIdI);
            list.add(mapOne);
        }

        return list;
    }


    public static boolean addRecordList(List<FootballTeamGame> teamGameList) {
        PreparedStatement pst = null;
        ResultSet rs = null;
        Connection conn = null;
        JDBCUtil.init();
        try {
            conn = JDBCUtil.getConnection();
            conn.setAutoCommit(false);
            //根据类的字段拼接sql
            String sql = sql(FootballTeamGame.class);
            //创建预处理
            pst = conn.prepareStatement(sql);
            FootballTeamGame teamGame;
            for (int i = 0, length = teamGameList.size(); i < length; i++) {
                teamGame = teamGameList.get(i);
                prepareParams(pst, teamGame, FootballTeamGame.class);
                pst.addBatch();
                //1000条执行一次
                if (i % 1000 == 0) {
                    //执行
                    pst.executeBatch();
                    //清空
                    pst.clearBatch();
                }
            }
            //执行剩余的sql
            pst.executeBatch();
            //手动提交
            conn.commit();
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        } finally {
            JDBCUtil.free(rs, pst, conn);
        }
    }

    public static String sql(Class clz) {
        StringBuffer sql = new StringBuffer("insert into football_team_game(");
        //反射获取fields
        Field[] fields = clz.getDeclaredFields();

        for (Field field : fields) {
            if (!(field.getName().equals("footballTeamGameStatisticsPlayers")) && !(field.getName().equals("footballTeamGameAbsents")) &&
                    !(field.getName().equals("footballTeamGameEnrolls")) && !(field.getName().equals("userHardwareDatas")) &&
                    !(field.getName().equals("footballTeamGameSigns"))) {
                //footballTeam和user外键特殊处理,其它直接使用字段名
                if ("footballTeam".equals(field.getName())) {
                    sql.append("teamId,");
                } else if ("user".equals(field.getName())) {
                    sql.append("publisherId,");
                } else {
                    sql.append(field.getName());
                    sql.append(",");
                }
            }
        }
        sql.deleteCharAt(sql.length() - 1);
        sql.append(")");
        sql.append(" values(");
        for (int i = 0, length = fields.length; i < length - 5; i++) {
            sql.append("?,");
        }
        sql.deleteCharAt(sql.length() - 1);
        sql.append(")");
        return sql.toString();
    }

    private static void prepareParams(PreparedStatement pst, Object obj, Class clz) throws Exception {
        //反射获取所有字段Fields
        Field[] fields = clz.getDeclaredFields();
        Method method;
        Field field;
        Object tmpObj;
        for (int i = 0; i < fields.length; i++) {

            field = fields[i];
            if (!(field.getName().equals("footballTeamGameStatisticsPlayers")) && !(field.getName().equals("footballTeamGameAbsents")) &&
                    !(field.getName().equals("footballTeamGameEnrolls")) && !(field.getName().equals("userHardwareDatas")) &&
                    !(field.getName().equals("footballTeamGameSigns"))) {
                //根据字段名得到getter方法
                System.out.println("--------------" + field.getName());
                PropertyDescriptor pd;
                if (field.getName().equals("competition")) {
                    pd = new PropertyDescriptor("Match", clz);
                } else {
                    pd = new PropertyDescriptor(field.getName(), clz);
                }

                method = pd.getReadMethod();
                //调用获取的getter方法，得到对应的值
                tmpObj = method.invoke(obj);
                //match和user特殊处理
                if ("footballTeam".equals(field.getName())) {
                    pst.setObject(i + 1, ((FootballTeam) tmpObj).getId());
                } else if ("user".equals(field.getName())) {
                    pst.setObject(i + 1, ((User) tmpObj).getId());
                } else {
                    pst.setObject(i + 1, tmpObj);
                }
            }
        }

    }


    //雷达图评分
    public static Map<String, Object> radarNumber(Map<String, Object> map, UserRadar userRadar) throws JSONException {
        Integer passCount = (Integer) map.get("touchCount");
        int moveDistance = (int) map.get("moveDistance");
        int goalsfor = (int) map.get("goalsfor");
        int assist = (int) map.get("assist");
        int fault = (int) map.get("fault"); //失误
        int shoot = (int) map.get("shoot");
        int holdUp = (int) map.get("holdUp");
        int excel = (int) map.get("excel");
        int redcard = (int) map.get("redcard");
        int yellowcard = (int) map.get("yellowcard");
        int calorie = (int) map.get("calorie");
        int saveBall = (int) map.get("saveBall");
        int foul = (int) map.get("foul");  //犯规
        int passProb = (int) map.get("passProb");
        int dangerBall = (int) map.get("dangerBall");
        int distance = (int) map.get("carryDistance");
//			BigDecimal distance = new BigDecimal(map.get("carryDistance").toString());


        int passCountNumber = 0;
        int moveDistanceNumber = 0;
        int goalsforNumber = 0;
        int assistNumber = 0;
        int faultNumber = 0;
        int shootNumber = 0;
        int holdUpNumber = 0;
        int excelNumber = 0;
        int redCardNumber = 0;
        int yellowCardNumber = 0;
        int calorieNumber = 0;
        int saveBallNumber = 0;
        int foulNumber = 0;
        int passProbNumber = 50;
        int dangerBallNumber = 0;
        int distanceNumber = 0;

        //传球
        if (passCount == 0) {
            passCountNumber = 1;
        } else if (passCount >= 1 && passCount <= 100) {
            passCountNumber = 2;
        } else if (passCount >= 101 && passCount <= 200) {
            passCountNumber = 3;
        } else if (passCount >= 201 && passCount <= 400) {
            passCountNumber = 4;
        } else if (passCount >= 401 && passCount <= 600) {
            passCountNumber = 5;
        } else if (passCount >= 601 && passCount <= 800) {
            passCountNumber = 6;
        } else if (passCount >= 801 && passCount <= 1100) {
            passCountNumber = 7;
        } else if (passCount >= 1101 && passCount <= 1500) {
            passCountNumber = 8;
        } else if (passCount >= 1501 && passCount <= 2000) {
            passCountNumber = 9;
        } else if (passCount > 2000) {
            passCountNumber = 10;
        }

        //跑动
        if (moveDistance == 0) {
            moveDistanceNumber = 1;
        } else if (moveDistance >= 1 && moveDistance <= 10) {
            moveDistanceNumber = 2;
        } else if (moveDistance >= 11 && moveDistance <= 20) {
            moveDistanceNumber = 3;
        } else if (moveDistance >= 21 && moveDistance <= 50) {
            moveDistanceNumber = 4;
        } else if (moveDistance >= 51 && moveDistance <= 100) {
            moveDistanceNumber = 5;
        } else if (moveDistance >= 101 && moveDistance <= 200) {
            moveDistanceNumber = 6;
        } else if (moveDistance >= 201 && moveDistance <= 300) {
            moveDistanceNumber = 7;
        } else if (moveDistance >= 301 && moveDistance <= 400) {
            moveDistanceNumber = 8;
        } else if (moveDistance >= 401 && moveDistance <= 550) {
            moveDistanceNumber = 9;
        } else if (moveDistance > 550) {
            moveDistanceNumber = 10;
        }
        //进球
        if (goalsfor == 0) {
            goalsforNumber = 1;
        } else if (goalsfor >= 1 && goalsfor <= 5) {
            goalsforNumber = 2;
        } else if (goalsfor >= 6 && goalsfor <= 10) {
            goalsforNumber = 3;
        } else if (goalsfor >= 11 && goalsfor <= 15) {
            goalsforNumber = 4;
        } else if (goalsfor >= 16 && goalsfor <= 20) {
            goalsforNumber = 5;
        } else if (goalsfor >= 21 && goalsfor <= 25) {
            goalsforNumber = 6;
        } else if (goalsfor >= 26 && goalsfor <= 30) {
            goalsforNumber = 7;
        } else if (goalsfor >= 31 && goalsfor <= 35) {
            goalsforNumber = 8;
        } else if (goalsfor >= 36 && goalsfor <= 40) {
            goalsforNumber = 9;
        } else if (goalsfor > 40) {
            goalsforNumber = 10;
        }
        //助攻
        if (assist == 0) {
            assistNumber = 1;
        } else if (assist >= 1 && assist <= 5) {
            assistNumber = 2;
        } else if (assist >= 6 && assist <= 10) {
            assistNumber = 3;
        } else if (assist >= 11 && assist <= 15) {
            assistNumber = 4;
        } else if (assist >= 16 && assist <= 20) {
            assistNumber = 5;
        } else if (assist >= 21 && assist <= 25) {
            assistNumber = 6;
        } else if (assist >= 26 && assist <= 30) {
            assistNumber = 7;
        } else if (assist >= 31 && assist <= 35) {
            assistNumber = 8;
        } else if (assist >= 36 && assist <= 40) {
            assistNumber = 9;
        } else if (assist > 40) {
            assistNumber = 10;
        }
        //失误
        if (fault > 500) {
            faultNumber = 1;
        } else if (fault >= 401) {
            faultNumber = 2;
        } else if (fault >= 301) {
            faultNumber = 3;
        } else if (fault >= 201) {
            faultNumber = 4;
        } else if (fault >= 101) {
            faultNumber = 5;
        } else if (fault >= 51) {
            faultNumber = 6;
        } else if (fault >= 21) {
            faultNumber = 7;
        } else if (fault >= 11) {
            faultNumber = 8;
        } else if (fault >= 1) {
            faultNumber = 9;
        } else if (fault == 0) {
            faultNumber = 10;
        }
        //射正
        if (shoot == 0) {
            shootNumber = 1;
        } else if (shoot >= 1 && shoot <= 20) {
            shootNumber = 2;
        } else if (shoot >= 24 && shoot <= 40) {
            shootNumber = 3;
        } else if (shoot >= 41 && shoot <= 60) {
            shootNumber = 4;
        } else if (shoot >= 61 && shoot <= 80) {
            shootNumber = 5;
        } else if (shoot >= 81 && shoot <= 100) {
            shootNumber = 6;
        } else if (shoot >= 101 && shoot <= 120) {
            shootNumber = 7;
        } else if (shoot >= 121 && shoot <= 140) {
            shootNumber = 8;
        } else if (shoot >= 141 && shoot <= 160) {
            shootNumber = 9;
        } else if (shoot > 160) {
            shootNumber = 10;
        }
        //抢断
        if (holdUp == 0) {
            holdUpNumber = 1;
        } else if (holdUp >= 1 && holdUp <= 10) {
            holdUpNumber = 2;
        } else if (holdUp >= 11 && holdUp <= 20) {
            holdUpNumber = 3;
        } else if (holdUp >= 21 && holdUp <= 40) {
            holdUpNumber = 4;
        } else if (holdUp >= 41 && holdUp <= 60) {
            holdUpNumber = 5;
        } else if (holdUp >= 61 && holdUp <= 80) {
            holdUpNumber = 6;
        } else if (holdUp >= 81 && holdUp <= 100) {
            holdUpNumber = 7;
        } else if (holdUp >= 101 && holdUp <= 120) {
            holdUpNumber = 8;
        } else if (holdUp >= 121 && holdUp <= 140) {
            holdUpNumber = 9;
        } else if (holdUp > 140) {
            holdUpNumber = 10;
        }
        //过人
        if (excel == 0) {
            excelNumber = 1;
        } else if (excel >= 1 && excel <= 10) {
            excelNumber = 2;
        } else if (excel >= 11 && excel <= 30) {
            excelNumber = 3;
        } else if (excel >= 31 && excel <= 50) {
            excelNumber = 4;
        } else if (excel >= 51 && excel <= 70) {
            excelNumber = 5;
        } else if (excel >= 71 && excel <= 90) {
            excelNumber = 6;
        } else if (excel >= 91 && excel <= 111) {
            excelNumber = 7;
        } else if (excel >= 112 && excel <= 130) {
            excelNumber = 8;
        } else if (excel >= 131 && excel <= 150) {
            excelNumber = 9;
        } else if (excel > 150) {
            excelNumber = 10;
        }
        //红牌
        if (redcard == 0) {
            redCardNumber = 10;
        } else if (redcard == 1) {
            redCardNumber = 8;
        } else if (redcard == 2) {
            redCardNumber = 6;
        } else if (redcard == 3) {
            redCardNumber = 4;
        } else if (redcard > 3) {
            redCardNumber = 2;
        }
        //黄牌
        if (yellowcard > 20) {
            yellowCardNumber = 1;
        } else if (yellowcard >= 17) {
            yellowCardNumber = 2;
        } else if (yellowcard >= 15) {
            yellowCardNumber = 3;
        } else if (yellowcard >= 13) {
            yellowCardNumber = 4;
        } else if (yellowcard >= 11) {
            yellowCardNumber = 5;
        } else if (yellowcard >= 9) {
            yellowCardNumber = 6;
        } else if (yellowcard >= 7) {
            yellowCardNumber = 7;
        } else if (yellowcard >= 5) {
            yellowCardNumber = 8;
        } else if (yellowcard >= 3) {
            yellowCardNumber = 9;
        } else if (yellowcard >= 0) {
            yellowCardNumber = 10;
        }
        //卡路里
        if (calorie == 0) {
            calorieNumber = 1;
        } else if (calorie >= 1 && calorie <= 1700) {
            calorieNumber = 2;
        } else if (calorie >= 1701 && calorie <= 3400) {
            calorieNumber = 3;
        } else if (calorie >= 3401 && calorie <= 8500) {
            calorieNumber = 4;
        } else if (calorie >= 8501 && calorie <= 17000) {
            calorieNumber = 5;
        } else if (calorie >= 17001 && calorie <= 34000) {
            calorieNumber = 6;
        } else if (calorie >= 34001 && calorie <= 51000) {
            calorieNumber = 7;
        } else if (calorie >= 51001 && calorie <= 68000) {
            calorieNumber = 8;
        } else if (calorie >= 68001 && calorie <= 93500) {
            calorieNumber = 9;
        } else if (calorie > 93500) {
            calorieNumber = 10;
        }
        //解围
        if (saveBall == 0) {
            saveBallNumber = 1;
        } else if (saveBall >= 1 && saveBall <= 30) {
            saveBallNumber = 2;
        } else if (saveBall >= 31 && saveBall <= 60) {
            saveBallNumber = 3;
        } else if (saveBall >= 61 && saveBall <= 90) {
            saveBallNumber = 4;
        } else if (saveBall >= 91 && saveBall <= 120) {
            saveBallNumber = 5;
        } else if (saveBall >= 121 && saveBall <= 150) {
            saveBallNumber = 6;
        } else if (saveBall >= 151 && saveBall <= 180) {
            saveBallNumber = 7;
        } else if (saveBall >= 181 && saveBall <= 210) {
            saveBallNumber = 8;
        } else if (saveBall >= 211 && saveBall <= 240) {
            saveBallNumber = 9;
        } else if (saveBall > 240) {
            saveBallNumber = 10;
        }
        //犯规
        if (foul > 80) {
            foulNumber = 1;
        } else if (foul >= 71) {
            foulNumber = 2;
        } else if (foul >= 61) {
            foulNumber = 3;
        } else if (foul >= 51) {
            foulNumber = 4;
        } else if (foul >= 41) {
            foulNumber = 5;
        } else if (foul >= 31) {
            foulNumber = 6;
        } else if (foul >= 21) {
            foulNumber = 7;
        } else if (foul >= 13) {
            foulNumber = 8;
        } else if (foul >= 6) {
            foulNumber = 9;
        } else if (foul >= 0) {
            foulNumber = 10;
        }
        //传球率
        if (passProb == 0) {
            passProbNumber = 1;
        } else if (passProb >= 1 && passProb <= 15) {
            passProbNumber = 2;
        } else if (passProb >= 16 && passProb <= 25) {
            passProbNumber = 3;
        } else if (passProb >= 26 && passProb <= 35) {
            passProbNumber = 4;
        } else if (passProb >= 36 && passProb <= 45) {
            passProbNumber = 5;
        } else if (passProb >= 46 && passProb <= 55) {
            passProbNumber = 6;
        } else if (passProb >= 56 && passProb <= 75) {
            passProbNumber = 7;
        } else if (passProb >= 76 && passProb <= 85) {
            passProbNumber = 8;
        } else if (passProb >= 86 && passProb <= 95) {
            passProbNumber = 9;
        } else if (passProb > 95) {
            passProbNumber = 10;
        }
        //威胁球
        if (dangerBall == 0) {
            dangerBallNumber = 1;
        } else if (dangerBall >= 1 && dangerBall <= 10) {
            dangerBallNumber = 2;
        } else if (dangerBall >= 11 && dangerBall <= 20) {
            dangerBallNumber = 3;
        } else if (dangerBall >= 21 && dangerBall <= 40) {
            dangerBallNumber = 4;
        } else if (dangerBall >= 41 && dangerBall <= 60) {
            dangerBallNumber = 5;
        } else if (dangerBall >= 61 && dangerBall <= 80) {
            dangerBallNumber = 6;
        } else if (dangerBall >= 81 && dangerBall <= 100) {
            dangerBallNumber = 7;
        } else if (dangerBall >= 101 && dangerBall <= 120) {
            dangerBallNumber = 8;
        } else if (dangerBall >= 121 && dangerBall <= 140) {
            dangerBallNumber = 9;
        } else if (dangerBall > 140) {
            dangerBallNumber = 10;
        }
        //带球距离 distance
        if (distance == 0) {
            distanceNumber = 1;
        } else if (distance >= 0.1 && distance <= 1) {
            distanceNumber = 2;
        } else if (distance >= 1.1 && distance <= 2) {
            distanceNumber = 3;
        } else if (distance >= 2.1 && distance <= 5) {
            distanceNumber = 4;
        } else if (distance >= 5.1 && distance <= 10) {
            distanceNumber = 5;
        } else if (distance >= 10.1 && distance <= 20) {
            distanceNumber = 6;
        } else if (distance >= 20.1 && distance <= 30) {
            distanceNumber = 7;
        } else if (distance >= 30.1 && distance <= 40) {
            distanceNumber = 8;
        } else if (distance >= 40.1 && distance <= 55) {
            distanceNumber = 9;
        } else if (distance > 55) {
            distanceNumber = 10;
        }


        map.put("passCountNumber", passCountNumber);
        map.put("moveDistanceNumber", moveDistanceNumber);
        map.put("goalsforNumber", goalsforNumber);
        map.put("assistNumber", assistNumber);
        map.put("faultNumber", faultNumber);
        map.put("shootNumber", shootNumber);
        map.put("holdUpNumber", holdUpNumber);
        map.put("excelNumber", excelNumber);
        map.put("redCardNumber", redCardNumber);
        map.put("yellowCardNumber", yellowCardNumber);
        map.put("calorieNumber", calorieNumber);
        map.put("saveBallNumber", saveBallNumber);
        map.put("foulNumber", foulNumber);
        map.put("passProbNumber", passProbNumber);
        map.put("dangerBallNumber", dangerBallNumber);
        map.put("distanceNumber", distanceNumber);

        Map<Integer, Integer> totalMap = new HashMap<Integer, Integer>();
        totalMap.put(1, goalsforNumber);
        totalMap.put(2, assistNumber);
        totalMap.put(3, moveDistanceNumber);
        totalMap.put(4, passCountNumber);
        totalMap.put(5, shootNumber);
        totalMap.put(6, calorieNumber);
        totalMap.put(7, saveBallNumber);
        totalMap.put(8, foulNumber);
        totalMap.put(9, passProbNumber);
        totalMap.put(10, excelNumber);
        totalMap.put(11, redCardNumber);
        totalMap.put(12, yellowCardNumber);
        totalMap.put(13, holdUpNumber);
        totalMap.put(14, faultNumber);
        totalMap.put(15, dangerBallNumber);
        totalMap.put(16, distanceNumber);

        Float total = 0f;
        if (userRadar != null) {
            map.put("userRadar", JSON.parseArray(userRadar.getTypeArray()));
        } else {
            JSONArray j = new JSONArray();
            j.add(1);
            j.add(2);
            j.add(3);
            j.add(4);
            map.put("userRadar", j);
        }

        JSONArray ja = (JSONArray) map.get("userRadar");
        for (Object o : ja) {
            total += (int) totalMap.get(o);
        }
        total = (float) Math.round(total / ja.size() * 10) / 10;//保留一位小数
        int scale = 0;
        if (total >= 1 && total <= 1.9) {
            scale = 5;
        } else if (total >= 2 && total <= 2.9) {
            scale = 16;
        } else if (total >= 3 && total <= 3.9) {
            scale = 35;
        } else if (total >= 4 && total <= 4.9) {
            scale = 49;
        } else if (total >= 5 && total <= 5.9) {
            scale = 61;
        } else if (total >= 6 && total <= 6.9) {
            scale = 72;
        } else if (total >= 7 && total <= 7.9) {
            scale = 80;
        } else if (total >= 8 && total <= 8.9) {
            scale = 85;
        } else if (total >= 9 && total <= 9.9) {
            scale = 90;
        } else if (total == 10) {
            scale = 99;
        }

        map.put("total", total.toString());
        map.put("scale", scale);
        return map;
    }

    public static List<User> teamUsersToUsers(List<FootballTeamUser> teamUserList) {
        List<User> users = new ArrayList<>();
        if (teamUserList != null && teamUserList.size() > 0) {
            for (FootballTeamUser teamUser : teamUserList) {
                users.add(teamUser.getUser());
            }
        }
        return users;
    }

    public static JSONArray pickMatchNode(JSONArray jArray, List<FootballTeamUser> teamUserList, Map<Long, FootballTeamTakeNode> nodeMap, Map<Long, String> imgMap, Map<Long, Long> poloMap) {
        for (FootballTeamUser teamUser : teamUserList) {
            Map<String, Object> map = new HashMap<>();
            map.put("userId", teamUser.getUser().getId());
            map.put("nickName", teamUser.getUser().getNickName());
            map.put("userImg", imgMap.getOrDefault(teamUser.getUser().getId(), ""));
            map.put("poloNumber", poloMap.getOrDefault(teamUser.getUser().getId(), null));
            pickTakeNode(nodeMap, map, teamUser.getUser());
            jArray.add(map);
        }
        return jArray;
    }

    public static JSONArray pickGameNode(JSONArray jArray, List<FootballTeamGameEnroll> enrollList, Map<Long, FootballTeamTakeNode> nodeMap, Map<Long, String> imgMap, Map<Long, Long> poloMap) {
        for (FootballTeamGameEnroll enroll : enrollList) {
            Map<String, Object> map = new HashMap<String, Object>();
            map.put("userId", enroll.getUser().getId());
            map.put("nickName", enroll.getUser().getNickName());
            map.put("userImg", imgMap.getOrDefault(enroll.getUser().getId(), ""));
            map.put("poloNumber", poloMap.getOrDefault(enroll.getUser().getId(), null));
            pickTakeNode(nodeMap, map, enroll.getUser());
            jArray.add(map);
        }
        return jArray;
    }

    public static Map<String, Object> pickTakeNode(Map<Long, FootballTeamTakeNode> nodeMap, Map<String, Object> map, User user) {
        if (nodeMap.containsKey(user.getId())) {
            FootballTeamTakeNode node = nodeMap.get(user.getId());
            map.put("goalsfor", node.getGoalsfor() == null ? Long.valueOf(0) : node.getGoalsfor());
            map.put("assist", node.getAssist() == null ? Long.valueOf(0) : node.getAssist());
            map.put("shoot", node.getShoot() == null ? Long.valueOf(0) : node.getShoot());
            map.put("shootAside", node.getShootAside() == null ? Long.valueOf(0) : node.getShootAside());
            map.put("waveShot", node.getWaveShot() == null ? Long.valueOf(0) : node.getWaveShot());
            map.put("ownGoal", node.getOwnGoal() == null ? Long.valueOf(0) : node.getOwnGoal());
            map.put("menace", node.getMenace() == null ? Long.valueOf(0) : node.getMenace());
            map.put("holdUp", node.getHoldUp() == null ? Long.valueOf(0) : node.getHoldUp());
            map.put("save", node.getSave() == null ? Long.valueOf(0) : node.getSave());
            map.put("excel", node.getExcel() == null ? Long.valueOf(0) : node.getExcel());
            map.put("foul", node.getFoul() == null ? Long.valueOf(0) : node.getFoul());
            map.put("offSide", node.getOffSide() == null ? Long.valueOf(0) : node.getOffSide());
            map.put("yellowCard", node.getYellowCard() == null ? Long.valueOf(0) : node.getYellowCard());
            map.put("redCard", node.getRedCard() == null ? Long.valueOf(0) : node.getRedCard());
            map.put("freeKick", node.getFreeKick() == null ? Long.valueOf(0) : node.getFreeKick());
            map.put("penaltyKick", node.getPenaltyKick() == null ? Long.valueOf(0) : node.getPenaltyKick());
            map.put("corner", node.getCorner() == null ? Long.valueOf(0) : node.getCorner());
            map.put("roof", node.getRoof() == null ? Long.valueOf(0) : node.getRoof());
            map.put("head", node.getHead() == null ? Long.valueOf(0) : node.getHead());
            map.put("stopFault", node.getStopFault() == null ? Long.valueOf(0) : node.getStopFault());
            map.put("passFault", node.getPassFault() == null ? Long.valueOf(0) : node.getPassFault());
            map.put("defendFault", node.getDefendFault() == null ? Long.valueOf(0) : node.getDefendFault());
            map.put("kickEmpty", node.getKickEmpty() == null ? Long.valueOf(0) : node.getKickEmpty());

        } else {
            map.put("goalsfor", 0L);
            map.put("assist", 0L);
            map.put("shoot", 0L);
            map.put("shootAside", 0L);
            map.put("waveShot", 0L);
            map.put("ownGoal", 0L);
            map.put("menace", 0L);
            map.put("holdUp", 0L);
            map.put("save", 0L);
            map.put("excel", 0L);
            map.put("foul", 0L);
            map.put("offSide", 0L);
            map.put("yellowCard", 0L);
            map.put("redCard", 0L);
            map.put("freeKick", 0L);
            map.put("penaltyKick", 0L);
            map.put("corner", 0L);
            map.put("roof", 0L);
            map.put("head", 0L);
            map.put("stopFault", 0L);
            map.put("passFault", 0L);
            map.put("defendFault", 0L);
            map.put("kickEmpty", 0L);
        }
        return map;
    }

    public boolean saveOnceNode(FootballTeamGame game, FootballTeam team, User user, FootballTeamTakeNodePojo takeNode) throws Exception {
        if (takeNode == null) {
            return false;
        }
        long startTime = game.getCompetitionTime().getTime();
        long endTime = game.getCompetitionTime().getTime() + FootballContants.GAMEDURATION;
        long now = System.currentTimeMillis();


        List<Action> actionList = actionDaoService.findAllAction();
        Map<String, Action> actionMap = (Map<String, Action>) TranslatorUtil.listToMap("actionName", actionList, 1);
        Map<Long, Action> actionLMap = (Map<Long, Action>) TranslatorUtil.listToMap("id", actionList, 1);

        List<FootballTeamGameOnceNode> onceNodeList = footballTeamGameOnceNodeDaoService.findByUserIdAndTeamIdAndGameId(user.getId(), team.getId(), game.getId());
        Map<String, List<FootballTeamGameOnceNode>> onceMap = new HashMap<>();   // key：实体类属性名 value:实体类List
        for (FootballTeamGameOnceNode once : onceNodeList) {
            String key = longToString(once.getActionId(), actionLMap);
            List<FootballTeamGameOnceNode> tempList = new ArrayList<>();
            if (onceMap.containsKey(key)) {
                tempList = onceMap.get(key);
            }
            tempList.add(once);
            onceMap.put(key, tempList);
        }

        FootballTeamTakeNode nowTakeNode = takeNodeDaoService.findPersonTakeNode_New(user.getId(), game.getId(), team.getId());
        if (nowTakeNode == null) {
            nowTakeNode = new FootballTeamTakeNode();
            nowTakeNode.setGoalsfor(0L);
            nowTakeNode.setAssist(0L);
            nowTakeNode.setFault(0L);
            nowTakeNode.setShoot(0L);
            nowTakeNode.setShootAside(0L);
            nowTakeNode.setWaveShot(0L);
            nowTakeNode.setHoldUp(0L);
            nowTakeNode.setExcel(0L);
            nowTakeNode.setCorner(0L);
            nowTakeNode.setFreeKick(0L);
            nowTakeNode.setPenaltyKick(0L);
            nowTakeNode.setRedCard(0L);
            nowTakeNode.setOwnGoal(0L);
            nowTakeNode.setMenace(0L);
            nowTakeNode.setSave(0L);
            nowTakeNode.setFoul(0L);
            nowTakeNode.setRoof(0L);
            nowTakeNode.setOffSide(0L);
            nowTakeNode.setHead(0L);
            nowTakeNode.setStopFault(0L);
            nowTakeNode.setPassFault(0L);
            nowTakeNode.setDefendFault(0L);
            nowTakeNode.setKickEmpty(0L);
            nowTakeNode.setYellowCard(0L);
        }

        Field[] fields = takeNode.getClass().getDeclaredFields();//获得属性
        Class clazz = takeNode.getClass();
        for (Field field : fields) {
            if (!field.getName().equals("id") && !field.getName().equals("user") && !field.getName().equals("footballTeam")
                    && !field.getName().equals("footballTeamNode") && !field.getName().equals("footballTeamGame") && !field.getName().equals("createTime")
                    && !field.getName().equals("updateTime") && !field.getName().equals("deleted") && !field.getName().equals("version")
                    && !field.getName().equals("userId") && !field.getName().equals("teamId") && !field.getName().equals("teamNodeId")
                    && !field.getName().equals("teamGameId")) {
                PropertyDescriptor pd = new PropertyDescriptor(field.getName(), clazz);
                Method getter = pd.getReadMethod();
                int value = 0;
                if (getter.invoke(takeNode) != null) {

                    String getName = "get" + TranslatorUtil.getGetter(field.getName());
                    Method nowgetter = nowTakeNode.getClass().getMethod(getName);
                    int nowNode = 0;
                    if (nowgetter.invoke(nowTakeNode) != null) {
                        nowNode = ((Long) nowgetter.invoke(nowTakeNode)).intValue();
                    }
                    int node = 0;
                    if (getter.invoke(takeNode) != null) {
                        node = ((Long) getter.invoke(takeNode)).intValue();
                    }
                    value = node - nowNode;
                }

                /*int size = 0;
                if (onceMap.containsKey(field.getName())) {
                    size = onceMap.get(field.getName()).size();
                }*/
                int i = value;

                if (i >= 0) {
                    if (now <= endTime && now >= startTime) {
                        for (int j = 0; j < i; j++) {
                            FootballTeamGameOnceNode onceNode = new FootballTeamGameOnceNode();
                            onceNode.setActionId(stringToLong(field.getName(), actionMap));
                            onceNode.setGameId(game.getId());
                            onceNode.setTeamId(team.getId());
                            onceNode.setUserId(user.getId());
                            onceNode.setOccurTime(new Timestamp(System.currentTimeMillis()));
                            onceNode.setCreateTime(new Timestamp(System.currentTimeMillis()));
                            onceNode.setUpdateTime(new Timestamp(System.currentTimeMillis()));
                            onceNode.setDeleted(false);
                            footballTeamGameOnceNodeDaoService.save(onceNode);
                        }
                    }
                } else {
                    if (onceMap.containsKey(field.getName())) {
                        if (onceMap.get(field.getName()).size() >= Math.abs(i)) {
                            for (int j = 0; j < Math.abs(i); j++) {
                                footballTeamGameOnceNodeDaoService.delByGameId(onceMap.get(field.getName()).get(j).getGameId());
                            }
                        } else {
                            for (int j = 0; j < onceMap.get(field.getName()).size(); j++) {
                                footballTeamGameOnceNodeDaoService.delByGameId(onceMap.get(field.getName()).get(j).getGameId());
                            }
                        }
                    }
                }
            }
        }
        return true;
    }

    private static Long stringToLong(String name, Map<String, Action> actionMap) {
        Long id = 0l;

        if (actionMap.containsKey(name)) {
            id = actionMap.get(name).getId();
        }
        return id;
    }

    private static String longToString(Long actonId, Map<Long, Action> actionLMap) {
        String name = "";
        if (actionLMap.containsKey(actonId)) {
            name = actionLMap.get(actonId).getActionName();
        }

        return name;
    }

    public int isUploadOfUser(FootballTeamGame game, FootballTeam team, User user) {
        int isUpload = 0;
        FootballTeamGameStatisticsPlayer player = footballTeamGameStatisticsPlayerDaoService.findByTeamIdAndGameIdAndUserId(team.getId(), game.getId(), user.getId());
        if (player != null) {
            isUpload = 1;
        }
        return isUpload;
    }

    public Map<String, Object> getNodeAndTime(Map<String, Object> map, FootballTeamGame game, FootballTeam team) throws JSONException, NoSuchMethodException, IllegalAccessException, InvocationTargetException {
        //查询进球 助攻 乌龙球 点球的单次时间并分别封装进map里

        Map<Long, List<FootballTeamGameOnceNode>> goalForMap = new LinkedHashMap<>();  // key:userId：用户id  value：occurTime(long):发生时间
        Map<Long, List<FootballTeamGameOnceNode>> assisMap = new LinkedHashMap<>();
        Map<Long, List<FootballTeamGameOnceNode>> ownGoalMap = new LinkedHashMap<>();
        Map<Long, List<FootballTeamGameOnceNode>> pointMap = new LinkedHashMap<>();
        List<Long> actionList = new ArrayList<>();
        actionList.add(1L);
        actionList.add(2L);
        actionList.add(23L);
        List<FootballTeamGameOnceNode> onceNodeList = footballTeamGameOnceNodeDaoService.findByTeamIdAndGameIdAndActionIdList(team.getId(), game.getId(), actionList);
        for (FootballTeamGameOnceNode onceNode : onceNodeList) {
            if (onceNode.getActionId() == 1L) { //进球
                List<FootballTeamGameOnceNode> temp = new ArrayList<>();
                if (goalForMap.containsKey(onceNode.getUserId())) {
                    temp = goalForMap.get(onceNode.getUserId());
                }
                temp.add(onceNode);
                goalForMap.put(onceNode.getUserId(), temp);
            } else if (onceNode.getActionId() == 2L) {   //助攻
                List<FootballTeamGameOnceNode> temp = new ArrayList<>();
                if (assisMap.containsKey(onceNode.getUserId())) {
                    temp = assisMap.get(onceNode.getUserId());
                }
                temp.add(onceNode);
                assisMap.put(onceNode.getUserId(), temp);
            } else if (onceNode.getActionId() == 23L) {  //点球
                List<FootballTeamGameOnceNode> temp = new ArrayList<>();
                if (pointMap.containsKey(onceNode.getUserId())) {
                    temp = pointMap.get(onceNode.getUserId());
                }
                temp.add(onceNode);
                pointMap.put(onceNode.getUserId(), temp);
            }
        }

        FootballTeam rivalTeam;
        if (game.getFootballTeam().getId().equals(team.getId())) {
            rivalTeam = footballTeamDaoService.findTeamsById(game.getGuestTeamId());
        } else {
            rivalTeam = game.getFootballTeam();
        }
        if (rivalTeam == null) {
            rivalTeam = new FootballTeam();
            rivalTeam.setId(0L);
        }

        //乌龙球
        List<Long> actionList2 = new ArrayList<>();
        actionList2.add(12L);
        List<FootballTeamGameOnceNode> onceNoderivalList = footballTeamGameOnceNodeDaoService.findByTeamIdAndGameIdAndActionIdList(rivalTeam.getId(), game.getId(), actionList2);
        for (FootballTeamGameOnceNode onceNode : onceNoderivalList) {
            if (onceNode.getActionId() == 12L) { //乌龙球
                List<FootballTeamGameOnceNode> temp = new ArrayList<>();
                if (ownGoalMap.containsKey(onceNode.getUserId())) {
                    temp = ownGoalMap.get(onceNode.getUserId());
                }
                temp.add(onceNode);
                ownGoalMap.put(onceNode.getUserId(), temp);
            }
        }


        //封装对象

        JSONArray goalForArray = new JSONArray();  // key:userId：用户id  value：occurTime(long):发生时间
        JSONArray assisArray = new JSONArray();
        JSONArray ownGoalArray = new JSONArray();
        JSONArray pointArray = new JSONArray();

        List<FootballTeamTakeNode> nodeList = takeNodeDaoService.findByGameIdAndTeamId(game.getId(), team.getId());
        Map<Long, Long> nodeGoalForMap = getNodeValueMap("goalsfor", nodeList);
        Map<Long, Long> nodeAssisMap = getNodeValueMap("assist", nodeList);
        Map<Long, Long> nodePointMap = getNodeValueMap("penaltyKick", nodeList);

        List<FootballTeamGameEnroll> enrollList = teamGameEnrollDaoService.findByTeamIdAndGameId(team.getId(), game.getId());
        for (FootballTeamGameEnroll enroll : enrollList) {
            User user = enroll.getUser();

            Map<String, Object> tempMap = ShareUtils.convertNodeTime(goalForMap, nodeGoalForMap, game, user, "goalsfor",nodePointMap);
            if (tempMap.size() > 0) {
                goalForArray.add(tempMap);
            }

            Map<String, Object> tempMap2 = ShareUtils.convertNodeTime(assisMap, nodeAssisMap, game, user, "assist",nodePointMap);
            if (tempMap2.size() > 0) {
                assisArray.add(tempMap2);
            }

            Map<String, Object> tempMap3 = ShareUtils.convertNodeTime(pointMap, nodePointMap, game, user, "penaltyKick",nodePointMap);
            if (tempMap3.size() > 0) {
                pointArray.add(tempMap3);
            }
        }

        List<FootballTeamTakeNode> nodeRivalList = takeNodeDaoService.findByGameIdAndTeamId(game.getId(), rivalTeam.getId());
        Map<Long, Long> nodeOwnGoalMap = getNodeValueMap("ownGoal", nodeRivalList);

        List<FootballTeamGameEnroll> enrollRivalList = teamGameEnrollDaoService.findByTeamIdAndGameId(rivalTeam.getId(), game.getId());
        for (FootballTeamGameEnroll enroll : enrollRivalList) {
            User user = enroll.getUser();
            Map<String, Object> tempMap = ShareUtils.convertNodeTime(ownGoalMap, nodeOwnGoalMap, game, user, "ownGoal",nodePointMap);
            if (tempMap.size() > 0) {
                ownGoalArray.add(tempMap);
            }
        }

        //把点球加到进球里边然后给有时间的对象升序排列
        for (int i = 0; i < pointArray.size(); i++) {
            for (int j = 0; j < goalForArray.size(); j++) {
                if (pointArray.getJSONObject(i).getLongValue("userId") == goalForArray.getJSONObject(j).getLongValue("userId")) {
                    JSONArray temparra = pointArray.getJSONObject(i).getJSONArray("when");
                    for (int k = 0; k < temparra.size(); k++) {
                        goalForArray.getJSONObject(j).getJSONArray("when").add(temparra.getJSONObject(k));
                    }
                    //FootballTeamGameUtils.sortForWhenArray(goalForArray.getJSONObject(i).getJSONArray("when"));
                }
            }
        }

        map.put("goalForList", goalForArray);
        map.put("assisList", assisArray);
        map.put("ownGoalList", ownGoalArray);
        /*map.put("pointArray",pointArray);*/
        return map;
    }

    private static Map<Long, Long> getNodeValueMap(String proper, List<FootballTeamTakeNode> nodeList) throws NoSuchMethodException, InvocationTargetException, IllegalAccessException {
        Map<Long, Long> map = new HashMap<>();
        String getName = "get" + TranslatorUtil.getGetter(proper);
        for (FootballTeamTakeNode node : nodeList) {
            Method getter = node.getClass().getMethod(getName);
            Long count = (Long) getter.invoke(node);
            map.put(node.getUser().getId(), count);
        }
        return map;
    }

    /**
     * @param game 比赛
     * @param team 所在球队
     * @param user 用户
     * @return
     */
    public Map<String, Boolean> getStartUpAndUpload(FootballTeamGame game, FootballTeam team, User user) {
        Map<String, Boolean> map = new HashMap<>();
        long nowTime = System.currentTimeMillis();
        long gameEndTime = game.getCompetitionTime().getTime() + FootballContants.GAMEDURATION;
        boolean isGameEnd = false;
        if (nowTime >= game.getCompetitionTime().getTime() + FootballContants.GAMEDURATION) {
            isGameEnd = true;
        }


        FootballTeamGameEnroll enroll = footballTeamGameEnrollDaoService.findAllByTeamIdAndGameIdAndUserId(team.getId(), game.getId(), user.getId());
        List<UserHardwareData> userHardwareDataList = new ArrayList<>();
        List<UserHardware> userHardwareList = new ArrayList<>();
        if (enroll != null) {
            userHardwareDataList = userHardwareDataDaoService.findAllByGameIdAndUserId(game.getId(), user.getId());
            if (isGameEnd) {
                userHardwareList = userHardwareDaoService.findIntersectionByUserIdAndTime(user.getId(), game.getCompetitionTime().getTime(), game.getFinishTime().getTime());
            } else {
                userHardwareList = userHardwareDaoService.findAllByUserId(user.getId());
            }
        }
        boolean isUpload = false;   //是否同步了
        boolean canUpload = true;   //能否上传
        boolean isStartUp = true;   //是否启动了
        if (userHardwareDataList != null && userHardwareDataList.size() > 0) {
            if (gameEndTime > System.currentTimeMillis()) {
                List<Long> hardwareIdList = new ArrayList<>();
                for (UserHardwareData data : userHardwareDataList) {
                    if (data.getIsStartUp() == 3 && !data.isDeleted()) {
                        isUpload = true;
                        canUpload = false;
                        break;
                    } else if (data.getIsStartUp() == 2 && !data.isDeleted()) {
                        hardwareIdList.add(data.getUserHardware().getId());
                    }
                }
                if (!isUpload && canUpload) {
                    if (userHardwareList != null && userHardwareList.size() > 0) {
                        for (UserHardware tmp : userHardwareList) {
                            if (!hardwareIdList.contains(tmp.getId())) {
                                isStartUp = false;
                                canUpload = false;
                            }
                        }
                    } else {
                        isStartUp = false;
                        canUpload = false;
                    }
                }
            } else {
                isStartUp = true;
                isUpload = false;
                canUpload = true;
                for (UserHardwareData data : userHardwareDataList) {
                    if (data.getIsStartUp() == 3 && !data.isDeleted()) {
                        isUpload = true;
                        canUpload = false;
                    }
                    if (data.getIsStartUp() == 1 || data.isDeleted()) {
                        canUpload = false;
                    }
                }
            }
        } else {
            isStartUp = false;
            isUpload = false;
            canUpload = false;
        }
        map.put("isStartUp", isStartUp);
        map.put("isUpload", isUpload);
        map.put("canUpload", canUpload);
        return map;
    }

    public JSONArray getUserNodeList(FootballTeam team, FootballTeamGame game) throws JSONException {
        JSONArray array = new JSONArray();
        if (team != null) {
            //为用户头像 球衣号码 录入数据的查询做准备
            List<FootballTeamUser> teamUserList = footballTeamUserDaoService.findByTeamId(team.getId());

            if (teamUserList != null && teamUserList.size() > 0) {
                List<FootballTeamTakeNode> nodeList = teamGameTakeNodeDaoService.findMatchDataListByTeamAndGame_New(team.getId(), game.getId());
                Map<Long, FootballTeamTakeNode> nodeMap = new HashMap<>();
                Map<Long, String> imgMap = new HashMap<>();
                Map<Long, Long> poloMap = new HashMap<>();
                //手记数据
                if (nodeList != null && nodeList.size() > 0) {
                    for (FootballTeamTakeNode node : nodeList) {
                        nodeMap.put(node.getUser().getId(), node);
                    }
                }

                List<User> userList = FootballTeamUtils.teamUsersToUsers(teamUserList);
                List<Long> userIdList = TranslatorUtil.getIdList(userList, User.class);
                //用户头像
                List<UserHeadimg> userImgs = userHeadimgDaoService.findByUserIdList(userIdList);
                if (userImgs != null && userImgs.size() > 0) {
                    for (UserHeadimg img : userImgs) {
                        imgMap.put(img.getUser().getId(), img.getHeadImgNetUrl() == null ? "" : img.getHeadImgNetUrl());
                    }
                }

                //球衣号码
                List<PoloShirt> poloList = poloShirtDaoService.findByTeamId(team.getId());
                if (poloList != null && poloList.size() > 0) {
                    for (PoloShirt polo : poloList) {
                        poloMap.put(polo.getUser().getId(), polo.getNumber());
                    }
                }

                if (game.isCompetition()) {
                    array = FootballTeamUtils.pickMatchNode(array, teamUserList, nodeMap, imgMap, poloMap);
                } else {
                    List<FootballTeamGameEnroll> enrollList = footballTeamGameEnrollDaoService.findByTeamIdAndGameId(team.getId(), game.getId());
                    array = FootballTeamUtils.pickGameNode(array, enrollList, nodeMap, imgMap, poloMap);
                }
            }
        }
        return array;
    }

    //比赛录入数据归零批处理
    public static boolean theGameTakeNodesMarkZero(Long gameId) {
        PreparedStatement pst = null;
        ResultSet rs = null;
        Connection conn = null;
        JDBCUtil.init();
        try {

            conn = JDBCUtil.getConnection();
            conn.setAutoCommit(false);
            //根据类的字段拼接sql
            String sql = "UPDATE football_team_game_takenotes as takenote SET "
                    + "takenote.goals_for = 0,"
                    + "takenote.assist = 0,"
                    + "takenote.fault = 0,"
                    + "takenote.shoot = 0,"
                    + "takenote.shoot_aside = 0,"
                    + "takenote.wave_shot = 0,"
                    + "takenote.hold_up = 0,"
                    + "takenote.excel = 0,"
                    + "takenote.corner = 0,"
                    + "takenote.free_kick = 0,"
                    + "takenote.penaltykick = 0,"
                    + "takenote.redcard = 0,"
                    + "takenote.yellowcard = 0,"
                    + "takenote.own_goal = 0,"
                    + "takenote.menace = 0, "
                    + "takenote.save = 0, "
                    + "takenote.foul = 0, "
                    + "takenote.offSide = 0, "
                    + "takenote.roof = 0, "
                    + "takenote.head = 0, "
                    + "takenote.stopFault = 0, "
                    + "takenote.passFault = 0, "
                    + "takenote.defendFault = 0, "
                    + "takenote.kickEmpty = 0 "
                    + "WHERE takenote.team_game_id = " + gameId;
            //创建预处理
            pst = conn.prepareStatement(sql);
            pst.addBatch();
            //执行
            pst.executeBatch();
            //清空
            pst.clearBatch();
            //手动提交
            conn.commit();
            return true;

        } catch (Exception e) {
            e.printStackTrace();
            return false;
        } finally {
            JDBCUtil.free(rs, pst, conn);
        }
    }

    public FootballTeamHeadimgRandom getTeamRandomImg() {
        FootballTeamHeadimgRandom teamHead = null;
        List<FootballTeamHeadimgRandom> allRandom = footballTeamHeadimgRandomDaoService.findAllImg();
        while (teamHead == null) {
            int j = (new Random().nextInt(allRandom.size()));
            teamHead = allRandom.get(j);
        }
        return teamHead;
    }

    public JSONArray getSearcheTeamList(List<FootballTeam> teamList, User user) throws JSONException {
        JSONArray array = new JSONArray();
        for (FootballTeam teams : teamList) {
            // 新增查询环信群组
            Groups groups = teams.getGroups();
            Map<String, Object> map = new LinkedHashMap<>();
            // 群组Id
            if (groups != null) {
                map.put("imGroupId", groups.getImGroupId());
            }
            map.put("abbreviationName", teams.getTeamAbbreviation());
            map.put("teamsid", teams.getId()); // 球队id；
            map.put("teamName", teams.getTeamName()); // 球队名称；
            map.put("owner", teams.getUser().getId()); // 球队id；
            map.put("teamsType", "footballTeams"); // 球队种类
            FootballTeamJoinApplicant teamJoinApplicant = footballTeamJoinApplicantDaoService.findByUserIdAndTeamId(user.getId(), teams.getId());
            int result = 0;
            FootballTeamInvitation teamInvitation = footballTeamInvitationDaoService.findByTeamIdAndInviteeId(teams.getId(), user.getId());
            if (teamJoinApplicant == null && teamInvitation != null) {
                result = 1;
            } else if (teamJoinApplicant != null && teamInvitation == null) {
                result = 2;
            } else if (teamJoinApplicant != null && teamInvitation != null) {
                result = 3;
            }
            if (teamInvitation == null && teamJoinApplicant == null) {
                map.put("userJoinStatus", 1);
            } else {
                if (result == 1) {
                    if (teamInvitation.getAudit() == (short) 1) {
                        map.put("userJoinStatus", 2);                       //1
                    } else if (teamInvitation.getAudit() == (short) 2) {
                        map.put("userJoinStatus", 3);                       //2
                    } else if (teamInvitation.getAudit() == (short) 3) {
                        map.put("userJoinStatus", 4);                       //3
                    }
                } else if (result == 2) {
                    if (teamJoinApplicant.getAudit() == (short) 1) {
                        map.put("userJoinStatus", 2);                       //1
                    } else if (teamJoinApplicant.getAudit() == (short) 2) {
                        map.put("userJoinStatus", 3);                          //4
                    } else if (teamJoinApplicant.getAudit() == (short) 3) {
                        map.put("userJoinStatus", 4);
                    }
                } else if (result == 3) {
                    if (teamJoinApplicant.getAudit() == (short) 1) {
                        map.put("userJoinStatus", 2);                   //1
                    } else {
                        map.put("userJoinStatus", 1);                   //4
                    }
                    if (teamInvitation.getAudit() == (short) 1) {
                        map.put("userJoinStatus", 2);                       //1
                    } else if (teamInvitation.getAudit() == (short) 2) {
                        map.put("userJoinStatus", 3);                       //2
                    } else if (teamInvitation.getAudit() == (short) 3) {
                        map.put("userJoinStatus", 4);                       //3
                    }
                }
            }// else
            map.put("teams_credits", teams.getCredits()); // 球队积分；
            map.put("teams_creditsRank", teams.getCreditsRank());
            map.put("teams_winrateRank", teams.getWinRateRank());
            map.put("selfEvaluation", teams.getSelfEvaluation());
            map.put("description", teams.getDescription());
            map.put("isConscribe", teams.getIsConscribe());
            map.put("slogan", teams.getSlogan());
            map.put("teamHistory", teams.getIsHistory());
            map.put("isCampusTeam", teams.getIsCampusTeam());
            map.put("teamsNatureType", teams.getFootballTeamNatureType().getTypeName());
            // 队徽
            map.put("badgeFilename", teams.getTeamBadgeName());
            map.put("badgeimgNeturl", teams.getTeamBadgeNetUrl());
            map.put("badgeLength", teams.getTeamBadgeLength());
            // 队旗
            map.put("pennantFilename", teams.getTeamPennantName());
            map.put("teampennantimgNeturl", teams.getTeamPennantNetUrl());
            map.put("pennantLength", teams.getTeamPennantLength());
            // 头像
            map.put("teamHeadfilename", teams.getTeamHeadImgName());
            map.put("teamHeadimgNeturl", teams.getTeamHeadImgNetUrl());
            map.put("teamHeadlength", teams.getTeamHeadImgLength());
            // 微信2微码
            map.put("teamWeixinQrcodefilename", teams.getTeamWeixinQrcodeName());
            map.put("teamWeixinQrcodeimgNeturl", teams.getTeamWeixinQrcodeNetUrl());
            map.put("teamWeixinQrcodelength", teams.getTeamWeixinQrcodeLength());
            map.put("favorRules", teams.getFavorRules());
            map.put("playStar", teams.getPlayStar());
            map.put("teamRecord", teams.getTeamRecord());
            map.put("teamHonour", teams.getTeamHonour());
            map.put("playerList", teams.getPlayerList());
            map.put("playGround", teams.getPlayGround());
            map.put("leader", teams.getLeader());
            map.put("coach", teams.getCoach());
            map.put("weixinGroup", teams.getWechatGroup());
            map.put("teamCreateTime", teams.getTeamCreateTime().getTime());
            map.put("schoolName", teams.getSchoolName());
            map.put("countryCode", teams.getCountryCode());
            map.put("provinceCode", teams.getProvinceCode());
            map.put("cityCode", teams.getCityCode());
            map.put("countyCode", teams.getCountyCode());

            int memberCount;
            List<FootballTeamUser> listFootballTeamUser = footballTeamUserDaoService.findByTeamId(teams.getId());
            if (listFootballTeamUser != null) {
                memberCount = listFootballTeamUser.size();
            } else {
                memberCount = 0;
            }
            map.put("teamMemberCount", memberCount);
            int haveHardware = userHardwareDaoService.findHaveHardwareOfTeam(teams.getId());
            map.put("haveHardwareCount", haveHardware);
//            FootballTeamMemberCount teamsMemberCount = teamsMemberCountDaoService.findTeamsMemberCountByTeams(teams);
//            JSONObject json = new JSONObject(teamsMemberCount.getFansArray());
//            JSONArray jsonArray = json.getJSONArray("fansArray");
            List<FootballTeamUser> teamUserList = footballTeamUserDaoService.findByTeamIdAndTeamMemberRoleId(teams.getId(), TeamMemberRoleEnum.TEAMFANS.getValue());
            map.put("fansCount", teamUserList.size());
            int isYouthAgencyTeam = 0;
            if (agencyTeamDaoService.findByTeamId(teams.getId()) != null) {
                isYouthAgencyTeam = 1;
            }
            map.put("isYouthAgencyTeam", isYouthAgencyTeam);
            FootballTeamUser teamUser = footballTeamUserDaoService.findByTeamIdAndUserIdReal(teams.getId(), user.getId());
            String userRole = "";
            if (teamUser != null) {
                userRole = teamUser.getFootballTeamMemberRole().getRoleName();
            }
            map.put("userRole", userRole);
            array.add(map);
        }
        return array;
    }

    public static void addStepWidth(int state, StepWidthPojo stepWidthPojo) {
        if (stepWidthPojo == null) {
            stepWidthPojo = new StepWidthPojo();
        }
        double stepCount;
        switch (state) {
            case 1:
                stepCount = 0;
                break;
            case 2:
                stepCount = 2.5 * 0.8;
                stepWidthPojo.setHighStepWidthSum(stepWidthPojo.getHighStepWidthSum() + stepCount);
                break;
            case 3:
                stepCount = 2 * 0.7;
                stepWidthPojo.setMiddleStepWidthSum(stepWidthPojo.getMiddleStepWidthSum() + stepCount);
                break;
            case 4:
                stepCount = 1 * 0.3;
                stepWidthPojo.setLowStepWidthSum(stepWidthPojo.getLowStepWidthSum() + stepCount);
                break;
            case 5:
                stepCount = 0;
                break;
            default:
                stepCount = 0;
                break;
        }
        stepWidthPojo.setStepWidthSum(stepCount + stepWidthPojo.getStepWidthSum());
    }

    public CalorieCurveSumPojo userCalorieCurveData(FootballTeamGameStatisticsPlayer statisticsPlayer) throws JSONException {
        List<FootballTeamGameStatisticsPlayer> list = new ArrayList<>();
        if (statisticsPlayer != null) {
            list.add(statisticsPlayer);
        }
        return this.packageCalorieCurveSumPojo(list);
    }

    public CalorieCurveSumPojo packageCalorieCurveSumPojo(List<FootballTeamGameStatisticsPlayer> list) throws JSONException {
        if (list == null) {
            list = new ArrayList<>();
        }
        Integer one = 0;
        Integer two = 0;
        Integer three = 0;
        Integer four = 0;
        Integer five = 0;
        Integer six = 0;
        Integer seven = 0;
        Integer eight = 0;
        Integer size = list.size();
        for (FootballTeamGameStatisticsPlayer obj : list) {
            String calorieCurveData = obj.getCalorieCurveData();
            JSONObject jsonObject = JSONObject.parseObject(calorieCurveData);
            if (jsonObject.containsKey("one")) {
                one += (Integer) jsonObject.get("one");
            }
            if (jsonObject.containsKey("two")) {
                two += (Integer) jsonObject.get("two");
            }
            if (jsonObject.containsKey("three")) {
                three += (Integer) jsonObject.get("three");
            }
            if (jsonObject.containsKey("four")) {
                four += (Integer) jsonObject.get("four");
            }
            if (jsonObject.containsKey("five")) {
                five += (Integer) jsonObject.get("five");
            }
            if (jsonObject.containsKey("six")) {
                six += (Integer) jsonObject.get("six");
            }
            if (jsonObject.containsKey("seven")) {
                seven += (Integer) jsonObject.get("seven");
            }
            if (jsonObject.containsKey("eight")) {
                eight += (Integer) jsonObject.get("eight");
            }
        }
        CalorieCurveSumPojo calorieCurveSumPojo = new CalorieCurveSumPojo();
        CalorieCurvePojo calorieCurvePojo = new CalorieCurvePojo();
        if (size == 0) {
            calorieCurvePojo.setOne(0);
            calorieCurvePojo.setTwo(0);
            calorieCurvePojo.setThree(0);
            calorieCurvePojo.setFour(0);
            calorieCurvePojo.setFive(0);
            calorieCurvePojo.setSix(0);
            calorieCurvePojo.setSeven(0);
            calorieCurvePojo.setEight(0);
            calorieCurveSumPojo.setCalorieCurvePojo(calorieCurvePojo);
            calorieCurveSumPojo.setSum(0);
            return calorieCurveSumPojo;
        }
        calorieCurvePojo.setOne(one / size);
        two += one;
        calorieCurvePojo.setTwo(two / size);
        three += two;
        calorieCurvePojo.setThree(three / size);
        four += three;
        calorieCurvePojo.setFour(four / size);
        five += four;
        calorieCurvePojo.setFive(five / size);
        six += five;
        calorieCurvePojo.setSix(six / size);
        seven += six;
        calorieCurvePojo.setSeven(seven / size);
        eight += seven;
        calorieCurvePojo.setEight(eight / size);
        calorieCurveSumPojo.setCalorieCurvePojo(calorieCurvePojo);
        Integer sum = eight / size;
        calorieCurveSumPojo.setSum(sum);
        return calorieCurveSumPojo;
    }

    public CalorieCurveSumPojo userPracticeAverCalorieCurveData(User user) throws JSONException {
        List<FootballTeamGameStatisticsPlayer> list = footballTeamGameStatisticsPlayerDaoService.findByUserId(user.getId());
        return this.packageCalorieCurveSumPojo(list);
    }

    /**
     * StepWidthPojo 高中低速带球步数
     */
    public static StepWidthPojo getStepWidthPojoSum(List<KickStatePojo> kickStatePojoList) {
        StepWidthPojo stepWidthPojo = new StepWidthPojo();
        if (kickStatePojoList != null && !kickStatePojoList.isEmpty()) {
            for (KickStatePojo kickStatePojo : kickStatePojoList) {
                addStepWidth(kickStatePojo.getState(), stepWidthPojo);
            }
        }
        return stepWidthPojo;
    }

    public static double calculateMoveDistance(double stepWidthSum, Short height) {
        double moveDistance;
        if (height == null) {
            height = 175;
        }
        moveDistance = Math.round((stepWidthSum * (height.doubleValue() / 100)));
        return moveDistance;
    }

    public static CarryCountPojo getCarryCount(List<KickStatePojo> kickStatePojoList) {
        CarryCountPojo carryCountPojo = new CarryCountPojo();
        int state = 5;
        int sec = 0;
        if (kickStatePojoList != null && !kickStatePojoList.isEmpty()) {
            state = kickStatePojoList.get(0).getState();
            sec = kickStatePojoList.get(0).getSec();
        }
        for (KickStatePojo kickStatePojo : kickStatePojoList) {
            int tmpState = kickStatePojo.getState();
            int tmpSec = kickStatePojo.getSec();
            if (tmpState != state || tmpSec - sec > 3) {
                addCarryCount(state, carryCountPojo);
            }
            state = tmpState;
            sec = tmpSec;
        }
        addCarryCount(state, carryCountPojo);
        return carryCountPojo;
    }

    public static void addCarryCount(int state, CarryCountPojo carryCountPojo) {
        switch (state) {
            case 1:
                break;
            case 2:
                carryCountPojo.setHighCarryCount(carryCountPojo.getHighCarryCount() + 1);
                break;
            case 3:
                carryCountPojo.setMiddleCarryCount(carryCountPojo.getMiddleCarryCount() + 1);
                break;
            case 4:
                carryCountPojo.setLowCarryCount(carryCountPojo.getLowCarryCount() + 1);
                break;
            case 5:
                break;
            default:
                break;
        }
    }
}
