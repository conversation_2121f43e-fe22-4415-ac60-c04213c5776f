package com.microteam.base.agency.dao;

import com.microteam.base.entity.agency.FootballAgencyClassPublicationAlbum;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

public interface FootballAgencyClassPublicationAlbumDao extends JpaRepository<FootballAgencyClassPublicationAlbum, Long>, JpaSpecificationExecutor<FootballAgencyClassPublicationAlbum> {

    @Query("from FootballAgencyClassPublicationAlbum as footballAgencyClassPublicationAlbum " +
            "where footballAgencyClassPublicationAlbum.footballAgencyClassPublication.id = (:publicationId) " +
            "and footballAgencyClassPublicationAlbum.deleted=false ")
    List<FootballAgencyClassPublicationAlbum> findByPublicationId(@Param("publicationId") Long publicationId);
}
