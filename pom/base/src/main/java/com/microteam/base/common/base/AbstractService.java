//package com.microteam.base.common.base;
//
//
//import org.springframework.transaction.annotation.Transactional;
//
//import java.io.Serializable;
//import java.util.List;
//
//@Transactional
//public abstract class AbstractService<T extends Serializable> implements IDaoOperations<T> {
//
//    protected abstract IDaoOperations<T> getDao();
//
//
//    @Override
//    public List<T> findAll() {
//        return getDao().findAll();
//    }
//
//    @Override
//    public T findOne(final long id) {
//        return getDao().findOne(id);
//    }
//
//    @Override
//    public void create(final T entity) {
//        getDao().create(entity);
//    }
//
//    @Override
//    public T save(final T entity) {
//        return getDao().save(entity);
//    }
//
//    @Override
//    public T update(final T entity) {
//        return getDao().update(entity);
//    }
//
//    @Override
//    public void delete(final T entity) {
//        getDao().delete(entity);
//    }
//
//    @Override
//    public void deleteById(long entityId) {
//        getDao().deleteById(entityId);
//    }
//
//}
