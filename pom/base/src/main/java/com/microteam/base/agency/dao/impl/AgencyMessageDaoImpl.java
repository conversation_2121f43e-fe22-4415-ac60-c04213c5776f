//package com.microteam.base.agency.dao.impl;
//
//import com.microteam.base.agency.dao.AgencyMessageDao;
//import com.microteam.base.entity.agency.AgencyMessage;
//import com.microteam.base.common.base.AbstractHibernateDao;
//import org.hibernate.query.Query;
//import org.springframework.stereotype.Repository;
//
//import java.util.List;
//
//@Repository
//public class AgencyMessageDaoImpl extends AbstractHibernateDao<AgencyMessage> implements AgencyMessageDao {
//
//    @Override
//    public List<AgencyMessage> findByAgencyIdOrderByCreateTimeForPage(Long agencyId, int page, int pageSize) {
//        String hql = "from AgencyMessage as agencyMessage " +
//                "left join fetch agencyMessage.agency as agency " +
//                "left join fetch agencyMessage.user as user " +
//                "where agency.id = (:agencyId) " +
//                "and agencyMessage.deleted = false " +
//                "order by agencyMessage.createTime desc ";
//        Query query = getCurrentSession().createQuery(hql)
//                .setParameter("agencyId", agencyId)
//                .setFirstResult((page - 1) * pageSize)
//                .setMaxResults(pageSize);
//        return query.list();
//    }
//}
