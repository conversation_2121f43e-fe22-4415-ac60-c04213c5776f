package com.microteam.base.common.pojo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
//ResponsebodypojoId类的 内容 与输入 资料 不一致时，需要申明JsonIgnoreProperties；

@JsonIgnoreProperties(ignoreUnknown = true)
public class MtJavaServerResponseBodyPojo<T> implements java.io.Serializable {

    private static final long serialVersionUID = 1L;
    //private static final long serialVersionUID = 1L;
    private String action;//请求方式
    private String uri;
    private Long userId; //请求用户的Id；
    private String accessToken;//访问的令牌
    private String data;  //返回的数据
    private String result;  //返回的处理结果
    private String timestamp; //返回请求发送时间，STRING显示；
    private Long duration;//请求持续时间
    private Long userLogId;//记录用户日志的Id

    public MtJavaServerResponseBodyPojo() {
    }

    public MtJavaServerResponseBodyPojo(String action, String uri, Long userId,
                                        String accessToken, String data, String result, String timestamp,
                                        Long duration, Long userLogId) {
        super();
        this.action = action;
        this.uri = uri;
        this.userId = userId;
        this.accessToken = accessToken;
        this.data = data;
        this.result = result;
        this.timestamp = timestamp;
        this.duration = duration;
        this.userLogId = userLogId;
    }

    public String getAction() {
        return action;
    }

    public void setAction(String action) {
        this.action = action;
    }

    public String getUri() {
        return uri;
    }

    public void setUri(String uri) {
        this.uri = uri;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getAccessToken() {
        return accessToken;
    }

    public void setAccessToken(String accessToken) {
        this.accessToken = accessToken;
    }

    public String getData() {
        return data;
    }

    public void setData(String data) {
        this.data = data;
    }

    public String getResult() {
        return result;
    }

    public void setResult(String result) {
        this.result = result;
    }

    public String getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(String timestamp) {
        this.timestamp = timestamp;
    }

    public Long getDuration() {
        return duration;
    }

    public void setDuration(Long duration) {
        this.duration = duration;
    }

    public Long getUserLogId() {
        return userLogId;
    }

    public void setUserLogId(Long userLogId) {
        this.userLogId = userLogId;
    }

}
