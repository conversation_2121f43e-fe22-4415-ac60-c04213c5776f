package com.microteam.base.common.filter.receiver;

import com.alibaba.fastjson.JSON;
import com.microteam.base.common.pojo.MtJavaServerResponseBodyPojo;
import com.microteam.base.entity.user.User;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import javax.servlet.*;
import javax.servlet.annotation.WebFilter;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.net.URLDecoder;

@Component
@WebFilter(urlPatterns = "/*", filterName = "receiverFilter")
@Order(1)
public class ReceiverFilter implements Filter {

    @Override
    public void init(FilterConfig filterConfig) {

    }

    @Override
    public void doFilter(ServletRequest servletRequest, ServletResponse servletResponse, Filter<PERSON>hain filterChain) throws IOException, ServletException {
        HttpServletRequest request = (HttpServletRequest) servletRequest;
        String responseBodyPojoStr = request.getHeader("responseBodyPojo");
        String userStr = request.getHeader("user");
        if (responseBodyPojoStr != null) {
//            responseBodyPojoStr = URLDecoder.decode(responseBodyPojoStr, StandardCharsets.UTF_8);
            responseBodyPojoStr = URLDecoder.decode(responseBodyPojoStr, "UTF-8");
            MtJavaServerResponseBodyPojo responseBodyPojo = JSON.parseObject(responseBodyPojoStr, MtJavaServerResponseBodyPojo.class);
            request.setAttribute("responseBodyPojo", responseBodyPojo);
        }
        if (userStr != null) {
//            userStr = URLDecoder.decode(userStr, StandardCharsets.UTF_8);
            userStr = URLDecoder.decode(userStr, "UTF-8");
            User user = JSON.parseObject(userStr, User.class);
            request.setAttribute("user", user);
        }
        filterChain.doFilter(servletRequest, servletResponse);
    }

    @Override
    public void destroy() {

    }
}
