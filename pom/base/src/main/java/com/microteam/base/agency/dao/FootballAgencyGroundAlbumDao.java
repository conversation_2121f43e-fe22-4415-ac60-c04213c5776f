package com.microteam.base.agency.dao;

import com.microteam.base.entity.agency.FootballAgencyGround;
import com.microteam.base.entity.agency.FootballAgencyGroundAlbum;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

public interface FootballAgencyGroundAlbumDao extends JpaRepository<FootballAgencyGroundAlbum, Long>, JpaSpecificationExecutor<FootballAgencyGroundAlbum> {

    //查询机构场地的相册
    @Query("from FootballAgencyGroundAlbum as agencyGroundAlbum " +
            "where agencyGroundAlbum.footballAgencyGround=?1 " +
            "and agencyGroundAlbum.deleted=false " +
            "order by agencyGroundAlbum.createTime desc ")
    List<FootballAgencyGroundAlbum> findGroundAlbumByAgency(FootballAgencyGround agencyGround);
}
