package com.microteam.base.common.util.user;

import com.microteam.base.entity.team.PoloShirt;
import com.microteam.base.team.service.PoloShirtDaoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class PoloShirtUtil {

    @Autowired
    private PoloShirtDaoService poloShirtDaoService;

    public long getNumberNotUsed(Long teamId) {
        List<PoloShirt> poloShirtList = poloShirtDaoService.findByTeamId(teamId);
        poloShirtList.sort((o1, o2) -> {
            if (o1.getNumber() > o2.getNumber()) {
                return 1;
            }
            if (o1.getNumber().equals(o2.getNumber())) {
                return 0;
            }
            return -1;
        });
        int i = 1;
        for (PoloShirt poloShirt : poloShirtList) {
            if (poloShirt.getNumber() > i) {
                return i;
            }
            i++;
        }
        return i;
    }
}
