package com.microteam.base.entity.agency;


import com.microteam.base.common.base.BaseEntity;
import com.microteam.base.entity.user.User;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;
import java.io.Serializable;

@Entity
@Table(name = "football_agency_ground")
@Getter
@Setter
public class FootballAgencyGround extends BaseEntity implements Serializable {

    @ManyToOne
    @JoinColumn(name = "agencyId", nullable = false)
    private FootballAgency footballAgency;
    @ManyToOne
    @JoinColumn(name = "creator", nullable = false)
    private User user;
    @Column(name = "logoImgNetUrl", length = 250)
    private String logoImgNetUrl;
    @Column(name = "logoImgUrl", length = 250)
    private String logoImgUrl;
    @Column(name = "logoImgName", length = 50)
    private String logoImgName;
    @Column(name = "logoImgLength")
    private Long logoImgLength;
    @Column(name = "groundName", length = 100)
    private String groundName;
    @Column(name = "countryCode", length = 15)
    private String countryCode;
    @Column(name = "provinceCode", length = 15)
    private String provinceCode;
    @Column(name = "cityCode", length = 15)
    private String cityCode;
    @Column(name = "countyCode", length = 15)
    private String countyCode;
    @Column(name = "detailLocation", length = 200)
    private String detailLocation;
    @Column(name = "longitude", length = 20)
    private String longitude;
    @Column(name = "latitude", length = 20)
    private String latitude;
    @Column(name = "introduction", length = 65535)
    private String introduction;
    @Column(name = "remark", length = 65535)
    private String remark;

}
