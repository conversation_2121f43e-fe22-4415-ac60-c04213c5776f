package com.microteam.base.common.filter.page;

import com.microteam.base.common.pojo.MtJavaServerResponseBodyPojo;
import com.microteam.base.common.util.common.CommonUtil;
import org.apache.log4j.Logger;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;

@Aspect
@Component
@Order(10)
public class PageFilterAspect {

    private static Logger logger = Logger.getLogger(PageFilterAspect.class.getName());

    @Around(value = "@annotation(com.microteam.base.common.filter.page.PageFilter)")
    public Object doPageCheck(ProceedingJoinPoint pjp) throws Throwable {
        //目标类名
        String methodName = pjp.getSignature().getName();
        System.out.println(methodName);
        //获取request
        HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
        MtJavaServerResponseBodyPojo responseBodyPojo = (MtJavaServerResponseBodyPojo) request.getAttribute("responseBodyPojo");
        Long createTimeStart = Long.valueOf(request.getHeader("createTimeStart"));
        try {
            String cursorStr = request.getHeader("cursor");
            String pageSizeStr = request.getHeader("pageSize");
            Integer cursor = Integer.valueOf(cursorStr);
            Integer pageSize = Integer.valueOf(pageSizeStr);
            logger.info("========== cursor : " + cursor + " ==========");
            logger.info("========== pageSize : " + pageSize + " ==========");
        } catch (Exception e) {
//            Class claz = pjp.getTarget().getClass();
//            Logger logger = Logger.getLogger(claz.getName());
//            logger.error(e);
            CommonUtil.pageException(responseBodyPojo, createTimeStart);
            return responseBodyPojo;
        }
        return pjp.proceed();
    }
}
