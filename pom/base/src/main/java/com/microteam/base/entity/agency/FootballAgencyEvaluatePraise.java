package com.microteam.base.entity.agency;


import com.microteam.base.common.base.BaseEntity;
import com.microteam.base.entity.user.User;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;
import java.io.Serializable;

@Entity
@Table(name = "football_agency_evaluate_praise")
@Getter
@Setter
public class FootballAgencyEvaluatePraise extends BaseEntity implements Serializable {

    @ManyToOne
    @JoinColumn(name = "evaluateId", nullable = false)
    private FootballAgencyEvaluate footballAgencyEvaluate;
    @ManyToOne
    @JoinColumn(name = "userId", nullable = false)
    private User user;
    @Column(name = "isPraised")
    private Boolean isPraised;

}
