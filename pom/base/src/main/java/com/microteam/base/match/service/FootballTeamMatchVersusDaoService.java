package com.microteam.base.match.service;


import com.microteam.base.entity.match.FootballTeamMatchVersus;

import java.util.List;

public interface FootballTeamMatchVersusDaoService {
    //查询是否已经进行分组了
    List<FootballTeamMatchVersus> findByMatchIdAndUserId(Long matchId, Long userId);

    //查询赛事的分组情况
    List<FootballTeamMatchVersus> findByMatchId(Long matchId);

    //根据淘汰赛类型查询
    List<FootballTeamMatchVersus> findByMatchIdAndContestRound(Long matchId, short contestRound);

    //查询小组赛的对阵
    List<FootballTeamMatchVersus> findVsOfGroupByMatch(Long matchId);

    //根据轮数和赛事查询对阵(分页)
    List<FootballTeamMatchVersus> findByMatchIdAndContestRoundForPage(Long matchId, Short contestRound, int pageSize, int page);

    FootballTeamMatchVersus findById(long id);

    //查询赛事淘汰赛的轮数
    List<FootballTeamMatchVersus> findVsOutGame(Long matchId);

//    boolean saveList(List<FootballTeamMatchVersus> matchVersusList);
}
