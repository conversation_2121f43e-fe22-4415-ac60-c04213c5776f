package com.microteam.base.lesson.dao;


import com.microteam.base.entity.lesson.FootballTeamLesson;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

public interface FootballTeamLessonDao extends JpaRepository<FootballTeamLesson, Long>, JpaSpecificationExecutor<FootballTeamLesson> {

    @Query("from FootballTeamLesson as teamLesson " +
            "where teamLesson.lessonId = (:lessonId) " +
            "and teamLesson.deleted = false")
    List<FootballTeamLesson> findByLessonId(@Param("lessonId") Long lessonId);

    @Query("from FootballTeamLesson as teamLesson " +
            "where teamLesson.teamId in (:teamIdList) " +
            "and teamLesson.deleted = false")
    List<FootballTeamLesson> findByTeamIdList(@Param("teamIdList") List<Long> teamIdList);

    @Query("update FootballTeamLesson as teamLesson " +
            "set teamLesson.deleted=true " +
            "where teamLesson.lessonId in (:lessonIdList) " +
            "and teamLesson.teamId=(:teamId) ")
    @Modifying
    int deleteByLessonIdListAndTeamId(@Param("lessonIdList") List<Long> lessonIdList, @Param("teamId") Long teamId);

    @Query("from FootballTeamLesson as teamLesson " +
            "where teamLesson.teamId = (:teamId) " +
            "and teamLesson.deleted = false")
    List<FootballTeamLesson> findByTeamId(@Param("teamId") Long teamId);

    @Query("delete from FootballTeamLesson as teamLesson " +
            "where teamLesson.teamId = (:teamId) ")
    @Modifying
    int deleteByTeamId(@Param("teamId") Long teamId);

    @Query(value = "select lessonId " +
            "from vsteam_db.football_team_lesson " +
            "where lessonId in ( " +
            "    select lessonId " +
            "    from vsteam_db.football_team_lesson " +
            "    where teamId = (:teamId) " +
            ") " +
            "group by lessonId " +
            "having count(teamId) <= 1 ", nativeQuery = true)
    List<Long> findAloneLessonIdListByTeamId(@Param("teamId") Long teamId);

}
