package com.microteam.base.agency.dao;

import com.microteam.base.entity.agency.FootballAgencyClassNotice;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

public interface FootballAgencyClassNoticeDao extends JpaRepository<FootballAgencyClassNotice, Long>, JpaSpecificationExecutor<FootballAgencyClassNotice> {
    //分页查询通知
    @Query("from FootballAgencyClassNotice as footballAgencyClassNotice " +
            "left join fetch footballAgencyClassNotice.user as user " +
            "where footballAgencyClassNotice.footballAgency.id = (:agencyId) " +
            "and footballAgencyClassNotice.footballAgencyClass.id = (:agencyClassId) " +
            "and footballAgencyClassNotice.deleted=false ")
    List<FootballAgencyClassNotice> findByAgencyIdAndAgencyClassIdForPage(@Param("agencyId") Long agencyId, @Param("agencyClassId") Long agencyClassId, Pageable pageable);

    //通过Id查询
    @Query("from FootballAgencyClassNotice as footballAgencyClassNotice " +
            "left join fetch footballAgencyClassNotice.user as user " +
            "left join fetch footballAgencyClassNotice.footballAgency as footballAgency " +
            "left join fetch footballAgencyClassNotice.footballAgencyClass as footballAgencyClass " +
            "where footballAgencyClassNotice.id=?1 " +
            "and footballAgencyClassNotice.deleted=false ")
    FootballAgencyClassNotice findById(long id);
}
