package com.microteam.base.agency.service.impl;

import com.microteam.base.agency.dao.FootballAgencyClassSubalbumDao;
import com.microteam.base.entity.agency.FootballAgencyClassSubalbum;
import com.microteam.base.agency.service.FootballAgencyClassSubalbumDaoService;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service("footballAgencyClassSubalbumDaoService")
public class FootballAgencyClassSubalbumDaoServiceImpl implements FootballAgencyClassSubalbumDaoService {
    static Logger logger = Logger.getLogger(FootballAgencyClassSubalbumDaoServiceImpl.class.getName());
    @Autowired
    private FootballAgencyClassSubalbumDao dao;

    //查询相册的图片列表
    @Override
    public List<FootballAgencyClassSubalbum> findByAgencyClassAlbumId(Long agencyClassAlbumId) {
        return dao.findByAgencyClassAlbumId(agencyClassAlbumId);
    }

}
