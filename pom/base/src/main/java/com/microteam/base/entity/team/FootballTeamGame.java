package com.microteam.base.entity.team;

import com.microteam.base.common.base.BaseEntity;
import com.microteam.base.entity.user.User;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

@Entity
@Table(name = "football_team_game")
@Getter
@Setter
public class FootballTeamGame extends BaseEntity implements Serializable {
    @ManyToOne
    @JoinColumn(name = "teamId", nullable = false)
    private FootballTeam footballTeam;
    @ManyToOne
    @JoinColumn(name = "publisherId", nullable = false)
    private User user;
    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "publishTime", nullable = false, length = 19)
    private Date publishTime;
    @Column(name = "typeName")
    private Short typeName;
    @Column(name = "clothColor")
    private Short clothColor;
    @Column(name = "guestClothColor")
    private Integer guestClothColor;
    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "competitionTime", length = 19)
    private Date competitionTime;
    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "finishTime")
    private Date finishTime;
    @Column(name = "guestTeamId")
    private Long guestTeamId;
    @Column(name = "opponent", nullable = false, length = 50)
    private String opponent;  //客队名字
    @Column(name = "oppoentHeadImg", length = 250)
    private String oppoentHeadImg;//客队随机头像
    @Column(name = "opponentClothColor")
    private Short opponentClothColor;
    @Column(name = "opponentGuestClothColor")
    private Integer opponentGuestClothColor;
    @Column(name = "attention", length = 200)
    private String attention;
    @Column(name = "longitude")
    private String longitude;
    @Column(name = "latitude")
    private String latitude;
    @Column(name = "countryCode")
    private String countryCode;
    @Column(name = "provinceCode")
    private String provinceCode;
    @Column(name = "cityCode")
    private String cityCode;
    @Column(name = "countyCode")
    private String countyCode;
    @Column(name = "fieldLocation", length = 100)
    private String fieldLocation;
    @Column(name = "location")
    private String location;
    @Column(name = "judge", length = 50)
    private String judge;
    @Column(name = "judgeFee", length = 50)
    private String judgeFee;
    @Column(name = "competitionFee", length = 50)
    private String competitionFee;
    @Column(name = "drinkFee", length = 100)
    private String drinkFee;
    @Column(name = "notice", length = 200)
    private String notice;
    @Column(name = "telephone")
    private String telephone;
    @Column(name = "contact")
    private String contact;
    @Column(name = "isLive")
    private Boolean isLive;
    @Column(name = "isOver")
    private Boolean isOver;
    @Column(name = "hostScore")
    private Integer hostScore;
    @Column(name = "guestScore")
    private Integer guestScore;
    @Column(name = "contestRule")
    private Short contestRule;
    @Column(name = "typeIn", length = 19)
    private Long typeIn;
    @Column(name = "typeInG")
    private long typeInG;
    @Column(name = "competition", nullable = false)
    private boolean competition; //是否是赛事比赛
    @Column(name = "matchVersusId", length = 19)
    private Long matchVersusId; //对阵id
    @Column(name = "matchId", length = 19)
    private Long matchId;//赛事id
    private String hostDesc;
    private String guestDesc;
    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "enrollDeadline", length = 19)
    private Date enrollDeadline;//报名截止时间
    @Column
    private Long groundId;

    public Date getPublishTime() {
        if (this.publishTime == null) {
            return null;
        }
        return (Date) this.publishTime.clone();
    }

    public void setPublishTime(Date publishTime) {
        this.publishTime = (Date) publishTime.clone();
    }


    public Date getCompetitionTime() {
        if (this.competitionTime == null) {
            return null;
        }
        return (Date) this.competitionTime.clone();
    }

    public void setCompetitionTime(Date competitionTime) {
        this.competitionTime = (Date) competitionTime.clone();
    }


    public Date getFinishTime() {
        if (this.finishTime == null) {
            return null;
        }
        return (Date) finishTime.clone();
    }

    public void setFinishTime(Date finishTime) {
        this.finishTime = (Date) finishTime.clone();
    }


    public Date getEnrollDeadline() {
        if (this.enrollDeadline == null) {
            return null;
        }
        return (Date) enrollDeadline.clone();
    }

    public void setEnrollDeadline(Date enrollDeadline) {
        this.enrollDeadline = (Date) enrollDeadline.clone();
    }

}
