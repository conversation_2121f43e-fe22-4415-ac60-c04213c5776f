package com.microteam.base.agency.dao;

import com.microteam.base.entity.agency.FootballAgencyClassEnrollmentMemberAlbum;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

public interface FootballAgencyClassEnrollmentMemberAlbumDao extends JpaRepository<FootballAgencyClassEnrollmentMemberAlbum, Long>, JpaSpecificationExecutor<FootballAgencyClassEnrollmentMemberAlbum> {

    @Query("from FootballAgencyClassEnrollmentMemberAlbum as enrollmentMemberAlbum " +
            "where enrollmentMemberAlbum.footballAgencyClassEnrollmentMember.id = (:enrollmentMemberId) " +
            "and enrollmentMemberAlbum.deleted=false " +
            "order by enrollmentMemberAlbum.createTime desc ")
    List<FootballAgencyClassEnrollmentMemberAlbum> findByEnrollmentMemberId(@Param("enrollmentMemberId") Long enrollmentMemberId);
}
