package com.microteam.base.entity.telMailValiManage;


import com.microteam.base.common.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.io.Serializable;

@Entity
@Table(name = "tel_mail_vali_info")
@Getter
@Setter
public class TelMailValiInfo extends BaseEntity implements Serializable {

    @Column(name = "valiAccount", nullable = false, length = 50)
    private String valiAccount;
    @Column(name = "valiMessage", length = 50)
    private String valiMessage;
    @Column(name = "valiStartTime")
    private Long valiStartTime;
    @Column(name = "valiEndTime")
    private Long valiEndTime;
    @Column(name = "isValidated")
    private Boolean isValidated;
    @Column(name = "isRetrieveSecret")
    private Boolean isRetrieveSecret;
    @Column(name = "enabled", nullable = false)
    private boolean enabled;

}
