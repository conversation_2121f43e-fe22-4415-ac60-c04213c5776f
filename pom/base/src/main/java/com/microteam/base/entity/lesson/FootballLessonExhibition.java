package com.microteam.base.entity.lesson;


import com.microteam.base.common.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

@Entity
@Table(name = "football_lesson_exhibition")
@Getter
@Setter
public class FootballLessonExhibition extends BaseEntity implements Serializable {

    @Column(name = "lessonId",nullable = false)
    private Long lessonId;
    @Column(name = "userId", nullable = false)
    private Long userId;
    @Column(name = "lessonName")
    private String lessonName;
    @Column(name = "lastArr", length = 500)
    private String lastArr;
    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "startTime", nullable = false, length = 19)
    private Date startTime;
    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "endTime",nullable = false,length = 19)
    private Date endTime;
    @Column(name = "duration")
    private Integer duration;

    public Date getStartTime() {
        if (this.startTime == null) {
            return null;
        }
        return (Date) startTime.clone();
    }

    public void setStartTime(Date startTime) {
        this.startTime = (Date) startTime.clone();
    }

    public Date getEndTime(){
        if(this.endTime == null){
            return null;
        }
        return (Date) endTime.clone();
    }

    public void setEndTime(Date endTime) {
        this.endTime = (Date) endTime.clone();
    }
}
