package com.microteam.base.demoManager.service.impl;

import com.microteam.base.demoManager.dao.DemoUserDao;
import com.microteam.base.demoManager.service.DemoUserDaoService;
import com.microteam.base.entity.demoManager.DemoUser;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service("demoUserDaoService")
public class DemoUserDaoServiceImpl implements DemoUserDaoService {

    @Resource(name = "demoUserDao")
    private DemoUserDao dao;

    @Override
    public List<DemoUser> findAllUser() {
        return dao.findAllUser();
    }

    @Override
    public DemoUser findUserByUserName(String userName) {
        if (userName != null) {
            return dao.findUserByUserName(userName);
        }
        return null;
    }

}
