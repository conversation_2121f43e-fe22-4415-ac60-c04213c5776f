package com.microteam.base.common.util.team;

import com.microteam.base.common.pojo.MtJavaServerResponseBodyPojo;
import com.microteam.base.common.pojo.MtJavaServerResponseBodyResultPojo;
import com.microteam.base.common.pojo.team.FollowTeamsRequestBodyPojo;
import com.microteam.base.common.util.common.CommonUtil;
import com.microteam.base.common.util.user.DataUtil;
import com.microteam.base.entity.team.FootballTeam;
import com.microteam.base.entity.team.FootballTeamMemberRole;
import com.microteam.base.entity.team.FootballTeamUser;
import com.microteam.base.entity.user.User;
import com.microteam.base.team.service.FootballTeamMemberRoleDaoService;
import com.microteam.base.team.service.FootballTeamUserDaoService;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.sql.Timestamp;

@Component
public class TeamUserUtil {
    static Logger logger = Logger.getLogger(TeamUserUtil.class.getName());
    @Autowired
    private DataUtil dataUtil;
    @Autowired
    private FootballTeamUserDaoService footballTeamUserDaoService;
    @Autowired
    private FootballTeamMemberRoleDaoService footballTeamMemberRoleDaoService;
//    @Autowired
//    private FootballTeamMemberCountDaoService footballTeamMemberCountDaoService;
//    @Autowired
//    private FootballTeamMemberUserCountDaoService footballTeamMemberUserCountDaoService;

    public MtJavaServerResponseBodyPojo saveTeamsUser(
            MtJavaServerResponseBodyPojo result,
            FollowTeamsRequestBodyPojo followTeamsRequestBodyPojo, User user,
            FootballTeam teams, String saveMode) {
        MtJavaServerResponseBodyResultPojo responseBodyResultPojo = new MtJavaServerResponseBodyResultPojo();
        try {
            FootballTeamUser teamsUser;
            FootballTeamMemberRole teamsMemberRole = footballTeamMemberRoleDaoService.findByRoleName(followTeamsRequestBodyPojo.getTeamsMemberRole());
            // gerald add redis
            if (teamsMemberRole == null) {
                responseBodyResultPojo.setCode("2025");
                responseBodyResultPojo.setMessage("teamMemberRole is null.");
                String resultString = CommonUtil.changeResultToString(responseBodyResultPojo);
                result.setResult(resultString);
                return result;
            }
            // new save
            Timestamp dateTime = new Timestamp(System.currentTimeMillis());
            /* 增加teams_user -gerald */
            teamsUser = footballTeamUserDaoService.findByTeamIdAndUserId(teams.getId(), user.getId());
            // gerald add redis
            if (teamsUser == null) {
                teamsUser = new FootballTeamUser();
                teamsUser.setCreateTime(dateTime);
                teamsUser.setDeleted(false);
                teamsUser.setAudit(1);
                teamsUser.setAuditTime(dateTime);
                teamsUser.setUser(user);
                teamsUser.setFootballTeam(teams);
                teamsUser.setJoinTime(dateTime);
                teamsUser.setFootballTeamMemberRole(teamsMemberRole);
                teamsUser.setIsFollowed(false);

            }
            /* 增加teams_user -gerald */
            if ("addFollowTeams".endsWith(saveMode)) {
                teamsUser.setIsLeaved(false);
//                teamsUser.setLeaveTime(null);
                teamsUser.setIsFollowed(true);
            }

            if (followTeamsRequestBodyPojo.getJoinMode() == 0) {
                teamsUser.setJoinMode((short) 1);
            } else {
                teamsUser.setJoinMode((short) followTeamsRequestBodyPojo
                        .getJoinMode());
            }
            if ((followTeamsRequestBodyPojo.getJoinMessage() != null)
                    && !followTeamsRequestBodyPojo.getJoinMessage().equals(""))
                teamsUser.setJoinMessage(followTeamsRequestBodyPojo
                        .getJoinMessage());
            if (followTeamsRequestBodyPojo.getJoinedMatch() != null
                    && !"".equals(followTeamsRequestBodyPojo.getJoinedMatch())) {
                teamsUser.setJoinedMatch(followTeamsRequestBodyPojo
                        .getJoinedMatch());
            }
            // 擅长位置:1:forward（前锋）2:centerforward(中场)3:guard（后卫）4:goalkeeper（守门员）
            if (followTeamsRequestBodyPojo.getTeamPosition() != 0) {
                teamsUser.setTeamPosition((short) followTeamsRequestBodyPojo
                        .getTeamPosition());
            }
            // 球队号码
            if (followTeamsRequestBodyPojo.getTeamNumber() != 0) {
                teamsUser.setTeamNumber(followTeamsRequestBodyPojo
                        .getTeamNumber());
            }
            teamsUser.setUpdateTime(dateTime);

            teamsUser = footballTeamUserDaoService.save(teamsUser);
            if (teamsUser.getId() == null) {
                responseBodyResultPojo.setCode("2026");
                responseBodyResultPojo.setMessage("save teamUser error.");
                String resultString = CommonUtil.changeResultToString(responseBodyResultPojo);
                result.setResult(resultString);
                return result;

            }
            responseBodyResultPojo.setCode("0000");
            responseBodyResultPojo.setMessage("success.");
            String resultString = CommonUtil.changeResultToString(responseBodyResultPojo);
            result.setResult(resultString);
        } catch (Exception e) {
            logger.debug("followTeams save TeamsUser Exception Error." + e.getMessage());
            responseBodyResultPojo.setCode("2026");
            responseBodyResultPojo.setMessage("followTeams save TeamsUser Exception Error.");
            String resultString = CommonUtil.changeResultToString(responseBodyResultPojo);
            result.setResult(resultString);
        }
        return result;
    }

}
