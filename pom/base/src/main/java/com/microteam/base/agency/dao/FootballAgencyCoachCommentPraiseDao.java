package com.microteam.base.agency.dao;

import com.microteam.base.entity.agency.FootballAgencyCoachCommentPraise;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

public interface FootballAgencyCoachCommentPraiseDao extends JpaRepository<FootballAgencyCoachCommentPraise, Long>, JpaSpecificationExecutor<FootballAgencyCoachCommentPraise> {

    @Query("from FootballAgencyCoachCommentPraise as agencyCoachCommentPraise " +
            "where agencyCoachCommentPraise.footballAgencyCoachComment.id = (:coachCommentId) " +
            "and agencyCoachCommentPraise.isPraised=true " +
            "and agencyCoachCommentPraise.deleted=false ")
    List<FootballAgencyCoachCommentPraise> findByCoachCommentId(@Param("coachCommentId") Long coachCommentId);

    @Query("from FootballAgencyCoachCommentPraise as agencyCoachCommentPraise " +
            "where agencyCoachCommentPraise.footballAgencyCoachComment.id = (:coachCommentId) " +
            "and agencyCoachCommentPraise.user.id = (:userId) " +
            "and agencyCoachCommentPraise.deleted=false ")
    FootballAgencyCoachCommentPraise findByCoachCommentIdAndUserId(@Param("coachCommentId") Long coachCommentId, @Param("userId") Long userId);
} 
