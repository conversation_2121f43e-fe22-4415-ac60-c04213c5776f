package com.microteam.base.common.util.imServerManage.pojo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
*  IM Response body  pojo类；；
* 
* @param 
* @param 
* @return
*/
@JsonIgnoreProperties(ignoreUnknown = true)
public class ImServerResponseBodyTestPojo implements java.io.Serializable {

	/**
	 * 
	 */
	private String action;
	private String application;
	private ImResponseParams params; //?????
	private String path;
	private String uri;            
	private List<Map<String,Object>> entities; //?????entities
	private List<Map<String,Object>> data; 
	private Date timestamp; //注意时间类型；java.util.Date
	private Long duration;
	private String organization;
	private String applicationName;
	private int count;
	private String cursor;

	public ImServerResponseBodyTestPojo() {
	}

	public ImServerResponseBodyTestPojo(String action, String application,
			ImResponseParams params, String path, String uri,
			List<Map<String, Object>> entities, List<Map<String, Object>> data,
			Date timestamp, Long duration, String organization,
			String applicationName, int count, String cursor) {
		super();
		this.action = action;
		this.application = application;
		this.params = params;
		this.path = path;
		this.uri = uri;
		this.entities = entities;
		this.data = data;
		this.timestamp = timestamp;
		this.duration = duration;
		this.organization = organization;
		this.applicationName = applicationName;
		this.count = count;
		this.cursor = cursor;
	}

	public String getAction() {
		return action;
	}

	public void setAction(String action) {
		this.action = action;
	}

	public String getApplication() {
		return application;
	}

	public void setApplication(String application) {
		this.application = application;
	}

	public ImResponseParams getParams() {
		return params;
	}

	public void setParams(ImResponseParams params) {
		this.params = params;
	}

	public String getPath() {
		return path;
	}

	public void setPath(String path) {
		this.path = path;
	}

	public String getUri() {
		return uri;
	}

	public void setUri(String uri) {
		this.uri = uri;
	}

	public List<Map<String, Object>> getEntities() {
		return entities;
	}

	public void setEntities(List<Map<String, Object>> entities) {
		this.entities = entities;
	}

	public List<Map<String, Object>> getData() {
		return data;
	}

	public void setData(List<Map<String, Object>> data) {
		this.data = data;
	}

	public Date getTimestamp() {
		return timestamp;
	}

	public void setTimestamp(Date timestamp) {
		this.timestamp = timestamp;
	}

	public Long getDuration() {
		return duration;
	}

	public void setDuration(Long duration) {
		this.duration = duration;
	}

	public String getOrganization() {
		return organization;
	}

	public void setOrganization(String organization) {
		this.organization = organization;
	}

	public String getApplicationName() {
		return applicationName;
	}

	public void setApplicationName(String applicationName) {
		this.applicationName = applicationName;
	}

	public int getCount() {
		return count;
	}

	public void setCount(int count) {
		this.count = count;
	}

	public String getCursor() {
		return cursor;
	}

	public void setCursor(String cursor) {
		this.cursor = cursor;
	}



	



	
}