package com.microteam.base.entity.lesson;

import com.microteam.base.common.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.io.Serializable;

@Entity
@Table(name = "football_lesson_datatype")
@Getter
@Setter
public class FootballLessonDataType extends BaseEntity implements Serializable {

    @Column(name = "lessonId")
    private Long lessonId;
    @Column(name = "dataTypeId", nullable = false)
    private Long dataTypeId;
    @Column(name = "courwareId")
    private Long courwareId;
}
