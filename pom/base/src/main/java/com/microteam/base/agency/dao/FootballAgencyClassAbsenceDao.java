package com.microteam.base.agency.dao;

import com.microteam.base.entity.agency.FootballAgencyClassAbsence;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

public interface FootballAgencyClassAbsenceDao extends JpaRepository<FootballAgencyClassAbsence, Long>, JpaSpecificationExecutor<FootballAgencyClassAbsence> {

    @Query("from FootballAgencyClassAbsence as classAbsence " +
            "left join fetch classAbsence.user as user " +
            "where classAbsence.id=?1 " +
            "and classAbsence.deleted=false ")
    FootballAgencyClassAbsence findAbsenceById(long id);

    //分页查询请假表
    @Query("from FootballAgencyClassAbsence as agencyClassAbsence " +
            "left join fetch agencyClassAbsence.user as user " +
            "left join fetch agencyClassAbsence.footballAgencyClassCourse as footballAgencyClassCourse " +
            "where agencyClassAbsence.footballAgency.id = (:agencyId) " +
            "and agencyClassAbsence.footballAgencyClass.id = (:agencyClassId) " +
            "and agencyClassAbsence.deleted=false")
    List<FootballAgencyClassAbsence> findByAgencyIdAndAgencyClassIdForPage(@Param("agencyId") Long agencyId, @Param("agencyClassId") Long agencyClassId, Pageable pageable);

    //查询班级课程请假名单
    @Query("from FootballAgencyClassAbsence as agencyClassAbsence " +
            "left join fetch agencyClassAbsence.user as user " +
            "where agencyClassAbsence.footballAgency.id = (:agencyId) " +
            "and agencyClassAbsence.footballAgencyClass.id = (:agencyClassId) " +
            "and agencyClassAbsence.footballAgencyClassCourse.id = (:agencyClassCourseId) " +
            "and agencyClassAbsence.deleted=false")
    List<FootballAgencyClassAbsence> findByAgencyIdAndAgencyClassIdAndAgencyClassCourseId(@Param("agencyId") Long agencyId, @Param("agencyClassId") Long agencyClassId, @Param("agencyClassCourseId") Long agencyClassCourseId);
}
