package com.microteam.base.grounds.service.impl;

import com.microteam.base.grounds.dao.FootballGroundTypeDao;
import com.microteam.base.grounds.service.FootballGroundTypeDaoService;
import com.microteam.base.entity.grounds.FootballGroundType;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;


@Service("footballGroundTypeDaoService")
public class FootballGroundTypeDaoServiceImpl implements FootballGroundTypeDaoService {
    static Logger logger = Logger.getLogger(FootballGroundTypeDaoServiceImpl.class.getName());

    @Resource(name = "footballGroundTypeDao")
    private FootballGroundTypeDao dao;

    @Override
    public FootballGroundType findByTypeName(String typeName) {
        return dao.findByTypeName(typeName);
    }

}
