package com.microteam.base.entity.integral;

import com.microteam.base.common.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.io.Serializable;
import java.sql.Timestamp;

@Entity
@Table(name = "football_integral_team")
@Getter
@Setter
public class FootballIntegralTeam extends BaseEntity implements Serializable {

    @Basic
    @Column(name = "teamId")
    private Long teamId;
    @Basic
    @Column(name = "integral", nullable = false)
    private int integral;
    @Basic
    @Column(name = "lastAdd")
    private Long lastAdd;

    public FootballIntegralTeam() {

    }

    public FootballIntegralTeam(Long teamId) {
        this.teamId = teamId;
        this.integral = 0;
        this.lastAdd = 0L;
        this.createTime = new Timestamp(System.currentTimeMillis());
        this.deleted = false;
    }

}
