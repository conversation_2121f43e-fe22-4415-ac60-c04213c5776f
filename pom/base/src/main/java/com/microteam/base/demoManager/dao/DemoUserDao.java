package com.microteam.base.demoManager.dao;


import com.microteam.base.entity.demoManager.DemoUser;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

public interface DemoUserDao extends JpaRepository<DemoUser, Long>, JpaSpecificationExecutor<DemoUser> {

    @Query("from DemoUser as user " +
            "where user.deleted = false")
    List<DemoUser> findAllUser();

    @Query("from DemoUser as user " +
            "where user.userName = :userName " +
            "and user.deleted = false")
    DemoUser findUserByUserName(@Param("userName") String userName);
}
