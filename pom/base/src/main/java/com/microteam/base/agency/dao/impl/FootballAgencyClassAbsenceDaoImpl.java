//package com.microteam.base.agency.dao.impl;
//
//import com.microteam.base.agency.dao.FootballAgencyClassAbsenceDao;
//import com.microteam.base.entity.agency.FootballAgencyClassAbsence;
//import com.microteam.base.common.base.AbstractHibernateDao;
//import org.apache.log4j.Logger;
//import org.hibernate.query.Query;
//import org.springframework.stereotype.Repository;
//
//import java.util.List;
//
//@Repository("footballAgencyClassAbsenceDao")
//public class FootballAgencyClassAbsenceDaoImpl extends AbstractHibernateDao<FootballAgencyClassAbsence> implements FootballAgencyClassAbsenceDao {
//    static Logger logger = Logger.getLogger(FootballAgencyClassAbsenceDaoImpl.class.getName());
//
//    public FootballAgencyClassAbsenceDaoImpl() {
//        super();
//        setClazz(FootballAgencyClassAbsence.class);
//    }
//
//    @Override
//    public FootballAgencyClassAbsence findAbsenceById(long id) {
//        String hql = "from FootballAgencyClassAbsence as classAbsence " +
//                "left join fetch classAbsence.user as user " +
//                "where classAbsence.id=? " +
//                "and classAbsence.deleted=false ";
//        Query query = getCurrentSession().createQuery(hql).setParameter(0, id);
//        List<FootballAgencyClassAbsence> list = query.list();
//        if (list != null && list.size() > 0) {
//            return list.get(0);
//        }
//        return null;
//    }
//
//    //分页查询请假表
//    @Override
//    public List<FootballAgencyClassAbsence> findByAgencyIdAndAgencyClassIdForPage(Long agencyId, Long agencyClassId, int pageSize, int page) {
//        String hql = "from FootballAgencyClassAbsence as agencyClassAbsence " +
//                "left join fetch agencyClassAbsence.user as user " +
//                "left join fetch agencyClassAbsence.footballAgencyClassCourse as footballAgencyClassCourse " +
//                "where agencyClassAbsence.footballAgency.id = (:agencyId) " +
//                "and agencyClassAbsence.footballAgencyClass.id = (:agencyClassId) " +
//                "and agencyClassAbsence.deleted=false";
//        Query query = getCurrentSession().createQuery(hql)
//                .setParameter("agencyId", agencyId)
//                .setParameter("agencyClassId", agencyClassId);
//        query.setFirstResult((page - 1) * pageSize);
//        query.setMaxResults(pageSize);
//        List<FootballAgencyClassAbsence> list = query.list();
//        return list;
//    }
//
//    //查询班级课程请假名单
//    @Override
//    public List<FootballAgencyClassAbsence> findByAgencyIdAndAgencyClassIdAndAgencyClassCourseId(Long agencyId, Long agencyClassId, Long agencyClassCourseId) {
//        String hql = "from FootballAgencyClassAbsence as agencyClassAbsence " +
//                "left join fetch agencyClassAbsence.user as user " +
//                "where agencyClassAbsence.footballAgency.id = (:agencyId) " +
//                "and agencyClassAbsence.footballAgencyClass.id = (:agencyClassId) " +
//                "and agencyClassAbsence.footballAgencyClassCourse.id = (:agencyClassCourseId) " +
//                "and agencyClassAbsence.deleted=false";
//        Query query = getCurrentSession().createQuery(hql)
//                .setParameter("agencyId", agencyId)
//                .setParameter("agencyClassId", agencyClassId)
//                .setParameter("agencyClassCourseId", agencyClassCourseId);
//        List<FootballAgencyClassAbsence> list = query.list();
//        return list;
//    }
//
//}
