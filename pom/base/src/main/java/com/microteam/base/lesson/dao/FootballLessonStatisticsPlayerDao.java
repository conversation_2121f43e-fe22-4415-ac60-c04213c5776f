package com.microteam.base.lesson.dao;


import com.microteam.base.common.pojo.lesson.LessonData;
import com.microteam.base.common.pojo.team.FootTeamballPassballAverage;
import com.microteam.base.entity.lesson.FootballLessonStatisticsPlayer;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import javax.persistence.Tuple;
import java.util.List;

public interface FootballLessonStatisticsPlayerDao extends JpaRepository<FootballLessonStatisticsPlayer, Long>, JpaSpecificationExecutor<FootballLessonStatisticsPlayer> {

    @Query("from FootballLessonStatisticsPlayer as player " +
            "where player.userId = (:userId) " +
            "and player.lessonId = (:lessonId) " +
            "and player.deleted = false ")
    FootballLessonStatisticsPlayer findByUserIdAndLessonId(@Param("userId") Long userId, @Param("lessonId") Long lessonId);

    @Query("delete from FootballLessonStatisticsPlayer as player " +
            "where player.userId = (:userId) " +
            "and player.lessonId = (:lessonId) " +
            "and player.deleted = false ")
    @Modifying
    boolean delByUserIdAndLessonId(@Param("userId") Long userId, @Param("lessonId") Long lessonId);

    @Query("from FootballLessonStatisticsPlayer as player " +
            "where player.lessonId = (:lessonId) " +
            "and player.deleted = false ")
    List<FootballLessonStatisticsPlayer> findByLessonId(@Param("lessonId") Long lessonId);

    @Query("from FootballLessonStatisticsPlayer footballLessonStatisticsPlayer " +
            "where footballLessonStatisticsPlayer.lessonId = (:lessonId) " +
            "and footballLessonStatisticsPlayer.userId in (:userIdList) " +
            "and footballLessonStatisticsPlayer.deleted = false ")
    List<FootballLessonStatisticsPlayer> findByLessonIdAndUserIdList(@Param("lessonId") Long lessonId, @Param("userIdList") List<Long> userIdList);

    @Query(value = "SELECT " +
            "IFNULL(SUM(player.wholeMoveDistance),0) as wholeMoveDistance," +
            "IFNULL(SUM(player.passBallCounts),0) as passBallCounts," +
            "IFNULL(SUM(player.carryDistance),0)  as carryDistance," +
            "IFNULL(MAX(player.maxSprintSpeed),0) as maxSprintSpeed," +
            "IFNULL(SUM(player.passBallError),0) as passBallError, " +

            "IFNULL(SUM(player.highMoveDistance),0) as highMoveDistance, " +
            "IFNULL(SUM(player.highMoveCount),0) as highMoveCount, " +
            "IFNULL(SUM(player.midMoveDistance),0) as midMoveDistance, " +
            "IFNULL(SUM(player.midMoveCount),0) as midMoveCount, " +
            "IFNULL(SUM(player.lowMoveDistance),0) as lowMoveDistance, " +
            "IFNULL(SUM(player.lowMoveCount),0) as lowMoveCount, " +
            "IFNULL(SUM(player.normalMoveDistance),0) as normalMoveDistance, " +
            "IFNULL(SUM(player.normalMoveCount),0) as normalMoveCount, " +

            "IFNULL(SUM(player.highCarryDistance),0) as highCarryDistance, " +
            "IFNULL(SUM(player.highCarryCount),0) as highCarryCount, " +
            "IFNULL(SUM(player.midCarryDistance),0) as midCarryDistance, " +
            "IFNULL(SUM(player.midCarryCount),0) as midCarryCount, " +
            "IFNULL(SUM(player.lowCarryDistance),0) as lowCarryDistance, " +
            "IFNULL(SUM(player.lowCarryCount),0) as lowCarryCount, " +
            "IFNULL(SUM(player.normalCarryDistance),0) as normalCarryDistance, " +
            "IFNULL(SUM(player.normalCarryCount),0) as normalCarryCount  " +
            "from football_lesson_statistics_player as player " +
            "WHERE player.deleted = 0 " +
            "AND player.lessonId = :lessonId " +
            "and player.teamId = :teamId ", nativeQuery = true)
    LessonData getDataByLessonIdAndTeamId(@Param("lessonId") Long lessonId, @Param("teamId") Long teamId);

    @Query(value = "SELECT " +
            "IFNULL(AVG(temp.wholeMoveDistance),0)  as wholeMoveDistance," +
            "IFNULL(SUM(temp.passBallCounts),0)  as passBallCounts," +
            "IFNULL(AVG(temp.carryDistance),0)  as carryDistance," +
            "IFNULL(MAX(temp.maxSprintSpeed),0)  as maxSprintSpeed," +
            "IFNULL(SUM(temp.passBallError),0)  as passBallError " +
            "FROM " +
            "(SELECT " +
            "IFNULL(SUM(player.wholeMoveDistance),0)  as wholeMoveDistance," +
            "IFNULL(SUM(player.passBallCounts),0)  as passBallCounts," +
            "IFNULL(SUM(player.carryDistance),0)  as carryDistance," +
            "IFNULL(MAX(player.maxSprintSpeed),0)  as maxSprintSpeed," +
            "IFNULL(SUM(player.passBallError),0)  as passBallError " +
            "from football_lesson_statistics_player as player " +
            "WHERE player.deleted = 0 " +
            "and player.teamId = :teamId " +
            "GROUP BY player.lessonId) as temp", nativeQuery = true)
    LessonData getDataByTeamId(@Param("teamId") Long teamId);

    @Query("from FootballLessonStatisticsPlayer as player " +
            "where player.lessonId = :lessonId " +
            "and player.teamId = :teamId " +
            "and player.deleted = false ")
    List<FootballLessonStatisticsPlayer> findByLessonIdAndTeamId(@Param("lessonId") Long lessonId, @Param("teamId") Long teamId);

    @Query("from FootballLessonStatisticsPlayer as player " +
            "where player.teamId = :teamId " +
            "order by player.lessonId")
    List<FootballLessonStatisticsPlayer> findByTeamId(@Param("teamId") Long teamId);

    @Query("from FootballLessonStatisticsPlayer as player " +
            "where player.teamId = :teamId " +
            "and player.lessonId = :lessonId " +
            "and player.userId = :userId")
    FootballLessonStatisticsPlayer findByTeamIdAndUserIdAndLessonId(@Param("teamId") Long teamId, @Param("lessonId") Long lessonId, @Param("userId") Long userId);

    //球员 训练课程的传球平均值
    @Query(value = "SELECT AVG(player.oneFootPassCount) AS oneFootPassCount," +
            "AVG(player.twoFootPassCount) AS twoFootPassCount," +
            "AVG(player.longPass) AS longPass," +
            "AVG(player.shortPass) AS shortPass," +
            "AVG(player.maxSprintSpeed) AS maxSprintSpeed," +
            "AVG(player.passBallCounts) AS passBallCounts," +
            "AVG(player.passBallError) AS passBallError, " +
            "SUM(player.passBallCounts) AS passBallCountSum, " +
            "SUM(player.passBallError) AS passBallErrorSum " +
            "FROM football_lesson_statistics_player as player " +
            "WHERE player.userId  = :userId " +
            "AND player.deleted = 0", nativeQuery = true)
    FootTeamballPassballAverage findAveragePassBallByUserId(@Param("userId") Long userId);

    //球员 训练概要的平均值
    @Query(value = "SELECT AVG(player.wholeMoveDistance) AS avgWholeMoveDistance," +
            "AVG(player.carryDistance) AS avgCarryDistance," +
            "AVG(player.maxSprintSpeed) AS avgMaxSprintSpeed," +
            "AVG(player.passBallCounts) AS avgPassBallCounts," +
            "AVG(player.passBallError) AS avgPassBallError, " +
            "IFNULL(SUM(player.passBallCounts)/(SUM(player.passBallError)+SUM(player.passBallCounts)),0) AS avgSuccessRate " +
            "FROM football_lesson_statistics_player as player " +
            "WHERE player.userId  = :userId " +
            "AND player.deleted = false  ", nativeQuery = true)
    Tuple getLessonSummaryAverage(@Param("userId") long userId);

    //获取传球分析
    @Query(value = "select lsp.* " +
            " from football_lesson_statistics_player as lsp " +
            " left join football_lesson as lesson on lesson.id =  lsp.lessonId  " +
            " left join user as u on u.id = lsp.userId " +
            " left join football_team as t on t.id = lsp.teamId " +
            " where lsp.lessonId = (:lessonId) " +
            "and lsp.teamId = (:teamId) " +
            "and lsp.userId = (:userId) " +
            "and lsp.deleted = 0 ", nativeQuery = true)
    FootballLessonStatisticsPlayer getLessonPassballAnalysis(@Param("lessonId") long lessonId, @Param("teamId") long teamId, @Param("userId") long userId);

    @Query("delete from FootballLessonStatisticsPlayer as player " +
            "where player.teamId = (:teamId) ")
    @Modifying
    int deleteByTeamId(@Param("teamId") Long teamId);
}
