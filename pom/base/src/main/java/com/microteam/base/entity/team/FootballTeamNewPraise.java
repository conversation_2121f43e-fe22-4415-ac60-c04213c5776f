package com.microteam.base.entity.team;


import com.microteam.base.common.base.BaseEntity;
import com.microteam.base.entity.user.User;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;
import java.io.Serializable;

@Entity
@Table(name = "football_team_new_praise")
@Getter
@Setter
public class FootballTeamNewPraise extends BaseEntity implements Serializable {

    @ManyToOne
    @JoinColumn(name = "teamNewId", nullable = false)
    private FootballTeamNew footballTeamNew;
    @ManyToOne
    @JoinColumn(name = "userId", nullable = false)
    private User user;
    @Column(name = "isPraised")
    private Boolean isPraised;


}
