//package com.microteam.base.integral.dao.impl;
//
//import com.microteam.base.common.base.AbstractHibernateDao;
//import com.microteam.base.integral.dao.FootballIntegralTypeDao;
//import com.microteam.base.entity.integral.FootballIntegralType;
//import org.apache.log4j.Logger;
//import org.hibernate.query.Query;
//import org.springframework.stereotype.Service;
//
//@Service(value = "footballIntegralTypeDao")
//public class FootballIntegralTypeDaoImpl extends AbstractHibernateDao<FootballIntegralType> implements FootballIntegralTypeDao {
//
//    static Logger logger = Logger.getLogger(FootballIntegralTypeDaoImpl.class.getName());
//
//    public FootballIntegralTypeDaoImpl() {
//        super();
//        setClazz(FootballIntegralType.class);
//    }
//
//    @Override
//    public FootballIntegralType findById(long id) {
//        String hql = "from FootballIntegralType as footballIntegralType " +
//                "where footballIntegralType.id = (:id) " +
//                "and footballIntegralType.deleted = 0 ";
//        Query query = getCurrentSession().createQuery(hql)
//                .setParameter("id", id);
//        return (FootballIntegralType) query.uniqueResult();
//    }
//}
