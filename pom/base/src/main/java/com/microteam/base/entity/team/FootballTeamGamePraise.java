package com.microteam.base.entity.team;

import com.microteam.base.common.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.io.Serializable;

@Entity
@Table(name = "football_team_game_praise")
@Getter
@Setter
public class FootballTeamGamePraise extends BaseEntity implements Serializable {

    @Column(name = "gameId", nullable = false)
    private Long gameId;
    @Column(name = "teamId", nullable = false)
    private Long teamId;
    @Column(name = "userId")
    private Long userId;
    @Column(name = "unionid")
    private String unionId;
    @Column(name = "byUserId", nullable = false)
    private Long byUserId;
    @Column(name = "isPraised", nullable = false)
    private boolean praised;

}
