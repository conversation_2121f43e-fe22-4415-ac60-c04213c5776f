package com.microteam.base.common.util.common;

import com.baidu.aip.contentcensor.AipContentCensor;
import org.apache.commons.lang.StringEscapeUtils;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class SensitiveWordUtil {
    // 初始化一个AipContentCensor
    static AipContentCensor client = new AipContentCensor("26686628", "2Vt4vwlpPBn4V3IBs49q8UOF", "gIcIVBwVYvMT3kFCR29cq9DXIRdhkts3");

    /**
     * @desc: 敏感词判断
     * @author: DH
     * @date: 2022/7/13 10:57
     */
    public static boolean textCensorUserDefined(String text) {
            JSONObject response = client.textCensorUserDefined(text);
            try {
                System.out.println(response.toString());
                if (response.getString("conclusion").equals("合规")) {
                    return true;
                } else {
                    return false;
                }
            } catch (JSONException e) {
                e.printStackTrace();
            }
        return false;
    }

    public static boolean judgeContainsStr(String cardNum) {

        String regex=".*[a-zA-Z]+.*";

        Matcher m=Pattern.compile(regex).matcher(cardNum);

        return m.matches();

    }

    public static void main(String[] args) {
        System.out.println(judgeContainsStr(StringEscapeUtils.unescapeJava("heroin1-离职")));
    }
}
