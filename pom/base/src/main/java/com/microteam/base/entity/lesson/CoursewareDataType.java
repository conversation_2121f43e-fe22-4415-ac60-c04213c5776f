package com.microteam.base.entity.lesson;

import com.microteam.base.common.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;
import java.io.Serializable;

@Entity
@Table(name = "courseware_datatype")
@Getter
@Setter
public class CoursewareDataType extends BaseEntity implements Serializable {


    @Column(name = "coursewareId", nullable = false)
    private Long coursewareId;
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "dataTypeId")
    private FootballDataType dataType;

}
