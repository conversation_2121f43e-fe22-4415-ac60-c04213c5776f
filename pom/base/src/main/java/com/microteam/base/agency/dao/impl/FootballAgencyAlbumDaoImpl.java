//package com.microteam.base.agency.dao.impl;
//
//import com.microteam.base.agency.dao.FootballAgencyAlbumDao;
//import com.microteam.base.entity.agency.FootballAgency;
//import com.microteam.base.entity.agency.FootballAgencyAlbum;
//import com.microteam.base.common.base.AbstractHibernateDao;
//import org.apache.log4j.Logger;
//import org.hibernate.query.Query;
//import org.springframework.stereotype.Repository;
//
//import java.util.List;
//
//@Repository("footballAgencyAlbumDao")
//public class FootballAgencyAlbumDaoImpl extends
//        AbstractHibernateDao<FootballAgencyAlbum> implements FootballAgencyAlbumDao {
//    static Logger logger = Logger.getLogger(FootballAgencyAlbumDaoImpl.class.getName());
//
//    public FootballAgencyAlbumDaoImpl() {
//        super();
//
//        setClazz(FootballAgencyAlbum.class);
//    }
//
//    @SuppressWarnings("unchecked")
//    @Override
//    public FootballAgencyAlbum findAlbumByAgency(FootballAgency footballAgency, String name) {
//
//        String hql = "from  FootballAgencyAlbum as footballAgencyAlbum where footballAgencyAlbum.footballAgency=? and footballAgencyAlbum.albumName=? and footballAgencyAlbum.deleted=false ";
//        Query query = getCurrentSession().createQuery(hql).setParameter(0, footballAgency).setParameter(1, name);
//        List<FootballAgencyAlbum> list = query.list();
//        if (list != null && list.size() > 0) {
//            return list.get(0);
//        }
//        return null;
//    }
//
//    @SuppressWarnings("unchecked")
//    @Override
//    public List<FootballAgencyAlbum> findAlbumByAgencyAndPage(
//            FootballAgency footballAgency, int page, int pageSize) {
//        String hql = "from  FootballAgencyAlbum as footballAgencyAlbum where footballAgencyAlbum.footballAgency=? and footballAgencyAlbum.deleted=false ";
//        Query query = getCurrentSession().createQuery(hql).setParameter(0, footballAgency);
//        query.setFirstResult((page - 1) * pageSize);
//        query.setMaxResults(pageSize);
//        List<FootballAgencyAlbum> list = query.list();
//        return list;
//    }
//
//    @SuppressWarnings("unchecked")
//    @Override
//    public FootballAgencyAlbum findAlbumById(long id) {
//        String hql = "from  FootballAgencyAlbum as footballAgencyAlbum left join fetch footballAgencyAlbum.user as user where footballAgencyAlbum.id=?  and footballAgencyAlbum.deleted=false ";
//        Query query = getCurrentSession().createQuery(hql).setParameter(0, id);
//        List<FootballAgencyAlbum> list = query.list();
//        if (list != null && list.size() > 0) {
//            return list.get(0);
//        }
//        return null;
//    }
//
//}
