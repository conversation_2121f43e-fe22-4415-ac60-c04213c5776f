package com.microteam.base.common.pojo.team;

public class StepWidthPojo {
    private double stepWidthSum;
    private double highStepWidthSum;
    private double middleStepWidthSum;
    private double lowStepWidthSum;

    public StepWidthPojo() {
        this.stepWidthSum = 0;
        this.highStepWidthSum = 0;
        this.middleStepWidthSum = 0;
        this.lowStepWidthSum = 0;
    }

    public StepWidthPojo(double stepWidthSum, double highStepWidthSum, double middleStepWidthSum, double lowStepWidthSum) {
        this.stepWidthSum = stepWidthSum;
        this.highStepWidthSum = highStepWidthSum;
        this.middleStepWidthSum = middleStepWidthSum;
        this.lowStepWidthSum = lowStepWidthSum;
    }

    public double getStepWidthSum() {
        return stepWidthSum;
    }

    public void setStepWidthSum(double stepWidthSum) {
        this.stepWidthSum = stepWidthSum;
    }

    public double getHighStepWidthSum() {
        return highStepWidthSum;
    }

    public void setHighStepWidthSum(double highStepWidthSum) {
        this.highStepWidthSum = highStepWidthSum;
    }

    public double getMiddleStepWidthSum() {
        return middleStepWidthSum;
    }

    public void setMiddleStepWidthSum(double middleStepWidthSum) {
        this.middleStepWidthSum = middleStepWidthSum;
    }

    public double getLowStepWidthSum() {
        return lowStepWidthSum;
    }

    public void setLowStepWidthSum(double lowStepWidthSum) {
        this.lowStepWidthSum = lowStepWidthSum;
    }
}
