package com.microteam.base.entity.match;

import com.microteam.base.common.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;
import java.io.Serializable;

@Entity
@Table(name = "football_team_match_new_album")
@Getter
@Setter
public class FootballTeamMatchNewAlbum extends BaseEntity implements Serializable {
    @ManyToOne
    @JoinColumn(name = "matchNewId", nullable = false)
    private FootballTeamMatchNew teamMatchNew;
    @Column(name = "imgNetUrl", length = 250)
    private String imgNetUrl;
    @Column(name = "imgUrl", length = 250)
    private String imgUrl;
    @Column(name = "imgName", length = 250)
    private String imgName;
    @Column(name = "imgLength")
    private Long imgLength;

}
