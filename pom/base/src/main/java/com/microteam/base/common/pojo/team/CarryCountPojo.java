package com.microteam.base.common.pojo.team;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class CarryCountPojo {
    private int carryCount;
    private int highCarryCount;
    private int middleCarryCount;
    private int lowCarryCount;

    public CarryCountPojo() {

    }

    public CarryCountPojo(int carryCount, int highCarryCount, int middleCarryCount, int lowCarryCount) {
        this.carryCount = carryCount;
        this.highCarryCount = highCarryCount;
        this.middleCarryCount = middleCarryCount;
        this.lowCarryCount = lowCarryCount;
    }

}
