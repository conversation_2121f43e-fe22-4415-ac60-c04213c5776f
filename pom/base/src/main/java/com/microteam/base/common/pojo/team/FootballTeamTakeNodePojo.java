package com.microteam.base.common.pojo.team;


import java.util.Date;

public class FootballTeamTakeNodePojo implements java.io.Serializable {
    private static final long serialVersionUID = 1L;
    private Long id;
    private Long userId;
    private Long teamId;
    private Long teamNodeId;
    private Long teamGameId;
    private Long goalsfor; //进球数
    private Long assist;  //助攻
    private Long fault;//失误
    private Long shoot;//射正
    private Long shootAside;//射偏
    private Long waveShot;//浪射
    private Long holdUp;//拦截
    private Long excel;//过人
    private Long corner;//角球
    private Long freeKick;//任意球
    private Long penaltyKick;//点球
    private Long redCard;//红牌
    private Long yellowCard;//黄牌
    private Long ownGoal;//乌龙球
    private Long menace;//威胁球
    private Long save;//解围
    private Long foul;//犯规
    private Long offSide;//越位
    private Long roof;//冒顶
    private Long head;//头球
    private Long stopFault;//停球失误
    private Long passFault;//传球失误
    private Long defendFault;//防守失误
    private Long kickEmpty;//踢空
    private Date createTime;
    private Date updateTime;
    private boolean deleted;

    public FootballTeamTakeNodePojo(Long id, Long userId, Long teamId,
                                    Long teamNodeId, Long teamGameId, Long goalsfor, Long assist,
                                    Long fault, Long shoot, Long shootAside, Long waveShot,
                                    Long holdUp, Long excel, Long corner, Long freeKick,
                                    Long penaltyKick, Long redCard, Long yellowCard, Long ownGoal,
                                    boolean deleted) {
        super();
        this.id = id;
        this.userId = userId;
        this.teamId = teamId;
        this.teamNodeId = teamNodeId;
        this.teamGameId = teamGameId;
        this.goalsfor = goalsfor;
        this.assist = assist;
        this.fault = fault;
        this.shoot = shoot;
        this.shootAside = shootAside;
        this.waveShot = waveShot;
        this.holdUp = holdUp;
        this.excel = excel;
        this.corner = corner;
        this.freeKick = freeKick;
        this.penaltyKick = penaltyKick;
        this.redCard = redCard;
        this.yellowCard = yellowCard;
        this.ownGoal = ownGoal;
        this.deleted = deleted;
    }

    public FootballTeamTakeNodePojo() {
        super();
    }


    public FootballTeamTakeNodePojo(Long id, Long userId, Long teamId, Long teamNodeId, Long teamGameId, Long goalsfor, Long assist, Long fault,
                                    Long shoot, Long shootAside, Long waveShot, Long holdUp, Long excel, Long corner, Long freeKick,
                                    Long penaltyKick, Long redCard, Long yellowCard, Long ownGoal, Long menace, Long save, Long foul, Long offSide,
                                    Long roof, Long head, Long stopFault, Long passFault, Long defendFault, Long kickEmpty, Date createTime,
                                    Date updateTime, boolean deleted) {
        super();
        this.id = id;
        this.userId = userId;
        this.teamId = teamId;
        this.teamNodeId = teamNodeId;
        this.teamGameId = teamGameId;
        this.goalsfor = goalsfor;
        this.assist = assist;
        this.fault = fault;
        this.shoot = shoot;
        this.shootAside = shootAside;
        this.waveShot = waveShot;
        this.holdUp = holdUp;
        this.excel = excel;
        this.corner = corner;
        this.freeKick = freeKick;
        this.penaltyKick = penaltyKick;
        this.redCard = redCard;
        this.yellowCard = yellowCard;
        this.ownGoal = ownGoal;
        this.menace = menace;
        this.save = save;
        this.foul = foul;
        this.offSide = offSide;
        this.roof = roof;
        this.head = head;
        this.stopFault = stopFault;
        this.passFault = passFault;
        this.defendFault = defendFault;
        this.kickEmpty = kickEmpty;
        this.createTime = createTime;
        this.updateTime = updateTime;
        this.deleted = deleted;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Long getTeamId() {
        return teamId;
    }

    public void setTeamId(Long teamId) {
        this.teamId = teamId;
    }

    public Long getTeamNodeId() {
        return teamNodeId;
    }

    public void setTeamNodeId(Long teamNodeId) {
        this.teamNodeId = teamNodeId;
    }

    public Long getTeamGameId() {
        return teamGameId;
    }

    public void setTeamGameId(Long teamGameId) {
        this.teamGameId = teamGameId;
    }

    public Long getGoalsfor() {
        return goalsfor;
    }

    public void setGoalsfor(Long goalsfor) {
        this.goalsfor = goalsfor;
    }

    public Long getAssist() {
        return assist;
    }

    public void setAssist(Long assist) {
        this.assist = assist;
    }

    public Long getFault() {
        return fault;
    }

    public void setFault(Long fault) {
        this.fault = fault;
    }

    public Long getShoot() {
        return shoot;
    }

    public void setShoot(Long shoot) {
        this.shoot = shoot;
    }

    public Long getShootAside() {
        return shootAside;
    }

    public void setShootAside(Long shootAside) {
        this.shootAside = shootAside;
    }

    public Long getWaveShot() {
        return waveShot;
    }

    public void setWaveShot(Long waveShot) {
        this.waveShot = waveShot;
    }

    public Long getHoldUp() {
        return holdUp;
    }

    public void setHoldUp(Long holdUp) {
        this.holdUp = holdUp;
    }

    public Long getExcel() {
        return excel;
    }

    public void setExcel(Long excel) {
        this.excel = excel;
    }

    public Long getCorner() {
        return corner;
    }

    public void setCorner(Long corner) {
        this.corner = corner;
    }

    public Long getFreeKick() {
        return freeKick;
    }

    public void setFreeKick(Long freeKick) {
        this.freeKick = freeKick;
    }

    public Long getPenaltyKick() {
        return penaltyKick;
    }

    public void setPenaltyKick(Long penaltyKick) {
        this.penaltyKick = penaltyKick;
    }

    public Long getRedCard() {
        return redCard;
    }

    public void setRedCard(Long redCard) {
        this.redCard = redCard;
    }

    public Long getYellowCard() {
        return yellowCard;
    }

    public void setYellowCard(Long yellowCard) {
        this.yellowCard = yellowCard;
    }

    public Long getOwnGoal() {
        return ownGoal;
    }

    public void setOwnGoal(Long ownGoal) {
        this.ownGoal = ownGoal;
    }

    public Long getMenace() {
        return menace;
    }

    public void setMenace(Long menace) {
        this.menace = menace;
    }

    public Long getSave() {
        return save;
    }

    public void setSave(Long save) {
        this.save = save;
    }

    public Long getFoul() {
        return foul;
    }

    public void setFoul(Long foul) {
        this.foul = foul;
    }

    public Long getOffSide() {
        return offSide;
    }

    public void setOffSide(Long offSide) {
        this.offSide = offSide;
    }

    public Long getRoof() {
        return roof;
    }

    public void setRoof(Long roof) {
        this.roof = roof;
    }

    public Long getHead() {
        return head;
    }

    public void setHead(Long head) {
        this.head = head;
    }

    public Long getStopFault() {
        return stopFault;
    }

    public void setStopFault(Long stopFault) {
        this.stopFault = stopFault;
    }

    public Long getPassFault() {
        return passFault;
    }

    public void setPassFault(Long passFault) {
        this.passFault = passFault;
    }

    public Long getDefendFault() {
        return defendFault;
    }

    public void setDefendFault(Long defendFault) {
        this.defendFault = defendFault;
    }

    public Long getKickEmpty() {
        return kickEmpty;
    }

    public void setKickEmpty(Long kickEmpty) {
        this.kickEmpty = kickEmpty;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public boolean isDeleted() {
        return deleted;
    }

    public void setDeleted(boolean deleted) {
        this.deleted = deleted;
    }

    public static long getSerialversionuid() {
        return serialVersionUID;
    }


}
