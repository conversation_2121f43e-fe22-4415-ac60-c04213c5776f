package com.microteam.base.telMailValiManage.service;


import com.microteam.base.entity.telMailValiManage.TelMailValiInfo;

public interface MobileMessageDaoService {
    //保存或更新MobileMessage--gerald
    TelMailValiInfo save(TelMailValiInfo mobileMessage);

    //根据手机号查询MobileMessage--gerald
    TelMailValiInfo findByValiAccount(String phoneNumber);

    //删除MobileMessage--gerald
    int delByValiAccount(String phoneNumber);

}
