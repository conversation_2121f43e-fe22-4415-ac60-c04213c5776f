package com.microteam.base.lesson.dao;


import com.microteam.base.entity.lesson.FootballDataType;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

public interface FootballDataTypeDao extends JpaRepository<FootballDataType, Long>, JpaSpecificationExecutor<FootballDataType> {

    @Query("from FootballDataType as dataType " +
            "where dataType.deleted = false")
    List<FootballDataType> findAllDataType();

    @Query("from FootballDataType as dataType " +
            "where dataType.id in (:idList) " +
            "and dataType.deleted = false")
    List<FootballDataType> findByIdList(@Param("idList") List<Long> idList);

    @Query("from FootballDataType as dataType " +
            "where dataType.id in (:idList) " +
            "and dataType.deleted = false " +
            "order by dataType.type, dataType.id ")
    List<FootballDataType> findByIdListOrderByType(@Param("idList") List<Long> idList);

    @Query("from FootballDataType as dataType " +
            "where dataType.type in (:type) " +
            "and dataType.deleted = false " +
            "order by dataType.type, dataType.id asc ")
    List<FootballDataType> findByTypeList(@Param("type") List<Integer> type);

    FootballDataType findByTypeIntro(String typeIntro);
}
