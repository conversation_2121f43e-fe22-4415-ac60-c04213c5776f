package com.microteam.base.common.util.common;


import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONException;
import com.alibaba.fastjson.JSONObject;
import com.microteam.base.common.constant.ConstantUserManage;
import com.microteam.base.common.constant.FootballContants;
import com.microteam.base.common.pojo.PackageTimePojo;
import com.microteam.base.common.pojo.team.CurvePojo;
import com.microteam.base.common.pojo.team.KickStatePojo;
import com.microteam.base.common.pojo.team.StepWidthPojo;
import com.microteam.base.common.util.team.FootballTeamGameUtils;
import com.microteam.base.common.util.team.FootballTeamUtils;
import com.microteam.base.common.util.user.UserDataUtil;
import com.microteam.base.entity.lesson.FootballLessonHardwareData;
import com.microteam.base.entity.team.FootballTeamGame;
import com.microteam.base.entity.team.FootballTeamGameStatisticsPlayer;
import com.microteam.base.entity.user.User;
import com.microteam.base.entity.user.UserHardware;
import com.microteam.base.entity.user.UserHardwareData;
import com.microteam.base.entity.user.UserHardwarePractice;
import com.microteam.base.team.service.FootballTeamGameStatisticsPlayerDaoService;
import com.microteam.base.user.service.UserHardwareDaoService;
import org.apache.log4j.Logger;
import org.aspectj.apache.bcel.util.ClassLoaderRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;


/**
 * 2017-7-24
 *
 * <AUTHOR>
 */
@Service
public class ArithmeticUtil {
    private static Logger logger = Logger.getLogger(ArithmeticUtil.class.getName());

    @Autowired
    private UserHardwareDaoService userHardwareDaoService;
    @Autowired
    private FootballTeamGameStatisticsPlayerDaoService footballTeamGameStatisticsPlayerDaoService;

    public Map<String, Object> countPhysicalAgility(List<UserHardwareData> userHardwareDataList, Map<String, Object> map, User user) {
        // ----统计完后保存到人比赛统计表,查看想双鞋子是否都同步了
        try {
            // 判断两个硬件是否都上传数据了
            if (userHardwareDataList != null && userHardwareDataList.size() > 0) {
                // 查看是左脚还是右脚
                UserHardware tempUserHardware = userHardwareDataList.get(0).getUserHardware();
                long rightFootStartTime = 0L;
                long leftFootStartTime = 0L;
                String rightHighSpeedMoveDataString = "";
                String rightMidSpeedMoveDataString = "";
                String rightLowSpeedMoveDataString = "";
                String rightNormalSpeedMoveDataString = "";
                String rightFootData = "";
                String leftHighSpeedMoveDataString = "";
                String leftMidSpeedMoveDataString = "";
                String leftLowSpeedMoveDataString = "";
                String leftNormalSpeedMoveDataString = "";
                String leftFootData = "";
                if (tempUserHardware.getHardwareType() == 1) {
                    // 表示是左脚
//                    if (userHardwareDataList.get(0).getKickBallStartTime() != null) {
//                        leftFootStartTime = userHardwareDataList.get(0).getKickBallStartTime().getTime();
//                    }
                    if (userHardwareDataList.get(0).getHighSpeedMoveData() != null) {
                        leftHighSpeedMoveDataString = userHardwareDataList.get(0).getHighSpeedMoveData();
                    }
                    if (userHardwareDataList.get(0).getMidSpeedMoveData() != null) {
                        leftMidSpeedMoveDataString = userHardwareDataList.get(0).getMidSpeedMoveData();
                    }
                    if (userHardwareDataList.get(0).getLowSpeedMoveData() != null) {
                        leftLowSpeedMoveDataString = userHardwareDataList.get(0).getLowSpeedMoveData();
                    }
                    if (userHardwareDataList.get(0).getNormalSpeedMoveData() != null) {
                        leftNormalSpeedMoveDataString = userHardwareDataList.get(0).getNormalSpeedMoveData();
                    }
//                    if (userHardwareDataList.get(0).getKickBallData() != null) {
//                    }
                    if (userHardwareDataList.size() > 1) {
//                        if (userHardwareDataList.get(1).getKickBallStartTime() != null) {
//                            rightFootStartTime = userHardwareDataList.get(1).getKickBallStartTime().getTime();
//                        }
                        rightHighSpeedMoveDataString = userHardwareDataList.get(1).getHighSpeedMoveData();
                        rightMidSpeedMoveDataString = userHardwareDataList.get(1).getMidSpeedMoveData();
                        rightLowSpeedMoveDataString = userHardwareDataList.get(1).getLowSpeedMoveData();
                        rightNormalSpeedMoveDataString = userHardwareDataList.get(1).getNormalSpeedMoveData();
//                        rightFootData = userHardwareDataList.get(1).getKickBallData();
                    }
                } else {
                    // 表示是右脚
//                    if (userHardwareDataList.get(0).getKickBallStartTime() != null) {
//                        rightFootStartTime = userHardwareDataList.get(0).getKickBallStartTime().getTime();
//                    }
                    if (userHardwareDataList.get(0).getHighSpeedMoveData() != null) {
                        rightHighSpeedMoveDataString = userHardwareDataList.get(0).getHighSpeedMoveData();
                    }
                    if (userHardwareDataList.get(0).getMidSpeedMoveData() != null) {
                        rightMidSpeedMoveDataString = userHardwareDataList.get(0).getMidSpeedMoveData();
                    }
                    if (userHardwareDataList.get(0).getLowSpeedMoveData() != null) {
                        rightLowSpeedMoveDataString = userHardwareDataList.get(0).getLowSpeedMoveData();
                    }
                    if (userHardwareDataList.get(0).getNormalSpeedMoveData() != null) {
                        rightNormalSpeedMoveDataString = userHardwareDataList.get(0).getNormalSpeedMoveData();
                    }
//                    if (userHardwareDataList.get(0).getKickBallData() != null) {
//                        rightFootData = userHardwareDataList.get(0).getKickBallData();
//                    }

                    if (userHardwareDataList.size() > 1) {
//                        if (userHardwareDataList.get(1).getKickBallStartTime() != null) {
//                            leftFootStartTime = userHardwareDataList.get(1).getKickBallStartTime().getTime();
//                        }
                        leftHighSpeedMoveDataString = userHardwareDataList.get(1).getHighSpeedMoveData();
                        leftMidSpeedMoveDataString = userHardwareDataList.get(1).getMidSpeedMoveData();
                        leftLowSpeedMoveDataString = userHardwareDataList.get(1).getLowSpeedMoveData();
                        leftNormalSpeedMoveDataString = userHardwareDataList.get(1).getNormalSpeedMoveData();
//                        leftFootData = userHardwareDataList.get(1).getKickBallData();
                    }
                }
                //左右脚高速移动数据数据合并
                JSONArray HighSpeedMoveDataJSONA = new JSONArray();
                HighSpeedMoveDataJSONA = packageMap(leftHighSpeedMoveDataString, HighSpeedMoveDataJSONA);
                HighSpeedMoveDataJSONA = packageMap(rightHighSpeedMoveDataString, HighSpeedMoveDataJSONA);

                //左右脚中速速移动数据数据合并
                JSONArray MidSpeedMoveDataJSONA = new JSONArray();
                MidSpeedMoveDataJSONA = packageMap(rightMidSpeedMoveDataString, MidSpeedMoveDataJSONA);
                MidSpeedMoveDataJSONA = packageMap(leftMidSpeedMoveDataString, MidSpeedMoveDataJSONA);

                //左右脚低速移动数据数据合并
                JSONArray LowSpeedMoveDataJSONA = new JSONArray();
                LowSpeedMoveDataJSONA = packageMap(leftLowSpeedMoveDataString, LowSpeedMoveDataJSONA);
                LowSpeedMoveDataJSONA = packageMap(rightLowSpeedMoveDataString, LowSpeedMoveDataJSONA);

                //左右脚正常移动数据数据合并
                JSONArray NormalSpeedMoveDataJSONA = new JSONArray();
                NormalSpeedMoveDataJSONA = packageMap(leftNormalSpeedMoveDataString, NormalSpeedMoveDataJSONA);
                NormalSpeedMoveDataJSONA = packageMap(rightNormalSpeedMoveDataString, NormalSpeedMoveDataJSONA);

                Date leftData = new Date();
                Date rightData = new Date();
                Date date = new Date();
                if (userHardwareDataList.size() > 0 && userHardwareDataList.get(0).getKickBallData() != null) {
                    leftData = userHardwareDataList.get(0).getKickBallStartTime();
                }
                if (userHardwareDataList.size() > 1 && userHardwareDataList.get(1).getKickBallData() != null) {
                    rightData = userHardwareDataList.get(1).getKickBallStartTime();
                }
                if (leftData != null) {
                    if (rightData != null) {
                        if (leftData.getTime() <= rightData.getTime()) {
                            date = leftData;
                        } else {
                            date = rightData;
                        }
                    } else {
                        date = leftData;
                    }
                } else if (rightData != null) {
                    date = rightData;
                }
                /*
                 * 切分时间段获取数据
                 * 冲刺距离数据
                 * 高速运动距离
                 */
                PackageTimePojo HighDistancePackageTimePojo = new PackageTimePojo();
                HighDistancePackageTimePojo = getDistance(HighSpeedMoveDataJSONA, HighDistancePackageTimePojo, user, date);
                /*
                 * 中速运动距离
                 */
                PackageTimePojo MidDistancePackageTimePojo = new PackageTimePojo();
                MidDistancePackageTimePojo = getDistance(MidSpeedMoveDataJSONA, MidDistancePackageTimePojo, user, date);
                /*
                 * 低速运动距离
                 */
                PackageTimePojo LowDistancePackageTimePojo = new PackageTimePojo();
                LowDistancePackageTimePojo = getDistance(LowSpeedMoveDataJSONA, LowDistancePackageTimePojo, user, date);
                /*
                 * 正常运动距离
                 */
                PackageTimePojo NormalDistancePackageTimePojo = new PackageTimePojo();
                NormalDistancePackageTimePojo = getDistance(NormalSpeedMoveDataJSONA, NormalDistancePackageTimePojo, user, date);
                JSONObject jsonObjHigh = new JSONObject();
                jsonObjHigh.put("one", HighDistancePackageTimePojo.getOneMsec() == null ? Long.valueOf(0) : (Long) HighDistancePackageTimePojo.getOneMsec());
                jsonObjHigh.put("two", HighDistancePackageTimePojo.getTwoMsec() == null ? Long.valueOf(0) : (Long) HighDistancePackageTimePojo.getTwoMsec());
                jsonObjHigh.put("three", HighDistancePackageTimePojo.getThreeMsec() == null ? Long.valueOf(0) : (Long) HighDistancePackageTimePojo.getThreeMsec());
                jsonObjHigh.put("four", HighDistancePackageTimePojo.getFourMsec() == null ? Long.valueOf(0) : (Long) HighDistancePackageTimePojo.getFourMsec());
                jsonObjHigh.put("five", HighDistancePackageTimePojo.getFiveMsec() == null ? Long.valueOf(0) : (Long) HighDistancePackageTimePojo.getFiveMsec());
                jsonObjHigh.put("six", HighDistancePackageTimePojo.getSixMsec() == null ? Long.valueOf(0) : (Long) HighDistancePackageTimePojo.getSixMsec());
                jsonObjHigh.put("seven", HighDistancePackageTimePojo.getSevenMsec() == null ? Long.valueOf(0) : (Long) HighDistancePackageTimePojo.getSevenMsec());
                jsonObjHigh.put("eight", HighDistancePackageTimePojo.getEightMsec() == null ? Long.valueOf(0) : (Long) HighDistancePackageTimePojo.getEightMsec());
                PackageTimePojo PackageTimePojo;
                PackageTimePojo = packagePojo(HighDistancePackageTimePojo, MidDistancePackageTimePojo);
                PackageTimePojo = packagePojo(LowDistancePackageTimePojo, PackageTimePojo);
                PackageTimePojo = packagePojo(NormalDistancePackageTimePojo, PackageTimePojo);

                JSONObject jsonObj = new JSONObject();
                jsonObj.put("one", PackageTimePojo.getOneMsec());
                jsonObj.put("two", PackageTimePojo.getTwoMsec());
                jsonObj.put("three", PackageTimePojo.getThreeMsec());
                jsonObj.put("four", PackageTimePojo.getFourMsec());
                jsonObj.put("five", PackageTimePojo.getFiveMsec());
                jsonObj.put("six", PackageTimePojo.getSixMsec());
                jsonObj.put("seven", PackageTimePojo.getSevenMsec());
                jsonObj.put("eight", PackageTimePojo.getEightMsec());
                map.put("sprintDistance", jsonObjHigh);
                map.put("playMovementDistance", jsonObj);
            }
        } catch (Exception e) {
            logger.error("countPhysicalAgility", e);
        }
        return map;
    }

    public JSONArray packageMap(String jsonString, JSONArray result) throws JSONException {
        if (!"".equals(jsonString) && jsonString != null) {
            //循环取出数据
            JSONArray jsonA;
            jsonA = JSONArray.parseArray(jsonString);
            for (int i = 0; i < jsonA.size(); i++) {
                JSONObject tempObject = jsonA.getJSONObject(i);
                Map<String, Object> tempMap = new HashMap<>();
                tempMap.put("intervalTime", tempObject.get("intervalTime"));
                tempMap.put("stepCount", tempObject.get("stepCount"));
                tempMap.put("startTime", tempObject.getLong("startTime"));
                tempMap.put("endTime", tempObject.getLong("endTime"));
                tempMap.put("isHaveBall", tempObject.getOrDefault("isHaveBall", 0));
                result.add(tempMap);
            }
        }
        return result;
    }

    /**
     * 每个阶段时间计算
     *
     * @Description:TODO
     * @author:zk
     * @time:2017年7月24日 下午5:13:48
     */
    public Long getStepCount(JSONArray jsonArray, Long beginTimeInfo, Long endTimeInfo, User user) throws JSONException {
        int stepCountSumOne = 0;
        Long stepCountSumTime = 0L;
        Long stepNum = 0L;
        if (jsonArray != null) {
            for (int i = 0; i < jsonArray.size(); i++) {
                JSONObject tempObject = jsonArray.getJSONObject(i);
                Long StartTime = (Long) tempObject.get("startTime");
                Long EndTime = (Long) tempObject.get("endTime");
                int stepCountInfo;
                Long EndIntervalTime;
                int stepCount = (int) tempObject.get("stepCount");
                int intervalTime = (int) tempObject.get("intervalTime");
                if (beginTimeInfo > EndTime) {
                    EndIntervalTime = 0L;
                    stepCountInfo = 0;
                } else if (StartTime >= beginTimeInfo && EndTime <= endTimeInfo) {
                    EndIntervalTime = EndTime - StartTime;
                    stepCountInfo = stepCount;
                } else if (StartTime < beginTimeInfo && EndTime <= endTimeInfo && beginTimeInfo < EndTime) {
                    EndIntervalTime = EndTime - beginTimeInfo;
                    stepCountInfo = (int) ((EndIntervalTime / intervalTime) * stepCount);
                } else if (StartTime < beginTimeInfo && EndTime > endTimeInfo) {
                    EndIntervalTime = EndTime - StartTime;
                    stepCountInfo = (int) ((intervalTime / EndIntervalTime) * stepCount);
                } else if (StartTime >= beginTimeInfo && EndTime > endTimeInfo && StartTime < endTimeInfo) {
                    EndIntervalTime = endTimeInfo - StartTime;
                    if (EndIntervalTime < 0) {
                        EndIntervalTime = StartTime - endTimeInfo;
                    }
                    stepCountInfo = (int) ((EndIntervalTime / intervalTime) * stepCount);
                } else {
                    EndIntervalTime = 0L;
                    stepCountInfo = 0;
                }
                stepCountSumOne += stepCountInfo;
                stepCountSumTime += EndIntervalTime;
            }
            // 计算跑动数据
            stepNum = UserDataUtil.calculateMoveDistance(stepCountSumOne, stepCountSumTime.intValue(), user.getHeight());
        }
        return stepNum;
    }


    public PackageTimePojo getDistance(JSONArray jsonarray, PackageTimePojo PackageTimePojo, User user, Date date) throws JSONException {
        //切分时间段
        Long KickBallDataTime = date.getTime();
        Long oneMsec = KickBallDataTime + ConstantUserManage.checkTimeMsec * 15;
        Long twoMsec = KickBallDataTime + ConstantUserManage.checkTimeMsec * 30;
        Long threeMsec = KickBallDataTime + ConstantUserManage.checkTimeMsec * 45;
        Long fourMsec = KickBallDataTime + ConstantUserManage.checkTimeMsec * 60;
        Long fiveMsec = KickBallDataTime + ConstantUserManage.checkTimeMsec * 75;
        Long sixMsec = KickBallDataTime + ConstantUserManage.checkTimeMsec * 90;
        Long seveneMsec = KickBallDataTime + ConstantUserManage.checkTimeMsec * 105;
        PackageTimePojo.setOneMsec(getStepCount(jsonarray, KickBallDataTime, oneMsec, user));
        PackageTimePojo.setTwoMsec(getStepCount(jsonarray, oneMsec, twoMsec, user));
        PackageTimePojo.setThreeMsec(getStepCount(jsonarray, twoMsec, threeMsec, user));
        PackageTimePojo.setFourMsec(getStepCount(jsonarray, threeMsec, fourMsec, user));
        PackageTimePojo.setFiveMsec(getStepCount(jsonarray, fourMsec, fiveMsec, user));
        PackageTimePojo.setSixMsec(getStepCount(jsonarray, fiveMsec, sixMsec, user));
        PackageTimePojo.setSevenMsec(getStepCount(jsonarray, sixMsec, seveneMsec, user));
        return PackageTimePojo;
    }

    /**
     * 体能速度封装
     *
     * @Description:TODO
     * @author:zk
     * @time:2017年7月24日 下午6:29:13
     */
    public PackageTimePojo getHighSpeed(PackageTimePojo PackageTimePojo) throws JSONException {
        PackageTimePojo SpeedPackageTimePojo = new PackageTimePojo();
        SpeedPackageTimePojo.setOneMsec((Long) PackageTimePojo.getOneMsec() / 900);
        SpeedPackageTimePojo.setTwoMsec((Long) PackageTimePojo.getTwoMsec() / 900);
        SpeedPackageTimePojo.setThreeMsec((Long) PackageTimePojo.getThreeMsec() / 900);
        SpeedPackageTimePojo.setFourMsec((Long) PackageTimePojo.getFourMsec() / 900);
        SpeedPackageTimePojo.setFiveMsec((Long) PackageTimePojo.getFiveMsec() / 900);
        SpeedPackageTimePojo.setSixMsec((Long) PackageTimePojo.getSixMsec() / 900);
        SpeedPackageTimePojo.setSevenMsec((Long) PackageTimePojo.getSevenMsec() / 900);
        SpeedPackageTimePojo.setEightMsec((Long) PackageTimePojo.getEightMsec() / 900);
        return SpeedPackageTimePojo;
    }

    /**
     * 数据对象封装
     *
     * @Description:TODO
     * @author:zk
     * @time:2017年7月25日 下午3:23:32
     */
    public PackageTimePojo packagePojo(PackageTimePojo PackageTimePojo, PackageTimePojo PackageTime) {
        PackageTimePojo packageTimePo = new PackageTimePojo();
        if (PackageTimePojo != null) {
            packageTimePo.setOneMsec((PackageTime.getOneMsec() == null ? 0 : (Long) PackageTime.getOneMsec()) + (PackageTimePojo.getOneMsec() == null ? 0 : (Long) PackageTimePojo.getOneMsec()));
            packageTimePo.setTwoMsec((PackageTime.getTwoMsec() == null ? 0 : (Long) PackageTime.getTwoMsec()) + (PackageTimePojo.getTwoMsec() == null ? 0 : (Long) PackageTimePojo.getTwoMsec()));
            packageTimePo.setThreeMsec((PackageTime.getThreeMsec() == null ? 0 : (Long) PackageTime.getThreeMsec()) + (PackageTimePojo.getThreeMsec() == null ? 0 : (Long) PackageTimePojo.getThreeMsec()));
            packageTimePo.setFourMsec((PackageTime.getFourMsec() == null ? 0 : (Long) PackageTime.getFourMsec()) + (PackageTimePojo.getFourMsec() == null ? 0 : (Long) PackageTimePojo.getFourMsec()));
            packageTimePo.setFiveMsec((PackageTime.getFiveMsec() == null ? 0 : (Long) PackageTime.getFiveMsec()) + (PackageTimePojo.getFiveMsec() == null ? 0 : (Long) PackageTimePojo.getFiveMsec()));
            packageTimePo.setSixMsec((PackageTime.getSixMsec() == null ? 0 : (Long) PackageTime.getSixMsec()) + (PackageTimePojo.getSixMsec() == null ? 0 : (Long) PackageTimePojo.getSixMsec()));
            packageTimePo.setSevenMsec((PackageTime.getSevenMsec() == null ? 0 : (Long) PackageTime.getSevenMsec()) + (PackageTimePojo.getSevenMsec() == null ? 0 : (Long) PackageTimePojo.getSevenMsec()));
            packageTimePo.setEightMsec((PackageTime.getEightMsec() == null ? 0 : (Long) PackageTime.getEightMsec()) + (PackageTimePojo.getEightMsec() == null ? 0 : (Long) PackageTimePojo.getEightMsec()));
        }
        return packageTimePo;
    }

    /**
     * DATA:2017-7-27
     * 计算带球速度
     * 10个时间段内各个带球分析
     * 分为10个时间段来分析
     */
    public JSONObject countDribbleSpeed(List<Integer> array, int passInterval,
                                        JSONArray highSpeedMoveDataArray, JSONArray midSpeedMoveDataArray,
                                        JSONArray lowSpeedMoveDataArray, JSONArray normalSpeedMoveDataArray,
                                        long kickballTime, Short height) {
        JSONObject map = new JSONObject();
        try {
            Long tenDistance = 0L;
            Long twentyDistance = 0L;
            Long thirtyDistance = 0L;
            Long fortyDistance = 0L;
            Long fortyFiveDistance = 0L;
            Long fiftyFiveDistance = 0L;
            Long sixtyFiveDistance = 0L;
            Long sevenTyFiveDistance = 0L;
            Long eightyFiveDistance = 0L;
            Long ninetyDistance = 0L;
//            Long HundredDistance = 0L;

            List<List<Integer>> list = new ArrayList<>();
            List<Integer> temp = new ArrayList<>();
            if (array != null && array.size() > 0) {
                for (int i = 0; i < array.size() - 1; i++) {
                    if ((array.get(i + 1) - array.get(i)) <= passInterval) {
                        if (temp.size() == 0) {
                            temp.add(0, array.get(i));
                            temp.add(1, array.get(i + 1));
                        } else {
                            temp.remove(1);
                            temp.add(1, array.get(i + 1));
                        }
                        if (i == array.size() - 2) {
                            if (temp.size() > 0) {
                                list.add(temp);
                            }
                        }
                    } else {
                        List<Integer> g = temp;
                        if (g.size() > 0) {
                            list.add(g);
                            temp = new ArrayList<>();
                        }
                    }
                } // for
            } // if

            List<Map<String, Object>> listHighMap = new ArrayList<>();
            List<Map<String, Object>> listMidMap = new ArrayList<>();
            List<Map<String, Object>> listLowMap = new ArrayList<>();
            List<Map<String, Object>> listNormalMap = new ArrayList<>();
            if (list.size() > 0) {
                for (List<Integer> t : list) {
                    listHighMap = this.packageDribbling(highSpeedMoveDataArray, kickballTime, t, height, listHighMap);
                }
                for (List<Integer> t : list) {
                    listMidMap = this.packageDribbling(midSpeedMoveDataArray, kickballTime, t, height, listMidMap);
                }
                for (List<Integer> t : list) {
                    listLowMap = this.packageDribbling(lowSpeedMoveDataArray, kickballTime, t, height, listLowMap);
                }
                for (List<Integer> t : list) {
                    listNormalMap = this.packageDribbling(normalSpeedMoveDataArray, kickballTime, t, height, listNormalMap);
                }
                Long KickBallDataTime = kickballTime;
                Long tenMsec = KickBallDataTime + ConstantUserManage.checkTimeMsec * 10;
                Long twentyMsec = KickBallDataTime + ConstantUserManage.checkTimeMsec * 20;//二十分钟
                Long thirtyMsec = KickBallDataTime + ConstantUserManage.checkTimeMsec * 30;//三十分钟
                Long fortyMsec = KickBallDataTime + ConstantUserManage.checkTimeMsec * 40;//四十分钟
                Long fortyFiveMsec = KickBallDataTime + ConstantUserManage.checkTimeMsec * 45;//四十五分钟
                Long fiftyFiveMsec = KickBallDataTime + ConstantUserManage.checkTimeMsec * 55;//五十五分钟
                Long sixtyFiveMsec = KickBallDataTime + ConstantUserManage.checkTimeMsec * 65;//六十五分钟
                Long sevenTyFiveMsec = KickBallDataTime + ConstantUserManage.checkTimeMsec * 75;//七十五分钟
                Long eightyFiveMsec = KickBallDataTime + ConstantUserManage.checkTimeMsec * 85;//八十五分钟
                Long ninetyMsec = KickBallDataTime + ConstantUserManage.checkTimeMsec * 90;//九十分钟

//                PackageTimePojo packageTime = new PackageTimePojo();
                    /*packageTime.setTenMsec(kickballTime);
                    packageTime.setTwentyMsec(KickBallDataTime+ConstantUserManage.checkTimeMsec*10);
                    packageTime.setThirtyMsec(KickBallDataTime+ConstantUserManage.checkTimeMsec*20);
                    packageTime.setFortyMsec(KickBallDataTime+ConstantUserManage.checkTimeMsec*30);
                    packageTime.setFortyFiveMsec(KickBallDataTime+ConstantUserManage.checkTimeMsec*40);
                    packageTime.setFiftyFiveMsec(KickBallDataTime+ConstantUserManage.checkTimeMsec*45);
                    packageTime.setSixtyFiveMsec(KickBallDataTime+ConstantUserManage.checkTimeMsec*55);
                    packageTime.setSevenTyFiveMsec(KickBallDataTime+ConstantUserManage.checkTimeMsec*65);
                    packageTime.setEightyFiveMsec(KickBallDataTime+ConstantUserManage.checkTimeMsec*75);
                    packageTime.setNinetyMsec(KickBallDataTime+ConstantUserManage.checkTimeMsec*85);
                    packageTime.setHundredMsec(KickBallDataTime+ConstantUserManage.checkTimeMsec*90);*/

//                List<Object> listpackageTime = new ArrayList<>();
////                List<Object> listDistance = new ArrayList<>();
//                listpackageTime.add(KickBallDataTime);
//                listpackageTime.add(tenMsec);
//                listpackageTime.add(twentyMsec);
//                listpackageTime.add(thirtyMsec);
//                listpackageTime.add(fortyMsec);
//                listpackageTime.add(fortyFiveMsec);
//                listpackageTime.add(fiftyFiveMsec);
//                listpackageTime.add(sixtyFiveMsec);
//                listpackageTime.add(sevenTyFiveMsec);
//                listpackageTime.add(ninetyMsec);


                for (Map<String, Object> mapPage : listHighMap) {
                    long beginTime = (long) mapPage.get("beginTime");
                    long endTime1 = (long) mapPage.get("endTime");
                    long distance = (long) mapPage.get("distance");
//                    for (int j = 0; j < (listpackageTime.size() - 1); j++) {
//                        listDistance.add(packageInfo((Long) (listpackageTime.get(j)), (Long) (listpackageTime.get(j + 1)), beginTime, endTime1, distance));
//                    }
                    tenDistance += packageInfo(KickBallDataTime, tenMsec, beginTime, endTime1, distance);
                    twentyDistance += packageInfo(tenMsec, twentyMsec, beginTime, endTime1, distance);
                    thirtyDistance += packageInfo(twentyMsec, thirtyMsec, beginTime, endTime1, distance);
                    fortyDistance += packageInfo(thirtyMsec, fortyMsec, beginTime, endTime1, distance);
                    fortyFiveDistance += packageInfo(fortyMsec, fortyFiveMsec, beginTime, endTime1, distance);
                    fiftyFiveDistance += packageInfo(fortyFiveMsec, fiftyFiveMsec, beginTime, endTime1, distance);
                    sixtyFiveDistance += packageInfo(fiftyFiveMsec, sixtyFiveMsec, beginTime, endTime1, distance);
                    sevenTyFiveDistance += packageInfo(sixtyFiveMsec, sevenTyFiveMsec, beginTime, endTime1, distance);
                    eightyFiveDistance += packageInfo(sevenTyFiveMsec, eightyFiveMsec, beginTime, endTime1, distance);
                    ninetyDistance += packageInfo(eightyFiveMsec, ninetyMsec, beginTime, endTime1, distance);
                }
                for (Map<String, Object> stringObjectMap : listMidMap) {
                    Map<String, Object> mapPage;
                    mapPage = stringObjectMap;
                    long beginTime = (long) mapPage.get("beginTime");
                    long endTime1 = (long) mapPage.get("endTime");
                    long distance = (long) mapPage.get("distance");
                    tenDistance += packageInfo(KickBallDataTime, tenMsec, beginTime, endTime1, distance);
                    twentyDistance += packageInfo(tenMsec, twentyMsec, beginTime, endTime1, distance);
                    thirtyDistance += packageInfo(twentyMsec, thirtyMsec, beginTime, endTime1, distance);
                    fortyDistance += packageInfo(thirtyMsec, fortyMsec, beginTime, endTime1, distance);
                    fortyFiveDistance += packageInfo(fortyMsec, fortyFiveMsec, beginTime, endTime1, distance);
                    fiftyFiveDistance += packageInfo(fortyFiveMsec, fiftyFiveMsec, beginTime, endTime1, distance);
                    sixtyFiveDistance += packageInfo(fiftyFiveMsec, sixtyFiveMsec, beginTime, endTime1, distance);
                    sevenTyFiveDistance += packageInfo(sixtyFiveMsec, sevenTyFiveMsec, beginTime, endTime1, distance);
                    eightyFiveDistance += packageInfo(sevenTyFiveMsec, eightyFiveMsec, beginTime, endTime1, distance);
                    ninetyDistance += packageInfo(eightyFiveMsec, ninetyMsec, beginTime, endTime1, distance);
                }
                for (Map<String, Object> stringObjectMap : listLowMap) {
                    Map<String, Object> mapPage;
                    mapPage = stringObjectMap;
                    long beginTime = (long) mapPage.get("beginTime");
                    long endTime1 = (long) mapPage.get("endTime");
                    long distance = (long) mapPage.get("distance");
                    tenDistance += packageInfo(KickBallDataTime, tenMsec, beginTime, endTime1, distance);
                    twentyDistance += packageInfo(tenMsec, twentyMsec, beginTime, endTime1, distance);
                    thirtyDistance += packageInfo(twentyMsec, thirtyMsec, beginTime, endTime1, distance);
                    fortyDistance += packageInfo(thirtyMsec, fortyMsec, beginTime, endTime1, distance);
                    fortyFiveDistance += packageInfo(fortyMsec, fortyFiveMsec, beginTime, endTime1, distance);
                    fiftyFiveDistance += packageInfo(fortyFiveMsec, fiftyFiveMsec, beginTime, endTime1, distance);
                    sixtyFiveDistance += packageInfo(fiftyFiveMsec, sixtyFiveMsec, beginTime, endTime1, distance);
                    sevenTyFiveDistance += packageInfo(sixtyFiveMsec, sevenTyFiveMsec, beginTime, endTime1, distance);
                    eightyFiveDistance += packageInfo(sevenTyFiveMsec, eightyFiveMsec, beginTime, endTime1, distance);
                    ninetyDistance += packageInfo(eightyFiveMsec, ninetyMsec, beginTime, endTime1, distance);
                }
                for (Map<String, Object> stringObjectMap : listNormalMap) {
                    Map<String, Object> mapPage;
                    mapPage = stringObjectMap;
                    long beginTime = (long) mapPage.get("beginTime");
                    long endTime1 = (long) mapPage.get("endTime");
                    long distance = (long) mapPage.get("distance");
                    long distanceSum = 0;
                    tenDistance += packageInfo(KickBallDataTime, tenMsec, beginTime, endTime1, distance);
                    twentyDistance += packageInfo(tenMsec, twentyMsec, beginTime, endTime1, distance);
                    thirtyDistance += packageInfo(twentyMsec, thirtyMsec, beginTime, endTime1, distance);
                    fortyDistance += packageInfo(thirtyMsec, fortyMsec, beginTime, endTime1, distance);
                    fortyFiveDistance += packageInfo(fortyMsec, fortyFiveMsec, beginTime, endTime1, distance);
                    fiftyFiveDistance += packageInfo(fortyFiveMsec, fiftyFiveMsec, beginTime, endTime1, distance);
                    sixtyFiveDistance += packageInfo(fiftyFiveMsec, sixtyFiveMsec, beginTime, endTime1, distance);
                    sevenTyFiveDistance += packageInfo(sixtyFiveMsec, sevenTyFiveMsec, beginTime, endTime1, distance);
                    eightyFiveDistance += packageInfo(sevenTyFiveMsec, eightyFiveMsec, beginTime, endTime1, distance);
                    ninetyDistance += packageInfo(eightyFiveMsec, ninetyMsec, beginTime, endTime1, distance);
                }
            } // if
                /*map.put("tenDistance", tenDistance);
                map.put("twentyDistance", twentyDistance);
                map.put("thirtyDistance", thirtyDistance);
                map.put("fortyDistance", fortyDistance);
                map.put("fortyFiveDistance", fortyFiveDistance);
                map.put("fiftyFiveDistance", fiftyFiveDistance);
                map.put("sixtyFiveDistance", sixtyFiveDistance);
                map.put("sevenTyFiveDistance", sevenTyFiveDistance);
                map.put("eightyFiveDistance", eightyFiveDistance);
                map.put("ninetyDistance", ninetyDistance);*/

            map.put("one", tenDistance / 10 * 60 / 1000);         //0-10分钟
            map.put("two", twentyDistance / 10 * 60 / 1000);      //10-20分钟
            map.put("three", thirtyDistance / 10 * 60 / 1000);    //20-30分钟
            map.put("four", fortyDistance / 10 * 60 / 1000);      //30-40分钟
            map.put("five", fortyFiveDistance / 5 * 60 / 1000);   //40-45分钟
            map.put("six", fiftyFiveDistance / 10 * 60 / 1000);   //45-55分钟
            map.put("seven", sixtyFiveDistance / 10 * 60 / 1000); //55-65分钟
            map.put("eight", sevenTyFiveDistance / 10 * 60 / 1000);//65-75分钟
            map.put("nine", eightyFiveDistance / 10 * 60 / 1000);     //75-85分钟
            map.put("ten", ninetyDistance / 5 * 60 / 1000);       //85-90分钟
        } catch (Exception e) {
            logger.error(e.getMessage());
        }
        return map;
    }


    public long packageInfo(long beginTime1, long endTime1, long beginTime2, long endTime2, long distance) {
        long sumDistance = 0;
        //切换之际继续切换时间段，切分9个时间段
        if (beginTime1 <= beginTime2 && endTime1 >= endTime2) {      //b3----[b4---e4]----b3
            sumDistance += distance;
        } else if (beginTime1 <= beginTime2 && endTime1 > beginTime2 && endTime2 > endTime1) {      //b3----[b4----e3---e4]
            sumDistance += ((endTime1 - beginTime2) / (endTime2 - beginTime2)) * distance;
        } else if (beginTime1 >= beginTime2 && beginTime1 < endTime1 && endTime1 > endTime2) {      //[b4---b3----e4]----e3
            sumDistance += ((endTime2 - beginTime1) / (endTime2 - beginTime2)) * distance;
        } else if (beginTime1 >= beginTime2 && endTime1 < endTime2) {      //[b4---b3----e3----e4]
            sumDistance += ((endTime1 - beginTime1) / (endTime2 - beginTime2)) * distance;
        } else {      //[b4---e4]----b3----e3||b3----e3----[b4---e4]
            sumDistance += 0;
        }
        return sumDistance;
    }

    /**
     * 封装带球距离数据
     *
     * @Description:TODO
     * @author:Administrator
     * @time:2017年7月26日 下午6:38:55
     */
    public List<Map<String, Object>> packageDribbling(JSONArray highSpeedMoveDataArray, long kickballTime, List<Integer> t, Short height, List<Map<String, Object>> listMap) throws JSONException {
        if (highSpeedMoveDataArray != null && highSpeedMoveDataArray.size() > 0) {
            for (int j = 0; j < highSpeedMoveDataArray.size(); j++) {
                JSONObject jsonObject = highSpeedMoveDataArray.getJSONObject(j);
                Map<String, Object> mapPackage = new HashMap<>();
                long b1 = kickballTime + t.get(0) * 1000;
                long e1 = kickballTime + t.get(1) * 1000;
                long b2 = jsonObject.getLong("startTime");
                long e2 = jsonObject.getLong("endTime");
                if (b1 < e2 && e1 > b2) {
                    if (b1 <= b2 && e1 >= e2) { // （b1---【b2-----e2】--e1）1包含2
                        //切换之际继续切换时间段，切分9个时间段
                        long distance;
                        distance = UserDataUtil.calculateMoveDistance(((jsonObject.getInteger("stepCount"))), jsonObject.getInteger("intervalTime"), height);
                        mapPackage.put("beginTime", b2);
                        mapPackage.put("endTime", e2);
                        mapPackage.put("distance", distance);
                    } else if (b1 >= b2 && e1 <= e2) { // 【b2---（b1-----e1）--e2】2包含1
                        long distance;
                        int avgStepCount = (int) (jsonObject.getInteger("stepCount") / (jsonObject.getInteger("intervalTime") / 1000) * ((e1 - b1) / 1000));
                        distance = UserDataUtil.calculateMoveDistance(avgStepCount, (int) (e1 - b1), height);
                        mapPackage.put("beginTime", b1);
                        mapPackage.put("endTime", e1);
                        mapPackage.put("distance", distance);
                    } else if (b1 >= b2 && b1 <= e2 && e2 <= e1) { // （b1---【b2---e1）----e2】
                        long distance;
                        int avgStepCount = (int) (jsonObject.getInteger("stepCount") / (jsonObject.getInteger("intervalTime") / 1000) * ((e1 - b2) / 1000));
                        distance = UserDataUtil.calculateMoveDistance(avgStepCount, (int) (e1 - b2), height);
                        mapPackage.put("beginTime", b2);
                        mapPackage.put("endTime", e1);
                        mapPackage.put("distance", distance);
                    } else if (b1 <= b2 && e1 <= e2 && e1 >= b2) { // 【b2---(b1---e2】----e1)
                        long distance;
                        int avgStepCount = (int) (jsonObject.getInteger("stepCount") / (jsonObject.getInteger("intervalTime") / 1000) * ((e2 - b1) / 1000));
                        distance = UserDataUtil.calculateMoveDistance(avgStepCount, (int) (e2 - b1), height);
                        mapPackage.put("beginTime", b1);
                        mapPackage.put("endTime", e2);
                        mapPackage.put("distance", distance);
                    }
                }
                if (mapPackage != null && mapPackage.size() > 0) {
                    listMap.add(mapPackage);
                }
            } // for
        }
        return listMap;
    }

    public Map<String, Object> countPhysicalAgilityOfLesson(List<FootballLessonHardwareData> lessonHardwareDataList, Map<String, Object> map, User user) {
        // ----统计完后保存到人比赛统计表,查看想双鞋子是否都同步了
        try {
            // 判断两个硬件是否都上传数据了
            if (lessonHardwareDataList != null && lessonHardwareDataList.size() > 0) {
                // 查看是左脚还是右脚
                UserHardware tempUserHardware = userHardwareDaoService.findAllById(lessonHardwareDataList.get(0).getHardwareId());
                long rightFootStartTime = 0L;
                long leftFootStartTime = 0L;
                String rightHighSpeedMoveDataString = "";
                String rightMidSpeedMoveDataString = "";
                String rightLowSpeedMoveDataString = "";
                String rightNormalSpeedMoveDataString = "";
                String rightFootData = "";
                String leftHighSpeedMoveDataString = "";
                String leftMidSpeedMoveDataString = "";
                String leftLowSpeedMoveDataString = "";
                String leftNormalSpeedMoveDataString = "";
                String leftFootData = "";
                if (tempUserHardware.getHardwareType() == 1) {
                    // 表示是左脚
//                    if (lessonHardwareDataList.get(0).getKickBallStartTime() != null) {
//                        leftFootStartTime = lessonHardwareDataList.get(0).getKickBallStartTime().getTime();
//                    }
                    if (lessonHardwareDataList.get(0).getHighSpeedMoveData() != null) {
                        leftHighSpeedMoveDataString = lessonHardwareDataList.get(0).getHighSpeedMoveData();
                    }
                    if (lessonHardwareDataList.get(0).getMidSpeedMoveData() != null) {
                        leftMidSpeedMoveDataString = lessonHardwareDataList.get(0).getMidSpeedMoveData();
                    }
                    if (lessonHardwareDataList.get(0).getLowSpeedMoveData() != null) {
                        leftLowSpeedMoveDataString = lessonHardwareDataList.get(0).getLowSpeedMoveData();
                    }
                    if (lessonHardwareDataList.get(0).getNormalSpeedMoveData() != null) {
                        leftNormalSpeedMoveDataString = lessonHardwareDataList.get(0).getNormalSpeedMoveData();
                    }
//                    if (lessonHardwareDataList.get(0).getKickBallData() != null) {
//                    }

                    if (lessonHardwareDataList.size() > 1) {
//                        if (lessonHardwareDataList.get(1).getKickBallStartTime() != null) {
//                            rightFootStartTime = lessonHardwareDataList.get(1).getKickBallStartTime().getTime();
//                        }

                        rightHighSpeedMoveDataString = lessonHardwareDataList.get(1).getHighSpeedMoveData();
                        rightMidSpeedMoveDataString = lessonHardwareDataList.get(1).getMidSpeedMoveData();
                        rightLowSpeedMoveDataString = lessonHardwareDataList.get(1).getLowSpeedMoveData();
                        rightNormalSpeedMoveDataString = lessonHardwareDataList.get(1).getNormalSpeedMoveData();
//                        rightFootData = lessonHardwareDataList.get(1).getKickBallData();
                    }
                } else {
                    // 表示是右脚
//                    if (lessonHardwareDataList.get(0).getKickBallStartTime() != null) {
//                        rightFootStartTime = lessonHardwareDataList.get(0).getKickBallStartTime().getTime();
//                    }
                    if (lessonHardwareDataList.get(0).getHighSpeedMoveData() != null) {
                        rightHighSpeedMoveDataString = lessonHardwareDataList.get(0).getHighSpeedMoveData();
                    }
                    if (lessonHardwareDataList.get(0).getMidSpeedMoveData() != null) {
                        rightMidSpeedMoveDataString = lessonHardwareDataList.get(0).getMidSpeedMoveData();
                    }
                    if (lessonHardwareDataList.get(0).getLowSpeedMoveData() != null) {
                        rightLowSpeedMoveDataString = lessonHardwareDataList.get(0).getLowSpeedMoveData();
                    }
                    if (lessonHardwareDataList.get(0).getNormalSpeedMoveData() != null) {
                        rightNormalSpeedMoveDataString = lessonHardwareDataList.get(0).getNormalSpeedMoveData();
                    }
//                    if (lessonHardwareDataList.get(0).getKickBallData() != null) {
//                        rightFootData = lessonHardwareDataList.get(0).getKickBallData();
//                    }

                    if (lessonHardwareDataList.size() > 1) {
//                        if (lessonHardwareDataList.get(1).getKickBallStartTime() != null) {
//                            leftFootStartTime = lessonHardwareDataList.get(1).getKickBallStartTime().getTime();
//                        }
                        leftHighSpeedMoveDataString = lessonHardwareDataList.get(1).getHighSpeedMoveData();
                        leftMidSpeedMoveDataString = lessonHardwareDataList.get(1).getMidSpeedMoveData();
                        leftLowSpeedMoveDataString = lessonHardwareDataList.get(1).getLowSpeedMoveData();
                        leftNormalSpeedMoveDataString = lessonHardwareDataList.get(1).getNormalSpeedMoveData();
//                        leftFootData = lessonHardwareDataList.get(1).getKickBallData();
                    }
                }
                //左右脚高速移动数据数据合并
                JSONArray HighSpeedMoveDataJSONA = new JSONArray();
                HighSpeedMoveDataJSONA = packageMap(leftHighSpeedMoveDataString, HighSpeedMoveDataJSONA);
                HighSpeedMoveDataJSONA = packageMap(rightHighSpeedMoveDataString, HighSpeedMoveDataJSONA);

                //左右脚中速速移动数据数据合并
                JSONArray MidSpeedMoveDataJSONA = new JSONArray();
                MidSpeedMoveDataJSONA = packageMap(rightMidSpeedMoveDataString, MidSpeedMoveDataJSONA);
                MidSpeedMoveDataJSONA = packageMap(leftMidSpeedMoveDataString, MidSpeedMoveDataJSONA);

                //左右脚低速移动数据数据合并
                JSONArray LowSpeedMoveDataJSONA = new JSONArray();
                LowSpeedMoveDataJSONA = packageMap(leftLowSpeedMoveDataString, LowSpeedMoveDataJSONA);
                LowSpeedMoveDataJSONA = packageMap(rightLowSpeedMoveDataString, LowSpeedMoveDataJSONA);

                //左右脚正常移动数据数据合并
                JSONArray NormalSpeedMoveDataJSONA = new JSONArray();
                NormalSpeedMoveDataJSONA = packageMap(leftNormalSpeedMoveDataString, NormalSpeedMoveDataJSONA);
                NormalSpeedMoveDataJSONA = packageMap(rightNormalSpeedMoveDataString, NormalSpeedMoveDataJSONA);

                Date leftData = new Date();
                Date rightData = new Date();
                Date date = new Date();
                if (lessonHardwareDataList.size() > 0 && lessonHardwareDataList.get(0).getKickBallData() != null) {
                    leftData = lessonHardwareDataList.get(0).getKickBallStartTime();
                }
                if (lessonHardwareDataList.size() > 1 && lessonHardwareDataList.get(1).getKickBallData() != null) {
                    rightData = lessonHardwareDataList.get(1).getKickBallStartTime();
                }
                if (leftData != null) {
                    if (rightData != null) {
                        if (leftData.getTime() <= rightData.getTime()) {
                            date = leftData;
                        } else {
                            date = rightData;
                        }
                    } else {
                        date = leftData;
                    }
                } else if (rightData != null) {
                    date = rightData;
                }
                /*
                 * 切分时间段获取数据
                 * 冲刺距离数据
                 * 高速运动距离
                 */
                PackageTimePojo HighDistancePackageTimePojo = new PackageTimePojo();
                HighDistancePackageTimePojo = getDistance(HighSpeedMoveDataJSONA, HighDistancePackageTimePojo, user, date);
                /*
                 * 中速运动距离
                 */
                PackageTimePojo MidDistancePackageTimePojo = new PackageTimePojo();
                MidDistancePackageTimePojo = getDistance(MidSpeedMoveDataJSONA, MidDistancePackageTimePojo, user, date);
                /*
                 * 低速运动距离
                 */
                PackageTimePojo LowDistancePackageTimePojo = new PackageTimePojo();
                LowDistancePackageTimePojo = getDistance(LowSpeedMoveDataJSONA, LowDistancePackageTimePojo, user, date);
                /*
                 * 正常运动距离
                 */
                PackageTimePojo NormalDistancePackageTimePojo = new PackageTimePojo();
                NormalDistancePackageTimePojo = getDistance(NormalSpeedMoveDataJSONA, NormalDistancePackageTimePojo, user, date);
                JSONObject jsonObjHigh = new JSONObject();
                jsonObjHigh.put("one", HighDistancePackageTimePojo.getOneMsec() == null ? Long.valueOf(0) : (Long) HighDistancePackageTimePojo.getOneMsec());
                jsonObjHigh.put("two", HighDistancePackageTimePojo.getTwoMsec() == null ? Long.valueOf(0) : (Long) HighDistancePackageTimePojo.getTwoMsec());
                jsonObjHigh.put("three", HighDistancePackageTimePojo.getThreeMsec() == null ? Long.valueOf(0) : (Long) HighDistancePackageTimePojo.getThreeMsec());
                jsonObjHigh.put("four", HighDistancePackageTimePojo.getFourMsec() == null ? Long.valueOf(0) : (Long) HighDistancePackageTimePojo.getFourMsec());
                jsonObjHigh.put("five", HighDistancePackageTimePojo.getFiveMsec() == null ? Long.valueOf(0) : (Long) HighDistancePackageTimePojo.getFiveMsec());
                jsonObjHigh.put("six", HighDistancePackageTimePojo.getSixMsec() == null ? Long.valueOf(0) : (Long) HighDistancePackageTimePojo.getSixMsec());
                jsonObjHigh.put("seven", HighDistancePackageTimePojo.getSevenMsec() == null ? Long.valueOf(0) : (Long) HighDistancePackageTimePojo.getSevenMsec());
                jsonObjHigh.put("eight", HighDistancePackageTimePojo.getEightMsec() == null ? Long.valueOf(0) : (Long) HighDistancePackageTimePojo.getEightMsec());
                PackageTimePojo PackageTimePojo;
                PackageTimePojo = packagePojo(HighDistancePackageTimePojo, MidDistancePackageTimePojo);
                PackageTimePojo = packagePojo(LowDistancePackageTimePojo, PackageTimePojo);
                PackageTimePojo = packagePojo(NormalDistancePackageTimePojo, PackageTimePojo);

                JSONObject jsonObj = new JSONObject();
                jsonObj.put("one", PackageTimePojo.getOneMsec());
                jsonObj.put("two", PackageTimePojo.getTwoMsec());
                jsonObj.put("three", PackageTimePojo.getThreeMsec());
                jsonObj.put("four", PackageTimePojo.getFourMsec());
                jsonObj.put("five", PackageTimePojo.getFiveMsec());
                jsonObj.put("six", PackageTimePojo.getSixMsec());
                jsonObj.put("seven", PackageTimePojo.getSevenMsec());
                jsonObj.put("eight", PackageTimePojo.getEightMsec());
                map.put("sprintDistance", jsonObjHigh);
                map.put("playMovementDistance", jsonObj);
            } else {
                map.put("sprintDistance", new JSONObject());
                map.put("playMovementDistance", new JSONObject());
            }
        } catch (Exception e) {
            System.out.println(e.getMessage());
            // TODO Auto-generated catch block
            e.printStackTrace();
        }
        return map;
    }

    public List<CurvePojo> curveOfTeam(List<UserHardwareData> dataList, FootballTeamGame game, UserHardwarePractice practice, long pointCount, User user) throws JSONException {
        CurvePojo curvePojo;
        CurvePojo ballCurvePojo;
        CurvePojo kcalCurvePojo;

        JSONArray HighSpeedMoveDataJSONA = new JSONArray();
        JSONArray MidSpeedMoveDataJSONA = new JSONArray();
        JSONArray LowSpeedMoveDataJSONA = new JSONArray();
        JSONArray NormalSpeedMoveDataJSONA = new JSONArray();
        List<KickStatePojo> pojoList = new ArrayList<>();
        if (dataList != null && dataList.size() > 0) {
            String rightHighSpeedMoveDataString = "";
            String rightMidSpeedMoveDataString = "";
            String rightLowSpeedMoveDataString = "";
            String rightNormalSpeedMoveDataString = "";
            String rightKickStateString = "";
            Date rightKickStartTime = new Date();
            String rightKickData = "";

            String leftHighSpeedMoveDataString = "";
            String leftMidSpeedMoveDataString = "";
            String leftLowSpeedMoveDataString = "";
            String leftNormalSpeedMoveDataString = "";
            String leftKickStateString = "";
            Date leftKickStartTime = new Date();
            String leftKickData = "";
            for (UserHardwareData data : dataList) {
                if (data.getUserHardware().getHardwareType() == 1) {  //左脚
                    leftHighSpeedMoveDataString = data.getHighSpeedMoveData();
                    leftMidSpeedMoveDataString = data.getMidSpeedMoveData();
                    leftLowSpeedMoveDataString = data.getLowSpeedMoveData();
                    leftNormalSpeedMoveDataString = data.getNormalSpeedMoveData();
                    leftKickStateString = data.getKickBallState();
                    if (data.getKickBallStartTime() != null) {
                        leftKickStartTime = data.getKickBallStartTime();
                    } else {
                        leftKickStartTime = data.getCreateTime();
                    }
                    leftKickData = data.getKickBallData();
                } else {    //右脚
                    rightHighSpeedMoveDataString = data.getHighSpeedMoveData();
                    rightMidSpeedMoveDataString = data.getMidSpeedMoveData();
                    rightLowSpeedMoveDataString = data.getLowSpeedMoveData();
                    rightNormalSpeedMoveDataString = data.getNormalSpeedMoveData();
                    rightKickStateString = data.getKickBallState();
                    if (data.getKickBallStartTime() != null) {
                        rightKickStartTime = data.getKickBallStartTime();
                    } else {
                        rightKickStartTime = data.getCreateTime();
                    }
                    rightKickData = data.getKickBallData();
                }
            }
            HighSpeedMoveDataJSONA = packageMap(leftHighSpeedMoveDataString, HighSpeedMoveDataJSONA);
            packageMap(rightHighSpeedMoveDataString, HighSpeedMoveDataJSONA);

            MidSpeedMoveDataJSONA = packageMap(rightMidSpeedMoveDataString, MidSpeedMoveDataJSONA);
            packageMap(leftMidSpeedMoveDataString, MidSpeedMoveDataJSONA);

            LowSpeedMoveDataJSONA = packageMap(leftLowSpeedMoveDataString, LowSpeedMoveDataJSONA);
            packageMap(rightLowSpeedMoveDataString, LowSpeedMoveDataJSONA);

            NormalSpeedMoveDataJSONA = packageMap(leftNormalSpeedMoveDataString, NormalSpeedMoveDataJSONA);
            packageMap(rightNormalSpeedMoveDataString, NormalSpeedMoveDataJSONA);

            //左右脚触球状态数据合并
            pojoList = packageKickMap(leftKickStartTime, leftKickStateString, pojoList);
            packageKickMap(rightKickStartTime, rightKickStateString, pojoList);

            if (rightKickData != null && leftKickData != null && pojoList.size() == 0) {
                pojoList = FootballTeamGameUtils.packKickState(dataList);
            }
            sortKickList(pojoList);
        }
        Date date = game.getCompetitionTime();
        long timeLag;   //平均时间间隔
        int activityTime = (int) Math.abs(date.getTime() - game.getFinishTime().getTime());   //开始->结束时间段
        if (game.getFinishTime().getTime() == 1514736000000L) {
            activityTime = (int) Math.abs(date.getTime() - date.getTime() + FootballContants.GAMEDURATION);
        }

        long tenMin = 10 * 60 * 1000;
        long oneMin = 1 * 60 * 1000;
        if (activityTime < tenMin && activityTime >= oneMin) {
            int min = new BigDecimal((double) activityTime / 1000 / 60).setScale(0, RoundingMode.HALF_UP).intValue();
            pointCount = min;
            activityTime = min * 60 * 1000;
        } else if (activityTime >= 0 && activityTime <= oneMin) {
            int min;
            if (activityTime == 0) {
                min = 1;
            } else {
                min = new BigDecimal((double) activityTime / 1000 / 60).setScale(0, BigDecimal.ROUND_UP).intValue();
            }
            pointCount = min;
            activityTime = min * 60 * 1000;
        } else {
            pointCount = 10;
            /*int min = new BigDecimal((double) activityTime / 1000 / 60).setScale(0, RoundingMode.HALF_UP).intValue();
            activityTime = min * 60 * 1000;*/
        }

        timeLag = new BigDecimal(activityTime / pointCount).setScale(0, RoundingMode.HALF_UP).longValue();
        //切割时间段计算曲线
        List<CurvePojo> highCurveList = getTempCurve(HighSpeedMoveDataJSONA, timeLag, pointCount, date, user, 1);
        List<CurvePojo> midCurveList = getTempCurve(MidSpeedMoveDataJSONA, timeLag, pointCount, date, user, 2);
        List<CurvePojo> lowCurveList = getTempCurve(LowSpeedMoveDataJSONA, timeLag, pointCount, date, user, 3);
        List<CurvePojo> normalCurveList = getTempCurve(NormalSpeedMoveDataJSONA, timeLag, pointCount, date, user, 4);

        CurvePojo highCurvePojo = highCurveList.get(0);
        CurvePojo midCurvePojo = midCurveList.get(0);
        CurvePojo lowCurvePojo = lowCurveList.get(0);
        CurvePojo normalCurvePojo = normalCurveList.get(0);

        CurvePojo kcalHighCurvePojo = highCurveList.get(2);
        CurvePojo kcalMidCurvePojo = midCurveList.get(2);
        CurvePojo kcalLowCurvePojo = lowCurveList.get(2);
        CurvePojo kcalNormalCurvePojo = normalCurveList.get(2);

        ballCurvePojo = getBallTempCurve(pojoList, timeLag, pointCount, date, user);
        ballCurvePojo.setRealMin(new BigDecimal(ballCurvePojo.getMaxX()).setScale(0, BigDecimal.ROUND_HALF_UP).doubleValue());
        //四条曲线相加
        curvePojo = FootballTeamGameUtils.mergeCurvePojo(highCurvePojo, midCurvePojo);
        curvePojo = FootballTeamGameUtils.mergeCurvePojo(curvePojo, lowCurvePojo);
        curvePojo = FootballTeamGameUtils.mergeCurvePojo(curvePojo, normalCurvePojo);
        curvePojo.setRealMin(new BigDecimal(curvePojo.getMaxX()).setScale(0, BigDecimal.ROUND_HALF_UP).doubleValue());

        kcalCurvePojo = FootballTeamGameUtils.mergeCurvePojo(kcalHighCurvePojo, kcalMidCurvePojo);
        kcalCurvePojo = FootballTeamGameUtils.mergeCurvePojo(kcalCurvePojo, kcalLowCurvePojo);
        kcalCurvePojo = FootballTeamGameUtils.mergeCurvePojo(kcalCurvePojo, kcalNormalCurvePojo);
        kcalCurvePojo.setRealMin(new BigDecimal(kcalCurvePojo.getMaxX()).setScale(0, BigDecimal.ROUND_HALF_UP).doubleValue());

        CurvePojo notAddUp = new CurvePojo();
        notAddUp.setMaxX(kcalCurvePojo.getMaxX());
        notAddUp.setMaxY(kcalCurvePojo.getMaxY());
        notAddUp.setAxisX(new ArrayList<>(kcalCurvePojo.getAxisX()));
        notAddUp.setAxisY(new ArrayList<>(kcalCurvePojo.getAxisY()));
        notAddUp.setAxisXLong(new ArrayList<>(kcalCurvePojo.getAxisXLong()));
        notAddUp.setRealMin(new BigDecimal(notAddUp.getMaxX()).setScale(0, BigDecimal.ROUND_HALF_UP).doubleValue());
        //卡路里曲线累加
        List<Integer> listY = kcalCurvePojo.getAxisY();
        for (int i = 0; i < listY.size(); i++) {
            if (i > 0) {
                listY.set(i, listY.get(i) + listY.get(i - 1));
            }
        }
        kcalCurvePojo.setMaxY(listY.get(listY.size() - 1));
        kcalCurvePojo.setAxisY(listY);
        List<CurvePojo> list = new ArrayList<>();
        list.add(curvePojo);
        list.add(ballCurvePojo);
        list.add(kcalCurvePojo);
        list.add(notAddUp);
        return list;
    }

    public List<CurvePojo> curveOfPerson(List<UserHardwareData> dataList, FootballTeamGame game, UserHardwarePractice practice, long pointCount, User user) throws JSONException {
        CurvePojo curvePojo;
        CurvePojo ballCurvePojo;
        CurvePojo kcalCurvePojo;

        JSONArray HighSpeedMoveDataJSONA = new JSONArray();
        JSONArray MidSpeedMoveDataJSONA = new JSONArray();
        JSONArray LowSpeedMoveDataJSONA = new JSONArray();
        JSONArray NormalSpeedMoveDataJSONA = new JSONArray();
        List<KickStatePojo> pojoList = new ArrayList<>();
        if (dataList != null && dataList.size() > 0) {
            String rightHighSpeedMoveDataString = "";
            String rightMidSpeedMoveDataString = "";
            String rightLowSpeedMoveDataString = "";
            String rightNormalSpeedMoveDataString = "";
            String rightKickStateString = "";
            Date rightKickStartTime = new Date();
            String rightKickDate = "";

            String leftHighSpeedMoveDataString = "";
            String leftMidSpeedMoveDataString = "";
            String leftLowSpeedMoveDataString = "";
            String leftNormalSpeedMoveDataString = "";
            String leftKickStateString = "";
            Date leftKickStartTime = new Date();
            String leftKickData = "";

            for (UserHardwareData data : dataList) {
                if (data.getUserHardware().getHardwareType() == 1) {  //左脚
                    leftHighSpeedMoveDataString = data.getHighSpeedMoveData();
                    leftMidSpeedMoveDataString = data.getMidSpeedMoveData();
                    leftLowSpeedMoveDataString = data.getLowSpeedMoveData();
                    leftNormalSpeedMoveDataString = data.getNormalSpeedMoveData();
                    leftKickStateString = data.getKickBallState();
                    if (data.getKickBallStartTime() != null) {
                        leftKickStartTime = data.getKickBallStartTime();
                    } else {
                        leftKickStartTime = data.getCreateTime();
                    }
                    leftKickData = data.getKickBallData();
                } else {    //右脚
                    rightHighSpeedMoveDataString = data.getHighSpeedMoveData();
                    rightMidSpeedMoveDataString = data.getMidSpeedMoveData();
                    rightLowSpeedMoveDataString = data.getLowSpeedMoveData();
                    rightNormalSpeedMoveDataString = data.getNormalSpeedMoveData();
                    rightKickStateString = data.getKickBallState();
                    if (data.getKickBallStartTime() != null) {
                        rightKickStartTime = data.getKickBallStartTime();
                    } else {
                        rightKickStartTime = data.getCreateTime();
                    }
                    rightKickDate = data.getKickBallData();
                }
            }

            //左右脚高速移动数据数据合并
            HighSpeedMoveDataJSONA = packageMap(leftHighSpeedMoveDataString, HighSpeedMoveDataJSONA);
            packageMap(rightHighSpeedMoveDataString, HighSpeedMoveDataJSONA);

            //左右脚中速速移动数据数据合并
            MidSpeedMoveDataJSONA = packageMap(rightMidSpeedMoveDataString, MidSpeedMoveDataJSONA);
            packageMap(leftMidSpeedMoveDataString, MidSpeedMoveDataJSONA);

            //左右脚低速移动数据数据合并
            LowSpeedMoveDataJSONA = packageMap(leftLowSpeedMoveDataString, LowSpeedMoveDataJSONA);
            packageMap(rightLowSpeedMoveDataString, LowSpeedMoveDataJSONA);

            //左右脚正常移动数据数据合并
            NormalSpeedMoveDataJSONA = packageMap(leftNormalSpeedMoveDataString, NormalSpeedMoveDataJSONA);
            packageMap(rightNormalSpeedMoveDataString, NormalSpeedMoveDataJSONA);

            //左右脚触球状态数据合并
            pojoList = packageKickMap(leftKickStartTime, leftKickStateString, pojoList);
            packageKickMap(rightKickStartTime, rightKickStateString, pojoList);

            if (rightKickDate != null && leftKickData != null && pojoList.size() == 0) {
                pojoList = FootballTeamGameUtils.packKickState(dataList);
            }
            sortKickList(pojoList);
        }
        Date leftStartData = null;
        Date rightStartData = null;
        Date startDate = new Date();

        Date leftUplodData = null;
        Date rightUplodData = null;
        Date uploadDate = new Date();
        if (dataList != null && dataList.size() > 0) {
            leftStartData = dataList.get(0).getCreateTime();
            leftUplodData = dataList.get(0).getUpdateTime();
        }
        if (dataList != null && dataList.size() > 1) {
            rightStartData = dataList.get(1).getCreateTime();
            rightUplodData = dataList.get(1).getUpdateTime();
        }

        //取个人曲线的起始时间
        if (leftStartData != null) {
            if (rightStartData != null) {
                if (leftStartData.getTime() <= rightStartData.getTime()) {
                    startDate = leftStartData;
                } else {
                    startDate = rightStartData;
                }
            } else {
                startDate = leftStartData;
            }
        } else if (rightStartData != null) {
            startDate = rightStartData;
        }
        //取个人曲线的上传时间
        if (leftUplodData != null) {
            if (rightUplodData != null) {
                if (leftUplodData.getTime() >= rightUplodData.getTime()) {
                    uploadDate = leftUplodData;
                } else {
                    uploadDate = rightUplodData;
                }
            } else {
                uploadDate = leftUplodData;
            }
        } else if (rightStartData != null) {
            uploadDate = rightUplodData;
        }

        //取个人触球时间
        long timeLag;   //平均时间间隔
        FootballTeamGameStatisticsPlayer player;
        if (game != null) {
            player = footballTeamGameStatisticsPlayerDaoService.findByGameIdAndUserId(game.getId(), user.getId());
        } else {
            player = footballTeamGameStatisticsPlayerDaoService.findByPracticeIdAndUserId(practice.getId(), user.getId());
        }
        if (player == null) {
            if (game != null) {
                uploadDate = game.getFinishTime();
            } else {
                uploadDate = new Date(practice.getCreateTime().getTime() + FootballContants.GAMEDURATION);
            }
        }
        int activityTime = (int) Math.abs(startDate.getTime() - uploadDate.getTime());   //个人启动->上传时间段
        long tenMin = 10 * 60 * 1000;
        long oneMin = 1 * 60 * 1000;
        if (activityTime < tenMin && activityTime > oneMin) {
            int min = new BigDecimal((double) activityTime / 1000 / 60).setScale(0, RoundingMode.HALF_UP).intValue();
            pointCount = min;
            activityTime = min * 60 * 1000;
        } else if (activityTime >= 0 && activityTime <= oneMin) {
            int min;
            if (activityTime == 0) {
                min = 1;
            } else {
                min = new BigDecimal((double) activityTime / 1000 / 60).setScale(0, BigDecimal.ROUND_UP).intValue();
            }
            pointCount = min;
            activityTime = min * 60 * 1000;
        } else {
            pointCount = 10;
        }
        timeLag = new BigDecimal(activityTime / pointCount).setScale(0, RoundingMode.HALF_UP).longValue();
        //切割时间段计算曲线
        List<CurvePojo> highCurveList = getTempCurve(HighSpeedMoveDataJSONA, timeLag, pointCount, startDate, user, 1);
        List<CurvePojo> midCurveList = getTempCurve(MidSpeedMoveDataJSONA, timeLag, pointCount, startDate, user, 2);
        List<CurvePojo> lowCurveList = getTempCurve(LowSpeedMoveDataJSONA, timeLag, pointCount, startDate, user, 3);
        List<CurvePojo> normalCurveList = getTempCurve(NormalSpeedMoveDataJSONA, timeLag, pointCount, startDate, user, 4);

        CurvePojo highCurvePojo = highCurveList.get(0);
        CurvePojo midCurvePojo = midCurveList.get(0);
        CurvePojo lowCurvePojo = lowCurveList.get(0);
        CurvePojo normalCurvePojo = normalCurveList.get(0);

        CurvePojo kcalHighCurvePojo = highCurveList.get(2);
        CurvePojo kcalMidCurvePojo = midCurveList.get(2);
        CurvePojo kcalLowCurvePojo = lowCurveList.get(2);
        CurvePojo kcalNormalCurvePojo = normalCurveList.get(2);

        ballCurvePojo = getBallTempCurve(pojoList, timeLag, pointCount, startDate, user);
        ballCurvePojo.setRealMin(new BigDecimal(ballCurvePojo.getMaxX()).setScale(0, BigDecimal.ROUND_HALF_UP).doubleValue());
        //四条曲线相加
        curvePojo = FootballTeamGameUtils.mergeCurvePojo(highCurvePojo, midCurvePojo);
        curvePojo = FootballTeamGameUtils.mergeCurvePojo(curvePojo, lowCurvePojo);
        curvePojo = FootballTeamGameUtils.mergeCurvePojo(curvePojo, normalCurvePojo);
        curvePojo.setRealMin(new BigDecimal(curvePojo.getMaxX()).setScale(0, BigDecimal.ROUND_HALF_UP).doubleValue());


        kcalCurvePojo = FootballTeamGameUtils.mergeCurvePojo(kcalHighCurvePojo, kcalMidCurvePojo);
        kcalCurvePojo = FootballTeamGameUtils.mergeCurvePojo(kcalCurvePojo, kcalLowCurvePojo);
        kcalCurvePojo = FootballTeamGameUtils.mergeCurvePojo(kcalCurvePojo, kcalNormalCurvePojo);
        kcalCurvePojo.setRealMin(new BigDecimal(kcalCurvePojo.getMaxX()).setScale(0, BigDecimal.ROUND_HALF_UP).doubleValue());

        List<CurvePojo> list = new ArrayList<>();
        CurvePojo notAddUp = new CurvePojo();
        notAddUp.setMaxX(kcalCurvePojo.getMaxX());
        notAddUp.setMaxY(kcalCurvePojo.getMaxY());
        notAddUp.setAxisX(new ArrayList<>(kcalCurvePojo.getAxisX()));
        notAddUp.setAxisY(new ArrayList<>(kcalCurvePojo.getAxisY()));
        notAddUp.setAxisXLong(new ArrayList<>(kcalCurvePojo.getAxisXLong()));
        notAddUp.setRealMin(new BigDecimal(notAddUp.getMaxX()).setScale(0, BigDecimal.ROUND_HALF_UP).doubleValue());
        //卡路里曲线累加
        List<Integer> listY = kcalCurvePojo.getAxisY();
        for (int i = 0; i < listY.size(); i++) {
            if (i > 0) {
                listY.set(i, listY.get(i) + listY.get(i - 1));
            }
        }
        kcalCurvePojo.setMaxY(listY.get(listY.size() - 1));
        kcalCurvePojo.setAxisY(listY);

        list.add(curvePojo);
        list.add(ballCurvePojo);
        list.add(kcalCurvePojo);
        list.add(notAddUp);
        return list;
    }


    public List<CurvePojo> getCurve(List<UserHardwareData> dataList, FootballTeamGame game, UserHardwarePractice practice, long pointCount, User user, String curveType) throws JSONException {
        JSONArray moveArray = new JSONArray();
        long zeroTime = game.getCompetitionTime().getTime();
        long finishTime = game.getFinishTime().getTime();

        if (dataList != null) {
            dataList.forEach(data -> {
                if (data.getHighSpeedMoveData() != null) {
                    moveArray.addAll(JSONArray.parseArray(data.getHighSpeedMoveData()));
                }
                if (data.getMidSpeedMoveData() != null) {
                    moveArray.addAll(JSONArray.parseArray(data.getMidSpeedMoveData()));
                }
                if (data.getLowSpeedMoveData() != null) {
                    moveArray.addAll(JSONArray.parseArray(data.getLowSpeedMoveData()));
                }
                if (data.getNormalSpeedMoveData() != null) {
                    moveArray.addAll(JSONArray.parseArray(data.getNormalSpeedMoveData()));
                }
            });
            if (curveType.equals("person")) {
                zeroTime = dataList.stream().filter(data -> data.getKickBallStartTime() != null).mapToLong(data -> data.getKickBallStartTime().getTime()).min().getAsLong();
                finishTime = dataList.stream().filter(data -> data.getUpdateTime() != null).mapToLong(data -> data.getUpdateTime().getTime()).max().getAsLong();
            }
        }

        long activityTime = Math.abs(zeroTime - finishTime);
        long tenMin = 10 * 60 * 1000;
        long oneMin = 1 * 60 * 1000;
        if (activityTime < tenMin && activityTime > oneMin) {
            int min = new BigDecimal((double) activityTime / 1000 / 60).setScale(0, RoundingMode.HALF_UP).intValue();
            pointCount = min;
            activityTime = min * 60 * 1000;
        } else if (activityTime >= 0 && activityTime <= oneMin) {
            int min = 0;
            if (activityTime == 0) {
                min = 1;
            } else {
                min = new BigDecimal((double) activityTime / 1000 / 60).setScale(0, RoundingMode.UP).intValue();
            }
            pointCount = min;
            activityTime = min * 60 * 1000;
        } else {
            pointCount = 10;
        }
        long timeLag = new BigDecimal(activityTime / pointCount).setScale(0, RoundingMode.HALF_UP).longValue();
        List list = getCurves(moveArray,timeLag,pointCount,zeroTime,finishTime,user);
        return list;
        /*CurvePojo curvePojo;
        CurvePojo ballCurvePojo;
        CurvePojo kcalCurvePojo;

        JSONArray HighSpeedMoveDataJSONA = new JSONArray();
        JSONArray MidSpeedMoveDataJSONA = new JSONArray();
        JSONArray LowSpeedMoveDataJSONA = new JSONArray();
        JSONArray NormalSpeedMoveDataJSONA = new JSONArray();
        List<KickStatePojo> pojoList = new ArrayList<>();
        if (dataList != null && dataList.size() > 0) {
            String rightHighSpeedMoveDataString = "";
            String rightMidSpeedMoveDataString = "";
            String rightLowSpeedMoveDataString = "";
            String rightNormalSpeedMoveDataString = "";
            String rightKickStateString = "";
            Date rightKickStartTime = new Date();
            String rightKickDate = "";

            String leftHighSpeedMoveDataString = "";
            String leftMidSpeedMoveDataString = "";
            String leftLowSpeedMoveDataString = "";
            String leftNormalSpeedMoveDataString = "";
            String leftKickStateString = "";
            Date leftKickStartTime = new Date();
            String leftKickData = "";

            for (UserHardwareData data : dataList) {
                if (data.getUserHardware().getHardwareType() == 1) {  //左脚
                    leftHighSpeedMoveDataString = data.getHighSpeedMoveData();
                    leftMidSpeedMoveDataString = data.getMidSpeedMoveData();
                    leftLowSpeedMoveDataString = data.getLowSpeedMoveData();
                    leftNormalSpeedMoveDataString = data.getNormalSpeedMoveData();
                    leftKickStateString = data.getKickBallState();
                    if (data.getKickBallStartTime() != null) {
                        leftKickStartTime = data.getKickBallStartTime();
                    } else {
                        leftKickStartTime = data.getCreateTime();
                    }
                    leftKickData = data.getKickBallData();
                } else {    //右脚
                    rightHighSpeedMoveDataString = data.getHighSpeedMoveData();
                    rightMidSpeedMoveDataString = data.getMidSpeedMoveData();
                    rightLowSpeedMoveDataString = data.getLowSpeedMoveData();
                    rightNormalSpeedMoveDataString = data.getNormalSpeedMoveData();
                    rightKickStateString = data.getKickBallState();
                    if (data.getKickBallStartTime() != null) {
                        rightKickStartTime = data.getKickBallStartTime();
                    } else {
                        rightKickStartTime = data.getCreateTime();
                    }
                    rightKickDate = data.getKickBallData();
                }
            }

            //左右脚高速移动数据数据合并
            HighSpeedMoveDataJSONA = packageMap(leftHighSpeedMoveDataString, HighSpeedMoveDataJSONA);
            packageMap(rightHighSpeedMoveDataString, HighSpeedMoveDataJSONA);

            //左右脚中速速移动数据数据合并
            MidSpeedMoveDataJSONA = packageMap(rightMidSpeedMoveDataString, MidSpeedMoveDataJSONA);
            packageMap(leftMidSpeedMoveDataString, MidSpeedMoveDataJSONA);

            //左右脚低速移动数据数据合并
            LowSpeedMoveDataJSONA = packageMap(leftLowSpeedMoveDataString, LowSpeedMoveDataJSONA);
            packageMap(rightLowSpeedMoveDataString, LowSpeedMoveDataJSONA);

            //左右脚正常移动数据数据合并
            NormalSpeedMoveDataJSONA = packageMap(leftNormalSpeedMoveDataString, NormalSpeedMoveDataJSONA);
            packageMap(rightNormalSpeedMoveDataString, NormalSpeedMoveDataJSONA);

            //左右脚触球状态数据合并
            pojoList = packageKickMap(leftKickStartTime, leftKickStateString, pojoList);
            packageKickMap(rightKickStartTime, rightKickStateString, pojoList);

            if (rightKickDate != null && leftKickData != null && pojoList.size() == 0) {
                pojoList = FootballTeamGameUtils.packKickState(dataList);
            }
            sortKickList(pojoList);
        }

        Date startDate = new Date();
        int activityTime = 0; // 开始-上传
        long timeLag;   //平均时间间隔
        if ("team".equals(curveType)) {
            startDate = game.getCompetitionTime();
            activityTime = (int) Math.abs(startDate.getTime() - game.getFinishTime().getTime());   //开始->结束时间段
            if (game.getFinishTime().getTime() == 1514736000000L) {
                activityTime = (int) Math.abs(startDate.getTime() - startDate.getTime() + FootballContants.GAMEDURATION);
            }
        } else if ("person".equals(curveType)) {
            Date leftStartData = null;
            Date rightStartData = null;

            Date leftUplodData = null;
            Date rightUplodData = null;
            Date uploadDate = new Date();
            if (dataList.size() > 0) {
                leftStartData = dataList.get(0).getCreateTime();
                leftUplodData = dataList.get(0).getUpdateTime();
            }
            if (dataList.size() > 1) {
                rightStartData = dataList.get(1).getCreateTime();
                rightUplodData = dataList.get(1).getUpdateTime();
            }

            //取个人曲线的起始时间
            if (leftStartData != null) {
                if (rightStartData != null) {
                    if (leftStartData.getTime() <= rightStartData.getTime()) {
                        startDate = leftStartData;
                    } else {
                        startDate = rightStartData;
                    }
                } else {
                    startDate = leftStartData;
                }
            } else if (rightStartData != null) {
                startDate = rightStartData;
            }
            //取个人曲线的上传时间
            if (leftUplodData != null) {
                if (rightUplodData != null) {
                    if (leftUplodData.getTime() >= rightUplodData.getTime()) {
                        uploadDate = leftUplodData;
                    } else {
                        uploadDate = rightUplodData;
                    }
                } else {
                    uploadDate = leftUplodData;
                }
            } else if (rightStartData != null) {
                uploadDate = rightUplodData;
            }

            //取个人触球时间
            FootballTeamGameStatisticsPlayer player;
            if (game != null) {
                player = footballTeamGameStatisticsPlayerDaoService.findByGameIdAndUserId(game.getId(), user.getId());
            } else {
                player = footballTeamGameStatisticsPlayerDaoService.findByPracticeIdAndUserId(practice.getId(), user.getId());
            }
            if (player == null) {
                if (game != null) {
                    uploadDate = game.getFinishTime();
                } else {
                    uploadDate = new Date(practice.getCreateTime().getTime() + FootballContants.GAMEDURATION);
                }
            }
            activityTime = (int) Math.abs(startDate.getTime() - uploadDate.getTime());   //个人启动->上传时间段
        }

        long tenMin = 10 * 60 * 1000;
        long oneMin = 1 * 60 * 1000;
        if (activityTime < tenMin && activityTime > oneMin) {
            int min = new BigDecimal((double) activityTime / 1000 / 60).setScale(0, RoundingMode.HALF_UP).intValue();
            pointCount = min;
            activityTime = min * 60 * 1000;
        } else if (activityTime >= 0 && activityTime <= oneMin) {
            int min;
            if (activityTime == 0) {
                min = 1;
            } else {
                min = new BigDecimal((double) activityTime / 1000 / 60).setScale(0, BigDecimal.ROUND_UP).intValue();
            }
            pointCount = min;
            activityTime = min * 60 * 1000;
        } else {
            pointCount = 10;
        }
        timeLag = new BigDecimal(activityTime / pointCount).setScale(0, RoundingMode.HALF_UP).longValue();
        //切割时间段计算曲线
        List<CurvePojo> highCurveList = getTempCurve(HighSpeedMoveDataJSONA, timeLag, pointCount, startDate, user, 1);
        List<CurvePojo> midCurveList = getTempCurve(MidSpeedMoveDataJSONA, timeLag, pointCount, startDate, user, 2);
        List<CurvePojo> lowCurveList = getTempCurve(LowSpeedMoveDataJSONA, timeLag, pointCount, startDate, user, 3);
        List<CurvePojo> normalCurveList = getTempCurve(NormalSpeedMoveDataJSONA, timeLag, pointCount, startDate, user, 4);

        CurvePojo highCurvePojo = highCurveList.get(0);
        CurvePojo midCurvePojo = midCurveList.get(0);
        CurvePojo lowCurvePojo = lowCurveList.get(0);
        CurvePojo normalCurvePojo = normalCurveList.get(0);

        CurvePojo kcalHighCurvePojo = highCurveList.get(2);
        CurvePojo kcalMidCurvePojo = midCurveList.get(2);
        CurvePojo kcalLowCurvePojo = lowCurveList.get(2);
        CurvePojo kcalNormalCurvePojo = normalCurveList.get(2);

        ballCurvePojo = getBallTempCurve(pojoList, timeLag, pointCount, startDate, user);
        ballCurvePojo.setRealMin(new BigDecimal(ballCurvePojo.getMaxX()).setScale(0, BigDecimal.ROUND_HALF_UP).doubleValue());
        //四条曲线相加
        curvePojo = FootballTeamGameUtils.mergeCurvePojo(highCurvePojo, midCurvePojo);
        curvePojo = FootballTeamGameUtils.mergeCurvePojo(curvePojo, lowCurvePojo);
        curvePojo = FootballTeamGameUtils.mergeCurvePojo(curvePojo, normalCurvePojo);
        curvePojo.setRealMin(new BigDecimal(curvePojo.getMaxX()).setScale(0, BigDecimal.ROUND_HALF_UP).doubleValue());


        kcalCurvePojo = FootballTeamGameUtils.mergeCurvePojo(kcalHighCurvePojo, kcalMidCurvePojo);
        kcalCurvePojo = FootballTeamGameUtils.mergeCurvePojo(kcalCurvePojo, kcalLowCurvePojo);
        kcalCurvePojo = FootballTeamGameUtils.mergeCurvePojo(kcalCurvePojo, kcalNormalCurvePojo);
        kcalCurvePojo.setRealMin(new BigDecimal(kcalCurvePojo.getMaxX()).setScale(0, BigDecimal.ROUND_HALF_UP).doubleValue());

        List<CurvePojo> list = new ArrayList<>();
        CurvePojo notAddUp = new CurvePojo();
        notAddUp.setMaxX(kcalCurvePojo.getMaxX());
        notAddUp.setMaxY(kcalCurvePojo.getMaxY());
        notAddUp.setAxisX(new ArrayList<>(kcalCurvePojo.getAxisX()));
        notAddUp.setAxisY(new ArrayList<>(kcalCurvePojo.getAxisY()));
        notAddUp.setAxisXLong(new ArrayList<>(kcalCurvePojo.getAxisXLong()));
        notAddUp.setRealMin(new BigDecimal(notAddUp.getMaxX()).setScale(0, BigDecimal.ROUND_HALF_UP).doubleValue());
        //卡路里曲线累加
        List<Integer> listY = kcalCurvePojo.getAxisY();
        for (int i = 0; i < listY.size(); i++) {
            if (i > 0) {
                listY.set(i, listY.get(i) + listY.get(i - 1));
            }
        }
        kcalCurvePojo.setMaxY(listY.get(listY.size() - 1));
        kcalCurvePojo.setAxisY(listY);

        list.add(curvePojo);
        list.add(ballCurvePojo);
        list.add(kcalCurvePojo);
        list.add(notAddUp);
        return list;*/
    }

    public static List<CurvePojo> getCurves(com.alibaba.fastjson.JSONArray array, long timeLag, long pointCount, long zeroTime,long uploadTime, User user) {
        CurvePojo runCurve = makeCurve();
        CurvePojo ballRunCurve = makeCurve();
        CurvePojo kcalCurve = makeCurve();
        for (int i = 0; i < pointCount; i++) {
            long before = zeroTime + (i * timeLag);
            long after = zeroTime + (i * timeLag) + timeLag;
            if (i == pointCount - 1) {
                after = uploadTime;
            }

            Double tempX = (double) ((i + 1) * timeLag) / 1000 / 60;
            if (i == pointCount - 1) {
                tempX = new BigDecimal(tempX).setScale(0, RoundingMode.HALF_UP).doubleValue();
            } else {
                tempX = new BigDecimal(tempX).setScale(1, RoundingMode.HALF_UP).doubleValue();
            }
            Map<String,Double> map =  getYOfMap(array,before,after);
            runCurve.getAxisX().add(tempX);
            runCurve.getAxisXLong().add(after);
            runCurve.getAxisY().add(map.get("distance").intValue());

            ballRunCurve.getAxisX().add(tempX);
            ballRunCurve.getAxisXLong().add(after);
            ballRunCurve.getAxisY().add(map.get("ballDistance").intValue());

            kcalCurve.getAxisX().add(tempX);
            kcalCurve.getAxisXLong().add(after);
            kcalCurve.getAxisY().add( new BigDecimal(user.getWeight().doubleValue() * map.get("distance") / 1000.0 * 1.036).setScale(0, RoundingMode.HALF_UP).intValue());
        }
        List<CurvePojo> list = new ArrayList<>();
        runCurve.setMaxX(Collections.max(runCurve.getAxisX()));runCurve.setMaxY(Collections.max(runCurve.getAxisY()));runCurve.setRealMin(runCurve.getMaxX());
        runCurve.setSumY(runCurve.getAxisY().stream().mapToInt(x -> x).sum());
        ballRunCurve.setMaxX(Collections.max(ballRunCurve.getAxisX()));ballRunCurve.setMaxY(Collections.max(ballRunCurve.getAxisY()));ballRunCurve.setRealMin(ballRunCurve.getMaxX());
        ballRunCurve.setSumY(ballRunCurve.getAxisY().stream().mapToInt(x -> x).sum());
        kcalCurve.setMaxX(Collections.max(kcalCurve.getAxisX()));kcalCurve.setMaxY(Collections.max(kcalCurve.getAxisY()));kcalCurve.setRealMin(kcalCurve.getMaxX());
        kcalCurve.setSumY(kcalCurve.getAxisY().stream().mapToInt(x -> x).sum());
        list.add(runCurve);
        list.add(ballRunCurve);
        list.add(kcalCurve);
        list.add(kcalCurve);
        return list;
    }

    public static CurvePojo makeCurve() {
        CurvePojo curvePojo = new CurvePojo();
        curvePojo.setAxisX(new ArrayList<>());
        curvePojo.setAxisY(new ArrayList<>());
        curvePojo.setAxisXLong(new ArrayList<>());
        return curvePojo;
    }

    public static Map getYOfMap(com.alibaba.fastjson.JSONArray array, long beforTime, long afterTime) {
        Map<String,Double> map = new HashMap<>();
        double distance = 0;
        double ballDistance = 0;
        for (int i = 0; i < array.size(); i++) {
            double dis = getScaleDis(array.getJSONObject(i),beforTime,afterTime);
            distance += dis;
            if (array.getJSONObject(i).getIntValue("isHaveBall") == 1) {
                ballDistance += dis;
            }
        }
        map.put("distance",distance);
        map.put("ballDistance",ballDistance);
        return map;
    }

    public static double getScaleDis( com.alibaba.fastjson.JSONObject jsonObject, long beforeTime, long afterTime) {
        long startTime = jsonObject.getLongValue("startTime");
        long endTime = jsonObject.getLongValue("endTime");
        double distance = 0;
        //有交集
        if (startTime <= afterTime && endTime >= beforeTime && endTime >= startTime && afterTime >= beforeTime) {
            //交集开始时间戳和结束时间戳
            long beTime = startTime >= beforeTime ? startTime : beforeTime;
            long fiTime = endTime <= afterTime ? endTime : afterTime;

            //交集差值和数据包差值（毫秒数）
            long inValue = Math.abs(fiTime - beTime);
            long xValue = Math.abs(endTime - startTime);

            //按比例四舍五入取距离
            if (inValue < xValue) {
                // * 1.0使long转为double 否则计算结果会为0
                double scale = (inValue * 1.0) / (xValue * 1.0);
                distance = scale * jsonObject.getIntValue("stepCount");
            } else {
                distance = jsonObject.getIntValue("stepCount");
            }
        }
        return distance;
    }

    //获得带球跑动曲线
    public CurvePojo getBallTempCurve(List<KickStatePojo> kickStatePojoList, long timeLag, long pointCount, Date zeroTime, User user) {
        //带球跑动曲线
        CurvePojo ballCurvePojo = new CurvePojo();
        List<Double> ballAxisX = new ArrayList<>();
        List<Long> ballAxisXLong = new ArrayList<>();
        List<Integer> ballAxisY = new ArrayList<>();
        Double ballMaxX = 0.0;
        Integer ballMaxY = 0;
        for (int i = 0; i < pointCount; i++) {
            long before = zeroTime.getTime() + (i * timeLag);
            long after = zeroTime.getTime() + (i * timeLag) + timeLag;

            Double tempX = (double) ((i + 1) * timeLag) / 1000 / 60;
            tempX = new BigDecimal(tempX).setScale(1, BigDecimal.ROUND_HALF_UP).doubleValue();
            if (i == pointCount - 1) {
                tempX = new BigDecimal(tempX).setScale(0, BigDecimal.ROUND_HALF_UP).doubleValue();
            }

            ballAxisX.add(tempX);
            ballAxisXLong.add(after);

            double ballDistance = getBallDistance(kickStatePojoList, before, after, user);
            Integer ballTempY;
            ballTempY = new BigDecimal(ballDistance).setScale(0, BigDecimal.ROUND_HALF_UP).intValue();

            ballAxisY.add(ballTempY);

            if (ballMaxX <= tempX) {
                ballMaxX = tempX;
            }
            if (ballMaxY <= ballTempY) {
                ballMaxY = ballTempY;
            }
            //封装带球
            ballCurvePojo.setAxisX(ballAxisX);
            ballCurvePojo.setAxisY(ballAxisY);
            ballCurvePojo.setMaxX(ballMaxX);
            ballCurvePojo.setMaxY(ballMaxY);
            ballCurvePojo.setAxisXLong(ballAxisXLong);
        }
        return ballCurvePojo;
    }

    //计算团队触球和球权曲线
    public static List<CurvePojo> getPowerAndTouchBall(List<UserHardwareData> dataOfown, List<UserHardwareData> dataOfOther, CurvePojo curveOfTemp,Date originTime,Long ownTeamId,Long otherTeamId) {
        //团队触球曲线
        CurvePojo touchCurve = new CurvePojo();
        touchCurve.setAxisX(curveOfTemp.getAxisX());
        touchCurve.setAxisXLong(curveOfTemp.getAxisXLong());
        touchCurve.setMaxX(curveOfTemp.getMaxX());
        touchCurve.setRealMin(curveOfTemp.getRealMin());

        //团队球权曲线
        CurvePojo powerCurve = new CurvePojo();
        powerCurve.setAxisX(curveOfTemp.getAxisX());
        powerCurve.setAxisXLong(curveOfTemp.getAxisXLong());
        powerCurve.setMaxX(curveOfTemp.getMaxX());
        powerCurve.setRealMin(curveOfTemp.getRealMin());

        JSONArray ownTouchArray = dataToTouchArray(dataOfown, ownTeamId);
        JSONArray oppoTouchArray = dataToTouchArray(dataOfOther, otherTeamId);
        oppoTouchArray.addAll(ownTouchArray);
        //触球时间排序
        ownTouchArray.sort(Comparator.comparing(obj -> ((JSONObject) obj).getLongValue("kickTime")));
        //主客队触球时间排序
        oppoTouchArray.sort(Comparator.comparing(obj -> ((JSONObject) obj).getLongValue("kickTime")));
        Map<Long, List> ownAndOppoMap = packTimeOfPower(oppoTouchArray);
        List<Map> timeList = new ArrayList<>();
        if (ownAndOppoMap.containsKey(ownTeamId)) {
            timeList = ownAndOppoMap.get(ownTeamId);
        }

        List<Integer> touchBallY = new ArrayList<>();
        List<Integer> powerBallY = new ArrayList<>();
        for (int i = 0; i < touchCurve.getAxisXLong().size(); i++) {
            //取i和i-1的点
            long afterTime = touchCurve.getAxisXLong().get(i);
            long beforTime;
            if (i > 0) {
                beforTime = touchCurve.getAxisXLong().get(i - 1);
            } else {
                beforTime = originTime.getTime();
            }
            //计算触球Y值
            int touchBall = 0;
            for (int j = 0; j < ownTouchArray.size(); j++) {
                long kickTime = ownTouchArray.getJSONObject(j).getLongValue("kickTime");
                if (kickTime >= beforTime && kickTime < afterTime) {
                    touchBall++;
                }
            }
            touchBallY.add(touchBall);
            //计算球权Y值
            int sec = 0;
            //比对球权时间，算出y值
            for (Map<String, Long> map : timeList) {
                long startTime = map.get("start");
                long endTime = map.get("end");
                //如果两段时间有交集，取交集秒数
                if (startTime <= afterTime && endTime >= beforTime && endTime >= startTime && afterTime >= beforTime) {
                    //交集开始时间戳和结束时间戳
                    long beTime = startTime >= beforTime ? startTime : beforTime;
                    long fiTime = endTime <= afterTime ? endTime : afterTime;
                    long inValue = Math.abs(fiTime - beTime);
                    sec += new Long(inValue / 1000L).intValue();
                }
            }
            powerBallY.add(sec);
        }
        //封装触球曲线
        touchCurve.setAxisY(touchBallY);
        touchCurve.setMaxY(Collections.max(touchBallY));
        //封装球权曲线
        powerCurve.setAxisY(powerBallY);
        powerCurve.setMaxY(Collections.max(powerBallY));

        List<CurvePojo> list = new ArrayList<>();
        list.add(touchCurve);
        list.add(powerCurve);
        return list;
    }

    public static Map packTimeOfPower(JSONArray touchBallArray) {
        Map<Long,List> map = new HashMap<>();
        int j = 0;
        for (int i = 0; i < touchBallArray.size(); i++) {
            JSONObject object = touchBallArray.getJSONObject(i);
            if (object.getLongValue("teamId") != touchBallArray.getJSONObject(j).getLongValue("teamId")) {
                Map<String, Long> timeMap = new HashMap<>();
                timeMap.put("start", touchBallArray.getJSONObject(j).getLongValue("kickTime"));
                timeMap.put("end", object.getLongValue("kickTime"));
                j = i;
                List<Map> timeList = new ArrayList<>();
                if (map.containsKey(object.getLongValue("teamId"))) {
                    timeList = map.get(object.getLongValue("teamId"));
                }
                timeList.add(timeMap);
                map.put(object.getLongValue("teamId"), timeList);
            } else if (i == touchBallArray.size() - 1) {
                Map<String, Long> timeMap = new HashMap<>();
                timeMap.put("start", touchBallArray.getJSONObject(j).getLongValue("kickTime"));
                timeMap.put("end", object.getLongValue("kickTime"));
                List<Map> timeList = new ArrayList<>();
                if (map.containsKey(object.getLongValue("teamId"))) {
                    timeList = map.get(object.getLongValue("teamId"));
                }
                timeList.add(timeMap);
                map.put(object.getLongValue("teamId"), timeList);
            }
        }
        return map;
    }

    public static JSONArray dataToTouchArray(List<UserHardwareData> dataOfsome,Long teamId) {
        JSONArray touchArray = new JSONArray();
        if (teamId != null) {
            dataOfsome.forEach(data -> {
                if (data.getKickBallStartTime() != null && data.getKickBallData() != null) {
                    JSONArray kickArray = JSONArray.parseArray(data.getKickBallData());
                    for (int i = 0; i < kickArray.size(); i++) {
                        JSONObject object = new JSONObject();
                        object.put("userId", data.getUser().getId());
                        object.put("kickTime", data.getKickBallStartTime().getTime() + kickArray.getIntValue(i) * 1000L);
                        object.put("teamId",teamId);
                        touchArray.add(object);
                    }
                }
            });
        }
        return touchArray;
    }

    //计算b1到e1的带球距离
    public Double getBallDistance(List<KickStatePojo> kickStatePojoList, long b1, long e1, User user) {
        double ballDistance;
        Short height = 170;
        if (user != null && user.getHeight() != null && user.getHeight() != 0) {
            height = user.getHeight();
        }
        StepWidthPojo stepWidthPojo = new StepWidthPojo();
        List<KickStatePojo> removeList = new ArrayList<>();
        for (KickStatePojo kickStatePojo : kickStatePojoList) {
            long kickTime = kickStatePojo.getKickTime();
            if (kickTime >= b1 && kickTime < e1) {
                FootballTeamUtils.addStepWidth(kickStatePojo.getState(), stepWidthPojo);
                removeList.add(kickStatePojo);
            } else if (kickTime < b1) {
                removeList.add(kickStatePojo);
            } else {
//                ballDistance += FootballTeamUtils.calculateMoveDistance(stepWidthPojo.getStepWidthSum(), height);
                break;
            }
        }
        if (removeList.size() > 0) {
            kickStatePojoList.removeAll(removeList);
        }
        ballDistance = FootballTeamUtils.calculateMoveDistance(stepWidthPojo.getStepWidthSum(), height);
        return ballDistance;
    }

    public List<KickStatePojo> packageKickMap(Date kickStartTime, String JSONString, List<KickStatePojo> list) throws JSONException {
        if (list == null) {
            list = new ArrayList<>();
        }
        if (!"".equals(JSONString) && JSONString != null) {
            JSONArray jsonA;
            jsonA = JSONArray.parseArray(JSONString);
            for (int i = 0; i < jsonA.size(); i++) {
                JSONObject tempObject = jsonA.getJSONObject(i);
                KickStatePojo pojo = new KickStatePojo();
                pojo.setSec(tempObject.getInteger("sec"));
                pojo.setState(tempObject.getInteger("state"));
                pojo.setKickTime(kickStartTime.getTime() + pojo.getSec() * 1000);
                list.add(pojo);
            }
        }
        return list;
    }

    public List<KickStatePojo> sortKickList(List<KickStatePojo> list) {
        list.sort(Comparator.comparingLong(KickStatePojo::getKickTime));
        return list;
    }

    //获得曲线
    public List<CurvePojo> getTempCurve(JSONArray array, long timeLag, long pointCount, Date zeroTime, User user, int type) throws JSONException {
        //跑动曲线
        CurvePojo tempCurvePojo = new CurvePojo();
        List<Double> axisX = new ArrayList<>();
        List<Long> axisXLong = new ArrayList<>();
        List<Integer> axisY = new ArrayList<>();
        Double maxX = 0.0;
        Integer maxY = 0;
        //带球跑动曲线
        CurvePojo ballCurvePojo = new CurvePojo();
        List<Double> ballAxisX = new ArrayList<>();
        List<Long> ballAxisXLong = new ArrayList<>();
        List<Integer> ballAxisY = new ArrayList<>();
        Double ballMaxX = 0.0;
        Integer ballMaxY = 0;
        //卡路里曲线
        CurvePojo kcalCurvePojo = new CurvePojo();
        List<Double> kcalAxisX = new ArrayList<>();
        List<Long> kcalAxisXLong = new ArrayList<>();
        List<Integer> kcalAxisY = new ArrayList<>();
        Double kcalMaxX = 0.0;
        Integer kcalMaxY = 0;

        for (int i = 0; i < pointCount; i++) {
            long before = zeroTime.getTime() + (i * timeLag);
            long after = zeroTime.getTime() + (i * timeLag) + timeLag;

            Double tempX = (double) ((i + 1) * timeLag) / 1000 / 60;
            tempX = new BigDecimal(tempX).setScale(1, BigDecimal.ROUND_HALF_UP).doubleValue();
            if (i == pointCount - 1) {
                tempX = new BigDecimal(tempX).setScale(0, BigDecimal.ROUND_HALF_UP).doubleValue();
            }

            axisX.add(tempX);
            ballAxisX.add(tempX);
            kcalAxisX.add(tempX);

            axisXLong.add(after);
            ballAxisXLong.add(after);
            kcalAxisXLong.add(after);

            Map<String, Long> distanceMap = getDistance(array, before, after, user, type);
            Integer tempY;
            tempY = distanceMap.get("distance").intValue();
            Integer ballTempY;
            ballTempY = distanceMap.get("ballDistance").intValue();
            Integer kcalTempY;
            kcalTempY = distanceMap.get("kcal").intValue();

            axisY.add(tempY);
            ballAxisY.add(ballTempY);
            kcalAxisY.add(kcalTempY);

            if (maxX <= tempX) {
                maxX = tempX;
            }
            if (maxY <= tempY) {
                maxY = tempY;
            }
            if (ballMaxX <= tempX) {
                ballMaxX = tempX;
            }
            if (ballMaxY <= ballTempY) {
                ballMaxY = ballTempY;
            }
            if (kcalMaxX <= tempX) {
                kcalMaxX = tempX;
            }
            if (kcalMaxY <= kcalTempY) {
                kcalMaxY = kcalTempY;
            }
            //封装跑动
            tempCurvePojo.setAxisX(axisX);
            tempCurvePojo.setAxisY(axisY);
            tempCurvePojo.setMaxX(maxX);
            tempCurvePojo.setMaxY(maxY);
            tempCurvePojo.setAxisXLong(axisXLong);
            //封装带球
            ballCurvePojo.setAxisX(ballAxisX);
            ballCurvePojo.setAxisY(ballAxisY);
            ballCurvePojo.setMaxX(ballMaxX);
            ballCurvePojo.setMaxY(ballMaxY);
            ballCurvePojo.setAxisXLong(ballAxisXLong);
            //封装卡路里
            kcalCurvePojo.setAxisX(kcalAxisX);
            kcalCurvePojo.setAxisY(kcalAxisY);
            kcalCurvePojo.setMaxX(kcalMaxX);
            kcalCurvePojo.setMaxY(kcalMaxY);
            kcalCurvePojo.setAxisXLong(kcalAxisXLong);
        }
        List<CurvePojo> list = new ArrayList<>();
        list.add(tempCurvePojo);
        list.add(ballCurvePojo);
        list.add(kcalCurvePojo);
        return list;
    }

    //计算b1到e1时间段的跑动和卡路里
    public Map<String, Long> getDistance(JSONArray array, long b1, long e1, User user, int type) throws JSONException {
        Short height = 170;
        Short weight = 60;
        //跑动距离
        Long distance = 0L;
        int stepCountSum = 0;
        int intervalTimeSum = 0;
        //带球跑动距离
        Long ballDistance = 0L;
        int ballStepCountSum = 0;
        int ballIntervalTimeSum = 0;
        //卡路里
        Long kcal = 0L;
        double stepWidth;
        if (user != null) {
            if (user.getHeight() != null && user.getHeight() > 0) {
                height = user.getHeight();
            }
            if (user.getWeight() != null && user.getWeight() > 0) {
                weight = user.getWeight();
            }
        }
        stepWidth = (height * 0.45 / 100.0);
        if (array != null && !"".equals(array.toString())) {
            for (int i = 0; i < array.size(); i++) {
                JSONObject tempObject = array.getJSONObject(i);

                long b2 = (long) tempObject.get("startTime");
                long e2 = (long) tempObject.get("endTime");

                int stepCount = (int) tempObject.get("stepCount");
                int intervalTime = (int) tempObject.get("intervalTime");
                int haveBall = 0;
                if (tempObject.containsKey("isHaveBall")) {
                    haveBall = (int) tempObject.get("isHaveBall");
                }

                if (b1 <= b2 && e1 >= e2) {   //（b1---【b2-----e2】--e1）1包含2
                    stepCountSum += stepCount;
                    intervalTimeSum += intervalTime;

                    if (haveBall == 1) {
                        ballStepCountSum += stepCount;
                        ballIntervalTimeSum += intervalTime;
                    }
                } else if (b1 >= b2 && e1 <= e2) {     //【b2---（b1-----e1）--e2】2包含1
                    double dStop = (double) stepCount * (e1 - b1) / intervalTime;
                    stepCountSum += (Long) Math.round(dStop);
                    intervalTimeSum += e1 - b1;

                    if (haveBall == 1) {
                        ballStepCountSum += stepCount * ((e1 - b1) / intervalTime);
                        ballIntervalTimeSum += e1 - b1;
                    }
                } else if (b1 <= b2 && e1 <= e2 && e1 >= b2) {     //（b1---【b2---e1）----e2】 相交1
                    double dStop = (double) stepCount * (e1 - b2) / intervalTime;
                    stepCountSum += (Long) Math.round(dStop);
                    intervalTimeSum += e1 - b2;

                    if (haveBall == 1) {
                        ballStepCountSum += stepCount * ((e1 - b2) / intervalTime);
                        ballIntervalTimeSum += e1 - b2;
                    }
                } else if (b1 >= b2 && b1 <= e2 && e2 <= e1) {     //【b2---(b1---e2】----e1) 相交2
                    double dStop = (double) stepCount * (e2 - b1) / intervalTime;
                    stepCountSum += (Long) Math.round(dStop);
                    intervalTimeSum += e2 - b1;

                    if (haveBall == 1) {
                        ballStepCountSum += stepCount * ((e2 - b1) / intervalTime);
                        ballIntervalTimeSum += e2 - b1;
                    }
                }
            }

            // 计算跑动数据
            distance = UserDataUtil.calculateMoveDistanceNew(stepCountSum, intervalTimeSum, height, type);
            // 带球跑动距离
            ballDistance = UserDataUtil.calculateMoveDistanceNew(ballStepCountSum, ballIntervalTimeSum, height, type);
            //计算卡路里
            kcal = new BigDecimal(weight * (stepCountSum * stepWidth) / 1000 * 1.036).setScale(0, BigDecimal.ROUND_UP).longValue();
        }
        Map<String, Long> map = new HashMap<>();
        map.put("distance", distance);
        map.put("ballDistance", ballDistance);
        map.put("kcal", kcal);
        return map;
    }
}






