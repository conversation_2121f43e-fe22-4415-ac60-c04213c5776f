//package com.microteam.base.agency.dao.impl;
//
//import com.microteam.base.agency.dao.FootballAgencyClassPublicationDao;
//import com.microteam.base.entity.agency.FootballAgencyClassPublication;
//import com.microteam.base.common.base.AbstractHibernateDao;
//import org.apache.log4j.Logger;
//import org.hibernate.Hibernate;
//import org.hibernate.query.Query;
//import org.springframework.stereotype.Repository;
//
//import java.util.ArrayList;
//import java.util.List;
//
//@Repository("footballAgencyClassPublicationDao")
//public class FootballAgencyClassPublicationDaoImpl extends AbstractHibernateDao<FootballAgencyClassPublication> implements FootballAgencyClassPublicationDao {
//    static Logger logger = Logger.getLogger(FootballAgencyClassPublicationDaoImpl.class.getName());
//
//    public FootballAgencyClassPublicationDaoImpl() {
//        super();
//        setClazz(FootballAgencyClassPublication.class);
//    }
//
//    @Override
//    public List<FootballAgencyClassPublication> findByAgencyIdAndAgencyClassIdForPage(Long agencyId, Long agencyClassId, int pageSize, int page) {
//        String hql = "from FootballAgencyClassPublication as footballAgencyClassPublication " +
//                "left join fetch footballAgencyClassPublication.user as user " +
//                "where footballAgencyClassPublication.footballAgency.id = (:agencyId) " +
//                "and footballAgencyClassPublication.footballAgencyClass.id = (:agencyClassId) " +
//                "and footballAgencyClassPublication.deleted=false ";
//        Query query = getCurrentSession().createQuery(hql)
//                .setParameter("agencyId", agencyId)
//                .setParameter("agencyClassId", agencyClassId);
//        query.setFirstResult((page - 1) * pageSize);
//        query.setMaxResults(pageSize);
//        List<FootballAgencyClassPublication> list = query.list();
//        return list;
//    }
//
//    @Override
//    public List<FootballAgencyClassPublication> findByAgencyIdForPage(Long agencyId, int pageSize, int page) {
//        String hql = "from FootballAgencyClassPublication as footballAgencyClassPublication " +
//                "left join fetch footballAgencyClassPublication.user as user " +
//                "where footballAgencyClassPublication.footballAgency.id = (:agencyId) " +
//                "and footballAgencyClassPublication.footballAgencyClass=null " +
//                "and footballAgencyClassPublication.deleted=false " +
//                "order by footballAgencyClassPublication.createTime desc ";
//        Query query = getCurrentSession().createQuery(hql)
//                .setParameter("agencyId", agencyId);
//        query.setFirstResult((page - 1) * pageSize);
//        query.setMaxResults(pageSize);
//        List<FootballAgencyClassPublication> list = query.list();
//        return list;
//    }
//
//    @Override
//    public List<FootballAgencyClassPublication> findByAgencyIdAndJudgeForPage(Long agencyId, Long judge, int pageSize, int page) {
//        String hql = "from FootballAgencyClassPublication as footballAgencyClassPublication " +
//                "left join fetch footballAgencyClassPublication.user as user " +
//                "where footballAgencyClassPublication.footballAgency.id = (:agencyId) " +
//                "and footballAgencyClassPublication.judge = (:judge) " +
//                "and footballAgencyClassPublication.footballAgencyClass=null " +
//                "and footballAgencyClassPublication.deleted=false " +
//                "order by footballAgencyClassPublication.createTime desc ";
//        Query query = getCurrentSession().createQuery(hql)
//                .setParameter("agencyId", agencyId)
//                .setParameter("judge", judge);
//        query.setFirstResult((page - 1) * pageSize);
//        query.setMaxResults(pageSize);
//        List<FootballAgencyClassPublication> list = query.list();
//        if (list.size() > 0) {
//            List<FootballAgencyClassPublication> listnew = new ArrayList<FootballAgencyClassPublication>();
//            for (int i = 0; i < list.size(); i++) {
//                FootballAgencyClassPublication agencyCoach = list.get(i);
//                Hibernate.initialize(agencyCoach);// 获取赖加载的集合内容；
//                Hibernate.initialize(agencyCoach.getUser());// 获取赖加载的集合内容；
//                Hibernate.initialize(agencyCoach.getFootballAgency());// 获取赖加载的集合内容；
//                listnew.add(i, agencyCoach);
//            }
//            return listnew;
//        }
//        return null;
//    }
//
//    @Override
//    public FootballAgencyClassPublication findById(long id) {
//        String hql = "from FootballAgencyClassPublication as footballAgencyClassPublication " +
//                "left join fetch footballAgencyClassPublication.user as user " +
//                "left join fetch footballAgencyClassPublication.footballAgency as footballAgency " +
//                "left join fetch footballAgencyClassPublication.footballAgencyClass as footballAgencyClass " +
//                "where footballAgencyClassPublication.id=? " +
//                "and footballAgencyClassPublication.deleted=false ";
//        Query query = getCurrentSession().createQuery(hql).setParameter(0, id);
//        List<FootballAgencyClassPublication> list = query.list();
//        if (list != null && list.size() > 0) {
//            return list.get(0);
//        }
//        return null;
//    }
//
//
//
//}
