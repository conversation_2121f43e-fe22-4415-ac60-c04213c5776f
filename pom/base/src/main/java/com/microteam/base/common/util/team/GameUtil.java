package com.microteam.base.common.util.team;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONException;
import com.alibaba.fastjson.JSONObject;
import com.microteam.base.common.util.common.TranslatorUtil;
import com.microteam.base.entity.lesson.FootballLesson;
import com.microteam.base.entity.team.*;
import com.microteam.base.entity.user.User;
import com.microteam.base.entity.user.UserHardwareData;
import com.microteam.base.entity.user.UserHeadimg;
import com.microteam.base.lesson.service.FootballLessonDaoService;
import com.microteam.base.team.service.*;
import com.microteam.base.user.service.UserDaoService;
import com.microteam.base.user.service.UserHardwareDataDaoService;
import com.microteam.base.user.service.UserHeadimgDaoService;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.text.SimpleDateFormat;
import java.util.*;

@Component
public class GameUtil {
    private static Logger logger = Logger.getLogger(GameUtil.class.getName());
    @Autowired
    FootballTeamGameEnrollDaoService footballTeamGameEnrollDaoService;
    @Autowired
    FootballTeamGamePraiseDaoService footballTeamGamePraiseDaoService;
    @Autowired
    UserDaoService userDaoService;
    @Autowired
    FootballTeamGameDaoService footballTeamGameDaoService;
    @Autowired
    FootballTeamDaoService footballTeamDaoService;
    @Autowired
    FootballLessonDaoService footballLessonDaoService;
    @Autowired
    FootballTeamGameStatisticsPlayerDaoService footballTeamGameStatisticsPlayerDaoService;
    @Autowired
    UserHardwareDataDaoService userHardwareDataDaoService;
    @Autowired
    DataSource dataSource;
    @Autowired
    FootballTeamGamePassMapDaoService footballTeamGamePassMapDaoService;
    @Autowired
    FootballTeamGameLineupDaoService footballTeamGameLineupDaoService;
    @Autowired
    UserHeadimgDaoService userHeadimgDaoService;

    public User getMvpUser(FootballTeam team, FootballTeamGame game) throws Exception {
        List<FootballTeamGameEnroll> teamGameEnrollList = footballTeamGameEnrollDaoService.findByTeamIdAndGameId(team.getId(), game.getId());
        List<Long> userIdList = new ArrayList<>();
        for (FootballTeamGameEnroll enroll : teamGameEnrollList) {
            userIdList.add(enroll.getUser().getId());
        }
        List<FootballTeamGamePraise> ofUserPraiseList = footballTeamGamePraiseDaoService.findByByUserIdListAndGameIdAndTeamIdAndPraised(userIdList, game.getId(), team.getId(), 1);
        Map<Long, List<FootballTeamGamePraise>> praiseMap = (Map<Long, List<FootballTeamGamePraise>>) TranslatorUtil.listToMap("byUserId", ofUserPraiseList, 2);

        int size_old = 0;
        int size_new = 0;
        long baseUserId = 0;
        for (Map.Entry<Long, List<FootballTeamGamePraise>> entry : praiseMap.entrySet()) {
            size_new = entry.getValue().size();
            if (size_new > size_old) {
                size_old = size_new;
                baseUserId = entry.getKey();
            }
        }
        return userDaoService.findById(baseUserId);
    }

    public JSONArray getTeamFlow(FootballTeam teams, User user, int year, int month) throws JSONException {
        JSONArray gameFlowArray = new JSONArray();    //赛程
        List<FootballTeamGame> teamGames = footballTeamGameDaoService.findByTeamNameAndYearAndMonth(teams.getTeamName(), year, month);
        List<FootballLesson> lessons = footballLessonDaoService.findByUserIdOfTimeSlot(user.getId(), teams.getId(), year, month);
        if (teamGames != null && teamGames.size() > 0) {
            Map<String, FootballTeam> guestMap = new HashMap<>();   //客队实体类Map  key:teamName value:FootballTeam
            List<Long> guestTeamId = new ArrayList<>();
            for (FootballTeamGame game : teamGames) {
                guestTeamId.add(game.getGuestTeamId());
            }
            List<FootballTeam> guestTeams = footballTeamDaoService.findTeamsByTeamIdList(guestTeamId);
            if (guestTeams != null && guestTeams.size() > 0) {
                for (FootballTeam team : guestTeams) {
                    guestMap.put(team.getTeamName(), team);
                }
            }
            for (FootballTeamGame game : teamGames) {
                Map<String, Object> gameMap = new HashMap<>();
                gameMap.put("gameId", game.getId());
                if (game.isCompetition()) {
                    gameMap.put("type", 1); // 1：赛事比赛
                } else {
                    gameMap.put("type", 2); //2：友谊赛
                }
                gameMap.put("time", game.getCompetitionTime().getTime());
                gameMap.put("hostTeamName", game.getFootballTeam().getTeamName());
                gameMap.put("hostTeamtName", game.getFootballTeam().getTeamName());
                gameMap.put("hostAbbreviation", game.getFootballTeam().getTeamAbbreviation());
                gameMap.put("hostImg", game.getFootballTeam().getTeamHeadImgNetUrl());
                gameMap.put("hostTeamId", game.getFootballTeam().getId());
                gameMap.put("hostScore", game.getHostScore());
                gameMap.put("guestScore", game.getGuestScore());
                if (guestMap.containsKey(game.getOpponent())) {
                    gameMap.put("guestTeamId", guestMap.get(game.getOpponent()).getId());
                    gameMap.put("guestTeamName", guestMap.get(game.getOpponent()).getTeamName());
                    gameMap.put("guestAbbreviation", guestMap.get(game.getOpponent()).getTeamAbbreviation());
                    gameMap.put("guestImg", guestMap.get(game.getOpponent()).getTeamHeadImgNetUrl());
                } else {
                    gameMap.put("guestTeamId", -1);
                    gameMap.put("guestTeamName", game.getOpponent());
                    gameMap.put("guestAbbreviation", game.getOpponent());
                    gameMap.put("guestImg", game.getOppoentHeadImg());
                }
                // 比赛默认时间不超过105分钟
                long nowTime = System.currentTimeMillis();
                long teamGameCompeteTime = game.getCompetitionTime().getTime();
                long gameEndTime = game.getFinishTime().getTime();
                if (gameEndTime < nowTime) {
                    gameMap.put("gameStatus", 1);   //比赛已结束
                } else if (teamGameCompeteTime <= nowTime && nowTime <= gameEndTime) {
                    gameMap.put("gameStatus", 2);   //比赛正在进行中
                } else if (teamGameCompeteTime > nowTime) {
                    gameMap.put("gameStatus", 3);   //比赛未开始
                }
                gameMap.put("location", game.getLocation());
                int isHomeCourt = 0;
                if (FootballTeamGameUtils.isHomeCourt(teams.getTeamName(), game)) {
                    isHomeCourt = 1;
                }
                gameMap.put("isHomeCourt", isHomeCourt);
                gameMap.put("isUpload", isUploadOfUser(game, teams, user));
                gameFlowArray.add(gameMap);
            }
        }
        for (FootballLesson lesson : lessons) {
            Map<String, Object> lessonMap = new HashMap<>();
            lessonMap.put("lessonId", lesson.getId());
            lessonMap.put("time", lesson.getStartTime().getTime());
            lessonMap.put("type", 3);    //课程
            if (lesson.getStatus().intValue() == 0) { //未开始
                lessonMap.put("gameStatus", 3);
            } else if (lesson.getStatus().intValue() <= 2 && lesson.getStatus().intValue() > 0) { //进行中
                lessonMap.put("gameStatus", 2);
            } else {
                lessonMap.put("gameStatus", 1);
            }
            gameFlowArray.add(lessonMap);
        }
        gameFlowArray = FootballTeamGameUtils.packTheSameDay(gameFlowArray);
        return gameFlowArray;
    }

    public int isUploadOfUser(FootballTeamGame game, FootballTeam team, User user) {
        int isUpload = 0;
        FootballTeamGameStatisticsPlayer player = footballTeamGameStatisticsPlayerDaoService.findByTeamIdAndGameIdAndUserId(team.getId(), game.getId(), user.getId());
        if (player != null) {
            isUpload = 1;
        }
        return isUpload;
    }

    //全队触球时间升序排列
    public JSONArray getTouchBallTime(FootballTeam team, FootballTeamGame teamGame) {
        List<FootballTeamGameEnroll> footballteamEnroll_list = footballTeamGameEnrollDaoService.findByTeamIdAndGameId(team.getId(), teamGame.getId());
        JSONArray touchball_Array = new JSONArray(); //触球时间和对应的用户id(个人)
        JSONArray teamtouch_timeArray = new JSONArray(); //触球时间和对应的用户id(全队)
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd  HH:mm:ss");
        try {
            //查询队员在比赛中的硬件数据(触球时间)
            if (footballteamEnroll_list != null && footballteamEnroll_list.size() > 1) {
                for (FootballTeamGameEnroll footballTeamGameEnroll : footballteamEnroll_list) {
                    List<UserHardwareData> userHardwaredate_list_user = userHardwareDataDaoService
                            .findByGameIdAndUserId(teamGame.getId(), footballTeamGameEnroll.getUser().getId());        //获取触球记录
                    if (userHardwaredate_list_user != null && userHardwaredate_list_user.size() > 0) {
                        for (UserHardwareData userhardwaredata_one : userHardwaredate_list_user) {
                            if (userhardwaredata_one.getIsStartUp() == 3) {
                                if (userhardwaredata_one.getKickBallData() != null && !Objects.equals(userhardwaredata_one.getKickBallData(), "")) {
                                    Date kickBalldataStartTime = userhardwaredata_one.getKickBallStartTime();
                                    long kickBalldataStartTime_long = kickBalldataStartTime.getTime();
                                    JSONArray kickBalldata_array;
                                    kickBalldata_array = JSONArray.parseArray(userhardwaredata_one.getKickBallData());
                                    for (int y = 0; y < kickBalldata_array.size(); y++) {
                                        long touchball_time = kickBalldataStartTime_long + kickBalldata_array.getInteger(y) * 1000;
                                        Map<String, Object> touchball_time_user_map = new HashMap<String, Object>();
                                        touchball_time_user_map.put("timeFormat", sdf.format(new Date(touchball_time)));
                                        touchball_time_user_map.put("touchTime", touchball_time);       //触球时间
                                        touchball_time_user_map.put("userId", userhardwaredata_one.getUser().getId());     //用户id
                                        touchball_time_user_map.put("hardwareType", userhardwaredata_one.getUserHardware().getHardwareType());     //左右脚 1、left，2、right
                                        touchball_Array.add(touchball_time_user_map);
                                    }
                                }  // if  userhardwaredata_one
                            } // if userhardwaredata_one.getIsStartUp()==3
                        } // if userHardwaredate_list_user
                    } // if
                } // for
            }// if


            //把全队每个人的的触球时间按时间排序合并到一起
            if (touchball_Array != null && touchball_Array.size() > 1) {
                for (int u = 0; u < touchball_Array.size(); u++) {
                    teamtouch_timeArray.add(touchball_Array.getJSONObject(u));
                }
            }

            //排序
            /*DataUtil.sortJSONArray(teamtouch_timeArray, "touchTime", 0);*/
            /*teamtouch_timeArray.sort((o1,o2)->((JSONObject)o1).getLong("touchTime").compareTo(((JSONObject)o2).getLong("touchTime")));*/
            if (teamtouch_timeArray != null && teamtouch_timeArray.size() > 0) {
                for (int v = 0; v < teamtouch_timeArray.size(); v++) {
                    for (int g = v + 1; g < teamtouch_timeArray.size(); g++) {
                        JSONObject tocuchTimev = teamtouch_timeArray.getJSONObject(v);
                        JSONObject tocuchTimeg = teamtouch_timeArray.getJSONObject(g);
                        if (tocuchTimev.getLong("touchTime") > tocuchTimeg.getLong("touchTime")) {
                            teamtouch_timeArray.set(v, tocuchTimeg);
                            teamtouch_timeArray.set(g, tocuchTimev);
                        }
                    }
                }
            }

        } catch (Exception e) {
            e.printStackTrace();
        }
        return teamtouch_timeArray;
    }

    public int batchSave(List<FootballTeamGamePassMap> list) throws SQLException {
        PreparedStatement pst;
        Connection conn = null;
        try {
            conn = dataSource.getConnection();
            conn.setAutoCommit(false);
            //根据类的字段拼接sql
            String sql = FootballTeamGameUtils.sql(FootballTeamGamePassMap.class);
            //创建预处理
            pst = conn.prepareStatement(sql);
            for (int i = 0, length = list.size(); i < length; i++) {
                FootballTeamGamePassMap pass = list.get(i);
                FootballTeamGameUtils.prepareParams(pst, pass, FootballTeamGamePassMap.class);
                pst.addBatch();
                //1000条执行一次
                if (i % 1000 == 0) {
                    //执行
                    pst.executeBatch();
                    //清空
                    pst.clearBatch();
                }
            }
            //执行剩余的sql
            pst.executeBatch();
            //手动提交
            conn.commit();
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            try {
                conn.close();
            } catch (NullPointerException e) {
                logger.error(e);
            }
        }
        return list.size();
    }

    public JSONArray getPassLineMap(Long gameId, Long teamId, Long userId) throws SQLException {
        if (footballTeamGamePassMapDaoService.findPassListByGameIdAndTeamId(gameId, teamId).size() == 0) {
            FootballTeam team = footballTeamDaoService.findTeamsById(teamId);
            FootballTeamGame game = footballTeamGameDaoService.findById(gameId);
            JSONArray touchArray = getTouchBallTime(team, game);
            List<FootballTeamGamePassMap> passList = FootballTeamGameUtils.getPassList(game, team, touchArray);
            batchSave(passList);
        }

        JSONArray array = new JSONArray();
        FootballTeamGameLineup lineup = footballTeamGameLineupDaoService.findByGameIdAndTeamIdAndUserId(gameId, teamId, userId);

        Map<Long, String> headMap = new HashMap<>();
        Map<Long, String> nickNameMap = new HashMap<>();
        if (lineup != null && lineup.getPosition() != null) {
            JSONArray lineArray = JSONArray.parseArray(lineup.getPosition());
            List<Long> userIdList = new ArrayList<>();
            for (int i = 0; i < lineArray.size(); i++) {
                JSONObject object = lineArray.getJSONObject(i);
                userIdList.add(object.getLong("userId"));
            }

            //封装昵称Map
            List<User> userList = userDaoService.findByUserIdList(userIdList);
            for (User tempUser : userList) {
                nickNameMap.put(tempUser.getId(), tempUser.getNickName());
            }
            //封装头像Map
            List<UserHeadimg> headimgList = userHeadimgDaoService.findByUserIdList(userIdList);
            for (UserHeadimg img : headimgList) {
                headMap.put(img.getUser().getId(), img.getHeadImgNetUrl());
            }

            List<FootballTeamGamePassMap> passList = footballTeamGamePassMapDaoService.findPassListByGameIdAndTeamId(gameId, teamId);
            Map<String, FootballTeamGamePassMap> passMap = new HashMap<>();
            for (FootballTeamGamePassMap pass : passList) {
                passMap.put(pass.getPassUserId() + "_" + pass.getByPassUserId(), pass);
            }

            for (int i = 0; i < lineArray.size(); i++) {
                JSONObject object = new JSONObject(lineArray.getJSONObject(i));
                String nickName = "";
                String headImg = "";
                if (nickNameMap.containsKey(object.getLong("userId"))) {
                    nickName = nickNameMap.get(object.getLong("userId"));
                }
                if (headMap.containsKey(object.getLong("userId"))) {
                    headImg = headMap.get(object.getLong("userId"));
                }
                object.put("nickName", nickName);
                object.put("headImg", headImg);
                JSONArray byPassArray = new JSONArray();
                for (int j = 0; j < lineArray.size(); j++) {
                    Long userIdi = lineArray.getJSONObject(i).getLong("userId");
                    Long userIdj = lineArray.getJSONObject(j).getLong("userId");
                    if (!userIdi.equals(userIdj) && passMap.containsKey(userIdi + "_" + userIdj)) {
                        JSONObject byObject = new JSONObject();
                        String byNickName = "";
                        String byHeadImg = "";
                        if (nickNameMap.containsKey(userIdj)) {
                            byNickName = nickNameMap.get(userIdj);
                        }
                        if (headMap.containsKey(userIdj)) {
                            byHeadImg = headMap.get(userIdj);
                        }
                        byObject.put("byUserId", userIdj);
                        byObject.put("byHeadImg", byHeadImg);
                        byObject.put("byPositionId", lineArray.getJSONObject(j).getLong("positionId"));
                        byObject.put("byNickName", byNickName);
                        byObject.put("count", passMap.get(userIdi + "_" + userIdj).getCount());
                        byPassArray.add(byObject);
                    }
                }
                object.put("byPass", byPassArray);
                array.add(object);
            }
        }
        return array;
    }
}
