package com.microteam.base.common.pojo.lesson;

public class LessonData {
    private Long wholeMoveDistance;
    private Integer passBallCounts;
    private Integer carryDistance;
    private Integer passBallError;
    private Double maxSprintSpeed;
    private Double passRate;
    private Integer highDistance;
    private Integer highMoveCount;
    private Integer midDistance;
    private Integer midMoveCount;
    private Integer lowDistance;
    private Integer lowMoveCount;
    private Integer normalDistance;
    private Integer normalMoveCount;
    private Integer highCarryDistance;
    private Integer highCarryCount;
    private Integer midCarryDistance;
    private Integer midCarryCount;
    private Integer lowCarryDistance;
    private Integer lowCarryCount;
    private Integer normalCarryDistance;
    private Integer normalCarryCount;


    public Long getWholeMoveDistance() {
        return wholeMoveDistance;
    }

    public void setWholeMoveDistance(Long wholeMoveDistance) {
        this.wholeMoveDistance = wholeMoveDistance == null ? Long.valueOf(0) : wholeMoveDistance;
    }

    public Integer getPassBallCounts() {
        return passBallCounts;
    }

    public void setPassBallCounts(Integer passBallCounts) {
        this.passBallCounts = passBallCounts == null ? Integer.valueOf(0) : passBallCounts;
    }

    public Integer getCarryDistance() {
        return carryDistance;
    }

    public void setCarryDistance(Integer carryDistance) {
        this.carryDistance = carryDistance == null ? Integer.valueOf(0) : carryDistance;
    }

    public Integer getPassBallError() {
        return passBallError;
    }

    public void setPassBallError(Integer passBallError) {
        this.passBallError = passBallError == null ? Integer.valueOf(0) : passBallError;
    }

    public Double getMaxSprintSpeed() {
        return maxSprintSpeed;
    }

    public void setMaxSprintSpeed(Double maxSprintSpeed) {
        this.maxSprintSpeed = maxSprintSpeed == null ? Double.valueOf(0) : maxSprintSpeed;
    }

    public Double getPassRate() {
        if (passBallCounts + passBallError > 0) {
            double temp = passBallCounts.doubleValue() / (passBallCounts.doubleValue() + passBallError.doubleValue());
            passRate = (Math.round(temp * 10.0) / 10.0);
        }
        return passRate;
    }

    public void setPassRate(Double passRate) {
        this.passRate = passRate == null ? Double.valueOf(0) : passRate;
    }

    public Integer getHighDistance() {
        return highDistance;
    }

    public void setHighDistance(Integer highDistance) {
        this.highDistance = highDistance == null ? Integer.valueOf(0) : highDistance;
    }

    public Integer getHighMoveCount() {
        return highMoveCount;
    }

    public void setHighMoveCount(Integer highMoveCount) {
        this.highMoveCount = highMoveCount == null ? Integer.valueOf(0) : highMoveCount;
    }

    public Integer getMidDistance() {
        return midDistance;
    }

    public void setMidDistance(Integer midDistance) {
        this.midDistance = midDistance == null ? Integer.valueOf(0) : midDistance;
    }

    public Integer getMidMoveCount() {
        return midMoveCount;
    }

    public void setMidMoveCount(Integer midMoveCount) {
        this.midMoveCount = midMoveCount == null ? Integer.valueOf(0) : midMoveCount;
    }

    public Integer getLowDistance() {
        return lowDistance;
    }

    public void setLowDistance(Integer lowDistance) {
        this.lowDistance = lowDistance == null ? Integer.valueOf(0) : lowDistance;
    }

    public Integer getLowMoveCount() {
        return lowMoveCount;
    }

    public void setLowMoveCount(Integer lowMoveCount) {
        this.lowMoveCount = lowMoveCount == null ? Integer.valueOf(0) : lowMoveCount;
    }

    public Integer getNormalDistance() {
        return normalDistance;
    }

    public void setNormalDistance(Integer normalDistance) {
        this.normalDistance = normalDistance == null ? Integer.valueOf(0) : normalDistance;
    }

    public Integer getNormalMoveCount() {
        return normalMoveCount;
    }

    public void setNormalMoveCount(Integer normalMoveCount) {
        this.normalMoveCount = normalMoveCount == null ? Integer.valueOf(0) : normalMoveCount;
    }

    public Integer getHighCarryDistance() {
        return highCarryDistance;
    }

    public void setHighCarryDistance(Integer highCarryDistance) {
        this.highCarryDistance = highCarryDistance == null ? Integer.valueOf(0) : highCarryDistance;
    }

    public Integer getHighCarryCount() {
        return highCarryCount;
    }

    public void setHighCarryCount(Integer highCarryCount) {
        this.highCarryCount = highCarryCount == null ? Integer.valueOf(0) : highCarryCount;
    }

    public Integer getMidCarryDistance() {
        return midCarryDistance;
    }

    public void setMidCarryDistance(Integer midCarryDistance) {
        this.midCarryDistance = midCarryDistance == null ? Integer.valueOf(0) : midCarryDistance;
    }

    public Integer getMidCarryCount() {
        return midCarryCount;
    }

    public void setMidCarryCount(Integer midCarryCount) {
        this.midCarryCount = midCarryCount == null ? Integer.valueOf(0) : midCarryCount;
    }

    public Integer getLowCarryDistance() {
        return lowCarryDistance;
    }

    public void setLowCarryDistance(Integer lowCarryDistance) {
        this.lowCarryDistance = lowCarryDistance == null ? Integer.valueOf(0) : lowCarryDistance;
    }

    public Integer getLowCarryCount() {
        return lowCarryCount;
    }

    public void setLowCarryCount(Integer lowCarryCount) {
        this.lowCarryCount = lowCarryCount == null ? Integer.valueOf(0) : lowCarryCount;
    }

    public Integer getNormalCarryDistance() {
        return normalCarryDistance;
    }

    public void setNormalCarryDistance(Integer normalCarryDistance) {
        this.normalCarryDistance = normalCarryDistance == null ? Integer.valueOf(0) : normalCarryDistance;
    }

    public Integer getNormalCarryCount() {
        return normalCarryCount;
    }

    public void setNormalCarryCount(Integer normalCarryCount) {
        this.normalCarryCount = normalCarryCount == null ? Integer.valueOf(0) : normalCarryCount;
    }
}
