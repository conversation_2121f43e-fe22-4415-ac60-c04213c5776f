package com.microteam.base.entity.lesson;

import com.microteam.base.common.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.io.Serializable;

@Entity
@Table(name = "training_tools")
@Getter
@Setter
public class TrainingTools extends BaseEntity implements Serializable {

    @Column(name = "imgUrl", nullable = false)
    private String imgUrl;    //训练工具图标
    @Column(name = "name", nullable = false)
    private String name;      //训练工具名称
    @Column(name = "enabled", nullable = false)
    private boolean enabled;  //就否可用

}
