//package com.microteam.base.common.util.common;
//
//import com.microteam.base.entity.user.User;
//import org.hibernate.SessionFactory;
//import org.hibernate.boot.Metadata;
//import org.hibernate.boot.MetadataSources;
//import org.hibernate.boot.registry.StandardServiceRegistry;
//import org.hibernate.boot.registry.StandardServiceRegistryBuilder;
//
//public class HibernateUtil {
//    private static SessionFactory sessionFactory;
//
//    public static SessionFactory getSessionFactory() {
//        if (sessionFactory == null) {
//            StandardServiceRegistry serviceRegistry = new StandardServiceRegistryBuilder().configure().build();
//            Metadata metadata = new MetadataSources(serviceRegistry)
//                    .addAnnotatedClass(User.class)
//                    .buildMetadata();
//
//            sessionFactory = metadata.getSessionFactoryBuilder().build();
//        }
//        return sessionFactory;
//    }
//}
