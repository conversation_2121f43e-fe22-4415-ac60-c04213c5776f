package com.microteam.base.agency.dao;

import com.microteam.base.entity.agency.FootballAgencyStudentScore;
import com.microteam.base.entity.agency.FootballAgencyStudentScoreAlbum;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

public interface FootballAgencyStudentScoreAlbumDao extends JpaRepository<FootballAgencyStudentScoreAlbum, Long>, JpaSpecificationExecutor<FootballAgencyStudentScoreAlbum> {
    //查询学员分数相册
    @Query("from FootballAgencyStudentScoreAlbum as agencyStudentScoreAlbum " +
            "where agencyStudentScoreAlbum.footballAgencyStudentScore=?1 " +
            "and footballAgencyStudentScore.deleted=false ")
    List<FootballAgencyStudentScoreAlbum> findStudentScoreAlbumByScore(FootballAgencyStudentScore agencyStudentScore);
}
