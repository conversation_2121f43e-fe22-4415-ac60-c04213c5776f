package com.microteam.base.entity.user;


import com.microteam.base.common.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;
import java.io.Serializable;

@Entity
@Table(name = "device_info")
@Getter
@Setter
public class DeviceInfo extends BaseEntity implements Serializable {

    @ManyToOne
    @JoinColumn(name = "userId", nullable = false)
    private User user;
    @Column(name = "identifierNumber")
    private String identifierNumber;
    @Column(name = "deviceName")
    private String deviceName;
    @Column(name = "phoneVersion")
    private String phoneVersion;
    @Column(name = "phoneModel")
    private String phoneModel;
    @Column(name = "appCurVersion")
    private String appCurVersion;

}
