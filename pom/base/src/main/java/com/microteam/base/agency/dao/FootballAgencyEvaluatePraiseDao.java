package com.microteam.base.agency.dao;

import com.microteam.base.entity.agency.FootballAgencyEvaluate;
import com.microteam.base.entity.agency.FootballAgencyEvaluatePraise;
import com.microteam.base.entity.user.User;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

public interface FootballAgencyEvaluatePraiseDao extends JpaRepository<FootballAgencyEvaluatePraise, Long>, JpaSpecificationExecutor<FootballAgencyEvaluatePraise> {

    //查询评论点赞
    @Query("from FootballAgencyEvaluatePraise as agencyEvaluatePraise " +
            "where agencyEvaluatePraise.footballAgencyEvaluate=?1 " +
            "and agencyEvaluatePraise.isPraised=true " +
            "and agencyEvaluatePraise.deleted=false ")
    List<FootballAgencyEvaluatePraise> findPraiseByEvaluate(FootballAgencyEvaluate agencyEvaluate);

    //查询用户对评论的点赞
    @Query("from FootballAgencyEvaluatePraise as agencyEvaluatePraise " +
            "where agencyEvaluatePraise.footballAgencyEvaluate=?1 " +
            "and agencyEvaluatePraise.user=?2 " +
            "and agencyEvaluatePraise.deleted=false ")
    FootballAgencyEvaluatePraise findPraiseByEvaluateAndUser(FootballAgencyEvaluate agencyEvaluate, User user);
}
