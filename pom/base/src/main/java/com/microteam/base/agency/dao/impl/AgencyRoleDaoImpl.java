//package com.microteam.base.agency.dao.impl;
//
//import com.microteam.base.agency.dao.AgencyRoleDao;
//import com.microteam.base.entity.agency.AgencyRole;
//import com.microteam.base.common.base.AbstractHibernateDao;
//import org.hibernate.query.Query;
//import org.springframework.stereotype.Repository;
//
//@Repository
//public class AgencyRoleDaoImpl extends AbstractHibernateDao<AgencyRole> implements AgencyRoleDao {
//
//    @Override
//    public AgencyRole findById(Long id) {
//        String hql = "from AgencyRole as agencyRole " +
//                " where agencyRole.id = (:id) " +
//                " and agencyRole.deleted = false ";
//        Query query = getCurrentSession().createQuery(hql).setParameter("id", id);
//        return (AgencyRole) query.uniqueResult();
//    }
//
////    @Override
////    public Integer findCountByAgencyIdAndRoleId(Long agencyId, Long roleId) {
////        String sql = "select count(1) from agency_role as role " +
////                " where role.agencyId = (:agencyId) " +
////                " and role.roleId = (:roleId) " +
////                " and role.deleted = 0 ";
////        Query query = getCurrentSession().createNativeQuery(sql).setParameter("agencyId", agencyId).setParameter("roleId", roleId);
////        int count = ((Number) query.uniqueResult()).intValue();
////        return count;
////    }
//}
