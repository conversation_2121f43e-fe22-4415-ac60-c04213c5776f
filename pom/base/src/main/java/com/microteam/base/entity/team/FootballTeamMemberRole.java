package com.microteam.base.entity.team;


import com.microteam.base.common.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;
import java.io.Serializable;

@Entity
@Table(name = "football_team_member_role", uniqueConstraints = @UniqueConstraint(columnNames = "roleName"))
@Getter
@Setter
public class FootballTeamMemberRole extends BaseEntity implements Serializable {

    @Column(name = "roleName", unique = true, nullable = false, length = 50)
    private String roleName;
    @Column(name = "description")
    private String description;

}
