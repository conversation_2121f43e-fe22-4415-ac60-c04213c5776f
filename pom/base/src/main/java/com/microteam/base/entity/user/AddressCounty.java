package com.microteam.base.entity.user;


import com.microteam.base.common.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.io.Serializable;

@Entity
@Table(name = "address_county")
@Getter
@Setter
public class AddressCounty extends BaseEntity implements Serializable {

    @Column(name = "code", nullable = false, length = 15)
    private String code;
    @Column(name = "county", nullable = false, length = 30)
    private String county;

}
