package com.microteam.base.entity.agency;


import com.microteam.base.common.base.BaseEntity;
import com.microteam.base.entity.user.User;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;

@Entity
@Table(name = "football_agency_class_notice")
@Getter
@Setter
public class FootballAgencyClassNotice extends BaseEntity {

    @ManyToOne
    @JoinColumn(name = "agencyId", nullable = false)
    private FootballAgency footballAgency;
    @ManyToOne
    @JoinColumn(name = "courseId")
    private FootballAgencyClassCourse footballAgencyClassCourse;
    @ManyToOne
    @JoinColumn(name = "userId", nullable = false)
    private User user;
    @ManyToOne
    @JoinColumn(name = "classId")
    private FootballAgencyClass footballAgencyClass;
    @Column(name = "content", length = 65535)
    private String content;
    @Column(name = "title", length = 200)
    private String title;


}
