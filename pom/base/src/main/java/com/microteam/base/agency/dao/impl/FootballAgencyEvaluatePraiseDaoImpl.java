//package com.microteam.base.agency.dao.impl;
//
//import com.microteam.base.agency.dao.FootballAgencyEvaluatePraiseDao;
//import com.microteam.base.entity.agency.FootballAgencyEvaluate;
//import com.microteam.base.entity.agency.FootballAgencyEvaluatePraise;
//import com.microteam.base.common.base.AbstractHibernateDao;
//import com.microteam.base.entity.user.User;
//import org.apache.log4j.Logger;
//import org.hibernate.query.Query;
//import org.springframework.stereotype.Repository;
//
//import java.util.List;
//
//@Repository("footballAgencyEvaluatePraiseDao")
//public class FootballAgencyEvaluatePraiseDaoImpl extends AbstractHibernateDao<FootballAgencyEvaluatePraise>
//        implements FootballAgencyEvaluatePraiseDao {
//    static Logger logger = Logger.getLogger(FootballAgencyEvaluatePraiseDaoImpl.class.getName());
//
//    public FootballAgencyEvaluatePraiseDaoImpl() {
//        super();
//        setClazz(FootballAgencyEvaluatePraise.class);
//
//    }
//
//    @SuppressWarnings("unchecked")
//    @Override
//    public List<FootballAgencyEvaluatePraise> findPraiseByEvaluate(
//            FootballAgencyEvaluate agencyEvaluate) {
//
//        String hql = "from FootballAgencyEvaluatePraise as agencyEvaluatePraise where agencyEvaluatePraise.footballAgencyEvaluate=? and agencyEvaluatePraise.isPraised=true and agencyEvaluatePraise.deleted=false ";
//        Query query = getCurrentSession().createQuery(hql).setParameter(0, agencyEvaluate);
//        List<FootballAgencyEvaluatePraise> list = query.list();
//        return list;
//    }
//
//    @SuppressWarnings("unchecked")
//    @Override
//    public FootballAgencyEvaluatePraise findPraiseByEvaluateAndUser(
//            FootballAgencyEvaluate agencyEvaluate, User user) {
//
//        String hql = "from FootballAgencyEvaluatePraise as agencyEvaluatePraise where agencyEvaluatePraise.footballAgencyEvaluate=? and agencyEvaluatePraise.user=? and agencyEvaluatePraise.deleted=false ";
//        Query query = getCurrentSession().createQuery(hql).setParameter(0, agencyEvaluate).setParameter(1, user);
//        List<FootballAgencyEvaluatePraise> list = query.list();
//        if (list != null && list.size() > 0) {
//            return list.get(0);
//        }
//        return null;
//    }
//
//
//}
