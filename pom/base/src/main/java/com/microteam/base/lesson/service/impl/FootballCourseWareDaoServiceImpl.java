package com.microteam.base.lesson.service.impl;


import com.microteam.base.entity.lesson.FootballCourseWare;
import com.microteam.base.entity.lesson.FootballLessonCourseWare;
import com.microteam.base.lesson.dao.FootballCourseWareDao;
import com.microteam.base.lesson.dao.FootballLessonCourseWareDao;
import com.microteam.base.lesson.service.FootballCourseWareDaoService;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service("footballCoursewareDaoService")
public class FootballCourseWareDaoServiceImpl implements FootballCourseWareDaoService {
    static Logger logger = Logger.getLogger(FootballCourseWareDaoServiceImpl.class.getName());

    @Autowired
    private FootballCourseWareDao dao;
    @Autowired
    private FootballLessonCourseWareDao footballLessonCoursewareDao;

    @Override
    public List<FootballCourseWare> findOpennessForPage(int page, int pageSize) {
        if (page > 0) {
            page--;
        }
        Pageable pageable = PageRequest.of(page, pageSize);
        return dao.findOpennessForPage(pageable);
    }

    @Override
    public List<FootballCourseWare> findByIdList(List<Long> idList) {
        if (idList != null && idList.size() > 0) {
            return dao.findByIdList(idList);
        }
        return new ArrayList<>();
    }

    @Override
    public FootballCourseWare findByLessonId(long lessonId) {
        List<FootballLessonCourseWare> lessonCourse = footballLessonCoursewareDao.findByLessonId(lessonId);
        if (lessonCourse != null && !lessonCourse.isEmpty()) {
            return dao.findById((long) lessonCourse.get(0).getCourseId());
        }
        return null;
    }

    @Override
    public List<FootballCourseWare> findByGradeAndMotionIdAndLevelAndOrder(int grade, Long motionId, int level, int order, int page, int pageSize) {
        if (page > 0) {
            page--;
        }
        Sort sort;
        switch (order) {
            case 1:
                List<Sort.Order> orders = new ArrayList<>();
                Sort.Order order1 = new Sort.Order(Sort.Direction.ASC, "level");
                Sort.Order order2 = new Sort.Order(Sort.Direction.DESC, "attention");
                orders.add(order1);
                orders.add(order2);
                sort = Sort.by(orders);
                break;
            case 2:
                sort = Sort.by(Sort.Direction.DESC, "level", "attention", "createTime");
                break;
            case 3:
                sort = Sort.by(Sort.Direction.DESC, "attention", "createTime");
                break;
            case 4:
                sort = Sort.by(Sort.Direction.DESC, "createTime");
                break;
            default:
                sort = Sort.by(Sort.Direction.DESC, "attention", "createTime");
                break;
        }

        Pageable pageable = PageRequest.of(page, pageSize, sort);
        return dao.findByGradeAndMotionIdAndLevelAndOrder(grade, motionId, level, pageable);
    }

    @Override
    public FootballCourseWare findById(long id) {
        return dao.findById(id);
    }

    @Override
    public FootballCourseWare findByCourseName(String courseName) {
        return dao.findByCourseName(courseName);
    }

    @Override
    public List<FootballCourseWare> findHotTop(int page, int pageSize) {
        Sort sort = Sort.by(Sort.Direction.DESC, "attention", "createTime");
        if (page > 0) {
            page--;
        }
        Pageable pageable = PageRequest.of(page, pageSize, sort);
        return dao.findHotTop(pageable);
    }

    @Override
    public List<FootballCourseWare> findNewTop(int page, int pageSize) {
        Sort sort = Sort.by(Sort.Direction.DESC, "createTime");
        if (page > 0) {
            page--;
        }
        Pageable pageable = PageRequest.of(page, pageSize, sort);
        return dao.findNewTop(pageable);
    }

    @Override
    public List<FootballCourseWare> findByType(Integer type, int page, int pageSize) {
        Sort sort = Sort.by(Sort.Direction.DESC, "attention");
        if (page > 0) {
            page--;
        }
        Pageable pageable = PageRequest.of(page, pageSize, sort);
        return dao.findByType(type, pageable);
    }

    @Override
    public List<FootballCourseWare> searchByNameAndType(String searchName, int type, int page, int pageSize) {
        Sort sort = Sort.by(Sort.Direction.DESC, "attention");
        if (page > 0) {
            page--;
        }
        Pageable pageable = PageRequest.of(page, pageSize, sort);
        searchName = '%' + searchName + '%';
        return dao.searchByNameAndType(searchName, type, pageable);
    }

    @Override
    public List<FootballCourseWare> findByChoice(Integer choice,Long userId) {
        return dao.findByChoice(choice,userId);
    }

    @Override
    public List<FootballCourseWare> findByVsteam() {
        return dao.findByVsteam();
    }

    @Override
    public List<FootballCourseWare> findBynoVsteam(Long userId) {
        return dao.findBynoVsteam(userId);
    }

    @Override
    public List<FootballCourseWare> findByOpen(Integer open) {
        return dao.findByOpen(open);
    }

    @Override
    public FootballCourseWare save(FootballCourseWare courseWare) {
        return dao.save(courseWare);
    }

    @Override
    public int updateImg(Long id, String headImg) {
        return dao.updateImg(id,headImg);
    }

    @Override
    public int updateVideo(Long id, String video) {
        return dao.updateVideo(id,video);
    }

    @Override
    public int saveFoundIsOpen(Long id, Integer isOpen) {
        return dao.saveFoundIsOpen(id,isOpen);
    }

    @Override
    public List<FootballCourseWare> findByDivide(Integer divide) {
        return dao.findByDivide(divide);
    }

    @Override
    public int updateQuote(Long id, Integer quote) {
        return dao.updateQuite(id,quote);
    }

    @Override
    public List<FootballCourseWare> remCourseware(Integer actionClassification,Integer level) {
        return dao.remCourseware(actionClassification,level);
    }
}
