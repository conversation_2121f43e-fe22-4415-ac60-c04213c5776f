package com.microteam.base.agency.service;

import com.microteam.base.entity.agency.FootballAgencyAlbum;
import com.microteam.base.entity.agency.FootballAgencyAlbumComment;

import java.util.List;

public interface FootballAgencyAlbumCommentDaoService {
    //查询相册评论
    List<FootballAgencyAlbumComment> findCommentByAlbum(FootballAgencyAlbum agencyAlbum);

    //分页查询相册评论
    List<FootballAgencyAlbumComment> findCommentByAlbumByPage(FootballAgencyAlbum agencyAlbum, int page, int pageSize);
}
