package com.microteam.base.common.util.imServerManage.pojo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import java.util.Date;

/**
*  IM response body 里面的entities  pojo类；；；
* 
* @param 
* @param 
* @return
*/
@JsonIgnoreProperties(ignoreUnknown = true)
public class ImResponseEntities implements java.io.Serializable{

	

		/**
		 * 
		 */
		
		private String uuid;
		
		private String type; //增加；
		private Date created;
		private Date modified;
		private String username;
		private boolean activated;
		
		public ImResponseEntities() {
		}

		public ImResponseEntities(String uuid, String type, Date created,Date modified,String username, boolean activated) {
			
			this.uuid = uuid;
			
			this.type = type;
        
			
			this.created = created;
            this.modified = modified;
			
			this.username = username;
			
		    this.activated = activated;
			
		}

		
		public String getUuid() {
			return this.uuid;
		}

		public void setUuid(String uuid) {
			this.uuid = uuid;
		}


		public String getType() {
			return this.type;
		}

		public void setType(String type) {
			this.type = type;
		}
		
		public Date getCreated() {
			return this.created;
		}

		public void setCreated(Date created) {
			this.created = created;
		}
	
	
		
		public Date getModified() {
			return this.modified;
		}

		public void setModified(Date modified) {
			this.modified = modified;
		}
	
		public String getUsername() {
			return this.username;
		}

		public void setUsername(String username) {
			this.username = username;
		}
		
		public boolean getActivated() {
			return this.activated;
		}

		public void setActivated(boolean activated) {
			this.activated = activated;
		}
		
		
		
		
}