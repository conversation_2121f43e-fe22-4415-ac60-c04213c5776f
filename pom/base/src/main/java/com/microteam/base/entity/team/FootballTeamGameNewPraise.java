package com.microteam.base.entity.team;


import com.microteam.base.common.base.BaseEntity;
import com.microteam.base.entity.user.User;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;
import java.io.Serializable;

@Entity
@Table(name = "football_team_game_new_praise")
@Getter
@Setter
public class FootballTeamGameNewPraise extends BaseEntity implements Serializable {
    @ManyToOne
    @JoinColumn(name = "userId", nullable = false)
    private User user;
    @ManyToOne
    @JoinColumn(name = "gameNewId", nullable = false)
    private FootballTeamGameNew footballTeamGameNew;
    @Column(name = "isPraised")
    private Boolean isPraised;

}
