package com.microteam.base.agency.service;

import com.microteam.base.entity.agency.AgencyUser;
import com.microteam.base.entity.agency.FootballAgency;

import java.util.List;

public interface AgencyUserDaoService {

    AgencyUser findById(long id);

    AgencyUser findByAgencyIdAndUserIdAndRoleId(Long agencyId, Long userId, Long roleId);

    AgencyUser save(AgencyUser agencyUser);

    Integer findCountByAgencyIdAndRoleId(Long agencyId, Long roleId);

    Integer findCountOfStuByAgencyId(Long agencyId);

    AgencyUser findOneByAgencyIdAndUserId(Long agencyId, Long userId);

    List<AgencyUser> findByAgencyIdAndUserId(Long agencyId, Long userId);

    List<AgencyUser> findByAgencyIdAndRoleId(Long agencyId, Long roleId);

    List<AgencyUser> findByAgencyIdAndRoleIdForPage(Long agencyId, Long roleId, int page, int pageSize);

    List<AgencyUser> findByUserId(Long userId);

    List<AgencyUser> findByUserIdOrderByRoleId(Long userId);

    List<AgencyUser> findYouthAgencyByUserIdAndRoleIdList(Long userId, List<Long> roleIdList);

    boolean delById(Long id);

    List<Long> findUserIdListByTeamId(Long teamId);

    List<AgencyUser> findByAgencyId(Long agencyId);

}
