//package com.microteam.base.agency.dao.impl;
//
//import com.microteam.base.agency.dao.FootballAgencyManagerDao;
//import com.microteam.base.entity.agency.FootballAgency;
//import com.microteam.base.entity.agency.FootballAgencyManager;
//import com.microteam.base.common.base.AbstractHibernateDao;
//import com.microteam.base.entity.user.User;
//import org.apache.log4j.Logger;
//import org.hibernate.Hibernate;
//import org.hibernate.query.Query;
//import org.springframework.stereotype.Repository;
//
//import java.util.ArrayList;
//import java.util.List;
//
//@Repository("footballAgencyManagerDao")
//public class FootballAgencyManagerDaoImpl extends
//        AbstractHibernateDao<FootballAgencyManager> implements FootballAgencyManagerDao {
//    static Logger logger = Logger.getLogger(FootballAgencyManagerDaoImpl.class.getName());
//
//    public FootballAgencyManagerDaoImpl() {
//        super();
//
//        setClazz(FootballAgencyManager.class);
//    }
//
//    //查询用户是不是机构下的管理员
//    @SuppressWarnings("unchecked")
//    @Override
//    public FootballAgencyManager findAgencyManagerByAgencyAndUser(
//            FootballAgency agency, User user) {
//
//        String hql = "from FootballAgencyManager as agencyManager where agencyManager.footballAgency=? and agencyManager.user=? and agencyManager.audit=1 and agencyManager.deleted=false ";
//        Query query = getCurrentSession().createQuery(hql).setParameter(0, agency).setParameter(1, user);
//        List<FootballAgencyManager> list = query.list();
//        if (list != null && list.size() > 0) {
//            return list.get(0);
//        }
//        return null;
//    }
//
//    //分页查询机构管理员
//    @SuppressWarnings("unchecked")
//    @Override
//    public List<FootballAgencyManager> findAgencyManagerByAgencyForPage(
//            FootballAgency agency, int page, int pageSize) {
//
//        String hql = "from FootballAgencyManager as agencyManager left join fetch agencyManager.user as user where agencyManager.footballAgency=? and agencyManager.deleted=false order by agencyManager.createTime desc ";
//        Query query = getCurrentSession().createQuery(hql).setParameter(0, agency);
//        query.setFirstResult((page - 1) * pageSize);
//        query.setMaxResults(pageSize);
//        List<FootballAgencyManager> list = query.list();
//        return list;
//    }
//
//    //根据Id查询
//    @SuppressWarnings("unchecked")
//    @Override
//    public FootballAgencyManager findAgencyManagerById(long id) {
//
//        String hql = "from FootballAgencyManager as agencyManager left join fetch agencyManager.user as user where agencyManager.id=? and agencyManager.deleted=false ";
//        Query query = getCurrentSession().createQuery(hql).setParameter(0, id);
//        List<FootballAgencyManager> list = query.list();
//        if (list != null && list.size() > 0) {
//            return list.get(0);
//        }
//        return null;
//    }
//
//    //根据机构查询
//    @SuppressWarnings("unchecked")
//    @Override
//    public List<FootballAgencyManager> findAgencyManagerByAgency(
//            FootballAgency agency) {
//        String hql = "from FootballAgencyManager as agencyManager left join fetch agencyManager.user as user where agencyManager.footballAgency=? and agencyManager.deleted=false order by agencyManager.createTime desc ";
//        Query query = getCurrentSession().createQuery(hql).setParameter(0, agency);
//        List<FootballAgencyManager> list = query.list();
//        return list;
//    }
//
//    //分页模糊查询机构下的管理员
//    @SuppressWarnings("unchecked")
//    @Override
//    public List<FootballAgencyManager> findAgencyManagerByAgencyAndManagerNameForPage(
//            FootballAgency agency, int page, int pageSize, String search) {
//
//        String hql = "from FootballAgencyManager as agencyManager left join fetch agencyManager.user as user ";
//        Query query = null;
//        if (!"".equals(search) && search != null) {
//            hql += " where agencyManager.footballAgency=? and agencyManager.managerName like ? and agencyManager.audit=1 and agencyManager.deleted=false order by agencyManager.createTime desc ";
//            query = getCurrentSession().createQuery(hql)
//                    .setParameter(0, agency)
//                    .setParameter(1, "%" + search + "%");
//            query.setFirstResult((page - 1) * pageSize);
//            query.setMaxResults(pageSize);
//        } else {
//            hql += " where agencyManager.footballAgency=?  and agencyManager.audit=1 and agencyManager.deleted=false order by agencyManager.createTime desc ";
//            query = getCurrentSession().createQuery(hql)
//                    .setParameter(0, agency);
//            query.setFirstResult((page - 1) * pageSize);
//            query.setMaxResults(pageSize);
//        }
//        List<FootballAgencyManager> list = query.list();
//        return list;
//    }
//
//    @SuppressWarnings("unchecked")
//    @Override
//    public List<FootballAgencyManager> findAgencyManagerByAgencyAndSearch(
//            FootballAgency agency, String search) {
//
//        String hql = "from FootballAgencyManager as agencyManager left join fetch agencyManager.user as user ";
//        hql += " where agencyManager.footballAgency=? and agencyManager.managerName like ? and agencyManager.audit=1 and agencyManager.deleted=false order by agencyManager.createTime desc ";
//        Query query = getCurrentSession().createQuery(hql)
//                .setParameter(0, agency)
//                .setParameter(1, "%" + search + "%");
//        List<FootballAgencyManager> list = query.list();
//        return list;
//    }
//
//    @SuppressWarnings("unchecked")
////    @Override
////    public List<FootballAgencyManager> findAgencyManagerByUser(User user, String cityCode, String countyCode, String search) {
////
////        //模糊查询机构工作人员
////        //String hql="from FootballAgencyManager as agencyManager left join fetch agencyManager.footballAgency as footballAgency where  footballAgency.user=? and footballAgency.deleted=false ";
////        String hql = "from FootballAgencyManager as agencyManager left join fetch agencyManager.footballAgency as footballAgency left join fetch  footballAgency.groups as groups ";
////        Query query = null;
////        if (!"".equals(search)) {
////            if ("1000000".equals(cityCode) || "".equals(cityCode)) {
////                //查询全国的
////                hql += " where footballAgency.agencyName like ?  and footballAgency.countryCode='1000000'   and footballAgency.deleted=false  and agencyManager.user=? and agencyManager.deleted=false order by agencyManager.createTime desc ";
////                query = getCurrentSession().createQuery(hql)
////                        .setParameter(0, "%" + search + "%")
////                        .setParameter(1, user);
////            } else {
////                //查询直辖市、特别行政区
////                if ("110000".equals(cityCode) ||
////                        "120000".equals(cityCode) ||
////                        "310000".equals(cityCode) ||
////                        "500000".equals(cityCode) ||
////                        "710000".equals(cityCode) ||
////                        "810000".equals(cityCode) ||
////                        "820000".equals(cityCode)) {
////                    hql += " where footballAgency.agencyName like ?  and footballAgency.provinceCode=?   and footballAgency.deleted=false and agencyManager.user=? and agencyManager.deleted=false order by agencyManager.createTime desc ";
////                    query = getCurrentSession().createQuery(hql)
////                            .setParameter(0, "%" + search + "%")
////                            .setParameter(1, cityCode)
////                            .setParameter(2, user);
////                } else {
////                    //普通城市
////                    if ((countyCode != null) && (!countyCode.equals(""))) {
////                        hql += " where  footballAgency.agencyName like ? and footballAgency.cityCode=? and footballAgency.countyCode=?   and footballAgency.deleted=false  and agencyManager.user=? and agencyManager.deleted=false order by agencyManager.createTime desc ";
////                        query = getCurrentSession().createQuery(hql)
////                                .setParameter(0, "%" + search + "%")
////                                .setParameter(1, cityCode)
////                                .setParameter(2, countyCode)
////                                .setParameter(3, user);
////                    } else {
////                        hql += " where footballAgency.agencyName like ?  and footballAgency.cityCode=?   and footballAgency.deleted=false  and agencyManager.user=? and agencyManager.deleted=false order by agencyManager.createTime desc ";
////                        query = getCurrentSession().createQuery(hql)
////                                .setParameter(0, "%" + search + "%")
////                                .setParameter(1, cityCode)
////                                .setParameter(2, user);
////                    }
////                    //普通城市
////                }
////
////            }
////        } else {
////            if ("1000000".equals(cityCode) || "".equals(cityCode)) {
////                //查询全国的
////                hql += " where  footballAgency.countryCode='1000000'   and footballAgency.deleted=false  and agencyManager.user=? and agencyManager.deleted=false order by agencyManager.createTime desc ";
////                query = getCurrentSession().createQuery(hql)
////                        .setParameter(0, user);
////            } else {
////                //查询直辖市、特别行政区
////                if ("110000".equals(cityCode) ||
////                        "120000".equals(cityCode) ||
////                        "310000".equals(cityCode) ||
////                        "500000".equals(cityCode) ||
////                        "710000".equals(cityCode) ||
////                        "810000".equals(cityCode) ||
////                        "820000".equals(cityCode)) {
////                    hql += " where  footballAgency.provinceCode=?   and footballAgency.deleted=false  and agencyManager.user=? and agencyManager.deleted=false order by agencyManager.createTime desc ";
////                    query = getCurrentSession().createQuery(hql)
////                            .setParameter(0, cityCode)
////                            .setParameter(1, user);
////                } else {
////                    //普通城市
////                    if ((countyCode != null) && (!countyCode.equals(""))) {
////                        hql += " where footballAgency.cityCode=?  and  footballAgency.countyCode=?   and footballAgency.deleted=false  and agencyManager.user=? and agencyManager.deleted=false order by agencyManager.createTime desc ";
////                        query = getCurrentSession().createQuery(hql)
////                                .setParameter(0, cityCode)
////                                .setParameter(1, countyCode)
////                                .setParameter(2, user);
////                    } else {
////                        hql += " where  footballAgency.cityCode=?   and footballAgency.deleted=false  and agencyManager.user=? and agencyManager.deleted=false order by agencyManager.createTime desc ";
////                        query = getCurrentSession().createQuery(hql)
////                                .setParameter(0, cityCode)
////                                .setParameter(1, user);
////                    }
////                    //普通城市
////                }
////
////            }
////        }//else
////        List<FootballAgencyManager> list = query.list();
////        if (list.size() > 0) {
////            List<FootballAgencyManager> listnew = new ArrayList<FootballAgencyManager>();
////            for (int i = 0; i < list.size(); i++) {
////                FootballAgencyManager agencyManager = list.get(i);
////                Hibernate.initialize(agencyManager);// 获取赖加载的集合内容；
////                Hibernate.initialize(agencyManager.getFootballAgency());
////                Hibernate.initialize(agencyManager.getFootballAgency().getGroups());
////                listnew.add(i, agencyManager);
////            }//for
////            return listnew;
////        }//if
////        return null;
////    }
//
//
//}
