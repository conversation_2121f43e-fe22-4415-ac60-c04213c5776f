package com.microteam.base.entity.agency;


import com.microteam.base.common.base.BaseEntity;
import com.microteam.base.entity.user.User;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

@Entity
@Table(name = "football_agency_coach")
@Getter
@Setter
public class FootballAgencyCoach extends BaseEntity implements Serializable {

    @ManyToOne
    @JoinColumn(name = "agencyId", nullable = false)
    private FootballAgency footballAgency;
    @ManyToOne
    @JoinColumn(name = "classId")
    private FootballAgencyClass footballAgencyClass;
    @ManyToOne
    @JoinColumn(name = "courseId")
    private FootballAgencyClassCourse footballAgencyClassCourse;
    @ManyToOne
    @JoinColumn(name = "userId", nullable = false)
    private User user;
    @Column(name = "coachName", length = 100)
    private String coachName;
    @Column(name = "headImgNetUrl", length = 250)
    private String headImgNetUrl;
    @Column(name = "headImgUrl", length = 250)
    private String headImgUrl;
    @Column(name = "headImgName", length = 50)
    private String headImgName;
    @Column(name = "headImgLength")
    private Long headImgLength;
    @Column(name = "birthDay", length = 100)
    private String birthDay;
    @Column(name = "resume", length = 65535)
    private String resume;
    @Column(name = "duty", length = 100)
    private String duty;

    public FootballAgencyCoach() {
    }

    public FootballAgencyCoach(FootballAgency footballAgency, User user,
                               Date createTime, boolean deleted) {
        this.footballAgency = footballAgency;
        this.user = user;
        this.createTime = createTime;
        this.deleted = deleted;
    }

    public FootballAgencyCoach(FootballAgency footballAgency, User user) {
        this.footballAgency = footballAgency;
        this.user = user;
    }

    public FootballAgencyCoach(FootballAgency footballAgency,
                               FootballAgencyClass footballAgencyClass,
                               FootballAgencyClassCourse footballAgencyClassCourse, User user,
                               String coachName, String headImgNetUrl, String headImgUrl,
                               String headImgName, Long headImgLength, String birthDay,
                               String resume, String duty, Date createTime, Date updateTime,
                               boolean deleted) {
        this.footballAgency = footballAgency;
        this.footballAgencyClass = footballAgencyClass;
        this.footballAgencyClassCourse = footballAgencyClassCourse;
        this.user = user;
        this.coachName = coachName;
        this.headImgNetUrl = headImgNetUrl;
        this.headImgUrl = headImgUrl;
        this.headImgName = headImgName;
        this.headImgLength = headImgLength;
        this.birthDay = birthDay;
        this.resume = resume;
        this.duty = duty;
        this.createTime = createTime;
        this.updateTime = updateTime;
        this.deleted = deleted;
    }
}
