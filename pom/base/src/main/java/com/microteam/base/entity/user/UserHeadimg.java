package com.microteam.base.entity.user;


import com.microteam.base.common.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;
import java.io.Serializable;

@Entity
@Table(name = "user_headimg")
@Getter
@Setter
public class UserHeadimg extends BaseEntity implements Serializable {

    @ManyToOne
    @JoinColumn(name = "userId", nullable = false)
    private User user;
    @Column(name = "headImgNetUrl", length = 250)
    private String headImgNetUrl;
    @Column(name = "headImgUrl", length = 250)
    private String headImgUrl;
    @Column(name = "fileName", length = 50)
    private String fileName;
    @Column(name = "length")
    private Long length;
    @Column(name = "enabled", nullable = false)
    private boolean enabled;

}
