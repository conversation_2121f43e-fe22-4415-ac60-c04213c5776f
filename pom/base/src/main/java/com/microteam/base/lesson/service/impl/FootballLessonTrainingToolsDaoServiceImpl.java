package com.microteam.base.lesson.service.impl;

import com.microteam.base.entity.lesson.FootballLessonTrainingTools;
import com.microteam.base.lesson.dao.FootballLessonEnrollDao;
import com.microteam.base.lesson.dao.FootballLessonTrainingToolsDao;
import com.microteam.base.lesson.service.FootballLessonTrainingToolsDaoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service("footballLessonTrainingToolsDaoService")
public class FootballLessonTrainingToolsDaoServiceImpl implements FootballLessonTrainingToolsDaoService {

    @Autowired
    private FootballLessonTrainingToolsDao dao;
    @Autowired
    private FootballLessonEnrollDao footballLessonEnrollDao;


    //通过课程ID获取选用的训练工具
    @Override
    public List<FootballLessonTrainingTools> findByLessonId(Long lessonId) {
        return dao.findByLessonId(lessonId);
    }

    //保存选用的训练工具
//    @Override
//    public boolean saveSelectedLessonTrainingTools(Long lessonId, List<Long> tools) {
//        boolean ret = false;
//        if (lessonId != null && !"".equals(lessonId) && lessonId != 0) {
//            ret = dao.saveSelectedLessonTrainingTools(lessonId, tools);
//        }
//        return ret;
//    }

    //保存选用的训练人员
//    @Override
//    public boolean saveSelectedLessonTrainingUsers(Long lessonId, Long teamId, List<Long> userIdList) {
//        boolean ret = false;
//        if (lessonId != null && !"".equals(lessonId) && lessonId != 0) {
//            ret = footballLessonEnrollDao.saveSelectedLessonTrainingUsers(lessonId, teamId, userIdList);
//        }
//        return ret;
//    }

    @Override
    public int deleteByLessonIdList(List<Long> lessonIdList) {
        if (lessonIdList != null && lessonIdList.size() > 0) {
            return dao.deleteByLessonIdList(lessonIdList);
        }
        return 0;
    }

    @Override
    public FootballLessonTrainingTools save(FootballLessonTrainingTools footballLessonTrainingTools) {
        return dao.save(footballLessonTrainingTools);
    }

    @Override
    public List<FootballLessonTrainingTools> fingByCourwareId(Long courwareId) {
        return dao.fingByCourwareId(courwareId);
    }
}
