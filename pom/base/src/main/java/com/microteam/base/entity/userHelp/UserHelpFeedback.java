package com.microteam.base.entity.userHelp;


import com.microteam.base.common.base.BaseEntity;
import com.microteam.base.entity.user.User;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

@Entity
@Table(name = "user_help_feedback")
@Getter
@Setter
public class UserHelpFeedback extends BaseEntity implements Serializable {
    @ManyToOne
    @JoinColumn(name = "feedBackUserId", nullable = false)
    private User user;
    @Column(name = "feedBackMessage", length = 150)
    private String feedBackMessage;
    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "feedBackTime", nullable = false, length = 19)
    private Date feedBackTime;
    @Column(name = "dealMessage", length = 150)
    private String dealMessage;
    @Column(name = "dealUserId", length = 30)
    private String dealUserId;
    @Column(name = "dealResult")
    private Short dealResult;
    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "dealTime", length = 19)
    private Date dealTime;

    public Date getFeedBackTime() {
        if (this.feedBackTime == null) {
            return null;
        }
        return (Date) this.feedBackTime.clone();
    }

    public void setFeedBackTime(Date feedBackTime) {
        this.feedBackTime = (Date) feedBackTime.clone();
    }


    public Date getDealTime() {
        if (this.dealTime == null) {
            return null;
        }
        return (Date) this.dealTime.clone();
    }

    public void setDealTime(Date dealTime) {
        this.dealTime = (Date) dealTime.clone();
    }


}
