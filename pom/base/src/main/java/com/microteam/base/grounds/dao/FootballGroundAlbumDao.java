package com.microteam.base.grounds.dao;


import com.microteam.base.entity.grounds.FootballGroundAlbum;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

public interface FootballGroundAlbumDao extends JpaRepository<FootballGroundAlbum, Long>, JpaSpecificationExecutor<FootballGroundAlbum> {
    //根据用户查找相册
    @Query("from FootballGroundAlbum as groundsAlbum " +
            "where groundsAlbum.user.id = (:userId) " +
            "and groundsAlbum.deleted=false " +
            "order by groundsAlbum.createTime desc")
    List<FootballGroundAlbum> findByUserId(@Param("userId") Long userId);

    //根据场地查找相册
    @Query("from FootballGroundAlbum as groundsAlbum " +
            "where groundsAlbum.footballGround.id = (:groundId) " +
            "and groundsAlbum.deleted=false " +
            "order by groundsAlbum.createTime desc")
    List<FootballGroundAlbum> findByGroundId(@Param("groundId") Long groundId);
}
