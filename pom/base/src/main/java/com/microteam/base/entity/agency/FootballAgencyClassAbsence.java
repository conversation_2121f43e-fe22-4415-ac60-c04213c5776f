package com.microteam.base.entity.agency;


import com.microteam.base.common.base.BaseEntity;
import com.microteam.base.entity.user.User;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

@Entity
@Table(name = "football_agency_class_absence")
@Getter
@Setter
public class FootballAgencyClassAbsence extends BaseEntity implements Serializable {

    @ManyToOne
    @JoinColumn(name = "agencyId", nullable = false)
    private FootballAgency footballAgency;
    @ManyToOne
    @JoinColumn(name = "courseId", nullable = false)
    private FootballAgencyClassCourse footballAgencyClassCourse;
    @ManyToOne
    @JoinColumn(name = "userId", nullable = false)
    private User user;
    @ManyToOne
    @JoinColumn(name = "classId", nullable = false)
    private FootballAgencyClass footballAgencyClass;
    @Column(name = "cause", length = 200)
    private String cause;
    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "courseStartTime", length = 19)
    private Date courseStartTime;
    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "courseEndTime", length = 19)
    private Date courseEndTime;
    @Column(name = "userRole")
    private String userRole;
    @Column(name = "absenceAllowed")
    private Short absenceAllowed;


    public Date getCourseStartTime() {
        if (this.courseStartTime == null) {
            return null;
        }
        return (Date) this.courseStartTime.clone();
    }

    public void setCourseStartTime(Date courseStartTime) {
        this.courseStartTime = (Date) courseStartTime.clone();
    }


    public Date getCourseEndTime() {
        if (this.courseEndTime == null) {
            return null;
        }
        return (Date) this.courseEndTime.clone();
    }

    public void setCourseEndTime(Date courseEndTime) {
        this.courseEndTime = (Date) courseEndTime.clone();
    }


}
