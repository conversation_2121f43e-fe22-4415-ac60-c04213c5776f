package com.microteam.base.entity.team;

import com.microteam.base.common.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.io.Serializable;

@Entity
@Table(name = "stardatatype")
@Getter
@Setter
public class StarDataType extends BaseEntity implements Serializable {

    @Column(name = "type")
    private Integer type;
    @Column(name = "name")
    private String name;
    @Column(name = "introduce")
    private String introduce;
    @Column(name = "imgUrl")
    private String imgUrl;
    @Column(name = "gameType")
    private Integer gameType;

    public String getImgUrl() {
        if (imgUrl == null) {
            return "";
        }
        return imgUrl;
    }

}
