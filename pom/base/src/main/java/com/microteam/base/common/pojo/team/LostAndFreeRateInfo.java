package com.microteam.base.common.pojo.team;

// Generated 2016-4-19 17:37:14 by Hibernate Tools 3.4.0.CR1


/**
 * 
 * <AUTHOR>
 *
 */
public class LostAndFreeRateInfo implements java.io.Serializable {

	private Long userId;
	private double  lostAndFreeRate;
	
	
	public LostAndFreeRateInfo() {
		super();
	}
	public LostAndFreeRateInfo(Long userId, double lostAndFreeRate) {
		super();
		this.userId = userId;
		this.lostAndFreeRate = lostAndFreeRate;
	}
	public Long getUserId() {
		return userId;
	}
	public void setUserId(Long userId) {
		this.userId = userId;
	}
	public double getLostAndFreeRate() {
		return lostAndFreeRate;
	}
	public void setLostAndFreeRate(double lostAndFreeRate) {
		this.lostAndFreeRate = lostAndFreeRate;
	}
	
	
}
