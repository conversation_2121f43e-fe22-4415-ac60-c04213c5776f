package com.microteam.base.entity.courseware;

import com.microteam.base.common.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.io.Serializable;

@Entity
@Table(name = "courseware_img")
@Getter
@Setter
public class CourseWareImg extends BaseEntity implements Serializable {
    @Column
    private Long coursewareId;
    @Column
    private String imgUrl;
    @Column
    private String imgPath;
}
