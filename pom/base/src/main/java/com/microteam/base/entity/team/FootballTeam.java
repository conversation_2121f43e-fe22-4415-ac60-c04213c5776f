package com.microteam.base.entity.team;


import com.microteam.base.common.base.BaseEntity;
import com.microteam.base.entity.user.Groups;
import com.microteam.base.entity.user.User;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.NotFound;
import org.hibernate.annotations.NotFoundAction;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

@Entity
@Table(name = "football_team")
@Getter
@Setter
public class FootballTeam extends BaseEntity implements Serializable {

    @ManyToOne
    @JoinColumn(name = "teamNatureTypeId", nullable = false)
    private FootballTeamNatureType footballTeamNatureType;
    @ManyToOne
    @JoinColumn(name = "groupId", nullable = false)
    private Groups groups;
    @ManyToOne
    @JoinColumn(name = "owner", nullable = false)
    @NotFound(action = NotFoundAction.IGNORE)
    private User user;
    @Column(name = "teamName", nullable = false, length = 50)
    private String teamName;
    @Column(name = "teamAbbreviation", length = 50)
    private String teamAbbreviation;
    @Column(name = "hostColor")
    private Integer hostColor;
    @Column(name = "guestColor")
    private Integer guestColor;
    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "teamCreateTime", length = 19)
    private Date teamCreateTime;
    @Column(name = "credits")
    private Integer credits;
    @Column(name = "creditsRank")
    private String creditsRank;
    @Column(name = "winRateRank", length = 50)
    private String winRateRank;
    @Column(name = "vsteamNumber", nullable = false, length = 50)
    private String vsteamNumber;
    @Column(name = "selfEvaluation")
    private Short selfEvaluation;
    @Column(name = "description")
    private String description;
    @Column(name = "slogan", length = 100)
    private String slogan;
    @Column(name = "countryCode")
    private String countryCode;
    @Column(name = "provinceCode")
    private String provinceCode;
    @Column(name = "cityCode")
    private String cityCode;
    @Column(name = "countyCode")
    private String countyCode;
    @Column(name = "teamHeadImgNetUrl", length = 250)
    private String teamHeadImgNetUrl;
    @Column(name = "teamHeadImgUrl", length = 250)
    private String teamHeadImgUrl;
    @Column(name = "teamHeadImgName", length = 50)
    private String teamHeadImgName;
    @Column(name = "teamHeadImgLength")
    private Long teamHeadImgLength;
    @Column(name = "teamPennantNetUrl", length = 250)
    private String teamPennantNetUrl;
    @Column(name = "teamPennantUrl", length = 250)
    private String teamPennantUrl;
    @Column(name = "teamPennantName", length = 50)
    private String teamPennantName;
    @Column(name = "teamPennantLength")
    private Long teamPennantLength;
    @Column(name = "teamBadgeNetUrl", length = 250)
    private String teamBadgeNetUrl;
    @Column(name = "teamBadgeUrl", length = 250)
    private String teamBadgeUrl;
    @Column(name = "teamBadgeName", length = 50)
    private String teamBadgeName;
    @Column(name = "teamBadgeLength")
    private Long teamBadgeLength;
    @Column(name = "teamWeixinQrcodeNetUrl", length = 250)
    private String teamWeixinQrcodeNetUrl;
    @Column(name = "teamWeixinQrcodeUrl", length = 250)
    private String teamWeixinQrcodeUrl;
    @Column(name = "teamWeixinQrcodeName", length = 50)
    private String teamWeixinQrcodeName;
    @Column(name = "teamWeixinQrcodeLength")
    private Long teamWeixinQrcodeLength;
    @Column(name = "teamBackgroundNetUrl")
    private String teamBackgroundNetUrl;
    @Column(name = "teamBackgroundUrl")
    private String teamBackgroundUrl;
    @Column(name = "teamBackgroundName")
    private String teamBackgroundName;
    @Column(name = "teamBackgroundLength")
    private Long teamBackgroundLength;
    @Column(name = "favorRules")
    private Short favorRules;
    @Column(name = "isConscribe")
    private Short isConscribe;
    @Column(name = "isHistory")
    private Short isHistory;
    @Column(name = "isCampusTeam")
    private Boolean isCampusTeam;
    @Column(name = "teamRecord", length = 250)
    private String teamRecord;
    @Column(name = "teamHonour", length = 250)
    private String teamHonour;
    @Column(name = "playerList", length = 250)
    private String playerList;
    @Column(name = "playGround", length = 250)
    private String playGround;
    @Column(name = "playStar", length = 250)
    private String playStar;
    @Column(name = "leader", length = 100)
    private String leader;
    @Column(name = "coach", length = 100)
    private String coach;
    @Column(name = "wechatGroup", length = 100)
    private String wechatGroup;
    @Column(name = "telephone")
    private String telephone;
    @Column(name = "contact")
    private String contact;
    @Column(name = "audit", nullable = false)
    private Short audit;
    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "auditTime")
    private Date auditTime;
    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "teamTime", nullable = false, length = 19)
    private Date teamTime;
    @Column(name = "enabled", nullable = false)
    private boolean enabled;
    @Column(name = "schoolName", length = 100)
    private String schoolName;

    public Date getTeamCreateTime() {
        if (this.teamCreateTime == null) {
            return null;
        }
        return (Date) this.teamCreateTime.clone();
    }

    public void setTeamCreateTime(Date teamCreateTime) {
        this.teamCreateTime = (Date) teamCreateTime.clone();
    }

    public Date getAuditTime() {
        if (this.auditTime == null) {
            return null;
        }
        return (Date) this.auditTime.clone();
    }

    public void setAuditTime(Date auditTime) {
        this.auditTime = (Date) auditTime.clone();
    }


    public Date getTeamTime() {
        if (this.teamTime == null) {
            return null;
        }
        return (Date) teamTime.clone();
    }

    public void setTeamTime(Date teamTime) {
        this.teamTime = (Date) teamTime.clone();
    }


}
