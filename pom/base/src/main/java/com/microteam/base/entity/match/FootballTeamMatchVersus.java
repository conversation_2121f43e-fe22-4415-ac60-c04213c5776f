package com.microteam.base.entity.match;


import com.microteam.base.common.base.BaseEntity;
import com.microteam.base.entity.user.User;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

@Entity
@Table(name = "football_team_match_versus")
@Getter
@Setter
public class FootballTeamMatchVersus extends BaseEntity implements Serializable {

    @ManyToOne
    @JoinColumn(name = "matchId", nullable = false)
    private FootballTeamMatch footballTeamMatch;
    @ManyToOne
    @JoinColumn(name = "creator", nullable = false)
    private User user;
    @Column(name = "hostTeamId", nullable = false)
    private Long hostTeamId;
    @Column(name = "oppTeamId")
    private Long oppTeamId;
    @Column(name = "contestGroup")
    private Short contestGroup;
    @Column(name = "contestRound")
    private Short contestRound;
    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "matchTime", length = 19)
    private Date matchTime;
    @Column(name = "countryCode")
    private String countryCode;
    @Column(name = "provinceCode")
    private String provinceCode;
    @Column(name = "longitude")
    private String longitude;
    @Column(name = "latitude")
    private String latitude;
    @Column(name = "playGround", length = 100)
    private String playGround;
    @Column(name = "cityCode")
    private String cityCode;
    @Column(name = "countyCode")
    private String countyCode;
    @Column(name = "detailLocation", length = 200)
    private String detailLocation;
    @Column(name = "phone", length = 50)
    private String phone;
    @Column(name = "clothColor")
    private Short clothColor;
    @Column(name = "remarks", length = 65535)
    private String remarks;

    public Date getMatchTime() {
        if (this.matchTime == null) {
            return null;
        }
        return (Date) this.matchTime.clone();
    }

    public void setMatchTime(Date matchTime) {
        this.matchTime = (Date) matchTime.clone();
    }


}
