package com.microteam.base.common.util.user;


import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.microteam.base.common.constant.FootballContants;
import com.microteam.base.common.pojo.UserHardWarePojo;
import com.microteam.base.common.pojo.UserHardWareStepPojo;
import com.microteam.base.entity.demoManager.MacQRCode;
import com.microteam.base.entity.user.User;
import com.microteam.base.entity.user.UserHardwareData;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version $Id: UserDataUtil.java, v 0.1 2017年5月12日 下午2:51:38 Administrator Exp $
 */
public class UserDataUtil {

    //计算跑动距离
    public static long calculateMoveDistanceNew(int speedMoveCount, int norIntervalTime, Short height, int type) {
        long moveDistance = 0;
        if (height == null) {
            height = 170;
        }
        if (norIntervalTime != 0) {
            double stepWidth = 0; // 步长

            // 4~3 从低到高
            if (type == 4) {
                stepWidth = height * 0.3;
            } else if (type == 3) {
                stepWidth = height * 0.3;
            } else if (type == 2) {
                stepWidth = height * 0.7;
            } else if (type == 1) {
                stepWidth = 0.8 * height;
            }
            moveDistance = Math.round((speedMoveCount * (stepWidth / 100)));

        }
        return moveDistance;
    }


    public static long calculateMoveDistance(int speedMoveCount, int norIntervalTime, Short height) {
        long moveDistance = 0;
        if (height == null) {
            height = 170;
        }
        if (norIntervalTime != 0) {
            double avgStepCount = (double) speedMoveCount / ((double) norIntervalTime / 1000) * 2; //每两秒的平均步数
            double stepWidth = 0; // 步长
            if (avgStepCount > 0 && avgStepCount <= 2) {
                stepWidth = ((double) height / 5);
            } else if (avgStepCount > 2 && avgStepCount <= 3) {
                stepWidth = ((double) height / 4);
            } else if (avgStepCount > 3 && avgStepCount <= 4) {
                stepWidth = ((double) height / 3);
            } else if (avgStepCount > 4 && avgStepCount <= 5) {
                stepWidth = ((double) height / 2);
            } else if (avgStepCount > 5 && avgStepCount <= 6) {
                stepWidth = (double) height / 1.2;
            } else if (avgStepCount > 6 && avgStepCount <= 8) {
                stepWidth = ((double) height / 1);
            } else if (avgStepCount > 8) {
                stepWidth = 1.2 * (double) height;
            }


            moveDistance = Math.round((speedMoveCount * (stepWidth / 100)));

        }
        return moveDistance;
    }

    //计算跑动距离
    public static long calculateMoveDistanceOld(int speedMoveCount, int norIntervalTime, Short height) {
        long moveDistance = 0;
        if (height == null) {
            height = 170;
        }
        if (norIntervalTime != 0) {
            double avgStepCount = speedMoveCount / (norIntervalTime / 1000) * 60; //每两秒的平均步数
            double bsl = 0.85 * height;
            double stepWidth = 0; // 步长
            if (avgStepCount < 80) {
                stepWidth = 0.4 * bsl;
            } else if (avgStepCount >= 80 && avgStepCount < 90) {
                stepWidth = 0.5 * bsl;
            } else if (avgStepCount >= 90 && avgStepCount < 120) {
                stepWidth = 0.007 * Math.pow((avgStepCount - 90), 2) + 0.5 * bsl;
            } else if (avgStepCount >= 120 && avgStepCount < 162) {
                stepWidth = 0.02 * Math.pow((avgStepCount - 120), 2) + bsl;
            } else if (avgStepCount >= 162 && avgStepCount < 198) {
                stepWidth = -0.02 * Math.pow((avgStepCount - 180), 2) + bsl;
            } else if (avgStepCount >= 198) {
                stepWidth = 0.95 * bsl;
            }
            moveDistance = Math.round((speedMoveCount * (stepWidth / 100)));

        }
        return moveDistance;
    }


    /**
     * ]封装UserHardWareStepPojo中的数据
     *
     * @Description:TODO
     * @author:Administrator
     * @time:2017年7月13日 下午2:59:18
     */
    public static JSONArray packagingDataUserHardWareStepPojo(List<UserHardWareStepPojo> listPojo, List<UserHardWareStepPojo> rightListPojo) {
        JSONArray Json = new JSONArray();
        if (listPojo != null && listPojo.size() > 0) {
            for (UserHardWareStepPojo tempPojo : listPojo) {
                Map<String, Object> tempMap = new HashMap<>();
                tempMap.put("intervalTime", tempPojo.getIntervalTime());
                tempMap.put("stepCount", tempPojo.getStepCount());
                tempMap.put("startTime", tempPojo.getStartTime());
                tempMap.put("endTime", tempPojo.getEndTime());
                Json.add(tempMap);
            }
            if (rightListPojo != null && rightListPojo.size() > 0) {
                for (UserHardWareStepPojo tempPojo : listPojo) {
                    Map<String, Object> tempMap = new HashMap<>();
                    tempMap.put("intervalTime", tempPojo.getIntervalTime());
                    tempMap.put("stepCount", tempPojo.getStepCount());
                    tempMap.put("startTime", tempPojo.getStartTime());
                    tempMap.put("endTime", tempPojo.getEndTime());
                    Json.add(tempMap);
                }
            }
            return Json;
        }
        return null;
    }


    /**
     * @param hardwareType
     * @param macQrCode
     * @return boolean
     * @Description: 判断是不是同一只脚
     * <AUTHOR>
     * @date 2018年2月6日 下午3:49:59
     */
    public static boolean judgeHardwareIsSame(int hardwareType, MacQRCode macQrCode) {
        boolean result = false;
        String qrCode = macQrCode.getQrCode();
        Integer orderCode = Integer.parseInt(qrCode.substring(qrCode.length() - 8));
        if (hardwareType == 1) {    //左脚
            result = orderCode % 2 != 0;
        } else if (hardwareType == 2) {  //右脚
            result = orderCode % 2 == 0;
        }
        return result;
    }

    public static Map<String, Integer> moveCount(List<UserHardwareData> list) {
        Map<String, Integer> map = new HashMap<>();
        int highMoveCount = 0;
        int midMoveCount = 0;
        int lowMoveCount = 0;
        int normalMoveCount = 0;

        for (int i = 0; i < list.size(); i++) {
            if (i == 0) {
                highMoveCount += list.get(i).getHighMoveCount();
                midMoveCount += list.get(i).getMidMoveCount();
                lowMoveCount += list.get(i).getLowMoveCount();
                normalMoveCount += list.get(i).getNormalMoveCount();
            }
            map.put("highMoveCount", highMoveCount);
            map.put("midMoveCount", midMoveCount);
            map.put("lowMoveCount", lowMoveCount);
            map.put("normalMoveCount", normalMoveCount);
        }
        return map;
    }

    public static Map<String, Integer> moveCountNew(List<UserHardWarePojo> list) {
        Map<String, Integer> map = new HashMap<>();
        int highMoveCount = 0;
        int midMoveCount = 0;
        int lowMoveCount = 0;
        int normalMoveCount = 0;

        if (list != null && list.size() > 0) {
            if (list.size() == 1) {
                highMoveCount = list.get(0).getHighMoveCount() + list.get(0).getRightHighMoveCount();
                midMoveCount = list.get(0).getMidMoveCount() + list.get(0).getRightMidMoveCount();
                lowMoveCount = list.get(0).getLowMoveCount() + list.get(0).getRightLowMoveCount();
                normalMoveCount = list.get(0).getNormalMoveCount() + list.get(0).getRightNormalMoveCount();
            } else if (list.size() == 2) {
                highMoveCount = list.get(0).getHighMoveCount() + list.get(0).getRightHighMoveCount() +
                        list.get(1).getHighMoveCount() + list.get(1).getRightHighMoveCount();
                midMoveCount = list.get(0).getMidMoveCount() + list.get(0).getRightMidMoveCount() +
                        list.get(1).getMidMoveCount() + list.get(1).getRightMidMoveCount();
                lowMoveCount = list.get(0).getLowMoveCount() + list.get(0).getRightLowMoveCount() +
                        list.get(1).getLowMoveCount() + list.get(1).getRightLowMoveCount();
                normalMoveCount = list.get(0).getNormalMoveCount() + list.get(0).getRightNormalMoveCount() +
                        list.get(1).getNormalMoveCount() + list.get(1).getRightNormalMoveCount();
            }
        }

        map.put("highMoveCount", highMoveCount);
        map.put("midMoveCount", midMoveCount);
        map.put("lowMoveCount", lowMoveCount);
        map.put("normalMoveCount", normalMoveCount);

        return map;
    }

    public static Map<String, Integer> getPassBallData(JSONArray teamtouch_timeArray, User user) {
        Map<String, Integer> map = new HashMap<>();
        int passballCount = 0;          //传球
        int oneFootPassCount = 0;       //一脚传球
        int twoFootballPassCount = 0;   //两脚传球
        int longPassCount = 0;          //长传
        int shortPassCount = 0;         //短传
        int passError = 0;              //传球失误
        int leftPassBallCounts = 0;     //左脚传球
        int rightPassBallCounts = 0;    //右脚传球
        try {
            if (teamtouch_timeArray != null && teamtouch_timeArray.size() > 0) {
                for (int k = 0; k < teamtouch_timeArray.size() - 1; k++) {
                    //传球 长、短传
                    JSONObject jsonObject = teamtouch_timeArray.getJSONObject(k);
                    long userId = jsonObject.getLong("userId");
                    if (user.getId() == userId) {
                        long userId_next = teamtouch_timeArray.getJSONObject(k + 1).getLong("userId");
                        if (userId != userId_next) {
                            long touchTime_next = teamtouch_timeArray.getJSONObject(k + 1).getLong("touchTime");
                            long interval = Math.abs(jsonObject.getLong("touchTime") - touchTime_next);
                            if (interval > FootballContants.PASSBALLCUTOFF) {
                                passError++;
                            } else {
                                if (interval <= FootballContants.LONG_SHORTPASSCUTOFF) {
                                    shortPassCount++;
                                } else {
                                    longPassCount++;
                                }
                                if (jsonObject.getInteger("hardwareType") == 1) {
                                    leftPassBallCounts++;
                                } else {
                                    rightPassBallCounts++;
                                }
                            }
                        }
                    }
                    //一脚传球 （A-B-C）
                    if (user.getId() == userId) {
                        if (k == 0) {
                            long userId_next = teamtouch_timeArray.getJSONObject(k + 1).getLong("userId");
                            long touchTime_next = teamtouch_timeArray.getJSONObject(k + 1).getLong("touchTime");
                            long interval = Math.abs(jsonObject.getLong("touchTime") - touchTime_next);
                            if (userId != userId_next && interval <= FootballContants.PASSBALLCUTOFF) {
                                oneFootPassCount++;
                            }
                        } else {
                            long userId_next = teamtouch_timeArray.getJSONObject(k + 1).getLong("userId");
                            long userId_befor = teamtouch_timeArray.getJSONObject(k - 1).getLong("userId");

                            long interval_one = Math.abs(jsonObject.getLong("touchTime") - teamtouch_timeArray.getJSONObject(k - 1).getLong("touchTime"));
                            long interval_two = Math.abs(jsonObject.getLong("touchTime") - teamtouch_timeArray.getJSONObject(k + 1).getLong("touchTime"));
                            if (userId != userId_next && userId != userId_befor &&
                                    interval_one <= FootballContants.PASSBALLCUTOFF && interval_two <= FootballContants.PASSBALLCUTOFF) {
                                oneFootPassCount++;
                            }
                        }
                    } // if
                    //两脚传球（A-B-B-C）
                    if (k > 0 && k < teamtouch_timeArray.size() - 2) {
                        long userId_next = teamtouch_timeArray.getJSONObject(k + 1).getLong("userId");
                        if (userId == userId_next) {
                            long userId_first = teamtouch_timeArray.getJSONObject(k - 1).getLong("userId");
                            long userId_four = teamtouch_timeArray.getJSONObject(k + 2).getLong("userId");

                            long interval_one = Math.abs(teamtouch_timeArray.getJSONObject(k - 1).getLong("touchTime") - jsonObject.getLong("touchTime"));
                            long interval_two = Math.abs(teamtouch_timeArray.getJSONObject(k + 1).getLong("touchTime") - jsonObject.getLong("touchTime"));
                            long interval_three = Math.abs(teamtouch_timeArray.getJSONObject(k + 1).getLong("touchTime") - teamtouch_timeArray.getJSONObject(k + 2).getLong("touchTime"));

                            if (userId_first != userId_four && userId_first != userId && userId_four != userId &&
                                    interval_one <= FootballContants.PASSBALLCUTOFF && interval_two <= FootballContants.PASSBALLCUTOFF && interval_three <= FootballContants.PASSBALLCUTOFF) {
                                twoFootballPassCount++;
                            }
                        }
                    }
                    //传球成功率
                }  // for
                passballCount = shortPassCount + longPassCount;
            } // if
        } catch (Exception e) {
            e.printStackTrace();
        }
        //封装结果集
        map.put("passballCount", passballCount);
        map.put("oneFootPassCount", oneFootPassCount);
        map.put("twoFootballPassCount", twoFootballPassCount);
        map.put("longPassCount", longPassCount);
        map.put("shortPassCount", shortPassCount);
        map.put("passError", passError);
        map.put("leftPassBallCounts", leftPassBallCounts);
        map.put("rightPassBallCounts", rightPassBallCounts);
        return map;
    }

    public static Map packFileds(Map map, List<UserHardwareData> userHardwareDataList) {
        long gameTimeL = 0;
        long gameTimeR = 0;
        JSONArray touchBallTimesL = new JSONArray();
        JSONArray touchBallTimesR = new JSONArray();
        JSONArray collectionTouchBallL = new JSONArray();
        JSONArray collectionTouchBallR = new JSONArray();
        Integer isUploadPose = 0;
        for (UserHardwareData hardwareData : userHardwareDataList) {
            if (hardwareData.getUserHardware().getHardwareType() == 1) {
                if (hardwareData.getKickBallStartTime() != null) {
                    gameTimeL = hardwareData.getKickBallStartTime().getTime();
                }
                if (hardwareData.getKickBallData() != null) {
                    touchBallTimesL = JSONArray.parseArray(hardwareData.getKickBallData());
                }
                collectionTouchBallL = packCollectionTouchBall(hardwareData, hardwareData.getUserHardware().getHardwareType());
            } else {
                if (hardwareData.getKickBallStartTime() != null) {
                    gameTimeR = hardwareData.getKickBallStartTime().getTime();
                }
                if (hardwareData.getKickBallData() != null) {
                    touchBallTimesR = JSONArray.parseArray(hardwareData.getKickBallData());
                }
                collectionTouchBallR = packCollectionTouchBall(hardwareData, hardwareData.getUserHardware().getHardwareType());
            }
            isUploadPose = hardwareData.getIsUploadPose();
        }
        collectionTouchBallL.addAll(collectionTouchBallR);
        collectionTouchBallL.sort((o1, o2) -> {
            long o1Time = ((JSONObject)o1).getLongValue("time");
            long o2Time = ((JSONObject)o2).getLongValue("time");
            if (o1Time < o2Time) {
                return -1;
            } else if (o1Time > o2Time) {
                return 1;
            }else {
                return 0;
            }
        });
        map.put("gameTimeL", gameTimeL);
        map.put("gameTimeR", gameTimeR);
        map.put("touchBallTimesL", touchBallTimesL);
        map.put("touchBallTimesR", touchBallTimesR);
        map.put("isUploadPose", isUploadPose);
        map.put("collectionTouchBall",collectionTouchBallL);
        return map;
    }

    public static JSONArray packCollectionTouchBall(UserHardwareData hardwareData,Integer hardwareType) {
        JSONArray array = new JSONArray();
        JSONArray kickData = JSONArray.parseArray(hardwareData.getKickBallData());
        JSONArray kickPart = JSONArray.parseArray(hardwareData.getKickBallPart());
        for (int i = 0; i < kickData.size(); i++) {
            JSONObject object = new JSONObject();
            object.put("hardwareType", hardwareType);
            object.put("part", kickPart.getIntValue(i));
            long kickTime = hardwareData.getKickBallStartTime().getTime() + (kickData.getIntValue(i) * 1000);
            object.put("time", kickTime);
            array.add(object);
        }
        return array;
    }
}
