package com.microteam.base.agency.service;

import com.microteam.base.entity.agency.FootballAgency;
import com.microteam.base.entity.agency.FootballAgencyManager;
import com.microteam.base.entity.user.User;

import java.util.List;

public interface FootballAgencyManagerDaoService {
    //查询用户是不是机构下的管理员
    FootballAgencyManager findAgencyManagerByAgencyAndUser(FootballAgency agency, User user);

    //分页查询机构管理员
    List<FootballAgencyManager> findAgencyManagerByAgencyForPage(FootballAgency agency, int page, int pageSize);

    //根据Id查询
    FootballAgencyManager findAgencyManagerById(long id);

    //根据机构查询
    List<FootballAgencyManager> findAgencyManagerByAgency(FootballAgency agency);

    //分页模糊查询机构下的管理员
    List<FootballAgencyManager> findAgencyManagerByAgencyAndManagerNameForPage(FootballAgency agency, int page, int pageSize, String managerName);

    List<FootballAgencyManager> findAgencyManagerByAgencyAndSearch(FootballAgency agency, String managerName);

//    List<FootballAgencyManager> findAgencyManagerByUser(User user, String cityCode, String countyCode, String search);
}
