package com.microteam.base.lesson.service.impl;

import com.microteam.base.entity.lesson.Motion;
import com.microteam.base.lesson.dao.MotionDao;
import com.microteam.base.lesson.service.MotionDaoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class MotionDaoServiceImpl implements MotionDaoService {

    @Autowired
    private MotionDao dao;

    @Override
    public List<Motion> findAll() {
        return dao.findAll();
    }
}
