package com.microteam.base.entity.integral;

import com.microteam.base.common.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.io.Serializable;

@Entity
@Table(name = "football_integral_history")
@Getter
@Setter
public class FootballIntegralHistory extends BaseEntity implements Serializable {

    @Basic
    @Column(name = "typeId")
    private Long typeId;
    @Basic
    @Column(name = "teamId")
    private Long teamId;
    @Basic
    @Column(name = "userId")
    private Long userId;

}
