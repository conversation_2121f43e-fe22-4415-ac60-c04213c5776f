package com.microteam.base.entity.team;

import com.microteam.base.common.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.io.Serializable;

@Entity
@Table(name = "honor_icon")
@Getter
@Setter
public class HonorIcon extends BaseEntity implements Serializable {

    @Column(name = "iconNetUrl", length = 250)
    private String iconNetUrl;
    @Column(name = "iconUrl", length = 250)
    private String iconUrl;
    @Column(name = "describe", length = 250)
    private String describe;
    @Column(name = "length")
    private Long length;
    @Column(name = "fileName", length = 250)
    private String fileName;

}
