package com.microteam.base.entity.user;


import com.microteam.base.common.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

@Entity
@Table(name = "tokens", uniqueConstraints = @UniqueConstraint(columnNames = "accessToken"))
@Getter
@Setter
public class Tokens extends BaseEntity implements Serializable {

    @Column(name = "accessToken", unique = true, nullable = false, length = 64)
    private String accessToken;
    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "tokensStartTime", nullable = false, length = 19)
    private Date tokensStartTime;
    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "tokensEndTime", nullable = false, length = 19)
    private Date tokensEndTime;
    @Column(name = "enabled", nullable = false)
    private boolean enabled;

    public Date getTokensStartTime() {
        if (this.tokensStartTime == null) {
            return null;
        }
        return (Date) this.tokensStartTime.clone();
    }

    public void setTokensStartTime(Date tokensStartTime) {
        this.tokensStartTime = (Date) tokensStartTime.clone();
    }


    public Date getTokensEndTime() {
        if (this.tokensEndTime == null) {
            return null;
        }
        return (Date) this.tokensEndTime.clone();
    }

    public void setTokensEndTime(Date tokensEndTime) {
        this.tokensEndTime = (Date) tokensEndTime.clone();
    }


}
