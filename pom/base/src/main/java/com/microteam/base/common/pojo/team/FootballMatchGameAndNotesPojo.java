package com.microteam.base.common.pojo.team;

import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
public class FootballMatchGameAndNotesPojo {

    private Integer hostScore;
    private Integer guestScore;
    private Long teamGameId;
    private Integer isOverTime;     //是否有加时赛        1：有加时赛   0：没有加时赛
    private Integer overHostScore;
    private Integer overGuestScore;
    private Integer isSpot;         //是否有点球大战   1:有点球大战   0：没有点球大战
    private Integer spotHostScore;
    private Integer spotGuestScore;
    private Integer isGiveUp;       //是否有球队弃权   1：有球队弃权   0：没有球队弃权
    private String giveUpTeamId;
    private String giveUpTeamName;      //弃权球队名字  （当客队不存在时传弃权球队名字）
    private List<FootballTeamTakeNodePojo> hostTakeNodes;
    private List<FootballTeamTakeNodePojo> guestTakeNodes;
    private String hostDesc;
    private String guestDesc;

}
