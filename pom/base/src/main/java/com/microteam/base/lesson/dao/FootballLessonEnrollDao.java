package com.microteam.base.lesson.dao;


import com.microteam.base.entity.lesson.FootballLessonEnroll;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.math.BigInteger;
import java.util.Date;
import java.util.List;

public interface FootballLessonEnrollDao extends JpaRepository<FootballLessonEnroll, Long>, JpaSpecificationExecutor<FootballLessonEnroll> {

    @Query("from FootballLessonEnroll as lessonEnroll " +
            "left join fetch lessonEnroll.lesson as lesson " +
            "left join fetch lessonEnroll.team as team " +
            "left join fetch lessonEnroll.user as user " +
            "where user.id=(:userId) " +
            "and lessonEnroll.deleted=false " +
            "and lesson.startTime>(:zero) " +
            "and lesson.startTime<(:lastDay) " +
            "and (lesson.status = 3 " +
            " or lesson.status = 4 " +
            " or lesson.status = 5) " +
            "order by lessonEnroll.updateTime desc ")
    List<FootballLessonEnroll> findByUserIdForDay(@Param("userId") Long userId, @Param("zero") Date zero, @Param("lastDay") Date lastDay);

    @Query("from FootballLessonEnroll as lessonEnroll " +
            "left join fetch lessonEnroll.lesson as lesson " +
            "left join fetch lessonEnroll.team as team " +
            "left join fetch lessonEnroll.user as user " +
            "where team.id=(:teamId) " +
            "and lessonEnroll.deleted=false ")
    List<FootballLessonEnroll> findByTeamId(@Param("teamId") Long teamId);

    @Query(value = "select DISTINCT(lessonEnroll.lessonId),lesson.startTime " +
            "from football_lesson_enroll as lessonEnroll " +
            "left join football_lesson as lesson " +
            "on lessonEnroll.lessonId = lesson.id " +
            "left join football_team as team " +
            "on lessonEnroll.teamId = team.id " +
            "left join user as t_user " +
            "on lessonEnroll.userId = t_user.id " +
            "where t_user.id = (:userId) " +
            "and ( lesson.status = 3 " +
            " or lesson.status = 4 " +
            " or lesson.status = 5) " +
            "and lessonEnroll.deleted = 0 " +
            "and lesson.deleted = 0 " +
            "and team.deleted = 0 " +
            "and t_user.deleted = 0 " +
            "order by lesson.startTime desc ", nativeQuery = true)
    List<BigInteger> findFinishedByUserIdOrderByStartTimeDescForPage(@Param("userId") Long userId, Pageable pageable);

    @Query(value = "select DISTINCT(lessonEnroll.lessonId),lesson.startTime " +
            "from football_lesson_enroll as lessonEnroll " +
            "left join football_lesson as lesson " +
            "on lessonEnroll.lessonId = lesson.id " +
            "left join football_team as team " +
            "on lessonEnroll.teamId = team.id " +
            "left join user as t_user " +
            "on lessonEnroll.userId = t_user.id " +
            "where t_user.id = (:userId) " +
            "and lesson.status!=3 " +
            "and lesson.status!=4 " +
            "and lesson.status!=5 " +
            "and lessonEnroll.deleted = 0 " +
            "and lesson.deleted = 0 " +
            "and team.deleted = 0 " +
            "and t_user.deleted = 0 " +
            "order by lesson.startTime ", nativeQuery = true)
    List<BigInteger> findUnFinishedByUserIdOrderByStartTimeForPage(@Param("userId") Long userId, Pageable pageable);

    @Query("from FootballLessonEnroll as lessonEnroll " +
            "left join fetch lessonEnroll.lesson as lesson " +
            "left join fetch lessonEnroll.team as team " +
            "left join fetch lessonEnroll.user as user " +
            "where lesson.id=:lessonId " +
            "and team.id=:teamId ")
    List<FootballLessonEnroll> findByLessonIdAndTeamId(@Param("lessonId") Long lessonId, @Param("teamId") Long teamId);

    @Query("from FootballLessonEnroll as lessonEnroll " +
            "left join fetch lessonEnroll.user as user " +
            "where lessonEnroll.lesson.id = :lessonId " +
            "and lessonEnroll.deleted = false")
    List<FootballLessonEnroll> findByLessonId(@Param("lessonId") Long lessonId);

    @Query("from FootballLessonEnroll as lessonEnroll " +
            "left join fetch lessonEnroll.lesson as lesson " +
            "left join fetch lessonEnroll.team as team " +
            "left join fetch lessonEnroll.user as user " +
            "where lesson.id=:lessonId " +
            "and team.id=:teamId " +
            "and user.id=:userId " +
            "and lessonEnroll.deleted = false ")
    FootballLessonEnroll findByUserIdAndLessonIdAndTeamId(@Param("userId") Long userId, @Param("lessonId") Long lessonId, @Param("teamId") Long teamId);

    @Query("from FootballLessonEnroll as lessonEnroll " +
            " left join fetch lessonEnroll.lesson as lesson " +
            " left join fetch lessonEnroll.user as user " +
            " where lesson.id=(:lessonId) " +
            "and user.id=(:userId) " +
            "and lessonEnroll.deleted = false ")
    FootballLessonEnroll findByUserIdAndLessonId(@Param("userId") Long userId, @Param("lessonId") Long lessonId);

    @Query(value = "select count(1) " +
            "from football_lesson_enroll as lessonEnroll " +
            "where lessonEnroll.lessonId=(:lessonId) " +
            "and lessonEnroll.userId=(:userId) " +
            "and lessonEnroll.deleted = false ", nativeQuery = true)
    long isExistUserLessonEnroll(@Param("userId") Long userId, @Param("lessonId") Long lessonId);

//    boolean saveSelectedLessonTrainingUsers(Long lessonId, Long teamId, List<Long> userIdList);

    @Query("delete from FootballLessonEnroll as lessonEnroll " +
            "where lessonEnroll.team.id = (:teamId) ")
    int deleteByTeamId(@Param("teamId") Long teamId);


}
