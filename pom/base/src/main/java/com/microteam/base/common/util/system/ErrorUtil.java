package com.microteam.base.common.util.system;


import com.alibaba.fastjson.JSONArray;
import com.microteam.base.common.pojo.MtJavaServerResponseBodyPojo;
import com.microteam.base.common.pojo.MtJavaServerResponseBodyResultPojo;
import com.microteam.base.common.util.common.CommonUtil;
import org.springframework.stereotype.Component;

@Component(value = "errorUtil")
public class ErrorUtil {

    public void setError(MtJavaServerResponseBodyPojo responseBodyResultPojo, MtJavaServerResponseBodyResultPojo resultPojo, String code, String message) {
        resultPojo.setCode(code);
        resultPojo.setMessage(message);
        String resultString = CommonUtil.changeResultToString(resultPojo);
        responseBodyResultPojo.setResult(resultString);
    }

    public void setSuccess(MtJavaServerResponseBodyPojo responseBodyResultPojo, MtJavaServerResponseBodyResultPojo resultPojo) {
        resultPojo.setCode("0000");
        resultPojo.setMessage("success");
        String resultString = CommonUtil.changeResultToString(resultPojo);
        responseBodyResultPojo.setResult(resultString);
    }

    public void setSuccessWithNullData(MtJavaServerResponseBodyPojo responseBodyResultPojo, MtJavaServerResponseBodyResultPojo resultPojo) {
        setSuccess(responseBodyResultPojo, resultPojo);
        responseBodyResultPojo.setData(new JSONArray().toString());
    }
}
