package com.microteam.base.common.util.imServerManage.pojo;


import com.fasterxml.jackson.annotation.JsonIgnoreProperties;


/**
*  IM group register Request body 群组注册  pojo类；；
* 
* @param 
* @param 
* @return
*/
@JsonIgnoreProperties(ignoreUnknown = true)
public class ImGroupRegisterRequestBodyPojo implements java.io.Serializable{
/*
	"groupname":"testrestgrp12", //群组名称, 此属性为必须的
    "desc":"server create group", //群组描述, 此属性为必须的
    "public":true, //是否是公开群, 此属性为必须的
    "maxusers":300, //群组成员最大数(包括群主), 值为数值类型,默认值200,此属性为可选的
    "approval":true, //加入公开群是否需要批准, 没有这个属性的话默认是true（不需要群主批准，直接加入）, 此属性为可选的
    "owner":"jma1", //群组的管理员, 此属性为必须的
    "members":["jma2","jma3"] //群组成员,此属性为可选的,但是如果加了此项,数组元素至少一个（注：
*/	

		/**
		 * 
		 */
		
		private String groupname;
		
		private String    desc; 
		private boolean   PUBLIC; //public为特殊字，不能使用；
		private int       maxusers; 
		private boolean  approval; 
		private String   owner; 
		private String[] members; 
		
		public ImGroupRegisterRequestBodyPojo() {
		}

		public ImGroupRegisterRequestBodyPojo(String groupname, 
				String desc,boolean   PUBLIC,int       maxusers,boolean  approval,String   owner,String[] members) {
			
			this.groupname = groupname;
			
			this.desc = desc;
			
			this.PUBLIC = PUBLIC;
			
            this.maxusers = maxusers;
			
			this.approval = approval;
			
			this.owner = owner;
			
			this.members = members;
		}

		
		public String getGroupname() {
			return this.groupname;
		}

		public void setGroupname(String groupname) {
			this.groupname = groupname;
		}


		public String getDesc() {
			return this.desc;
		}

		public void setDesc(String desc) {
			this.desc = desc;
		}
		
		public boolean getPUBLIC() {
			return this.PUBLIC;
		}

		public void setPUBLIC(boolean PUBLIC) {
			this.PUBLIC = PUBLIC;
		}
		
	
		public int getMaxusers() {
			return this.maxusers;
		}

		public void setMaxusers(int maxusers) {
			this.maxusers = maxusers;
		}
		
		public boolean getApproval() {
			return this.approval;
		}

		public void setApproval(boolean approval) {
			this.approval = approval;
		}

		public String getOwner() {
			return this.owner;
		}

		public void setOwner(String owner) {
			this.owner = owner;
		}


		public String[] getMembers() {
			return this.members;
		}

		public void setMembers(String[] members) {
			this.members = members;
		}
		
		
		
		
		
	
}
