package com.microteam.base.lesson.dao;


import com.microteam.base.entity.lesson.FootballLessonCourseWare;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

public interface FootballLessonCourseWareDao extends JpaRepository<FootballLessonCourseWare, Long>, JpaSpecificationExecutor<FootballLessonCourseWare> {

    @Query("delete from FootballLessonCourseWare as lessonCourse " +
            "where lessonCourse.lessonId = ?1 " +
            "and lessonId.deleted = false ")
    @Modifying
    int deleteByLessonId(@Param("lessonId") Long lessonId);

    @Query("from FootballLessonCourseWare as lessonCourse " +
            "where lessonCourse.lessonId = ?1 " +
            "and lessonCourse.deleted = false ")
    List<FootballLessonCourseWare> findByLessonId(@Param("lessonId") Long lessonId);

    @Query("from FootballLessonCourseWare as lessonCourse " +
            "where lessonCourse.lessonId in (:lessonIdList) " +
            "and lessonCourse.deleted = false ")
    List<FootballLessonCourseWare> findByLessonIdList(@Param("lessonIdList") List<Long> lessonIdList);


}
