package com.microteam.base.agency.dao;

import com.microteam.base.entity.agency.FootballAgencyClass;
import com.microteam.base.entity.agency.FootballAgencyClassAlbum;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

public interface FootballAgencyClassAlbumDao extends JpaRepository<FootballAgencyClassAlbum, Long>, JpaSpecificationExecutor<FootballAgencyClassAlbum> {
    //查看班级相册是否存在
    @Query("from FootballAgencyClassAlbum as footballAgencyClassAlbum " +
            "where footballAgencyClassAlbum.footballAgencyClass.id = (:agencyClassId) " +
            "and footballAgencyClassAlbum.albumName = (:albumName) " +
            "and footballAgencyClassAlbum.deleted=false ")
    FootballAgencyClassAlbum findByAgencyClassIdAndAlbumName(@Param("agencyClassId") FootballAgencyClass agencyClassId, @Param("albumName") String albumName);

    @Query("from FootballAgencyClassAlbum as footballAgencyClassAlbum " +
            "left join fetch footballAgencyClassAlbum.user as user " +
            "where footballAgencyClassAlbum.id=?1 " +
            "and  footballAgencyClassAlbum.deleted=false ")
    FootballAgencyClassAlbum findById(long id);

    //分页查询班级相册
    @Query("from FootballAgencyClassAlbum as footballAgencyClassAlbum " +
            "where footballAgencyClassAlbum.footballAgencyClass.id = (:agencyClassId) " +
            "and footballAgencyClassAlbum.deleted=false ")
    List<FootballAgencyClassAlbum> findByAgencyClassIdForPage(@Param("agencyClassId") FootballAgencyClass agencyClassId, Pageable pageable);
}
