package com.microteam.base.userHelp.service.impl;


import com.microteam.base.userHelp.dao.UserHelpFeedbackDao;
import com.microteam.base.userHelp.service.UserHelpFeedbackDaoService;
import com.microteam.base.entity.userHelp.UserHelpFeedback;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service("userHelpFeedbackDaoService")
public class UserHelpFeedbackDaoServiceImpl implements UserHelpFeedbackDaoService {
    static Logger logger = Logger.getLogger(UserHelpFeedbackDaoServiceImpl.class.getName());

    @Autowired
    private UserHelpFeedbackDao dao;

    @Override
    public UserHelpFeedback findById(long id) {
        return dao.findById(id);
    }

    @Override
    public UserHelpFeedback findByUserIdAndMessage(Long userId, String message) {
        return dao.findByUserIdAndMessage(userId, message);

    }

}