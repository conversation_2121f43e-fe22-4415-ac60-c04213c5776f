package com.microteam.base.agency.dao;

import com.microteam.base.entity.agency.FootballAgencyClassAlbumComment;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

public interface FootballAgencyClassAlbumCommentDao extends JpaRepository<FootballAgencyClassAlbumComment, Long>, JpaSpecificationExecutor<FootballAgencyClassAlbumComment> {
    //查询相册评论
    @Query("from FootballAgencyClassAlbumComment as footballAgencyClassAlbumComment " +
            "where footballAgencyClassAlbumComment.footballAgencyClassAlbum.id = (:agencyClassAlbumId) " +
            "and footballAgencyClassAlbumComment.deleted=false ")
    List<FootballAgencyClassAlbumComment> findByAgencyClassAlbumId(@Param("agencyClassAlbumId") Long agencyClassAlbumId);

    //分页查询
    @Query("from FootballAgencyClassAlbumComment as footballAgencyClassAlbumComment " +
            "left join fetch footballAgencyClassAlbumComment.user as user " +
            "where footballAgencyClassAlbumComment.footballAgencyClassAlbum.id = (:agencyClassAlbumId) " +
            "and footballAgencyClassAlbumComment.deleted=false ")
    List<FootballAgencyClassAlbumComment> findByAgencyClassAlbumIdForPage(@Param("agencyClassAlbumId") Long agencyClassAlbumId, Pageable pageable);
}
