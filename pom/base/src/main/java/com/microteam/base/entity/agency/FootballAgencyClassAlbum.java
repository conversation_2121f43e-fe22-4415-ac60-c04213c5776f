package com.microteam.base.entity.agency;


import com.microteam.base.common.base.BaseEntity;
import com.microteam.base.entity.user.User;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;
import java.io.Serializable;

@Entity
@Table(name = "football_agency_class_album")
@Getter
@Setter
public class FootballAgencyClassAlbum extends BaseEntity implements Serializable {

    @ManyToOne
    @JoinColumn(name = "userId")
    private User user;
    @ManyToOne
    @JoinColumn(name = "classId", nullable = false)
    private FootballAgencyClass footballAgencyClass;
    @Column(name = "albumName", length = 100)
    private String albumName;
    @Column(name = "albumDesc", length = 100)
    private String albumDesc;

}
