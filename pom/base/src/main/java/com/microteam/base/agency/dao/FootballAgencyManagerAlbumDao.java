package com.microteam.base.agency.dao;

import com.microteam.base.entity.agency.FootballAgencyManager;
import com.microteam.base.entity.agency.FootballAgencyManagerAlbum;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

public interface FootballAgencyManagerAlbumDao extends JpaRepository<FootballAgencyManagerAlbum, Long>, JpaSpecificationExecutor<FootballAgencyManagerAlbum> {

    //查询机构管理员图片
    @Query("from FootballAgencyManagerAlbum as agencyManagerAlbum " +
            "where agencyManagerAlbum.footballAgencyManager=?1 " +
            "and agencyManagerAlbum.deleted=false " +
            "order by agencyManagerAlbum.createTime desc ")
    List<FootballAgencyManagerAlbum> findAlbumByManager(FootballAgencyManager agencyManager);

}
