package com.microteam.base.agency.service.impl;

import com.microteam.base.agency.dao.FootballAgencyApplicantDao;
import com.microteam.base.agency.service.FootballAgencyApplicantDaoService;
import com.microteam.base.entity.agency.FootballAgencyApplicant;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service("footballAgencyApplicantDaoService")
public class FootballAgencyApplicantDaoServiceImpl implements FootballAgencyApplicantDaoService {
    static Logger logger = Logger.getLogger(FootballAgencyApplicantDaoServiceImpl.class.getName());
    @Autowired
    private FootballAgencyApplicantDao dao;

    @Override
    public FootballAgencyApplicant findByAgencyIdAndApplicantId(Long agencyId, Long applicantId) {
        return dao.findByAgencyIdAndApplicantId(agencyId, applicantId);
    }

    @Override
    public FootballAgencyApplicant findById(long id) {
        return dao.findById(id);
    }

    @Override
    public List<FootballAgencyApplicant> findByAgencyIdForPageOrderByCreateTimeAndAudit(Long agencyId, int page, int pageSize) {
        if (page > 0) {
            page--;
        }
        Pageable pageable = PageRequest.of(page, pageSize);
        List<FootballAgencyApplicant> list = dao.findByAgencyIdForPageOrderByCreateTimeAndAudit(agencyId, pageable);
        if (list == null) {
            return new ArrayList<>();
        }
        return list;
    }
}
