package com.microteam.base.agency.dao;

import com.microteam.base.entity.agency.FootballAgencyClassPublication;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

public interface FootballAgencyClassPublicationDao extends JpaRepository<FootballAgencyClassPublication, Long>, JpaSpecificationExecutor<FootballAgencyClassPublication> {
    //分页查询通知
    @Query("from FootballAgencyClassPublication as footballAgencyClassPublication " +
            "left join fetch footballAgencyClassPublication.user as user " +
            "where footballAgencyClassPublication.footballAgency.id = (:agencyId) " +
            "and footballAgencyClassPublication.footballAgencyClass.id = (:agencyClassId) " +
            "and footballAgencyClassPublication.deleted=false ")
    List<FootballAgencyClassPublication> findByAgencyIdAndAgencyClassIdForPage(@Param("agencyId") Long agencyId, @Param("agencyClassId") Long agencyClassId, Pageable pageable);

    //分页查询通知
    @Query("from FootballAgencyClassPublication as footballAgencyClassPublication " +
            "left join fetch footballAgencyClassPublication.user as user " +
            "where footballAgencyClassPublication.footballAgency.id = (:agencyId) " +
            "and footballAgencyClassPublication.footballAgencyClass=null " +
            "and footballAgencyClassPublication.deleted=false " +
            "order by footballAgencyClassPublication.createTime desc ")
    List<FootballAgencyClassPublication> findByAgencyIdForPage(@Param("agencyId") Long agencyId, Pageable pageable);

    @Query("from FootballAgencyClassPublication as footballAgencyClassPublication " +
            "left join fetch footballAgencyClassPublication.user as user " +
            "where footballAgencyClassPublication.footballAgency.id = (:agencyId) " +
            "and footballAgencyClassPublication.judge = (:judge) " +
            "and footballAgencyClassPublication.footballAgencyClass=null " +
            "and footballAgencyClassPublication.deleted=false " +
            "order by footballAgencyClassPublication.createTime desc ")
    List<FootballAgencyClassPublication> findByAgencyIdAndJudgeForPage(@Param("agencyId") Long agencyId, @Param("judge") Long judge, Pageable pageable);

    //通过Id查询
    @Query("from FootballAgencyClassPublication as footballAgencyClassPublication " +
            "left join fetch footballAgencyClassPublication.user as user " +
            "left join fetch footballAgencyClassPublication.footballAgency as footballAgency " +
            "left join fetch footballAgencyClassPublication.footballAgencyClass as footballAgencyClass " +
            "where footballAgencyClassPublication.id=?1 " +
            "and footballAgencyClassPublication.deleted=false ")
    FootballAgencyClassPublication findById(long id);
}
