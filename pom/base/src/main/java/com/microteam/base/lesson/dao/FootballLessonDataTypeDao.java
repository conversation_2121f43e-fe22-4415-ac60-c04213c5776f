package com.microteam.base.lesson.dao;


import com.microteam.base.entity.lesson.FootballLessonDataType;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

public interface FootballLessonDataTypeDao extends JpaRepository<FootballLessonDataType, Long>, JpaSpecificationExecutor<FootballLessonDataType> {

    @Query("from FootballLessonDataType as dataType " +
            "where dataType.lessonId = (:lessonId) " +
            "and dataType.deleted = false " +
            "ORDER BY dataType.lessonId,dataType.dataTypeId ASC")
    List<FootballLessonDataType> findByLessonId(@Param("lessonId") Long lessonId);

    @Query(value = "SELECT dataType.* " +
            "FROM football_lesson_datatype as dataType," +
            "football_datatype as type " +
            "WHERE dataType.dataTypeId = type.id " +
            "AND dataType.lessonId in (:lessonIdList) " +
            "AND dataType.deleted = 0 " +
            "ORDER BY dataType.lessonId,dataType.dataTypeId ASC", nativeQuery = true)
    List<FootballLessonDataType> findByLessonIdList(@Param("lessonIdList") List<Long> lessonIdList);

    @Query("from FootballLessonDataType as dataType " +
            "where dataType.courwareId = (:courwareId) " +
            "and dataType.deleted = false " +
            "ORDER BY dataType.courwareId,dataType.dataTypeId ASC")
    List<FootballLessonDataType> findByCourwareId(@Param("courwareId") Long courwareId);
}
