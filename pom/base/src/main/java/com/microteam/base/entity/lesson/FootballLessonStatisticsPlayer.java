package com.microteam.base.entity.lesson;

import com.microteam.base.common.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.io.Serializable;

@Entity
@Table(name = "football_lesson_statistics_player")
@Getter
@Setter
public class FootballLessonStatisticsPlayer extends BaseEntity implements Serializable {

    @Basic
    @Column(name = "lessonId", nullable = false)
    private long lessonId;
    @Basic
    @Column(name = "userId", nullable = false)
    private long userId;
    @Basic
    @Column(name = "teamId", nullable = false)
    private Long teamId;
    @Basic
    @Column(name = "touchCounts")
    private Integer touchCounts;
    @Basic
    @Column(name = "passBallCounts")
    private Integer passBallCounts;
    @Basic
    @Column(name = "oneFootPassCount")
    private Integer oneFootPassCount;
    @Basic
    @Column(name = "twoFootPassCount")
    private Integer twoFootballPassCount;
    @Basic
    @Column(name = "velocity")
    private Long velocity;
    @Basic
    @Column(name = "carryCount")
    private Integer carryCount;
    @Basic
    @Column(name = "carryTime")
    private Integer carryTime;
    @Basic
    @Column(name = "highMoveCount")
    private Integer highMoveCount;
    @Basic
    @Column(name = "highMoveDistance")
    private Long highMoveDistance;
    @Basic
    @Column(name = "highMoveCalorie")
    private String highMoveCalorie;
    @Basic
    @Column(name = "midMoveCount")
    private Integer midMoveCount;
    @Basic
    @Column(name = "midMoveDistance")
    private Long midMoveDistance;
    @Basic
    @Column(name = "midMoveCalorie")
    private String midMoveCalorie;
    @Basic
    @Column(name = "lowMoveCount")
    private Integer lowMoveCount;
    @Basic
    @Column(name = "lowMoveDistance")
    private Long lowMoveDistance;
    @Basic
    @Column(name = "lowMoveCalorie")
    private String lowMoveCalorie;
    @Basic
    @Column(name = "normalMoveCount")
    private Integer normalMoveCount;
    @Basic
    @Column(name = "normalMoveDistance")
    private Long normalMoveDistance;
    @Basic
    @Column(name = "normalMoveCalorie")
    private String normalMoveCalorie;
    @Basic
    @Column(name = "wholeMoveDistance")
    private Long wholeMoveDistance;
    @Basic
    @Column(name = "moveCalorie")
    private Long moveCalorie;
    @Basic
    @Column(name = "calorieCurveData")
    private String calorieCurveData;
    @Basic
    @Column(name = "carryDistance")
    private Integer carryDistance;
    @Basic
    @Column(name = "highCarryDistance")
    private Integer highCarryDistance;
    @Basic
    @Column(name = "midCarryDistance")
    private Integer midCarryDistance;
    @Basic
    @Column(name = "lowCarryDistance")
    private Integer lowCarryDistance;
    @Basic
    @Column(name = "normalCarryDistance")
    private Integer normalCarryDistance;
    @Basic
    @Column(name = "sprintDistance", length = 500)
    private String sprintDistance;
    @Basic
    @Column(name = "playMovementDistance", length = 500)
    private String playMovementDistance;
    @Basic
    @Column(name = "ballSpeed", length = 500)
    private String ballSpeed;
    @Basic
    @Column(name = "highCarryCount")
    private Integer highCarryCount;
    @Basic
    @Column(name = "midCarryCount")
    private Integer midCarryCount;
    @Basic
    @Column(name = "lowCarryCount")
    private Integer lowCarryCount;
    @Basic
    @Column(name = "normalCarryCount")
    private Integer normalCarryCount;
    @Basic
    @Column(name = "leftPassBallCounts")
    private Integer leftPassBallCounts;
    @Basic
    @Column(name = "rightPassBallCounts")
    private Integer rightPassBallCounts;
    @Basic
    @Column(name = "longPass")
    private Integer longPass;
    @Basic
    @Column(name = "shortPass")
    private Integer shortPass;
    @Basic
    @Column(name = "passBallError")
    private Integer passBallError;
    @Basic
    @Column(name = "maxSprintSpeed")
    private Double maxSprintSpeed;

}
