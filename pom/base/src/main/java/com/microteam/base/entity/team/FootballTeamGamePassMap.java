package com.microteam.base.entity.team;

import com.microteam.base.common.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.io.Serializable;

@Getter
@Setter
@Entity
@Table(name = "football_team_game_passmap")
public class FootballTeamGamePassMap extends BaseEntity implements Serializable {

    @Column(name = "gameId", nullable = false)
    private Long gameId;
    @Column(name = "teamId", nullable = false)
    private Long teamId;
    @Column(name = "passUserId", nullable = false)
    private Long passUserId;
    @Column(name = "byPassUserId", nullable = false)
    private Long byPassUserId;
    @Column(name = "count")
    private Integer count;
}
