package com.microteam.base.agency.service.impl;

import com.microteam.base.agency.dao.FootballAgencyClassAlbumCommentDao;
import com.microteam.base.entity.agency.FootballAgencyClassAlbumComment;
import com.microteam.base.agency.service.FootballAgencyClassAlbumCommentDaoService;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.List;

@Service("footballAgencyClassAlbumCommentDaoService")
public class FootballAgencyClassAlbumCommentDaoServiceImpl implements FootballAgencyClassAlbumCommentDaoService {
    static Logger logger = Logger.getLogger(FootballAgencyClassAlbumCommentDaoServiceImpl.class.getName());
    @Autowired
    private FootballAgencyClassAlbumCommentDao dao;

    //查询相册评论
    @Override
    public List<FootballAgencyClassAlbumComment> findByAgencyClassAlbumId(Long agencyClassAlbumId) {
        return dao.findByAgencyClassAlbumId(agencyClassAlbumId);
    }

    //分页查询
    @Override
    public List<FootballAgencyClassAlbumComment> findByAgencyClassAlbumIdForPage(Long agencyClassAlbumId, int page, int pageSize) {
        if (page > 0) {
            page--;
        }
        Pageable pageable = PageRequest.of(page, pageSize);
        return dao.findByAgencyClassAlbumIdForPage(agencyClassAlbumId, pageable);
    }

}
