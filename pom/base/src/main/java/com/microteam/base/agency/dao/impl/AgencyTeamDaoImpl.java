//package com.microteam.base.agency.dao.impl;
//
//import com.microteam.base.agency.dao.AgencyTeamDao;
//import com.microteam.base.common.base.AbstractHibernateDao;
//import com.microteam.base.entity.agency.AgencyTeam;
//import org.hibernate.query.Query;
//import org.springframework.stereotype.Repository;
//
//import java.util.List;
//
//@Repository
//public class AgencyTeamDaoImpl extends AbstractHibernateDao<AgencyTeam> implements AgencyTeamDao {
//
//    @Override
//    public Integer findContByAgencyId(Long agencyId) {
//        String sql = "select count(1) from agency_team as agencyTeam " +
//                " where agencyTeam.agencyId = (:agencyId) " +
//                " and agencyTeam.deleted = 0 ";
//        Query query = getCurrentSession().createNativeQuery(sql).setParameter("agencyId", agencyId);
//        int count = ((Number) query.uniqueResult()).intValue();
//        return count;
//    }
//
//    @Override
//    public List<AgencyTeam> findByAgencyId(Long agencyId) {
//        String hql = " from AgencyTeam as agencyTeam " +
//                " left join fetch agencyTeam.agency as agency " +
//                " left join fetch agencyTeam.team as team " +
//                " where agency.id = (:agencyId) " +
//                " and agencyTeam.deleted = false " +
//                " and agency.deleted = false " +
//                " and team.deleted = false ";
//        Query query = getCurrentSession().createQuery(hql).setParameter("agencyId", agencyId);
//        return query.list();
//    }
//
//    @Override
//    public List<AgencyTeam> findByAgencyIdForPage(Long agencyId, int page, int pageSize) {
//        String hql = " from AgencyTeam as agencyTeam " +
//                " left join fetch agencyTeam.agency as agency " +
//                " left join fetch agencyTeam.team as team " +
//                " where agency.id = (:agencyId) " +
//                " and agencyTeam.deleted = false " +
//                " and agency.deleted = false " +
//                " and team.deleted = false ";
//        Query query = getCurrentSession().createQuery(hql).setParameter("agencyId", agencyId);
//        query.setFirstResult((page - 1) * pageSize);
//        query.setMaxResults(pageSize);
//        return query.list();
//    }
//
//    @Override
//    public AgencyTeam findByTeamId(Long teamId) {
//        String hql = " from AgencyTeam as agencyTeam " +
//                " left join fetch agencyTeam.agency as agency " +
//                " left join fetch agencyTeam.team as team " +
//                " where team.id = (:teamId) " +
//                " and agencyTeam.deleted = false " +
//                " and agency.deleted = false " +
//                " and team.deleted = false ";
//        Query query = getCurrentSession().createQuery(hql).setParameter("teamId", teamId);
//        return (AgencyTeam) query.uniqueResult();
//    }
//
//    @Override
//    public List<AgencyTeam> findByTeamUserId(Long userId) {
//        String hql = " from AgencyTeam as agencyTeam " +
//                " left join fetch agencyTeam.agency as agency " +
//                " left join fetch agencyTeam.team as team " +
//                " left join fetch team.user as teamUser" +
//                " where teamUser.id = (:userId) " +
//                " and agencyTeam.deleted = false " +
//                " and agency.deleted = false " +
//                " and team.deleted = false ";
//        Query query = getCurrentSession().createQuery(hql).setParameter("userId", userId);
//        return query.list();
//    }
//
//    @Override
//    public int deleteByTeamId(Long teamId) {
//        String hql = "delete AgencyTeam as agencyTeam " +
//                "where agencyTeam.team.id = (:teamId) ";
//        Query query = getCurrentSession().createQuery(hql)
//                .setParameter("teamId", teamId);
//        return query.executeUpdate();
//    }
//}
