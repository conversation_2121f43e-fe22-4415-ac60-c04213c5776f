//package com.microteam.base.common.config;
//
//import org.springframework.core.annotation.Order;
//import org.springframework.orm.hibernate5.support.OpenSessionInViewFilter;
//
//import javax.servlet.*;
//import javax.servlet.annotation.WebFilter;
//import java.io.IOException;
//
//@WebFilter(filterName="commonFilter",urlPatterns="/*")
//@Order(1)
//public class CommonFilter implements Filter {
//
//    private final OpenSessionInViewFilter filter;
//
//    public CommonFilter() {
//        filter = new OpenSessionInViewFilter();
//        filter.setSessionFactoryBeanName("sessionFactory");
//    }
//
//    @Override
//    public void init(FilterConfig filterConfig) throws ServletException {
//        filter.init(filterConfig);
//    }
//
//    @Override
//    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain) throws IOException, ServletException {
//        filter.doFilter(request, response, chain);
//    }
//
//    @Override
//    public void destroy() {
//        filter.destroy();
//    }
//}
