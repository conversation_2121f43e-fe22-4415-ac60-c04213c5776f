package com.microteam.base.entity.agency;


import com.microteam.base.common.base.BaseEntity;
import com.microteam.base.entity.user.User;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

@Entity
@Table(name = "football_agency_fans")
@Getter
@Setter
public class FootballAgencyFans extends BaseEntity implements Serializable {

    @ManyToOne
    @JoinColumn(name = "agencyId", nullable = false)
    private FootballAgency footballAgency;
    @ManyToOne
    @JoinColumn(name = "classId")
    private FootballAgencyClass footballAgencyClass;
    @ManyToOne
    @JoinColumn(name = "courseId")
    private FootballAgencyClassCourse footballAgencyClassCourse;
    @ManyToOne
    @JoinColumn(name = "userId", nullable = false)
    private User user;
    @Column(name = "fansName", length = 100)
    private String fansName;
    @Column(name = "headImgNetUrl", length = 250)
    private String headImgNetUrl;
    @Column(name = "headImgUrl", length = 250)
    private String headImgUrl;
    @Column(name = "headImgName", length = 50)
    private String headImgName;
    @Column(name = "headImgLength")
    private Long headImgLength;
    @Column(name = "followedClass")
    private Boolean followedClass;
    @Column(name = "followedAgency")
    private Boolean followedAgency;
    @Column(name = "applyMessage", length = 200)
    private String applyMessage;
    @Column(name = "isPermited")
    private Integer isPermited;

    public FootballAgencyFans() {
    }

    public FootballAgencyFans(FootballAgency footballAgency, User user,
                              Date createTime, boolean deleted) {
        this.footballAgency = footballAgency;
        this.user = user;
        this.createTime = createTime;
        this.deleted = deleted;
    }

    public FootballAgencyFans(FootballAgency footballAgency, User user) {
        this.footballAgency = footballAgency;
        this.user = user;
    }

    public FootballAgencyFans(FootballAgency footballAgency,
                              FootballAgencyClass footballAgencyClass,
                              FootballAgencyClassCourse footballAgencyClassCourse, User user,
                              String fansName, String headImgNetUrl, String headImgUrl,
                              String headImgName, Long headImgLength, Boolean followedClass,
                              Boolean followedAgency, Date createTime, String applyMessage,
                              Integer isPermited, boolean deleted) {
        this.footballAgency = footballAgency;
        this.footballAgencyClass = footballAgencyClass;
        this.footballAgencyClassCourse = footballAgencyClassCourse;
        this.user = user;
        this.fansName = fansName;
        this.headImgNetUrl = headImgNetUrl;
        this.headImgUrl = headImgUrl;
        this.headImgName = headImgName;
        this.headImgLength = headImgLength;
        this.followedClass = followedClass;
        this.followedAgency = followedAgency;
        this.createTime = createTime;
        this.applyMessage = applyMessage;
        this.isPermited = isPermited;
        this.deleted = deleted;
    }

}
