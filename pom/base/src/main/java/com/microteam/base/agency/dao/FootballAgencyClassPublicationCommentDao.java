package com.microteam.base.agency.dao;

import com.microteam.base.entity.agency.FootballAgencyClassPublicationComment;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

public interface FootballAgencyClassPublicationCommentDao extends JpaRepository<FootballAgencyClassPublicationComment, Long>, JpaSpecificationExecutor<FootballAgencyClassPublicationComment> {
    //查询公告评论
    @Query("from FootballAgencyClassPublicationComment as agencyClassPublicationComment " +
            "left join fetch agencyClassPublicationComment.user as user " +
            "where agencyClassPublicationComment.footballAgencyClassPublication.id = (:publicationId) " +
            "and agencyClassPublicationComment.deleted=false ")
    List<FootballAgencyClassPublicationComment> findByPublicationId(@Param("publicationId") Long publicationId);

    @Query("from FootballAgencyClassPublicationComment as agencyClassPublicationComment " +
            "left join fetch agencyClassPublicationComment.user as user " +
            "where agencyClassPublicationComment.footballAgencyClassPublication.id = (:publicationId) " +
            "and agencyClassPublicationComment.deleted=false ")
    List<FootballAgencyClassPublicationComment> findByPublicationIdForPage(@Param("publicationId") Long publicationId, Pageable pageable);
}
