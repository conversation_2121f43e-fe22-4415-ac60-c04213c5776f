package com.microteam.base.agency.service.impl;

import com.microteam.base.agency.dao.FootballAgencyClassDao;
import com.microteam.base.agency.service.FootballAgencyClassDaoService;
import com.microteam.base.entity.agency.FootballAgencyClass;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.List;

@Service("footballAgencyClassDaoService")
public class FootballAgencyClassDaoServiceImpl implements FootballAgencyClassDaoService {
    static Logger logger = Logger.getLogger(FootballAgencyClassDaoServiceImpl.class.getName());
    @Autowired
    private FootballAgencyClassDao dao;

    //查询机构下的班级
    @Override
    public List<FootballAgencyClass> findByAgencyId(Long agencyId) {
        return dao.findByAgencyId(agencyId);
    }

    @Override
    public List<FootballAgencyClass> findHistoryByAgencyId(Long agencyId, int page, int pageSize, String searchType) {
        boolean isOver = ("historyClass".equals(searchType));
        if (page > 0) {
            page--;
        }
        Pageable pageable = PageRequest.of(page, pageSize);
        return dao.findHistoryByAgencyId(agencyId, isOver, pageable);
    }

    @Override
    public FootballAgencyClass findById(long id) {
        return dao.findById(id);
    }


}
