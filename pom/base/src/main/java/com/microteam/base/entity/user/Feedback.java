package com.microteam.base.entity.user;

import com.microteam.base.common.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.io.Serializable;

@Entity
@Table(name = "feedback")
@Getter
@Setter
public class Feedback extends BaseEntity implements Serializable {
    @Column(name = "userId")
    private Long userId;
    @Column(name = "feedback")
    private String feedback;
    @Column(name = "img")
    private String img;
    @Column(name = "type")
    private String type;
}
