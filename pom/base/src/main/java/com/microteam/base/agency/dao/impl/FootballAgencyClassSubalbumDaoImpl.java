//package com.microteam.base.agency.dao.impl;
//
//import com.microteam.base.agency.dao.FootballAgencyClassSubalbumDao;
//import com.microteam.base.common.base.AbstractHibernateDao;
//import com.microteam.base.entity.agency.FootballAgencyClassSubalbum;
//import org.apache.log4j.Logger;
//import org.hibernate.query.Query;
//import org.springframework.stereotype.Repository;
//
//import java.util.List;
//
//@Repository("footballAgencyClassSubalbumDao")
//public class FootballAgencyClassSubalbumDaoImpl extends AbstractHibernateDao<FootballAgencyClassSubalbum> implements FootballAgencyClassSubalbumDao {
//    static Logger logger = Logger.getLogger(FootballAgencyClassSubalbumDaoImpl.class.getName());
//
//    public FootballAgencyClassSubalbumDaoImpl() {
//        super();
//        setClazz(FootballAgencyClassSubalbum.class);
//    }
//
//    //查询相册的图片列表
//    @Override
//    public List<FootballAgencyClassSubalbum> findByAgencyClassAlbumId(Long agencyClassAlbumId) {
//        String hql = "from FootballAgencyClassSubalbum as footballAgencyClassSubalbum " +
//                "left join fetch footballAgencyClassSubalbum.user as user " +
//                "where footballAgencyClassSubalbum.footballAgencyClassAlbum.id = (:agencyClassAlbumId) " +
//                "and footballAgencyClassSubalbum.deleted=false " +
//                "order by footballAgencyClassSubalbum.createTime desc ";
//        Query query = getCurrentSession().createQuery(hql)
//                .setParameter("agencyClassAlbumId", agencyClassAlbumId);
//        return (List<FootballAgencyClassSubalbum>) query.list();
//    }
//
//
//}
