package com.microteam.base.agency.service.impl;

import com.microteam.base.agency.dao.FootballAgencyEvaluateDao;
import com.microteam.base.agency.service.FootballAgencyEvaluateDaoService;
import com.microteam.base.entity.agency.FootballAgency;
import com.microteam.base.entity.agency.FootballAgencyEvaluate;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.List;

@Service("footballAgencyEvaluateDaoService")
public class FootballAgencyEvaluateDaoServiceImpl implements FootballAgencyEvaluateDaoService {
    static Logger logger = Logger.getLogger(FootballAgencyEvaluateDaoServiceImpl.class.getName());
    @Autowired
    private FootballAgencyEvaluateDao dao;

    @Override
    public List<FootballAgencyEvaluate> findEvaluateByAgency(FootballAgency agency, int page, int pageSize) {
        if (page > 0) {
            page--;
        }
        Pageable pageable = PageRequest.of(page, pageSize);
        return dao.findEvaluateByAgency(agency, pageable);
    }

}
