package com.microteam.base.lesson.dao;


import com.microteam.base.entity.lesson.FootballLessonSecondEnroll;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

public interface FootballLessonSecondEnrollDao extends JpaRepository<FootballLessonSecondEnroll, Long>, JpaSpecificationExecutor<FootballLessonSecondEnroll> {

    @Query("from FootballLessonSecondEnroll as enroll " +
            "left join fetch enroll.user as user " +
            "left join fetch enroll.lesson as lesson " +
            "left join fetch enroll.team " +
            "where lesson.id = :lessonId " +
            "and enroll.group = :isGroup " +
            "and enroll.deleted = false " +
            "order by enroll.id asc")
    List<FootballLessonSecondEnroll> findByLessonIdAndIsGroup(@Param("lessonId") Long lessonId, @Param("isGroup") Integer isGroup);

    @Query("from FootballLessonSecondEnroll as enroll " +
            "left join fetch enroll.user as user " +
            "left join fetch enroll.lesson as lesson " +
            "left join fetch enroll.team as team " +
            "where user.id = :userId " +
            "and lesson.id = :lessonId " +
            "and team.id = :teamId " +
            "and enroll.deleted = false")
    FootballLessonSecondEnroll findByUserIdAndTeamIdAndLessonId(@Param("userId") Long userId, @Param("teamId") Long teamId, @Param("lessonId") Long lessonId);

    @Query("from FootballLessonSecondEnroll  " +
            "where userId = :userId " +
            "and lessonId = :lessonId " +
            "and deleted = false")
    FootballLessonSecondEnroll findUserIdAndLessonId(@Param("userId") Long userId,@Param("lessonId") Long lessonId);

    @Query("from FootballLessonSecondEnroll as enroll " +
            "left join fetch enroll.user as user " +
            "left join fetch enroll.lesson as lesson " +
            "where user.id = :userId " +
            "and lesson.id = :lessonId " +
            "and enroll.deleted = false")
    FootballLessonSecondEnroll findByUserIdAndLessonId(@Param("userId")Long userId,@Param("lessonId") Long lessonId);

    @Query("from FootballLessonSecondEnroll as enroll " +
            "left join fetch enroll.team as team " +
            "left join fetch enroll.user as user " +
            "left join fetch enroll.lesson as lesson " +
            "where user.id not in (:userList) " +
            "and team.id = :teamId " +
            "and lesson.id = :lessonId " +
            "and enroll.deleted = false")
    List<FootballLessonSecondEnroll> findNotOfUser(@Param("userList") List<Long> userList, @Param("teamId") Long teamId, @Param("lessonId") Long lessonId);

    @Query("from FootballLessonSecondEnroll as enroll " +
            "left join fetch enroll.user as user " +
            "left join fetch enroll.lesson as lesson " +
            "left join fetch enroll.team as team " +
            "where lesson.id = :lessonId " +
            "and team.id = :teamId " +
            "and enroll.deleted = false ")
    List<FootballLessonSecondEnroll> findByTeamIdAndLessonId(@Param("teamId") Long teamId, @Param("lessonId") Long lessonId);

    @Query("from FootballLessonSecondEnroll as enroll " +
            " left join fetch enroll.user as user " +
            " left join fetch enroll.lesson as lesson " +
            " left join fetch enroll.team as team" +
            " where lesson.id = (:lessonId) " +
            " and enroll.deleted = false ")
    List<FootballLessonSecondEnroll> findByLessonId(@Param("lessonId") Long lessonId);

    @Query("delete from FootballLessonSecondEnroll as lessonEnroll " +
            "where lessonEnroll.team.id = (:teamId) ")
    @Modifying
    int deleteByTeamId(@Param("teamId") Long teamId);

}
