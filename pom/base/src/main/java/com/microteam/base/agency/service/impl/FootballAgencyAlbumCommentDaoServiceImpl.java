package com.microteam.base.agency.service.impl;

import com.microteam.base.agency.dao.FootballAgencyAlbumCommentDao;
import com.microteam.base.agency.service.FootballAgencyAlbumCommentDaoService;
import com.microteam.base.entity.agency.FootballAgencyAlbum;
import com.microteam.base.entity.agency.FootballAgencyAlbumComment;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.List;

@Service("footballAgencyAlbumCommentDaoService")
public class FootballAgencyAlbumCommentDaoServiceImpl implements FootballAgencyAlbumCommentDaoService {
    static Logger logger = Logger.getLogger(FootballAgencyAlbumCommentDaoServiceImpl.class.getName());
    @Autowired
    private FootballAgencyAlbumCommentDao dao;

    public FootballAgencyAlbumCommentDaoServiceImpl() {
        super();
    }

    //查询相册评论
    @Override
    public List<FootballAgencyAlbumComment> findCommentByAlbum(
            FootballAgencyAlbum agencyAlbum) {
        return dao.findCommentByAlbum(agencyAlbum);
    }

    //分页查询相册评论
    @Override
    public List<FootballAgencyAlbumComment> findCommentByAlbumByPage(
            FootballAgencyAlbum agencyAlbum, int page, int pageSize) {
        if (page > 0) {
            page--;
        }
        Pageable pageable = PageRequest.of(page, pageSize);
        return dao.findCommentByAlbumByPage(agencyAlbum, pageable);
    }

}
