package com.microteam.base.agency.service.impl;

import com.microteam.base.agency.dao.FootballAgencyUserHasCountDao;
import com.microteam.base.entity.agency.FootballAgencyUserHasCount;
import com.microteam.base.agency.service.FootballAgencyUserHasCountDaoService;
import com.microteam.base.entity.user.User;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service("footballAgencyUserHasCountDaoService")
public class FootballAgencyUserHasCountDaoServiceImpl implements FootballAgencyUserHasCountDaoService {
    static Logger logger = Logger.getLogger(FootballAgencyUserHasCountDaoServiceImpl.class.getName());
    @Autowired
    private FootballAgencyUserHasCountDao dao;

    public FootballAgencyUserHasCountDaoServiceImpl() {
        super();

    }

    //查询用户的机构表
    @Override
    public FootballAgencyUserHasCount findAgencyUserHasCountByUser(User user) {

        return dao.findAgencyUserHasCountByUser(user);
    }

}
