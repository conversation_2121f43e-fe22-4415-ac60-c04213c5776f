package com.microteam.base.agency.dao;

import com.microteam.base.entity.agency.FootballAgency;
import com.microteam.base.entity.agency.FootballAgencyClass;
import com.microteam.base.entity.agency.FootballAgencyClassCourse;
import com.microteam.base.entity.agency.FootballAgencyCoach;
import com.microteam.base.entity.user.User;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

public interface FootballAgencyCoachDao extends JpaRepository<FootballAgencyCoach, Long>, JpaSpecificationExecutor<FootballAgencyCoach> {
    //查询教练
    @Query("from FootballAgencyCoach as agencyCoach " +
            "where agencyCoach.footballAgency.id = (:agencyId) " +
            "and agencyCoach.footballAgencyClass.id = (:agencyClassId) " +
            "and agencyCoach.footballAgencyClassCourse.id = (:agencyClassCourseId) " +
            "and agencyCoach.user.id = (:userId) " +
            "and agencyCoach.deleted=false ")
    FootballAgencyCoach findByAgencyIdAndAgencyClassIdAndAgencyClassCourseIdAndUserId(@Param("agencyId") Long agencyId, @Param("agencyClassId") Long agencyClassId, @Param("agencyClassCourseId") Long agencyClassCourseId, @Param("userId") Long userId);

    //查询教练
    @Query("from FootballAgencyCoach as agencyCoach " +
            "where agencyCoach.footballAgency.id = (:agencyId) " +
            "and agencyCoach.footballAgencyClass.id = (:agencyClassId) " +
            "and agencyCoach.user.id = (:userId) " +
            "and agencyCoach.deleted=false ")
    List<FootballAgencyCoach> findByAgencyIdAndAgencyClassIdAndUserId(@Param("agencyId") Long agencyId, @Param("agencyClassId") Long agencyClassId, @Param("userId") Long userId);

    //查询是不是机构下的教练
    @Query("from FootballAgencyCoach as agencyCoach " +
            "where agencyCoach.footballAgency.id = (:agencyId) " +
            "and agencyCoach.user.id = (:userId) " +
            "and agencyCoach.deleted=false ")
    List<FootballAgencyCoach> findByAgencyIdAndUserId(@Param("agencyId") Long agencyId, @Param("userId") Long userId);

    //查询班级课程教练
    @Query("from FootballAgencyCoach as agencyCoach " +
            "left join fetch agencyCoach.user as user " +
            "where agencyCoach.footballAgency.id = (:agencyId) " +
            "and agencyCoach.footballAgencyClass.id = (:agencyClassId) " +
            "and agencyCoach.footballAgencyClassCourse.id = (:agencyClassCourseId) " +
            "and agencyCoach.deleted=false ")
    List<FootballAgencyCoach> findByAgencyIdAndAgencyClassIdAndAgencyClassCourseId(@Param("agencyId") Long agencyId, @Param("agencyClassId") Long agencyClassId, @Param("agencyClassCourseId") Long agencyClassCourseId);

    //查询机构下的所有教练
    @Query("from FootballAgencyCoach as agencyCoach " +
            "left join fetch agencyCoach.user as user " +
            "where agencyCoach.footballAgency.id = (:agencyId) " +
            "and agencyCoach.deleted=false ")
    List<FootballAgencyCoach> findByAgencyId(@Param("agencyId") Long agencyId);

    @Query("from FootballAgencyCoach as agencyCoach " +
            "left join fetch agencyCoach.user as user " +
            "left join fetch agencyCoach.footballAgencyClass as footballAgencyClass " +
            "where agencyCoach.footballAgency.id = (:agencyId) " +
            "and agencyCoach.deleted=false ")
    List<FootballAgencyCoach> findByAgencyIdForPage(@Param("agencyId") Long agencyId, Pageable pageable);

    //查询机构下的教练
    @Query("from FootballAgencyCoach as agencyCoach " +
            "left join fetch agencyCoach.user as user " +
            "left join fetch agencyCoach.footballAgencyClass as footballAgencyClass " +
            "where agencyCoach.footballAgency.id = (:agencyId) " +
            "and agencyCoach.coachName like (:coachName) " +
            "and agencyCoach.deleted=false ")
    List<FootballAgencyCoach> findByAgencyIdAndSearchForPage(@Param("agencyId") Long agencyId, @Param("coachName") String coachName, Pageable pageable);

    //查询课程教练名单列表
    @Query("from FootballAgencyCoach as agencyCoach " +
            "left join fetch agencyCoach.user as user " +
            "where agencyCoach.footballAgency.id = (:agencyId) " +
            "and agencyCoach.footballAgencyClass.id = (:agencyClassId) " +
            "and agencyCoach.footballAgencyClassCourse.id = (:agencyClassCourseId) " +
            "and agencyCoach.deleted=false ")
    List<FootballAgencyCoach> findByAgencyIdAndAgencyClassIdAndAgencyClassCourseIdForPage(@Param("agencyId") Long agencyId, @Param("agencyClassId") Long agencyClassId, @Param("agencyClassCourseId") Long agencyClassCourseId, Pageable pageable);

//    List<FootballAgencyCoach> findCoachByUser(User user, String cityCode, String countyCode, String search);

    @Query("from FootballAgencyCoach as agencyCoach " +
            "left join fetch agencyCoach.footballAgency as footballAgency " +
            "left join fetch agencyCoach.footballAgencyClass as footballAgencyClass " +
            "left join fetch agencyCoach.footballAgencyClassCourse as footballAgencyClassCourse " +
            "left join fetch agencyCoach.user as user " +
            "where agencyCoach.id =?1 ")
    FootballAgencyCoach findAllById(long id);

    @Query("from FootballAgencyCoach as agencyCoach " +
            "left join fetch agencyCoach.user as user " +
            "left join fetch agencyCoach.footballAgencyClass as footballAgencyClass " +
            "where agencyCoach.id =?1 " +
            "and agencyCoach.deleted=false ")
    FootballAgencyCoach findById(long id);

    //查询机构下的教练数量
    @Query("select distinct new com.microteam.base.entity.agency.FootballAgencyCoach(agencyCoach.footballAgency,agencyCoach.user) " +
            "from FootballAgencyCoach as agencyCoach " +
            "left join agencyCoach.user as user " +
            "left join  agencyCoach.footballAgency as footballAgency " +
            "where agencyCoach.footballAgency.id = (:agencyId) " +
            "and agencyCoach.footballAgencyClass.id = (:agencyClassId) " +
            "and agencyCoach.deleted=false ")
    List<FootballAgencyCoach> findByAgencyIdAndAgencyClassId(@Param("agencyId") Long agencyId, @Param("agencyClassId") Long agencyClassId);

    @Query("from FootballAgencyCoach as agencyCoach " +
            "left join fetch agencyCoach.user as user " +
            "where agencyCoach.footballAgency.id = (:agencyId) " +
            "and agencyCoach.footballAgencyClass.id = (:agencyClassId) " +
            "and agencyCoach.deleted=false ")
    List<FootballAgencyCoach> findByAgencyIdAndAgencyClassIdForPage(@Param("agencyId") Long agencyId, @Param("agencyClassId") Long agencyClassId, Pageable pageable);

    @Query("from FootballAgencyCoach as agencyCoach " +
            "left join fetch agencyCoach.user as user " +
            "where agencyCoach.footballAgency.id = (:agencyId) " +
            "and agencyCoach.footballAgencyClass.id = (:agencyClassId) " +
            "and agencyCoach.coachName like (:coachName) " +
            "and agencyCoach.deleted = false ")
    List<FootballAgencyCoach> findByAgencyIdAndAgencyClassIdAndCoachNameForPage(@Param("agencyId") Long agencyId, @Param("agencyClassId") Long agencyClassId, @Param("coachName") String coachName, Pageable pageable);

    //删除班级下的所有教练
    @Query("delete from FootballAgencyCoach as agencyCoach " +
            "where agencyCoach.footballAgency.id = (:agencyId) " +
            "and agencyCoach.footballAgencyClass.id = (:agencyClassId) ")
    @Modifying
    int delByAgencyIdAndAgencyClassId(@Param("agencyId") Long agencyId, @Param("agencyClassId") Long agencyClassId);

    //查询教练是否报名了
    @Query("from FootballAgencyCoach as agencyCoach " +
            "where agencyCoach.footballAgency =?1 " +
            "and agencyCoach.user=?2 ")
    List<FootballAgencyCoach> findCoachIsEnrolled(FootballAgency agency, User user);

    //分页查询机构教练报名申请
    @Query("from FootballAgencyCoach as agencyCoach " +
            "left join fetch agencyCoach.user as user " +
            "where agencyCoach.footballAgency =?1 " +
            "and agencyCoach.deleted=true ")
    List<FootballAgencyCoach> findCoachEnrollByAgencyForPage(FootballAgency agency, Pageable pageable);

    //查询课程下的所有教练
    @Query("from FootballAgencyCoach as agencyCoach " +
            "left join fetch agencyCoach.user as user " +
            "where agencyCoach.footballAgencyClassCourse =?1 " +
            "and agencyCoach.deleted=true ")
    List<FootballAgencyCoach> findCoachByCourse(FootballAgencyClassCourse agencyClassCourse);

    @Query("from FootballAgencyCoach as agencyCoach left join fetch agencyCoach.user as user " +
            "where agencyCoach.footballAgency=?1 " +
            "and agencyCoach.coachName like ?2 " +
            "and agencyCoach.deleted=false " +
            "order by agencyCoach.createTime desc ")
    List<FootballAgencyCoach> findCoachBySearch(FootballAgency agency, String coachName);

    //分页查询机构教练报名申请
    @Query("from FootballAgencyCoach as agencyCoach " +
            "left join fetch agencyCoach.user as user " +
            "where agencyCoach.footballAgency =?1 " +
            "and agencyCoach.deleted=true ")
    List<FootballAgencyCoach> findCoachEnrollByAgency(FootballAgency agency);

    //分页模糊查询教练申请表
    @Query("from FootballAgencyCoach as agencyCoach " +
            "left join fetch agencyCoach.user as user " +
            "where agencyCoach.footballAgency =?1 " +
            "and agencyCoach.coachName like ?2 " +
            "and agencyCoach.deleted=true ")
    List<FootballAgencyCoach> findCoachEnrollBySearch(FootballAgency agency, String coachName, Pageable pageable);

    //删除课程下的教练
    @Query("delete from FootballAgencyCoach as agencyCoach " +
            "where agencyCoach.footballAgency=?1 " +
            "and agencyCoach.footballAgencyClass=?2 " +
            "and agencyCoach.footballAgencyClassCourse=?3 ")
    @Modifying
    int delCoachByAgency(FootballAgency agency, FootballAgencyClass agencyClass, FootballAgencyClassCourse agencyClassCourse);
}
