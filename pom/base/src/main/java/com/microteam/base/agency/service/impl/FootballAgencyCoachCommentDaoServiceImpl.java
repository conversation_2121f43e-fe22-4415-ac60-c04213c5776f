package com.microteam.base.agency.service.impl;

import com.microteam.base.agency.dao.FootballAgencyCoachCommentDao;
import com.microteam.base.entity.agency.FootballAgencyCoachComment;
import com.microteam.base.agency.service.FootballAgencyCoachCommentDaoService;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.List;

@Service("footballAgencyCoachCommentDaoService")
public class FootballAgencyCoachCommentDaoServiceImpl implements FootballAgencyCoachCommentDaoService {
    static Logger logger = Logger.getLogger(FootballAgencyCoachCommentDaoServiceImpl.class.getName());
    @Autowired
    private FootballAgencyCoachCommentDao dao;

    @Override
    public List<FootballAgencyCoachComment> findByAgencyCoachIdForPage(Long agencyCoachId, int page, int pageSize) {
        if (page > 0) {
            page--;
        }
        Pageable pageable = PageRequest.of(page, pageSize);
        return dao.findByAgencyCoachIdForPage(agencyCoachId, pageable);
    }

}
