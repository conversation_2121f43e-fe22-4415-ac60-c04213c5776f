package com.microteam.base.common.util.security;


import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

/**
 * 令牌处理器
 *
 * <AUTHOR>
 * @Date 20150301
 */
public class GenerateToken {

    private static GenerateToken instance = new GenerateToken();

    private long previous;

    private GenerateToken() {
    }

    public static GenerateToken getInstance() {
//        if (instance == null)
//            instance = new GenerateToken();
        return instance;
    }

    //  =GenerateToken.getInstance().generateToken(String msg, boolean timeChange);
    //同步线程，一次只能一个用户使用此线程 ，生成唯一token，因为唯一性要求；
    public synchronized String generateToken(String msg, boolean timeChange) {
        try {

            long current = System.currentTimeMillis();
            if (current == previous) current++;
            previous = current;
            MessageDigest md = MessageDigest.getInstance("MD5");
            md.update(msg.getBytes(StandardCharsets.UTF_8));
            if (timeChange) {
                // byte now[] = (current+"").toString().getBytes();
                byte[] now = (Long.valueOf(current)).toString().getBytes(StandardCharsets.UTF_8);
                md.update(now);
            }
            return toHex(md.digest());
        } catch (NoSuchAlgorithmException e) {
            return null;
        }
    }

    private String toHex(byte[] buffer) {
        StringBuilder sb = new StringBuilder(buffer.length * 2);
        for (byte b : buffer) {
            sb.append(Character.forDigit((b & 240) >> 4, 16));
            sb.append(Character.forDigit(b & 15, 16));
        }

        return sb.toString();
    }

}
