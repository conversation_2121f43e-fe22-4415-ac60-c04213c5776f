//package com.microteam.base.agency.dao.impl;
//
//import com.microteam.base.agency.dao.FootballAgencyClassEnrollmentMemberAlbumDao;
//import com.microteam.base.entity.agency.FootballAgencyClassEnrollmentMemberAlbum;
//import com.microteam.base.common.base.AbstractHibernateDao;
//import org.apache.log4j.Logger;
//import org.hibernate.query.Query;
//import org.springframework.stereotype.Repository;
//
//import java.util.List;
//
//@Repository("footballAgencyClassEnrollmentMemberAlbumDao")
//public class FootballAgencyClassEnrollmentMemberAlbumDaoImpl extends AbstractHibernateDao<FootballAgencyClassEnrollmentMemberAlbum> implements FootballAgencyClassEnrollmentMemberAlbumDao {
//    static Logger logger = Logger.getLogger(FootballAgencyClassEnrollmentMemberAlbumDaoImpl.class.getName());
//
//    public FootballAgencyClassEnrollmentMemberAlbumDaoImpl() {
//        super();
//        setClazz(FootballAgencyClassEnrollmentMemberAlbum.class);
//    }
//
//    @Override
//    public List<FootballAgencyClassEnrollmentMemberAlbum> findByEnrollmentMemberId(Long enrollmentMemberId) {
//        String hql = "from FootballAgencyClassEnrollmentMemberAlbum as enrollmentMemberAlbum " +
//                "where enrollmentMemberAlbum.footballAgencyClassEnrollmentMember.id = (:enrollmentMemberId) " +
//                "and enrollmentMemberAlbum.deleted=false " +
//                "order by enrollmentMemberAlbum.createTime desc ";
//        Query query = getCurrentSession().createQuery(hql)
//                .setParameter("enrollmentMemberId", enrollmentMemberId);
//        return (List<FootballAgencyClassEnrollmentMemberAlbum>) query.list();
//    }
//
//
//}
