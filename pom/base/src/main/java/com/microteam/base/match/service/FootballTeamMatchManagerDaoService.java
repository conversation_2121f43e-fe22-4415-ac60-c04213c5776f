package com.microteam.base.match.service;

import com.microteam.base.entity.match.FootballTeamMatchManager;

import java.util.List;

public interface FootballTeamMatchManagerDaoService {
    //查询用户权限
    FootballTeamMatchManager findByMatchIdAndUserId(Long matchId, Long userId);

    //分页模糊查询机构管理员
    List<FootballTeamMatchManager> findByMatchIdAndSearchForPage(Long matchId, String search, int page, int pageSize);

    //根据Id查询管理员
    FootballTeamMatchManager findById(long id);

    //删除管理员
    void delByMatchIdAndUserId(Long matchId, Long userId);
}
