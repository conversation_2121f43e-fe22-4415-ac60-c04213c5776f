package com.microteam.base.common.util.lesson;


import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONException;
import com.alibaba.fastjson.JSONObject;
import com.microteam.base.common.constant.ConstantUserManage;
import com.microteam.base.common.pojo.PackageTimePojo;
import com.microteam.base.common.pojo.UpLoadLessonDataPojo;
import com.microteam.base.common.pojo.UserHardWareStepPojo;
import com.microteam.base.common.pojo.lesson.LessonData;
import com.microteam.base.common.util.common.TranslatorUtil;
import com.microteam.base.common.util.jdbc.JDBCUtil;
import com.microteam.base.common.util.user.UserDataUtil;
import com.microteam.base.entity.lesson.*;
import com.microteam.base.entity.team.FootballTeam;
import com.microteam.base.entity.team.FootballTeamUser;
import com.microteam.base.entity.user.User;
import com.microteam.base.entity.user.UserHeadimg;

import java.beans.PropertyDescriptor;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.math.BigInteger;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.*;

public class FootballLessonUtils {

    //FootballLessonDatatype批量添加
    public static boolean addLessonDataTypeList(List<FootballLessonDataType> dataTypeList, String table) {
        PreparedStatement pst = null;
        ResultSet rs = null;
        Connection conn = null;
        JDBCUtil.init();
        try {
            conn = JDBCUtil.getConnection();
            conn.setAutoCommit(false);
            //根据类的字段拼接sql
            String sql = sql(FootballLessonDataType.class, table);
            pst = conn.prepareStatement(sql);
            FootballLessonDataType lessonDataType;
            for (int i = 0, length = dataTypeList.size(); i < length; i++) {
                lessonDataType = dataTypeList.get(i);
                prepareParams(pst, lessonDataType, FootballLessonDataType.class);
                pst.addBatch();
                //1000条执行一次
                if (i % 1000 == 0) {
                    //执行
                    pst.executeBatch();
                    //清空
                    pst.clearBatch();
                }
            }
            //执行剩余的sql
            pst.executeBatch();
            //手动提交
            conn.commit();
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        } finally {
            JDBCUtil.free(rs, pst, conn);
        }
    }

    public static boolean addTeamLessonList(List<FootballTeamLesson> teamLessonList, String table) {
        PreparedStatement pst = null;
        ResultSet rs = null;
        Connection conn = null;
        JDBCUtil.init();
        try {
            conn = JDBCUtil.getConnection();
            conn.setAutoCommit(false);
            //根据类的字段拼接sql
            String sql = sql(FootballTeamLesson.class, table);
            pst = conn.prepareStatement(sql);
            FootballTeamLesson teamLesson;
            for (int i = 0, length = teamLessonList.size(); i < length; i++) {
                teamLesson = teamLessonList.get(i);
                prepareParams(pst, teamLesson, FootballTeamLesson.class);
                pst.addBatch();
                //1000条执行一次
                if (i % 1000 == 0) {
                    //执行
                    pst.executeBatch();
                    //清空
                    pst.clearBatch();
                }
            }
            //执行剩余的sql
            pst.executeBatch();
            //手动提交
            conn.commit();
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        } finally {
            JDBCUtil.free(rs, pst, conn);
        }
    }

    public static String sql(Class clz, String table) {
        StringBuffer sql = new StringBuffer("insert into " + table + "(");
        //反射获取fields
        Field[] fields = clz.getDeclaredFields();   //获得字段名

        for (Field field : fields) {
            sql.append(field.getName());
            sql.append(",");
        }
        sql.deleteCharAt(sql.length() - 1);
        sql.append(")");
        sql.append(" values(");
        for (int i = 0, length = fields.length; i < length; i++) {
            sql.append("?,");
        }
        sql.deleteCharAt(sql.length() - 1);
        sql.append(")");
        System.out.println(sql);
        return sql.toString();
    }

    private static void prepareParams(PreparedStatement pst, Object obj, Class clz) throws Exception {
        //反射获取所有字段Fields
        Field[] fields = clz.getDeclaredFields();
        Method method;
        Field field;
        Object tmpObj;
        for (int i = 0; i < fields.length; i++) {
            field = fields[i];
            //根据字段名得到getter方法
            PropertyDescriptor pd = new PropertyDescriptor(field.getName(), clz);
            method = pd.getReadMethod();
            //调用获取的getter方法，得到对应的值
            tmpObj = method.invoke(obj);
            pst.setObject(i + 1, tmpObj);
        }
    }

    public static List<?> getFileldList(String parName, List<?> list) throws Exception {
        List<Object> arra = new ArrayList<>();
        if (list != null && list.size() > 0) {
            parName = "get" + TranslatorUtil.getGetter(parName);
            Method getter = list.get(0).getClass().getMethod(parName);
            for (Object obj : list) {
                Object file = getter.invoke(obj);
                arra.add(file);
            }
        }
        return arra;
    }

    public static List<Long> getLessonIdList(List<FootballLesson> lessonList) {
        List<Long> list = new ArrayList<>();
        if (lessonList != null && lessonList.size() > 0) {
            for (FootballLesson lesson : lessonList) {
                list.add(lesson.getId());
            }
        }
        return list;
    }

    public static Set getLessonIdSet(List<FootballLesson> lessonList) {
        Set<Long> set = new TreeSet<>();
        if (lessonList != null && lessonList.size() > 0) {
            for (FootballLesson lesson : lessonList) {
                set.add(lesson.getId());
            }
        }
        return set;
    }

    public static List<Long> getCourseIdList(List<FootballLessonCourseWare> lessonCourseList) {
        List<Long> list = new ArrayList<>();
        if (lessonCourseList != null && lessonCourseList.size() > 0) {
            for (FootballLessonCourseWare lessonCourse : lessonCourseList) {
                list.add(lessonCourse.getCourseId());
            }
        }
        return list;
    }

    public static List<Long> getDataTypeIdList(List<FootballLessonDataType> lessonCourseList) {
        List<Long> list = new ArrayList<>();
        if (lessonCourseList != null && lessonCourseList.size() > 0) {
            for (FootballLessonDataType lessonCourse : lessonCourseList) {
                list.add(lessonCourse.getDataTypeId());
            }
        }
        return list;
    }

    public static List<Long> getTeamIdList(List<FootballTeamLesson> teamLessonList) {
        List<Long> list = new ArrayList<>();
        if (teamLessonList != null && teamLessonList.size() > 0) {
            for (FootballTeamLesson teamLesson : teamLessonList) {
                list.add(teamLesson.getTeamId());
            }
        }
        return list;
    }

    public static List<Long> getIdList(List<FootballTeam> teamList) {
        List<Long> list = new ArrayList<>();
        if (teamList != null && teamList.size() > 0) {
            for (FootballTeam team : teamList) {
                list.add(team.getId());
            }
        }
        return list;
    }

    public static List<Long> getLessonIdListByteamLessons(List<FootballTeamLesson> teamLessons) {
        List<Long> list = new ArrayList<>();
        if (teamLessons != null && teamLessons.size() > 0) {
            for (FootballTeamLesson teamLesson : teamLessons) {
                list.add(teamLesson.getLessonId());
            }
        }
        return list;
    }

    public static JSONArray arrayToJSONArray(int[] kickBallData) {
        JSONArray jArray = new JSONArray();
        if (kickBallData != null && kickBallData.length > 0) {
            for (int i : kickBallData) {
                jArray.add(i);
            }
        }
        return jArray;
    }

    //封装脚步数据
    public static JSONArray packTheFootData(UpLoadLessonDataPojo pojo) {
        JSONArray jArray = new JSONArray();
        if (pojo != null && !"".equals(pojo.toString())) {
            Map<String, Integer> map = new HashMap<>();

            map.put("exteriorData", pojo.getExteriorData()); //外脚背
            map.put("instepKickingData", pojo.getInstepKickingData()); // 正脚背
            map.put("archData", pojo.getArchData()); //脚弓
            map.put("tiptoeData", pojo.getTiptoeData()); //脚尖
            map.put("heelData", pojo.getHeelData()); //脚后跟
            map.put("soleFootData", pojo.getSoleFootData()); //脚底
            jArray.add(map);
        }
        return jArray;
    }

    //封装上传的硬件跑动数据
    public static Map<String, Object> packCalorieArray(String calorieArray, User user, double stepWidth) throws JSONException {
        Map<String, Object> map = new HashMap<String, Object>();
        int speedMoveCount = 0;//步数
        int moveCount = 0;//次数
        int intervalTime = 0;
        JSONArray moveCalorieArray = new JSONArray();
        if (calorieArray != null && !"".equals(calorieArray)) {
            JSONArray temp = JSONArray.parseArray(calorieArray);
            if (temp != null && temp.size() > 0) {
                for (int i = 1; i < temp.size(); i++) {
                    JSONObject tempObject = temp.getJSONObject(i);
                    int stepCount = tempObject.getInteger("stepCount");
                    speedMoveCount += stepCount;
                    long tempMoveCalarie;
                    int tempIntervalTime = tempObject.getInteger("intervalTime");
                    intervalTime += tempIntervalTime;
                    Map<String, Object> tempMap = new HashMap<>();
                    if (user.getWeight() != null
                            && user.getWeight() != 0) {
                        tempMoveCalarie = (long) (user.getWeight() * (stepCount * stepWidth) / 1000 * 1.036);
                    } else {
                        // 没有体重，默认为60
                        tempMoveCalarie = (long) (60 * (stepCount * stepWidth) / 1000 * 1.036);
                    }
                    tempMap.put("intervalTime", intervalTime);
                    tempMap.put("stepCount", stepCount);
                    tempMap.put("moveCalarie", tempMoveCalarie);
                    tempMap.put("startTime",
                            tempObject.getLong("startTime"));
                    tempMap.put("endTime",
                            tempObject.getLong("endTime"));
                    moveCalorieArray.add(tempMap);
                    if (intervalTime > 2000) {
                        moveCount += 1;
                    }
                }
            }
        }
        map.put("speedMoveCount", speedMoveCount);
        map.put("moveCount", moveCount);
        map.put("intervalTime", intervalTime);
        map.put("moveCalorieArray", moveCalorieArray);
        return map;
    }

    //封装左右脚上传的硬件跑动数据
    public static Map<String, Object> packTwoCalorieArray(String leftCalorieArray, String rightCalorieArray, User user, double stepWidth) throws JSONException {
        Map<String, Object> map = new HashMap<String, Object>();
        int speedMoveCount = 0;//步数
        int moveCount = 0;//次数
        int intervalTime = 0;
        JSONArray moveCalorieArray = new JSONArray();
        if (leftCalorieArray != null && !"".equals(leftCalorieArray)) {
            JSONArray temp = JSONArray.parseArray(leftCalorieArray);
            if (temp != null && temp.size() > 0) {
                for (int i = 0; i < temp.size(); i++) {
                    JSONObject tempObject = temp.getJSONObject(i);
                    int stepCount = tempObject.getInteger("stepCount");
                    speedMoveCount += stepCount;
                    long tempMoveCalarie;
                    int tempIntervalTime = tempObject.getInteger("intervalTime");
                    intervalTime += tempIntervalTime;
                    Map<String, Object> tempMap = new HashMap<>();
                    // 计算卡路里，计算公式：跑步热量=体重（kg）*距离（公里）*1.036
                    if (user.getWeight() != null && user.getWeight() != 0) {
                        tempMoveCalarie = (long) (user.getWeight() * (stepCount * stepWidth) / 1000 * 1.036);
                    } else {
                        // 没有体重，默认为60
                        tempMoveCalarie = (long) (60 * (stepCount * stepWidth) / 1000 * 1.036);
                    }
                    tempMap.put("intervalTime", intervalTime);
                    tempMap.put("stepCount", stepCount);
                    tempMap.put("moveCalarie", tempMoveCalarie);
                    tempMap.put("startTime",
                            tempObject.getLong("startTime"));
                    tempMap.put("endTime",
                            tempObject.getLong("endTime"));
                    tempMap.put("isHaveBall", tempObject.getInteger("isHaveBall"));
                    moveCalorieArray.add(tempMap);
                    if (intervalTime > 2000) {
                        moveCount += 1;
                    }
                }
            }
        }
        if (rightCalorieArray != null && !"".equals(rightCalorieArray)) {
            JSONArray temp = JSONArray.parseArray(rightCalorieArray);
            if (temp != null && temp.size() > 0) {
                for (int i = 0; i < temp.size(); i++) {
                    JSONObject tempObject = temp.getJSONObject(i);
                    int stepCount = tempObject.getInteger("stepCount");
                    speedMoveCount += stepCount;
                    long tempMoveCalarie;
                    int tempIntervalTime = tempObject.getInteger("intervalTime");
                    intervalTime += tempIntervalTime;
                    Map<String, Object> tempMap = new HashMap<>();
                    if (user.getWeight() != null
                            && user.getWeight() != 0) {
                        tempMoveCalarie = (long) (user.getWeight() * (stepCount * stepWidth) / 1000 * 1.036);
                    } else {
                        // 没有体重，默认为60
                        tempMoveCalarie = (long) (60 * (stepCount * stepWidth) / 1000 * 1.036);
                    }
                    tempMap.put("intervalTime", intervalTime);
                    tempMap.put("stepCount", stepCount);
                    tempMap.put("moveCalarie", tempMoveCalarie);
                    tempMap.put("startTime",
                            tempObject.getLong("startTime"));
                    tempMap.put("endTime",
                            tempObject.getLong("endTime"));
                    tempMap.put("isHaveBall", tempObject.getInteger("isHaveBall"));
                    moveCalorieArray.add(tempMap);
                    if (intervalTime > 2000) {
                        moveCount += 1;
                    }
                }
            }
        }
        map.put("speedMoveCount", speedMoveCount);
        map.put("moveCount", moveCount);
        map.put("intervalTime", intervalTime);
        map.put("moveCalorieArray", moveCalorieArray);
        return map;
    }

    public static List<Integer> packKickData(String footData) throws JSONException {
        List<Integer> list = new ArrayList<Integer>();
        if (!"".equals(footData) && footData != null) {
            JSONArray leftArray = JSONArray.parseArray(footData);
            if (leftArray != null && leftArray.size() > 0) {
                for (int i = 0; i < leftArray.size(); i++) {
                    list.add(leftArray.getInteger(i));
                }
            }
        }
        return list;
    }

    //封装卡路里曲线
    public static JSONArray packCalarie(JSONArray highjArray, JSONArray midArray, JSONArray lowjArray, JSONArray norjArray, JSONArray calorieCurveArray) throws JSONException {
        if (highjArray != null && highjArray.size() > 0) {
            for (int j = 0; j < highjArray.size(); j++) {
                calorieCurveArray.add(highjArray.getJSONObject(j));
            }
//            highjArray = FootballTeamUtils.bubbleSortForHardWare(highjArray);
        }

        if (midArray != null && midArray.size() > 0) {
            for (int j = 0; j < midArray.size(); j++) {
                calorieCurveArray.add(midArray.getJSONObject(j));
            }
//            midArray = FootballTeamUtils.bubbleSortForHardWare(midArray);
        }

        if (lowjArray != null && lowjArray.size() > 0) {
            for (int j = 0; j < lowjArray.size(); j++) {
                calorieCurveArray.add(lowjArray.getJSONObject(j));
            }
//            lowjArray = FootballTeamUtils.bubbleSortForHardWare(lowjArray);
        }

        if (norjArray != null && norjArray.size() > 0) {
            for (int j = 0; j < norjArray.size(); j++) {
                calorieCurveArray.add(norjArray.getJSONObject(j));
            }
//            norjArray = FootballTeamUtils.bubbleSortForHardWare(norjArray);
        }
        return calorieCurveArray;
    }

    public static int getCarrayDistance(int carray, int distance) {
        if (carray > distance) {
            carray = 0;
        }
        return carray;
    }

    public static JSONArray packData(List<UserHardWareStepPojo> list) {
        JSONArray jArray = new JSONArray();
        if (list != null && list.size() > 0) {
            for (UserHardWareStepPojo pojo : list) {
                Map<String, Object> map = new HashMap<String, Object>();
                map.put("intervalTime", pojo.getIntervalTime());
                map.put("stepCount", pojo.getStepCount());
                map.put("startTime", pojo.getStartTime());
                map.put("endTime", pojo.getEndTime());
                map.put("isHaveBall", pojo.getIsHaveBall());
                jArray.add(map);
            }
        }
        return jArray;
    }


    public static Long getStepCount(JSONArray jsonarray, Long beginTimeInfo, Long endTimeInfo, User user) throws JSONException {
        int stepCountSumOne = 0;
        Long stepCountSumTime = 0L;
        double stepWidth = 0;
        Long stepNum = 0L;
        if (jsonarray != null && !"".equals(jsonarray.toString())) {
            for (int i = 0; i < jsonarray.size(); i++) {
                JSONObject tempObject = jsonarray.getJSONObject(i);
                Long StartTime = (Long) tempObject.get("startTime");
                Long EndTime = (Long) tempObject.get("endTime");
                int stepCountInfo;
                Long EndIntervalTime;
                int stepCount = (int) tempObject.get("stepCount");
                int intervalTime = (int) tempObject.get("intervalTime");
                if (beginTimeInfo > EndTime) {
                    EndIntervalTime = 0L;
                    stepCountInfo = 0;
                } else if (StartTime >= beginTimeInfo && EndTime <= endTimeInfo) {
                    EndIntervalTime = EndTime - StartTime;
                    stepCountInfo = stepCount;
                } else if (StartTime < beginTimeInfo && EndTime <= endTimeInfo && beginTimeInfo < EndTime) {
                    EndIntervalTime = EndTime - beginTimeInfo;
                    stepCountInfo = (int) ((EndIntervalTime / intervalTime) * stepCount);
                } else if (StartTime < beginTimeInfo && EndTime > endTimeInfo) {
                    EndIntervalTime = EndTime - StartTime;
                    stepCountInfo = (int) ((intervalTime / EndIntervalTime) * stepCount);
                } else if (StartTime >= beginTimeInfo && EndTime > endTimeInfo && StartTime < endTimeInfo) {
                    EndIntervalTime = endTimeInfo - StartTime;
                    if (EndIntervalTime < 0) {
                        EndIntervalTime = StartTime - endTimeInfo;
                    }
                    stepCountInfo = (int) ((EndIntervalTime / intervalTime) * stepCount);
                } else {
                    EndIntervalTime = 0L;
                    stepCountInfo = 0;
                }
                stepCountSumOne += stepCountInfo;
                stepCountSumTime += EndIntervalTime;
            }
            // 计算跑动数据
            stepNum = UserDataUtil.calculateMoveDistance(stepCountSumOne, stepCountSumTime.intValue(), user.getHeight());
        }
        return stepNum;
    }

    public static PackageTimePojo getDistance(JSONArray jsonarray, PackageTimePojo PackageTimePojo, User user, Date date) throws JSONException {
        //切分时间段
        Long KickBallDataTime = date.getTime();
        Long oneMsec = KickBallDataTime + ConstantUserManage.checkTimeMsec * 15;
        Long twoMsec = KickBallDataTime + ConstantUserManage.checkTimeMsec * 30;
        Long threeMsec = KickBallDataTime + ConstantUserManage.checkTimeMsec * 45;
        Long fourMsec = KickBallDataTime + ConstantUserManage.checkTimeMsec * 60;
        Long fiveMsec = KickBallDataTime + ConstantUserManage.checkTimeMsec * 75;
        Long sixMsec = KickBallDataTime + ConstantUserManage.checkTimeMsec * 90;
        Long seveneMsec = KickBallDataTime + ConstantUserManage.checkTimeMsec * 105;
        Long eightMsec = KickBallDataTime + ConstantUserManage.checkTimeMsec * 120;
        PackageTimePojo.setOneMsec(getStepCount(jsonarray, KickBallDataTime, oneMsec, user));
        PackageTimePojo.setTwoMsec(getStepCount(jsonarray, oneMsec, twoMsec, user));
        PackageTimePojo.setThreeMsec(getStepCount(jsonarray, twoMsec, threeMsec, user));
        PackageTimePojo.setFourMsec(getStepCount(jsonarray, threeMsec, fourMsec, user));
        PackageTimePojo.setFiveMsec(getStepCount(jsonarray, fourMsec, fiveMsec, user));
        PackageTimePojo.setSixMsec(getStepCount(jsonarray, fiveMsec, sixMsec, user));
        PackageTimePojo.setSevenMsec(getStepCount(jsonarray, sixMsec, seveneMsec, user));
        PackageTimePojo.setEightMsec(getStepCount(jsonarray, seveneMsec, eightMsec, user));
        return PackageTimePojo;
    }

    public static PackageTimePojo packagePojo(PackageTimePojo PackageTimePojo, PackageTimePojo PackageTime) {
        PackageTimePojo packageTimePo = new PackageTimePojo();
        if (PackageTimePojo != null) {
            packageTimePo.setOneMsec((PackageTime.getOneMsec() == null ? 0 : (Long) PackageTime.getOneMsec()) + (PackageTimePojo.getOneMsec() == null ? 0 : (Long) PackageTimePojo.getOneMsec()));
            packageTimePo.setTwoMsec((PackageTime.getTwoMsec() == null ? 0 : (Long) PackageTime.getTwoMsec()) + (PackageTimePojo.getTwoMsec() == null ? 0 : (Long) PackageTimePojo.getTwoMsec()));
            packageTimePo.setThreeMsec((PackageTime.getThreeMsec() == null ? 0 : (Long) PackageTime.getThreeMsec()) + (PackageTimePojo.getThreeMsec() == null ? 0 : (Long) PackageTimePojo.getThreeMsec()));
            packageTimePo.setFourMsec((PackageTime.getFourMsec() == null ? 0 : (Long) PackageTime.getFourMsec()) + (PackageTimePojo.getFourMsec() == null ? 0 : (Long) PackageTimePojo.getFourMsec()));
            packageTimePo.setFiveMsec((PackageTime.getFiveMsec() == null ? 0 : (Long) PackageTime.getFiveMsec()) + (PackageTimePojo.getFiveMsec() == null ? 0 : (Long) PackageTimePojo.getFiveMsec()));
            packageTimePo.setSixMsec((PackageTime.getSixMsec() == null ? 0 : (Long) PackageTime.getSixMsec()) + (PackageTimePojo.getSixMsec() == null ? 0 : (Long) PackageTimePojo.getSixMsec()));
            packageTimePo.setSevenMsec((PackageTime.getSevenMsec() == null ? 0 : (Long) PackageTime.getSevenMsec()) + (PackageTimePojo.getSevenMsec() == null ? 0 : (Long) PackageTimePojo.getSevenMsec()));
            packageTimePo.setEightMsec((PackageTime.getEightMsec() == null ? 0 : (Long) PackageTime.getEightMsec()) + (PackageTimePojo.getEightMsec() == null ? 0 : (Long) PackageTimePojo.getEightMsec()));
        }
        return packageTimePo;
    }

    public static List<Map<String, Object>> packageDribbling(JSONArray highSpeedMoveDataArray, long kickballTime, List<Integer> t, Short height, List<Map<String, Object>> listMap) throws JSONException {
        if (highSpeedMoveDataArray != null && highSpeedMoveDataArray.size() > 0) {
            for (int j = 0; j < highSpeedMoveDataArray.size(); j++) {
                JSONObject Object;
                Object = highSpeedMoveDataArray.getJSONObject(j);
                Map<String, Object> mapPackage = new HashMap<String, Object>();
                long b1 = kickballTime + t.get(0) * 1000;
                long e1 = kickballTime + t.get(1) * 1000;
                long b2 = Object.getLong("startTime");
                long e2 = Object.getLong("endTime");
                if (b1 < e2 && e1 > b2) {
                    if (b1 <= b2 && e1 >= e2) { // （b1---【b2-----e2】--e1）1包含2
                        //切换之际继续切换时间段，切分9个时间段
                        long distance;
                        distance = UserDataUtil.calculateMoveDistance(((Object.getInteger("stepCount"))), Object.getInteger("intervalTime"), height);
                        mapPackage.put("beginTime", b2);
                        mapPackage.put("endTime", e2);
                        mapPackage.put("distance", distance);
                    } else if (b1 >= b2 && e1 <= e2) { // 【b2---（b1-----e1）--e2】2包含1
                        long distance;
                        int avgStepCount = (int) (Object.getInteger("stepCount") / (Object.getInteger("intervalTime") / 1000) * ((e1 - b1) / 1000));
                        distance = UserDataUtil.calculateMoveDistance(avgStepCount, (int) (e1 - b1), height);
                        mapPackage.put("beginTime", b1);
                        mapPackage.put("endTime", e1);
                        mapPackage.put("distance", distance);
                    } else if (b1 >= b2 && b1 <= e2 && e2 <= e1) { // （b1---【b2---e1）----e2】
                        long distance;
                        int avgStepCount = (int) (Object.getInteger("stepCount") / (Object.getInteger("intervalTime") / 1000) * ((e1 - b2) / 1000));
                        distance = UserDataUtil.calculateMoveDistance(avgStepCount, (int) (e1 - b2), height);
                        mapPackage.put("beginTime", b2);
                        mapPackage.put("endTime", e1);
                        mapPackage.put("distance", distance);
                    } else if (b1 <= b2 && e1 <= e2 && e1 >= b2) { // 【b2---(b1---e2】----e1)
                        long distance;
                        int avgStepCount = (int) (Object.getInteger("stepCount") / (Object.getInteger("intervalTime") / 1000) * ((e2 - b1) / 1000));
                        distance = UserDataUtil.calculateMoveDistance(avgStepCount, (int) (e2 - b1), height);
                        mapPackage.put("beginTime", b1);
                        mapPackage.put("endTime", e2);
                        mapPackage.put("distance", distance);
                    }
                }
                if (mapPackage != null && mapPackage.size() > 0) {
                    listMap.add(mapPackage);
                }
            } // for
        }
        return listMap;
    }

    public static long packageInfo(long beginTime1, long endTime1, long beginTime2, long endTime2, long distance) {
        long sumDistance = 0;
        //切换之际继续切换时间段，切分9个时间段
        if (beginTime1 <= beginTime2 && endTime1 >= endTime2) {      //b3----[b4---e4]----b3
            sumDistance += distance;
        } else if (beginTime1 <= beginTime2 && endTime1 > beginTime2 && endTime2 > endTime1) {      //b3----[b4----e3---e4]
            sumDistance += ((endTime1 - beginTime2) / (endTime2 - beginTime2)) * distance;
        } else if (beginTime1 >= beginTime2 && beginTime1 < endTime1 && endTime1 > endTime2) {      //[b4---b3----e4]----e3
            sumDistance += ((endTime2 - beginTime1) / (endTime2 - beginTime2)) * distance;
        } else if (beginTime1 >= beginTime2 && endTime1 < endTime2) {      //[b4---b3----e3----e4]
            sumDistance += ((endTime1 - beginTime1) / (endTime2 - beginTime2)) * distance;
        } else {      //[b4---e4]----b3----e3||b3----e3----[b4---e4]
            sumDistance += 0;
        }
        return sumDistance;
    }

    public static JSONObject countDribbleSpeed(List<Integer> array, int passInterval,
                                               JSONArray highSpeedMoveDataArray, JSONArray midSpeedMoveDataArray,
                                               JSONArray lowSpeedMoveDataArray, JSONArray normalSpeedMoveDataArray,
                                               long kickballTime, Short height) {
        JSONObject map = new JSONObject();
        try {


            Long tenDistance = 0L;
            Long twentyDistance = 0L;
            Long thirtyDistance = 0L;
            Long fortyDistance = 0L;
            Long fortyFiveDistance = 0L;
            Long fiftyFiveDistance = 0L;
            Long sixtyFiveDistance = 0L;
            Long sevenTyFiveDistance = 0L;
            Long eightyFiveDistance = 0L;
            Long ninetyDistance = 0L;
//            Long HundredDistance = 0L;

            List<List<Integer>> list = new ArrayList<>();
            List<Integer> temp = new ArrayList<>();
            if (array != null && array.size() > 0) {
                for (int i = 0; i < array.size() - 1; i++) {
                    if ((array.get(i + 1) - array.get(i)) <= passInterval) {
                        if (temp == null || temp.size() == 0) {
                            temp.add(0, array.get(i));
                            temp.add(1, array.get(i + 1));
                        } else {
                            temp.remove(1);
                            temp.add(1, array.get(i + 1));
                        }
                        if (i == array.size() - 2) {
                            if (!temp.isEmpty()) {
                                list.add(temp);
                            }
                        }
                    } else {
                        List<Integer> g = temp;
                        if (g.size() > 0 && g != null) {
                            list.add(g);
                            temp = new ArrayList<Integer>();
                        }
                    }
                } // for
            } // if

            List<Map<String, Object>> listHighMap = new ArrayList<>();
            List<Map<String, Object>> listMidMap = new ArrayList<>();
            List<Map<String, Object>> listLowMap = new ArrayList<>();
            List<Map<String, Object>> listNormalMap = new ArrayList<>();
            if (list.size() > 0 && list != null) {
                for (List<Integer> t : list) {
                    listHighMap = packageDribbling(highSpeedMoveDataArray, kickballTime, t, height, listHighMap);
                }
                for (List<Integer> t : list) {
                    listMidMap = packageDribbling(midSpeedMoveDataArray, kickballTime, t, height, listMidMap);
                }
                for (List<Integer> t : list) {
                    listLowMap = packageDribbling(lowSpeedMoveDataArray, kickballTime, t, height, listLowMap);
                }
                for (List<Integer> t : list) {
                    listNormalMap = packageDribbling(normalSpeedMoveDataArray, kickballTime, t, height, listNormalMap);
                }
                Long KickBallDataTime = kickballTime;
                Long tenMsec = KickBallDataTime + ConstantUserManage.checkTimeMsec * 10;
                Long twentyMsec = KickBallDataTime + ConstantUserManage.checkTimeMsec * 20;//二十分钟
                Long thirtyMsec = KickBallDataTime + ConstantUserManage.checkTimeMsec * 30;//三十分钟
                Long fortyMsec = KickBallDataTime + ConstantUserManage.checkTimeMsec * 40;//四十分钟
                Long fortyFiveMsec = KickBallDataTime + ConstantUserManage.checkTimeMsec * 45;//四十五分钟
                Long fiftyFiveMsec = KickBallDataTime + ConstantUserManage.checkTimeMsec * 55;//五十五分钟
                Long sixtyFiveMsec = KickBallDataTime + ConstantUserManage.checkTimeMsec * 65;//六十五分钟
                Long sevenTyFiveMsec = KickBallDataTime + ConstantUserManage.checkTimeMsec * 75;//七十五分钟
                Long eightyFiveMsec = KickBallDataTime + ConstantUserManage.checkTimeMsec * 85;//八十五分钟
                Long ninetyMsec = KickBallDataTime + ConstantUserManage.checkTimeMsec * 90;//九十分钟

//                PackageTimePojo packageTime = new PackageTimePojo();


//                List<Object> listpackageTime = new ArrayList<>();
//                List<Object> listDistance = new ArrayList<>();
//                listpackageTime.add(KickBallDataTime);
//                listpackageTime.add(tenMsec);
//                listpackageTime.add(twentyMsec);
//                listpackageTime.add(thirtyMsec);
//                listpackageTime.add(fortyMsec);
//                listpackageTime.add(fortyFiveMsec);
//                listpackageTime.add(fiftyFiveMsec);
//                listpackageTime.add(sixtyFiveMsec);
//                listpackageTime.add(sevenTyFiveMsec);
//                listpackageTime.add(ninetyMsec);


                for (Map<String, Object> mapPage : listHighMap) {
                    long beginTime = (long) mapPage.get("beginTime");
                    long endTime1 = (long) mapPage.get("endTime");
                    long distance = (long) mapPage.get("distance");
//                    for (int j = 0; j < (listpackageTime.size() - 1); j++) {
//                        listDistance.add(packageInfo((Long) (listpackageTime.get(j)), (Long) (listpackageTime.get(j + 1)), beginTime, endTime1, distance));
//                    }
                    tenDistance += packageInfo(KickBallDataTime, tenMsec, beginTime, endTime1, distance);
                    twentyDistance += packageInfo(tenMsec, twentyMsec, beginTime, endTime1, distance);
                    thirtyDistance += packageInfo(twentyMsec, thirtyMsec, beginTime, endTime1, distance);
                    fortyDistance += packageInfo(thirtyMsec, fortyMsec, beginTime, endTime1, distance);
                    fortyFiveDistance += packageInfo(fortyMsec, fortyFiveMsec, beginTime, endTime1, distance);
                    fiftyFiveDistance += packageInfo(fortyFiveMsec, fiftyFiveMsec, beginTime, endTime1, distance);
                    sixtyFiveDistance += packageInfo(fiftyFiveMsec, sixtyFiveMsec, beginTime, endTime1, distance);
                    sevenTyFiveDistance += packageInfo(sixtyFiveMsec, sevenTyFiveMsec, beginTime, endTime1, distance);
                    eightyFiveDistance += packageInfo(sevenTyFiveMsec, eightyFiveMsec, beginTime, endTime1, distance);
                    ninetyDistance += packageInfo(eightyFiveMsec, ninetyMsec, beginTime, endTime1, distance);
                }
                for (Map<String, Object> stringObjectMap : listMidMap) {
                    long beginTime = (long) stringObjectMap.get("beginTime");
                    long endTime1 = (long) stringObjectMap.get("endTime");
                    long distance = (long) stringObjectMap.get("distance");
                    tenDistance += packageInfo(KickBallDataTime, tenMsec, beginTime, endTime1, distance);
                    twentyDistance += packageInfo(tenMsec, twentyMsec, beginTime, endTime1, distance);
                    thirtyDistance += packageInfo(twentyMsec, thirtyMsec, beginTime, endTime1, distance);
                    fortyDistance += packageInfo(thirtyMsec, fortyMsec, beginTime, endTime1, distance);
                    fortyFiveDistance += packageInfo(fortyMsec, fortyFiveMsec, beginTime, endTime1, distance);
                    fiftyFiveDistance += packageInfo(fortyFiveMsec, fiftyFiveMsec, beginTime, endTime1, distance);
                    sixtyFiveDistance += packageInfo(fiftyFiveMsec, sixtyFiveMsec, beginTime, endTime1, distance);
                    sevenTyFiveDistance += packageInfo(sixtyFiveMsec, sevenTyFiveMsec, beginTime, endTime1, distance);
                    eightyFiveDistance += packageInfo(sevenTyFiveMsec, eightyFiveMsec, beginTime, endTime1, distance);
                    ninetyDistance += packageInfo(eightyFiveMsec, ninetyMsec, beginTime, endTime1, distance);
                }
                for (Map<String, Object> stringObjectMap : listLowMap) {
                    long beginTime = (long) stringObjectMap.get("beginTime");
                    long endTime1 = (long) stringObjectMap.get("endTime");
                    long distance = (long) stringObjectMap.get("distance");
                    tenDistance += packageInfo(KickBallDataTime, tenMsec, beginTime, endTime1, distance);
                    twentyDistance += packageInfo(tenMsec, twentyMsec, beginTime, endTime1, distance);
                    thirtyDistance += packageInfo(twentyMsec, thirtyMsec, beginTime, endTime1, distance);
                    fortyDistance += packageInfo(thirtyMsec, fortyMsec, beginTime, endTime1, distance);
                    fortyFiveDistance += packageInfo(fortyMsec, fortyFiveMsec, beginTime, endTime1, distance);
                    fiftyFiveDistance += packageInfo(fortyFiveMsec, fiftyFiveMsec, beginTime, endTime1, distance);
                    sixtyFiveDistance += packageInfo(fiftyFiveMsec, sixtyFiveMsec, beginTime, endTime1, distance);
                    sevenTyFiveDistance += packageInfo(sixtyFiveMsec, sevenTyFiveMsec, beginTime, endTime1, distance);
                    eightyFiveDistance += packageInfo(sevenTyFiveMsec, eightyFiveMsec, beginTime, endTime1, distance);
                    ninetyDistance += packageInfo(eightyFiveMsec, ninetyMsec, beginTime, endTime1, distance);
                }
                for (Map<String, Object> stringObjectMap : listNormalMap) {
                    long beginTime = (long) stringObjectMap.get("beginTime");
                    long endTime1 = (long) stringObjectMap.get("endTime");
                    long distance = (long) stringObjectMap.get("distance");
                    long distanceSum = 0;
                    tenDistance += packageInfo(KickBallDataTime, tenMsec, beginTime, endTime1, distance);
                    twentyDistance += packageInfo(tenMsec, twentyMsec, beginTime, endTime1, distance);
                    thirtyDistance += packageInfo(twentyMsec, thirtyMsec, beginTime, endTime1, distance);
                    fortyDistance += packageInfo(thirtyMsec, fortyMsec, beginTime, endTime1, distance);
                    fortyFiveDistance += packageInfo(fortyMsec, fortyFiveMsec, beginTime, endTime1, distance);
                    fiftyFiveDistance += packageInfo(fortyFiveMsec, fiftyFiveMsec, beginTime, endTime1, distance);
                    sixtyFiveDistance += packageInfo(fiftyFiveMsec, sixtyFiveMsec, beginTime, endTime1, distance);
                    sevenTyFiveDistance += packageInfo(sixtyFiveMsec, sevenTyFiveMsec, beginTime, endTime1, distance);
                    eightyFiveDistance += packageInfo(sevenTyFiveMsec, eightyFiveMsec, beginTime, endTime1, distance);
                    ninetyDistance += packageInfo(eightyFiveMsec, ninetyMsec, beginTime, endTime1, distance);
                }
            } // if


            map.put("one", tenDistance / 10 * 60 / 1000);         //0-10分钟
            map.put("two", twentyDistance / 10 * 60 / 1000);      //10-20分钟
            map.put("three", thirtyDistance / 10 * 60 / 1000);    //20-30分钟
            map.put("four", fortyDistance / 10 * 60 / 1000);      //30-40分钟
            map.put("five", fortyFiveDistance / 5 * 60 / 1000);   //40-45分钟
            map.put("six", fiftyFiveDistance / 10 * 60 / 1000);   //45-55分钟
            map.put("seven", sixtyFiveDistance / 10 * 60 / 1000); //55-65分钟
            map.put("eight", sevenTyFiveDistance / 10 * 60 / 1000);//65-75分钟
            map.put("nine", eightyFiveDistance / 10 * 60 / 1000);     //75-85分钟
            map.put("ten", ninetyDistance / 5 * 60 / 1000);       //85-90分钟


        } catch (Exception e) {
            e.printStackTrace();
        }
        return map;
    }

    //封装体能数据
    public static Map<String, JSONObject> packPhysicalData(JSONArray highjArray, JSONArray midjArray, JSONArray lowjArray,
                                                           JSONArray norjArray, List<FootballLessonHardwareData> dataList,
                                                           User user, long leftFootStartTime, long rightFootStartTime, List<Integer> array) throws JSONException {
        Map<String, JSONObject> map = new HashMap<>();
        Date leftData = new Date();
        Date rightData = new Date();
        Date date = new Date();
        if (dataList.size() > 0 && dataList.get(0).getKickBallStartTime() != null) {
            leftData = dataList.get(0).getKickBallStartTime();
        }
        if (dataList.size() > 1 && dataList.get(1).getKickBallStartTime() != null) {
            rightData = dataList.get(1).getKickBallStartTime();
        }
        if (leftData != null && !("").equals(leftData.toString())) {
            if (rightData != null && !("").equals(rightData.toString())) {
                if (leftData.getTime() >= rightData.getTime()) {
                    date = leftData;
                } else {
                    date = rightData;
                }
            } else {
                date = leftData;
            }
        } else if (rightData != null && !("").equals(rightData.toString())) {
            date = rightData;
        }
        long kickballTime;
        if (leftFootStartTime >= rightFootStartTime) {
            if (rightFootStartTime > 0) {
                kickballTime = rightFootStartTime;
            } else {
                kickballTime = leftFootStartTime;
            }
        } else {
            if (leftFootStartTime > 0) {
                kickballTime = leftFootStartTime;
            } else {
                kickballTime = rightFootStartTime;
            }
        }
        PackageTimePojo HighDistancePackageTimePojo = new PackageTimePojo();
        HighDistancePackageTimePojo = getDistance(highjArray, HighDistancePackageTimePojo, user, date);
        PackageTimePojo MidDistancePackageTimePojo = new PackageTimePojo();
        MidDistancePackageTimePojo = getDistance(midjArray, MidDistancePackageTimePojo, user, date);
        PackageTimePojo LowDistancePackageTimePojo = new PackageTimePojo();
        LowDistancePackageTimePojo = getDistance(lowjArray, LowDistancePackageTimePojo, user, date);
        PackageTimePojo NormalDistancePackageTimePojo = new PackageTimePojo();
        NormalDistancePackageTimePojo = getDistance(norjArray, NormalDistancePackageTimePojo, user, date);
        JSONObject jsonObjHigh = new JSONObject();
        jsonObjHigh.put("one", HighDistancePackageTimePojo.getOneMsec() == null ? Long.valueOf(0) : (Long) HighDistancePackageTimePojo.getOneMsec());
        jsonObjHigh.put("two", HighDistancePackageTimePojo.getTwoMsec() == null ? Long.valueOf(0) : (Long) HighDistancePackageTimePojo.getTwoMsec());
        jsonObjHigh.put("three", HighDistancePackageTimePojo.getThreeMsec() == null ? Long.valueOf(0) : (Long) HighDistancePackageTimePojo.getThreeMsec());
        jsonObjHigh.put("four", HighDistancePackageTimePojo.getFourMsec() == null ? Long.valueOf(0) : (Long) HighDistancePackageTimePojo.getFourMsec());
        jsonObjHigh.put("five", HighDistancePackageTimePojo.getFiveMsec() == null ? Long.valueOf(0) : (Long) HighDistancePackageTimePojo.getFiveMsec());
        jsonObjHigh.put("six", HighDistancePackageTimePojo.getSixMsec() == null ? Long.valueOf(0) : (Long) HighDistancePackageTimePojo.getSixMsec());
        jsonObjHigh.put("seven", HighDistancePackageTimePojo.getSevenMsec() == null ? Long.valueOf(0) : (Long) HighDistancePackageTimePojo.getSevenMsec());
        jsonObjHigh.put("eight", HighDistancePackageTimePojo.getEightMsec() == null ? Long.valueOf(0) : (Long) HighDistancePackageTimePojo.getEightMsec());
        PackageTimePojo PackageTimePojo;
        PackageTimePojo = packagePojo(HighDistancePackageTimePojo, MidDistancePackageTimePojo);
        PackageTimePojo = packagePojo(LowDistancePackageTimePojo, PackageTimePojo);
        PackageTimePojo = packagePojo(NormalDistancePackageTimePojo, PackageTimePojo);

        JSONObject jsonObj = new JSONObject();
        jsonObj.put("one", PackageTimePojo.getOneMsec());
        jsonObj.put("two", PackageTimePojo.getTwoMsec());
        jsonObj.put("three", PackageTimePojo.getThreeMsec());
        jsonObj.put("four", PackageTimePojo.getFourMsec());
        jsonObj.put("five", PackageTimePojo.getFiveMsec());
        jsonObj.put("six", PackageTimePojo.getSixMsec());
        jsonObj.put("seven", PackageTimePojo.getSevenMsec());
        jsonObj.put("eight", PackageTimePojo.getEightMsec());
        map.put("sprintDistance", jsonObjHigh);
        map.put("playMovementDistance", jsonObj);
        JSONObject speedCurve = countDribbleSpeed(array, 3, highjArray, midjArray, lowjArray, norjArray, kickballTime, user.getHeight());
        map.put("ballSpeed", speedCurve);
        return map;
    }

    public static Map<String, Integer> moveCount(List<FootballLessonHardwareData> list) {
        Map<String, Integer> map = new HashMap<>();
        int highMoveCount = 0;
        int midMoveCount = 0;
        int lowMoveCount = 0;
        int normalMoveCount = 0;

        for (int i = 0; i < list.size(); i++) {
            if (i == 0) {
                highMoveCount += list.get(i).getHighMoveCount();
                midMoveCount += list.get(i).getMidMoveCount();
                lowMoveCount += list.get(i).getLowMoveCount();
                normalMoveCount += list.get(i).getNormalMoveCount();
            }
            map.put("highMoveCount", highMoveCount);
            map.put("midMoveCount", midMoveCount);
            map.put("lowMoveCount", lowMoveCount);
            map.put("normalMoveCount", normalMoveCount);
        }
        return map;
    }

    public static Map<String, Integer> moveCountNew(List<UpLoadLessonDataPojo> list) {
        Map<String, Integer> map = new HashMap<>();
        int highMoveCount = 0;
        int midMoveCount = 0;
        int lowMoveCount = 0;
        int normalMoveCount = 0;

        if (list != null && list.size() > 0) {
            if (list.size() == 1) {
                highMoveCount = list.get(0).getHighMoveCount() + list.get(0).getRightHighMoveCount();
                midMoveCount = list.get(0).getMidMoveCount() + list.get(0).getRightMidMoveCount();
                lowMoveCount = list.get(0).getLowMoveCount() + list.get(0).getRightLowMoveCount();
                normalMoveCount = list.get(0).getNormalMoveCount() + list.get(0).getRightNormalMoveCount();
            } else if (list.size() == 2) {
                highMoveCount = list.get(0).getHighMoveCount() + list.get(0).getRightHighMoveCount() +
                        list.get(1).getHighMoveCount() + list.get(1).getRightHighMoveCount();
                midMoveCount = list.get(0).getMidMoveCount() + list.get(0).getRightMidMoveCount() +
                        list.get(1).getMidMoveCount() + list.get(1).getRightMidMoveCount();
                lowMoveCount = list.get(0).getLowMoveCount() + list.get(0).getRightLowMoveCount() +
                        list.get(1).getLowMoveCount() + list.get(1).getRightLowMoveCount();
                normalMoveCount = list.get(0).getNormalMoveCount() + list.get(0).getRightNormalMoveCount() +
                        list.get(1).getNormalMoveCount() + list.get(1).getRightNormalMoveCount();
            }
        }

        map.put("highMoveCount", highMoveCount);
        map.put("midMoveCount", midMoveCount);
        map.put("lowMoveCount", lowMoveCount);
        map.put("normalMoveCount", normalMoveCount);

        return map;
    }

    //计算课程的计时(返回毫秒数)
    public static Long getLessonTime(FootballLesson lesson) throws JSONException {
        Long lessonTime = 0L;
        if (lesson != null && lesson.getTimeSlot() != null) {
            JSONArray array = JSONArray.parseArray(lesson.getTimeSlot());
            if (array.size() > 0) {
                for (int i = 0; i < array.size(); i++) {
                    JSONObject object = array.getJSONObject(i);
                    Long startTime = object.getLong("startTime");
                    Long endTime = object.getLong("endTime");
                    if (i == array.size() - 1) {                    //最后一个时间段
                        if (object.getLong("endTime") == 0L) {
                            endTime = System.currentTimeMillis();
                        } else {
                            endTime = object.getLong("endTime");
                        }
                        lessonTime += Math.abs(endTime - startTime);
                    } else {
                        if (startTime > 0 && endTime > 0) {
                            lessonTime += Math.abs(endTime - startTime);
                        }
                    }    // if else                                                       
                }// for
            }
        }
        return lessonTime;
    }

    //计算学员课程的计时(返回毫秒数)
    public static Long getEnrollTime(FootballLessonSecondEnroll enroll) throws JSONException {
        Long lessonTime = 0L;
        if (enroll != null && enroll.getTimeSlot() != null) {
            JSONArray array = JSONArray.parseArray(enroll.getTimeSlot());
            if (array.size() > 0) {
                for (int i = 0; i < array.size(); i++) {
                    JSONObject object = array.getJSONObject(i);
                    Long startTime = object.getLong("startTime");
                    Long endTime = object.getLong("endTime");
                    if (i == array.size() - 1) {                    //最后一个时间段
                        if (object.getLong("endTime") == 0L) {
                            endTime = System.currentTimeMillis();
                        } else {
                            endTime = object.getLong("endTime");
                        }
                        lessonTime += Math.abs(endTime - startTime);
                    } else {
                        if (startTime > 0 && endTime > 0) {
                            lessonTime += Math.abs(endTime - startTime);
                        }
                    }    // if else
                }// for
            }
        }
        return lessonTime;
    }

    public static List<FootballTeamUser> uniqTeam(List<FootballTeamUser> teamUserList) {
        List<FootballTeamUser> list = new ArrayList<>();
        Map<Long, FootballTeamUser> map = new LinkedHashMap<>();
        if (teamUserList != null && teamUserList.size() > 0) {
            for (FootballTeamUser teamUser : teamUserList) {
                if (teamUser.getFootballTeamMemberRole().getId() == 2) {
                    if (!map.containsKey(teamUser.getUser().getId())) {
                        map.put(teamUser.getUser().getId(), teamUser);
                    }
                } else if (teamUser.getFootballTeamMemberRole().getId() == 5) {
                    map.put(teamUser.getUser().getId(), teamUser);
                }
            }

            for (Map.Entry<Long, FootballTeamUser> entry : map.entrySet()) {
                list.add(entry.getValue());
            }
        }
        return list;
    }

    public static List<User> pickUsers(List<FootballLessonSecondEnroll> list) {
        List<User> users = new ArrayList<>();
        if (list != null && list.size() > 0) {
            for (FootballLessonSecondEnroll enroll : list) {
                users.add(enroll.getUser());
            }
        }
        return users;
    }

    public static List<FootballTeam> pickTeams(List<FootballLessonSecondEnroll> list) {
        List<FootballTeam> teams = new ArrayList<>();
        if (list != null && list.size() > 0) {
            for (FootballLessonSecondEnroll enroll : list) {
                teams.add(enroll.getTeam());
            }
        }
        return teams;
    }

    public static JSONArray pickLessonNode(JSONArray array, List<FootballLessonSecondEnroll> secondEnrollList,
                                           Map<String, FootballLessonTakeNodes> nodeMap, Map<Long, String> imgMap, Map<String, Long> poloMap) {
        for (FootballLessonSecondEnroll enroll : secondEnrollList) {
            Map<String, Object> map = new HashMap<>();
            Long userId = enroll.getUser().getId();
            Long teamId = enroll.getTeam().getId();

            map.put("userId", enroll.getUser().getId());
            map.put("nickName", enroll.getUser().getNickName());
            map.put("teamId", enroll.getTeam().getId());
            String img = "";
            if (imgMap.containsKey(userId)) {
                img = imgMap.get(userId);
            }
            map.put("userImg", img);
            Long poloNumber = null;
            if (poloMap.containsKey(userId + "_" + teamId)) {
                poloNumber = poloMap.get(userId + "_" + teamId);
            }
            map.put("poloNumber", poloNumber);
            map = pickNode(map, nodeMap, enroll.getUser(), enroll.getTeam());
            array.add(map);
        }
        return array;
    }

    public static Map<String, Object> pickNode(Map<String, Object> map, Map<String, FootballLessonTakeNodes> nodeMap, User user, FootballTeam team) {
        if (nodeMap.containsKey(user.getId() + "_" + team.getId())) {
            FootballLessonTakeNodes node = nodeMap.get(user.getId() + "_" + team.getId());
            map.put("goalsfor", node.getGoalsfor() == null ? Long.valueOf(0) : node.getGoalsfor());
            map.put("assist", node.getAssist() == null ? Long.valueOf(0) : node.getAssist());
            map.put("shoot", node.getShoot() == null ? Long.valueOf(0) : node.getShoot());
            map.put("shootAside", node.getShootAside() == null ? Long.valueOf(0) : node.getShootAside());
            map.put("waveShot", node.getShootAside() == null ? Long.valueOf(0) : node.getShootAside());
            map.put("ownGoal", node.getOwnGoal() == null ? Long.valueOf(0) : node.getOwnGoal());
            map.put("menace", node.getMenace() == null ? Long.valueOf(0) : node.getMenace());
            map.put("holdUp", node.getHoldUp() == null ? Long.valueOf(0) : node.getHoldUp());
            map.put("save", node.getSave() == null ? Long.valueOf(0) : node.getSave());
            map.put("excel", node.getExcel() == null ? Long.valueOf(0) : node.getExcel());
            map.put("foul", node.getFoul() == null ? Long.valueOf(0) : node.getFoul());
            map.put("offSide", node.getOffSide() == null ? Long.valueOf(0) : node.getOffSide());
            map.put("yellowCard", node.getYellowCard() == null ? Long.valueOf(0) : node.getYellowCard());
            map.put("redCard", node.getRedCard() == null ? Long.valueOf(0) : node.getRedCard());
            map.put("freeKick", node.getFreeKick() == null ? Long.valueOf(0) : node.getFreeKick());
            map.put("penaltyKick", node.getPenaltyKick() == null ? Long.valueOf(0) : node.getPenaltyKick());
            map.put("corner", node.getCorner() == null ? Long.valueOf(0) : node.getCorner());
            map.put("roof", node.getRoof() == null ? Long.valueOf(0) : node.getRoof());
            map.put("head", node.getHead() == null ? Long.valueOf(0) : node.getHead());
            map.put("stopFault", node.getStopFault() == null ? Long.valueOf(0) : node.getStopFault());
            map.put("passFault", node.getPassFault() == null ? Long.valueOf(0) : node.getPassFault());
            map.put("defendFault", node.getDefendFault() == null ? Long.valueOf(0) : node.getDefendFault());
            map.put("kickEmpty", node.getKickEmpty() == null ? Long.valueOf(0) : node.getKickEmpty());
        } else {
            map.put("goalsfor", 0L);
            map.put("assist", 0L);
            map.put("shoot", 0L);
            map.put("shootAside", 0L);
            map.put("waveShot", 0L);
            map.put("ownGoal", 0L);
            map.put("menace", 0L);
            map.put("holdUp", 0L);
            map.put("save", 0L);
            map.put("excel", 0L);
            map.put("foul", 0L);
            map.put("offSide", 0L);
            map.put("yellowCard", 0L);
            map.put("redCard", 0L);
            map.put("freeKick", 0L);
            map.put("penaltyKick", 0L);
            map.put("corner", 0L);
            map.put("roof", 0L);
            map.put("head", 0L);
            map.put("stopFault", 0L);
            map.put("passFault", 0L);
            map.put("defendFault", 0L);
            map.put("kickEmpty", 0L);
        }
        return map;
    }

    public static Map<String, Object> pickDataOfLesson(LessonData data, LessonData avgData, Map<String, Object> map) throws Exception {
        map = getSumUpOfLesson(data, avgData, map, "wholeMoveDistance");
        map = getSumUpOfLesson(data, avgData, map, "passBallCounts");
        map = getSumUpOfLesson(data, avgData, map, "passRate");
        map = getSumUpOfLesson(data, avgData, map, "carryDistance");
        map = getSumUpOfLesson(data, avgData, map, "maxSprintSpeed");
        return map;
    }

    private static Map<String, Object> getSumUpOfLesson(LessonData data, LessonData avgData, Map<String, Object> map, String propertyName) throws Exception {
        String getName = "get" + TranslatorUtil.getGetter(propertyName);
        Method dataGetter = data.getClass().getMethod(getName);
        Method avgGetter = avgData.getClass().getMethod(getName);
        double value = 0;
        double avg = 0;
        int compare;
        Map<String, Object> tempMap = new HashMap<>();
        if (propertyName.equals("passRate")) {

            int passCountSum = data.getPassBallCounts();
            int passEorrorSum = data.getPassBallError();

            int passCountAllSum = avgData.getPassBallCounts();
            int passEorrorAllSum = avgData.getPassBallError();
            if (passCountSum + passEorrorSum > 0) {
                value = (double) passCountSum / ((double) passCountSum + (double) passEorrorSum);
            }
            if (passCountAllSum + passEorrorAllSum > 0) {
                avg = (double) passCountAllSum / ((double) passCountAllSum + (double) passEorrorAllSum);
            }

        } else {
            if (dataGetter.invoke(data) instanceof Long) {
                value = ((Long) dataGetter.invoke(data)).doubleValue();
                avg = ((Long) avgGetter.invoke(avgData)).doubleValue();
            } else if (dataGetter.invoke(data) instanceof Integer) {
                value = ((Integer) dataGetter.invoke(data)).doubleValue();
                avg = ((Integer) avgGetter.invoke(avgData)).doubleValue();
            }
        }
        if (value > avg) {
            compare = 2;    //上升
        } else if (value < avg) {
            compare = 0;    //下降
        } else {
            compare = 1;    //相等
        }

        tempMap.put("data", (Math.round(value * 10.0) / 10.0));
        tempMap.put("avg", (Math.round(avg * 10.0) / 10.0));
        tempMap.put("compare", compare);
        map.put(propertyName, tempMap);
        return map;
    }

    public static List<Long> getEnrollUserId(List<FootballLessonSecondEnroll> list) {
        List<Long> userIds = new ArrayList<>();
        if (list != null && list.size() > 0) {
            for (FootballLessonSecondEnroll enroll : list) {
                userIds.add(enroll.getUser().getId());
            }
        }
        return userIds;
    }

    public static Map<Long, String> getNikeMap(List<FootballLessonSecondEnroll> list) {
        Map<Long, String> nikeMap = new HashMap<>();
        if (list != null && list.size() > 0) {
            for (FootballLessonSecondEnroll enroll : list) {
                nikeMap.put(enroll.getUser().getId(), enroll.getUser().getNickName());
            }
        }
        return nikeMap;
    }

    public static Map<Long, String> getImgMap(List<UserHeadimg> list) {
        Map<Long, String> imgMap = new HashMap<>();
        if (list != null && list.size() > 0) {
            for (UserHeadimg img : list) {
                imgMap.put(img.getUser().getId(), img.getHeadImgNetUrl());
            }
        }
        return imgMap;
    }

    public static JSONArray pickTop(List<FootballLessonStatisticsPlayer> teamPlayers, List<FootballLessonTakeNodes> nodes, Map<Long, String> imgMap, Map<Long, String> nikeMap) throws Exception {
        JSONArray array = new JSONArray();
        array = getKeyPlayerOfStatic("passBallCounts", teamPlayers, imgMap, nikeMap, array);
        array = getKeyPlayerOfStatic("wholeMoveDistance", teamPlayers, imgMap, nikeMap, array);

        array = getKeyPlayerOfNode("goalsfor", nodes, imgMap, nikeMap, array);
        array = getKeyPlayerOfNode("shoot", nodes, imgMap, nikeMap, array);
        array = getKeyPlayerOfNode("assist", nodes, imgMap, nikeMap, array);
        array = getKeyPlayerOfNode("corner", nodes, imgMap, nikeMap, array);
        array = getKeyPlayerOfNode("freeKick", nodes, imgMap, nikeMap, array);
        array = getKeyPlayerOfNode("excel", nodes, imgMap, nikeMap, array);
        array = getKeyPlayerOfNode("menace", nodes, imgMap, nikeMap, array);
        array = getKeyPlayerOfNode("penaltyKick", nodes, imgMap, nikeMap, array);
        array = getKeyPlayerOfNode("head", nodes, imgMap, nikeMap, array);
        array = getKeyPlayerOfNode("save", nodes, imgMap, nikeMap, array);
        array = getKeyPlayerOfNode("holdUp", nodes, imgMap, nikeMap, array);
        array = getKeyPlayerOfNode("shootAside", nodes, imgMap, nikeMap, array);
        array = getKeyPlayerOfNode("foul", nodes, imgMap, nikeMap, array);
        array = getKeyPlayerOfNode("offSide", nodes, imgMap, nikeMap, array);
        array = getKeyPlayerOfNode("redCard", nodes, imgMap, nikeMap, array);
        array = getKeyPlayerOfNode("yellowCard", nodes, imgMap, nikeMap, array);
        array = getKeyPlayerOfNode("ownGoal", nodes, imgMap, nikeMap, array);
        array = getKeyPlayerOfNode("waveShot", nodes, imgMap, nikeMap, array);
        array = getKeyPlayerOfNode("roof", nodes, imgMap, nikeMap, array);
        array = getKeyPlayerOfNode("kickEmpty", nodes, imgMap, nikeMap, array);
        array = getKeyPlayerOfNode("defendFault", nodes, imgMap, nikeMap, array);
        array = getKeyPlayerOfNode("stopFault", nodes, imgMap, nikeMap, array);
        return array;
    }

    private static JSONArray getKeyPlayerOfStatic(String name, List<FootballLessonStatisticsPlayer> teamPlayers, Map<Long, String> imgMap, Map<Long, String> nikeMap, JSONArray array) throws Exception {
        String getName = "get" + TranslatorUtil.getGetter(name);

        int data = 0;
        Long userId = 0L;
        if (teamPlayers != null && teamPlayers.size() > 0) {
            for (FootballLessonStatisticsPlayer player : teamPlayers) {
                Method getter = player.getClass().getMethod(getName);
                int tempData = 0;
                if (getter.invoke(player) instanceof Long) {
                    tempData = ((Long) getter.invoke(player)).intValue();
                } else if (getter.invoke(player) instanceof Integer) {
                    tempData = ((Integer) getter.invoke(player)).intValue();
                }
                if (tempData > data) {
                    data = tempData;
                    userId = player.getUserId();
                }
            }
        }

        if (data > 0) {
            Map<String, Object> tempMap = new HashMap<>();
            tempMap.put(name, data);
            String img = "";
            if (imgMap.containsKey(userId)) {
                img = imgMap.get(userId);
            }
            String nikeName = "";
            if (nikeMap.containsKey(userId)) {
                nikeName = nikeMap.get(userId);
            }
            tempMap.put("img", img);
            tempMap.put("nikeName", nikeName);
            tempMap.put("data", data);
            tempMap.put("dataType", name);
            array.add(tempMap);
        }
        return array;
    }

    private static JSONArray getKeyPlayerOfNode(String name, List<FootballLessonTakeNodes> nodes, Map<Long, String> imgMap, Map<Long, String> nikeMap, JSONArray array) throws Exception {
        String getName = "get" + TranslatorUtil.getGetter(name);

        int data = 0;
        Long userId = 0L;
        if (nodes != null && nodes.size() > 0) {
            for (FootballLessonTakeNodes node : nodes) {
                Method getter = node.getClass().getMethod(getName);
                int tempData;
                tempData = ((Long) getter.invoke(node)).intValue();

                if (tempData > data) {
                    data = tempData;
                    userId = node.getUser().getId();
                }
            }
        }

        if (data > 0) {
            Map<String, Object> tempMap = new HashMap<>();
            tempMap.put(name, data);
            String img = "";
            if (imgMap.containsKey(userId)) {
                img = imgMap.get(userId);
            }
            String nikeName = "";
            if (nikeMap.containsKey(userId)) {
                nikeName = nikeMap.get(userId);
            }
            tempMap.put("img", img);
            tempMap.put("nikeName", nikeName);
            tempMap.put("data", data);
            tempMap.put("dataType", name);
            array.add(tempMap);
        }
        return array;
    }

    public static List<Long> getToolIds(List<FootballLessonTrainingTools> tools) {
        List<Long> ids = new ArrayList<>();
        if (tools != null && tools.size() > 0) {
            for (FootballLessonTrainingTools tool : tools) {
                ids.add(tool.getToolId());
            }
        }
        return ids;
    }

    public static List<Long> getHardwareIds(List<FootballLessonHardwareData> dataList) {
        List<Long> list = new ArrayList<>();
        if (dataList != null && dataList.size() > 0) {
            for (FootballLessonHardwareData data : dataList) {
                list.add(data.getHardwareId());
            }
        }
        return list;
    }

    public static List<Long> getLesonIdList(List<BigInteger> bList) {
        List<Long> list = new ArrayList<>();
        if (bList != null && bList.size() > 0) {
            for (BigInteger b : bList) {
                list.add(b.longValue());
            }
        }
        return list;
    }
}
