package com.microteam.base.agency.service;

import com.microteam.base.entity.agency.AgencyTeam;

import java.util.List;

public interface AgencyTeamDaoService {

    Integer findContByAgencyId(Long agencyId);

    List<AgencyTeam> findByAgencyId(Long agencyId);

    List<AgencyTeam> findByAgencyIdForPage(Long agencyId, int page, int pageSize);

    AgencyTeam findByTeamId(Long teamId);

    AgencyTeam save(AgencyTeam agencyTeam);

    List<AgencyTeam> findByTeamUserId(Long userId);

    int deleteByTeamId(Long teamId);


}
