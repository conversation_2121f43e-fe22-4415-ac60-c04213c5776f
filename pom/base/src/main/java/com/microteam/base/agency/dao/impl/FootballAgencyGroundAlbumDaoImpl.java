//package com.microteam.base.agency.dao.impl;
//
//import com.microteam.base.agency.dao.FootballAgencyGroundAlbumDao;
//import com.microteam.base.entity.agency.FootballAgencyGround;
//import com.microteam.base.entity.agency.FootballAgencyGroundAlbum;
//import com.microteam.base.common.base.AbstractHibernateDao;
//import org.apache.log4j.Logger;
//import org.hibernate.query.Query;
//import org.springframework.stereotype.Repository;
//
//import java.util.List;
//
//@Repository("footballAgencyGroundAlbumDao")
//public class FootballAgencyGroundAlbumDaoImpl extends
//        AbstractHibernateDao<FootballAgencyGroundAlbum> implements FootballAgencyGroundAlbumDao {
//    static Logger logger = Logger.getLogger(FootballAgencyGroundAlbumDaoImpl.class.getName());
//
//    public FootballAgencyGroundAlbumDaoImpl() {
//        super();
//
//        setClazz(FootballAgencyGroundAlbum.class);
//    }
//
//    //查询机构场地的相册
//    @SuppressWarnings("unchecked")
//    @Override
//    public List<FootballAgencyGroundAlbum> findGroundAlbumByAgency(
//            FootballAgencyGround agencyGround) {
//
//        String hql = "from FootballAgencyGroundAlbum as agencyGroundAlbum where agencyGroundAlbum.footballAgencyGround=? and agencyGroundAlbum.deleted=false order by agencyGroundAlbum.createTime desc ";
//        Query query = getCurrentSession().createQuery(hql).setParameter(0, agencyGround);
//        List<FootballAgencyGroundAlbum> list = query.list();
//        return list;
//    }
//
//}
