package com.microteam.base.common.util.imServerManage.pojo;



import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

/**
*  IM response body 里面的data pojo类；
* 
* @param 
* @param 
* @return
*/
@JsonIgnoreProperties(ignoreUnknown = true)
public class ImResponseDatas implements java.io.Serializable{

	

		/**
		 * 
		 */
		
		private String groupid;
		
		
		
		public ImResponseDatas() {
		}

		public ImResponseDatas(String groupid) {
			
			this.groupid = groupid;
			
			
		}

		
		public String getGroupid() {
			return this.groupid;
		}

		public void setGroupid(String groupid) {
			this.groupid = groupid;
		}


		
		
}