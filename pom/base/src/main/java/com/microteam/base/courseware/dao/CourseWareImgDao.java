package com.microteam.base.courseware.dao;

import com.microteam.base.entity.courseware.CourseWareImg;
import com.microteam.base.entity.lesson.LessonImg;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

public interface CourseWareImgDao extends JpaRepository<CourseWareImg, Long>, JpaSpecificationExecutor<CourseWareImg> {

    @Query("from CourseWareImg as coursewareImg " +
            "where coursewareImg.coursewareId = (:coursewareId) " +
            "and coursewareImg.deleted = false ")
    List<CourseWareImg> findByCourseWareId(@Param("coursewareId") Long coursewareId);
}
