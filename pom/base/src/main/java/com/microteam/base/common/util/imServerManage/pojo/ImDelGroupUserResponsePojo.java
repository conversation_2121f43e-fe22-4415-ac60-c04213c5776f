package com.microteam.base.common.util.imServerManage.pojo;

import java.util.Date;
import java.util.List;
import java.util.Map;

public class ImDelGroupUserResponsePojo implements java.io.Serializable{
	/**
	 * 
	 */
	private String action;
	private String application;
	private String uri;            
	private List<Map<String,Object>> entities; //?????entities
	private Map<String,Object> data; 
	private Date timestamp; //注意时间类型；java.util.Date
	private Long duration;
	private String organization;
	private String applicationName;
	public ImDelGroupUserResponsePojo() {
	}
	public ImDelGroupUserResponsePojo(String action, String application,
			String uri, List<Map<String, Object>> entities,
			Map<String, Object> data, Date timestamp, Long duration,
			String organization, String applicationName) {
		super();
		this.action = action;
		this.application = application;
		this.uri = uri;
		this.entities = entities;
		this.data = data;
		this.timestamp = timestamp;
		this.duration = duration;
		this.organization = organization;
		this.applicationName = applicationName;
	}
	public String getAction() {
		return action;
	}
	public void setAction(String action) {
		this.action = action;
	}
	public String getApplication() {
		return application;
	}
	public void setApplication(String application) {
		this.application = application;
	}
	public String getUri() {
		return uri;
	}
	public void setUri(String uri) {
		this.uri = uri;
	}
	public List<Map<String, Object>> getEntities() {
		return entities;
	}
	public void setEntities(List<Map<String, Object>> entities) {
		this.entities = entities;
	}
	public Map<String, Object> getData() {
		return data;
	}
	public void setData(Map<String, Object> data) {
		this.data = data;
	}
	public Date getTimestamp() {
		return timestamp;
	}
	public void setTimestamp(Date timestamp) {
		this.timestamp = timestamp;
	}
	public Long getDuration() {
		return duration;
	}
	public void setDuration(Long duration) {
		this.duration = duration;
	}
	public String getOrganization() {
		return organization;
	}
	public void setOrganization(String organization) {
		this.organization = organization;
	}
	public String getApplicationName() {
		return applicationName;
	}
	public void setApplicationName(String applicationName) {
		this.applicationName = applicationName;
	}
	
}
