package com.microteam.base.agency.service.impl;

import com.microteam.base.agency.dao.FootballAgencyClassPublicationAlbumDao;
import com.microteam.base.entity.agency.FootballAgencyClassPublicationAlbum;
import com.microteam.base.agency.service.FootballAgencyClassPublicationAlbumDaoService;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service("footballAgencyClassPublicationAlbumDaoService")
public class FootballAgencyClassPublicationAlbumDaoServiceImpl implements FootballAgencyClassPublicationAlbumDaoService {
    static Logger logger = Logger.getLogger(FootballAgencyClassPublicationAlbumDaoServiceImpl.class.getName());
    @Autowired
    private FootballAgencyClassPublicationAlbumDao dao;

    @Override
    public List<FootballAgencyClassPublicationAlbum> findByPublicationId(Long publicationId) {
        return dao.findByPublicationId(publicationId);
    }


}
