//package com.microteam.base.common.base;
//
//
//import com.google.common.base.Preconditions;
//import org.hibernate.Session;
//import org.hibernate.SessionFactory;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.transaction.annotation.Transactional;
//
//import javax.persistence.EntityManagerFactory;
//import java.io.Serializable;
//import java.lang.reflect.InvocationTargetException;
//import java.lang.reflect.Method;
//import java.sql.Timestamp;
//import java.util.Date;
//import java.util.List;
//
//@Transactional
//public abstract class AbstractHibernateDao<T extends Serializable> implements IDaoOperations<T> {
//
//    private Class<T> clazz;
//
////    @Autowired
////    private SessionFactory sessionFactory;
//
//    @Autowired
//    private EntityManagerFactory entityManagerFactory;
//
//    protected final void setClazz(final Class<T> clazzToSet) {
//        this.clazz = Preconditions.checkNotNull(clazzToSet);
//    }
//
//    protected final Session getCurrentSession() {
//        SessionFactory sessionFactory = entityManagerFactory.unwrap(SessionFactory.class);
//        if (null == sessionFactory.getCurrentSession()) {
//            return sessionFactory.openSession();
//        }
//        return sessionFactory.getCurrentSession();
//    }
//
//    @Override
//    public final T findOne(final long id) {
//        return getCurrentSession().get(clazz, id);
//    }
//
//    @Override
//    public final List<T> findAll() {
//        return getCurrentSession().createQuery("from " + clazz.getName()).list();
//    }
//
//    @Override
//    public final void create(final T entity) {
//        Preconditions.checkNotNull(entity);
//        // getCurrentSession().persist(entity);
//        getCurrentSession().saveOrUpdate(entity);
//    }
//
//    @Override
//    public T save(final T entity) {
//        try {
//            Method setUpdateTime = entity.getClass().getMethod("setUpdateTime", Timestamp.class);
//            if (setUpdateTime != null) {
//                setUpdateTime.invoke(entity, new Timestamp(System.currentTimeMillis()));
//            }
//        } catch (NoSuchMethodException e) {
//        } catch (InvocationTargetException | IllegalAccessException e) {
//            e.printStackTrace();
//        }
//        try {
//            Method setUpdateTimeDate = entity.getClass().getMethod("setUpdateTime", Date.class);
//            if (setUpdateTimeDate != null) {
//                setUpdateTimeDate.invoke(entity, new Date());
//            }
//        } catch (NoSuchMethodException e) {
//
//        } catch (InvocationTargetException | IllegalAccessException e) {
//            e.printStackTrace();
//        }
//        Preconditions.checkNotNull(entity);
//        Session session = getCurrentSession();
//        /*session.saveOrUpdate(entity);*/
//        session.merge(entity);
////        session.persist(entity);
////        session.save(entity);
//        return entity;
//    }
//
//
//    @Override
//    public final T update(final T entity) {
//
//        Preconditions.checkNotNull(entity);
//        /*getCurrentSession().merge(entity);*/
//        getCurrentSession().update(entity);
//        return entity;
//        //return (T)getCurrentSession().merge(entity);
//    }
//
//    @Override
//    public final void delete(final T entity) {
//        //Preconditions.checkNotNull(entity);
//        if (entity != null) {
//            getCurrentSession().delete(entity);
//        }
//
//    }
//
//    @Override
//    public final void deleteById(final long entityId) {
//        final T entity = findOne(entityId);
//        if (entity != null) {
//            Preconditions.checkState(entity != null);
//            delete(entity);
//        }
//
//    }
//
//}