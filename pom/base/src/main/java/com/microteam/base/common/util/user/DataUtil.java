package com.microteam.base.common.util.user;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONException;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.microteam.base.common.constant.ConstantUserManage;
import com.microteam.base.common.constant.FootballContants;
import com.microteam.base.common.constant.TeamMemberRoleEnum;
import com.microteam.base.common.pojo.DataRankingPojo;
import com.microteam.base.common.pojo.MtJavaServerResponseBodyPojo;
import com.microteam.base.common.pojo.MtJavaServerResponseBodyResultPojo;
import com.microteam.base.common.pojo.RankingPojo;
import com.microteam.base.common.pojo.team.CurvePojo;
import com.microteam.base.common.pojo.team.PassBallOfUserPojo;
import com.microteam.base.common.pojo.team.UserPojo;
import com.microteam.base.common.util.common.CommonUtil;
import com.microteam.base.common.util.common.TranslatorUtil;
import com.microteam.base.common.util.common.ValueComparator;
import com.microteam.base.common.util.team.FootballTeamUtils;
import com.microteam.base.entity.match.FootballTeamMatch;
import com.microteam.base.entity.team.*;
import com.microteam.base.entity.user.*;
import com.microteam.base.match.service.FootballTeamMatchDaoService;
import com.microteam.base.team.service.FootballTeamDaoService;
import com.microteam.base.team.service.FootballTeamGameDaoService;
import com.microteam.base.team.service.FootballTeamUserDaoService;
import com.microteam.base.team.service.PoloShirtDaoService;
import com.microteam.base.user.service.TransparentMessageListDaoService;
import com.microteam.base.user.service.UserDaoService;
import com.microteam.base.user.service.UserHeadimgDaoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.util.*;
import java.util.stream.Collectors;

@Component
public class DataUtil {

    @Autowired
    private FootballTeamDaoService footballTeamDaoService;
    //    @Autowired
//    private FootballTeamMemberCountDaoService teamsMemberCountDaoService;
    @Autowired
    private FootballTeamGameDaoService footballTeamsGameDaoService;
    @Autowired
    private UserHeadimgDaoService userHeadimgDaoService;
    @Autowired
    private TransparentMessageListDaoService transparentMessageListDaoService;
    @Autowired
    private FootballTeamUserDaoService footballTeamUserDaoService;
    @Autowired
    private FootballTeamMatchDaoService footballTeamMatchDaoService;
    @Autowired
    private PoloShirtDaoService poloShirtDaoService;
    @Autowired
    private UserDaoService userDaoService;
    @Autowired
    private UserHardwareDataUtil userHardwareDataUtil;

    // 创建空的json数组对象
//    public static FootballTeamMemberCount setArrayEmptyInTeamsMemberCount(
//            FootballTeamMemberCount teamsMemberCount) throws JSONException {
//        JSONArray jArray = new JSONArray();
//
//        JSONObject jObject = new JSONObject();
//        jObject.put("playerArray", jArray);
//        teamsMemberCount.setPlayerArray(jObject.toString());
//
//        jObject = new JSONObject();
//        jObject.put("cheerArray", jArray);
//        teamsMemberCount.setCheerArray(jObject.toString());
//
//        jObject = new JSONObject();
//        jObject.put("manageArray", jArray);
//        teamsMemberCount.setManageArray(jObject.toString());
//
//        jObject = new JSONObject();
//        jObject.put("leaderArray", jArray);
//        teamsMemberCount.setLeaderArray(jObject.toString());
//
//        jObject = new JSONObject();
//        jObject.put("coachArray", jArray);
//        teamsMemberCount.setCoachArray(jObject.toString());
//
//        jObject = new JSONObject();
//        jObject.put("fansArray", jArray);
//        teamsMemberCount.setFansArray(jObject.toString());
//        return teamsMemberCount;
//    }

//    public static FootballTeamMemberCount setOneArrayInTeamsMemberCount(User user, FootballTeamMemberCount teamsMemberCount, String type, String[] members) throws JSONException {
//        // 存储对象
//        JSONArray jArray;
//        JSONObject jObject;
//        String arrayStr;
//        switch (type) {
//            case "teamManage":
//                arrayStr = teamsMemberCount.getManageArray();
//                // 创建者默认为管理员
//                type = "manageArray";
//                jArray = setArrayForTeamsMemberCount(arrayStr, type, members, user);
//                jObject = new JSONObject();
//                jObject.put(type, jArray);
//                teamsMemberCount.setManageArray(jObject.toString());
//                // 删除队员中的用户
//                jArray = delUserForTeamsMemberCount("playerArray", user, teamsMemberCount);
//                jObject = new JSONObject();
//                jObject.put("playerArray", jArray);
//                teamsMemberCount.setPlayerArray(jObject.toString());
//                // 删除拉拉队中的用户
//                jArray = delUserForTeamsMemberCount("cheerArray", user, teamsMemberCount);
//                jObject = new JSONObject();
//                jObject.put("cheerArray", jArray);
//                teamsMemberCount.setCheerArray(jObject.toString());
//                // 删除队长中的用户
//                jArray = delUserForTeamsMemberCount("leaderArray", user, teamsMemberCount);
//                jObject = new JSONObject();
//                jObject.put("leaderArray", jArray);
//                teamsMemberCount.setLeaderArray(jObject.toString());
//                // 删除教练中的用户
//                jArray = delUserForTeamsMemberCount("coachArray", user, teamsMemberCount);
//                jObject = new JSONObject();
//                jObject.put("coachArray", jArray);
//                teamsMemberCount.setCoachArray(jObject.toString());
//                // 删除粉丝中的用户
//                jArray = delUserForTeamsMemberCount("fansArray", user, teamsMemberCount);
//                jObject = new JSONObject();
//                jObject.put("fansArray", jArray);
//                teamsMemberCount.setFansArray(jObject.toString());
//                break;
//            case "teamPlayer":
//                arrayStr = teamsMemberCount.getPlayerArray();
//                type = "playerArray";
//                jArray = setArrayForTeamsMemberCount(arrayStr, type, members, user);
//                jObject = new JSONObject();
//                jObject.put(type, jArray);
//                teamsMemberCount.setPlayerArray(jObject.toString());
//                // 删除管理员中的用户
//                jArray = delUserForTeamsMemberCount("manageArray", user, teamsMemberCount);
//                jObject = new JSONObject();
//                jObject.put("manageArray", jArray);
//                teamsMemberCount.setManageArray(jObject.toString());
//                // 删除拉拉队中的用户
//                jArray = delUserForTeamsMemberCount("cheerArray", user, teamsMemberCount);
//                jObject = new JSONObject();
//                jObject.put("cheerArray", jArray);
//                teamsMemberCount.setCheerArray(jObject.toString());
//                // 删除队长中的用户
//                jArray = delUserForTeamsMemberCount("leaderArray", user, teamsMemberCount);
//                jObject = new JSONObject();
//                jObject.put("leaderArray", jArray);
//                teamsMemberCount.setLeaderArray(jObject.toString());
//                // 删除教练中的用户
//                jArray = delUserForTeamsMemberCount("coachArray", user, teamsMemberCount);
//                jObject = new JSONObject();
//                jObject.put("coachArray", jArray);
//                teamsMemberCount.setCoachArray(jObject.toString());
//                // 删除粉丝中的用户
//                jArray = delUserForTeamsMemberCount("fansArray", user, teamsMemberCount);
//                jObject = new JSONObject();
//                jObject.put("fansArray", jArray);
//                teamsMemberCount.setFansArray(jObject.toString());
//                break;
//            case "cheerTeam":
//                type = "cheerArray";
//                arrayStr = teamsMemberCount.getCheerArray();
//                jArray = setArrayForTeamsMemberCount(arrayStr, type, members, user);
//                jObject = new JSONObject();
//                jObject.put(type, jArray);
//                teamsMemberCount.setCheerArray(jObject.toString());
//                // 删除管理员中的用户
//                jArray = delUserForTeamsMemberCount("manageArray", user, teamsMemberCount);
//                jObject = new JSONObject();
//                jObject.put("manageArray", jArray);
//                teamsMemberCount.setManageArray(jObject.toString());
//                // 删除队员中的用户
//                jArray = delUserForTeamsMemberCount("playerArray", user, teamsMemberCount);
//                jObject = new JSONObject();
//                jObject.put("playerArray", jArray);
//                teamsMemberCount.setPlayerArray(jObject.toString());
//                // 删除队长中的用户
//                jArray = delUserForTeamsMemberCount("leaderArray", user, teamsMemberCount);
//                jObject = new JSONObject();
//                jObject.put("leaderArray", jArray);
//                teamsMemberCount.setLeaderArray(jObject.toString());
//                // 删除教练中的用户
//                jArray = delUserForTeamsMemberCount("coachArray", user, teamsMemberCount);
//                jObject = new JSONObject();
//                jObject.put("coachArray", jArray);
//                teamsMemberCount.setCoachArray(jObject.toString());
//                // 删除粉丝中的用户
//                jArray = delUserForTeamsMemberCount("fansArray", user, teamsMemberCount);
//                jObject = new JSONObject();
//                jObject.put("fansArray", jArray);
//                teamsMemberCount.setFansArray(jObject.toString());
//                break;
//            case "teamLeader":
//                type = "leaderArray";
//                arrayStr = teamsMemberCount.getLeaderArray();
//                jArray = setArrayForTeamsMemberCount(arrayStr, type, members, user);
//                jObject = new JSONObject();
//                jObject.put(type, jArray);
//                teamsMemberCount.setLeaderArray(jObject.toString());
//                // 删除管理员中的用户
//                jArray = delUserForTeamsMemberCount("manageArray", user, teamsMemberCount);
//                jObject = new JSONObject();
//                jObject.put("manageArray", jArray);
//                teamsMemberCount.setManageArray(jObject.toString());
//                // 删除队员中的用户
//                jArray = delUserForTeamsMemberCount("playerArray", user, teamsMemberCount);
//                jObject = new JSONObject();
//                jObject.put("playerArray", jArray);
//                teamsMemberCount.setPlayerArray(jObject.toString());
//                // 删除拉拉队中的用户
//                jArray = delUserForTeamsMemberCount("cheerArray", user, teamsMemberCount);
//                jObject = new JSONObject();
//                jObject.put("cheerArray", jArray);
//                teamsMemberCount.setCheerArray(jObject.toString());
//                // 删除教练中的用户
//                jArray = delUserForTeamsMemberCount("coachArray", user, teamsMemberCount);
//                jObject = new JSONObject();
//                jObject.put("coachArray", jArray);
//                teamsMemberCount.setCoachArray(jObject.toString());
//                // 删除粉丝中的用户
//                jArray = delUserForTeamsMemberCount("fansArray", user, teamsMemberCount);
//                jObject = new JSONObject();
//                jObject.put("fansArray", jArray);
//                teamsMemberCount.setFansArray(jObject.toString());
//                break;
//            case "teamCoach":
//                type = "coachArray";
//                arrayStr = teamsMemberCount.getCoachArray();
//                jArray = setArrayForTeamsMemberCount(arrayStr, type, members, user);
//                jObject = new JSONObject();
//                jObject.put(type, jArray);
//                teamsMemberCount.setCoachArray(jObject.toString());
//                // 删除管理员中的用户
//                jArray = delUserForTeamsMemberCount("manageArray", user, teamsMemberCount);
//                jObject = new JSONObject();
//                jObject.put("manageArray", jArray);
//                teamsMemberCount.setManageArray(jObject.toString());
//                // 删除队员中的用户
//                jArray = delUserForTeamsMemberCount("playerArray", user, teamsMemberCount);
//                jObject = new JSONObject();
//                jObject.put("playerArray", jArray);
//                teamsMemberCount.setPlayerArray(jObject.toString());
//                // 删除拉拉队中的用户
//                jArray = delUserForTeamsMemberCount("cheerArray", user, teamsMemberCount);
//                jObject = new JSONObject();
//                jObject.put("cheerArray", jArray);
//                teamsMemberCount.setCheerArray(jObject.toString());
//                // 删除队长中的用户
//                jArray = delUserForTeamsMemberCount("leaderArray", user, teamsMemberCount);
//                jObject = new JSONObject();
//                jObject.put("leaderArray", jArray);
//                teamsMemberCount.setLeaderArray(jObject.toString());
//                // 删除粉丝中的用户
//                jArray = delUserForTeamsMemberCount("fansArray", user, teamsMemberCount);
//                jObject = new JSONObject();
//                jObject.put("fansArray", jArray);
//                teamsMemberCount.setFansArray(jObject.toString());
//                break;
//            case "teamFans":
//                type = "fansArray";
//                arrayStr = teamsMemberCount.getFansArray();
//                jArray = setArrayForTeamsMemberCount(arrayStr, type, members, user);
//                jObject = new JSONObject();
//                jObject.put(type, jArray);
//                teamsMemberCount.setFansArray(jObject.toString());
//                // 删除管理员中的用户
//                jArray = delUserForTeamsMemberCount("manageArray", user, teamsMemberCount);
//                jObject = new JSONObject();
//                jObject.put("manageArray", jArray);
//                teamsMemberCount.setManageArray(jObject.toString());
//                // 删除队员中的用户
//                jArray = delUserForTeamsMemberCount("playerArray", user, teamsMemberCount);
//                jObject = new JSONObject();
//                jObject.put("playerArray", jArray);
//                teamsMemberCount.setPlayerArray(jObject.toString());
//                // 删除拉拉队中的用户
//                jArray = delUserForTeamsMemberCount("cheerArray", user, teamsMemberCount);
//                jObject = new JSONObject();
//                jObject.put("cheerArray", jArray);
//                teamsMemberCount.setCheerArray(jObject.toString());
//                // 删除队长中的用户
//                jArray = delUserForTeamsMemberCount("leaderArray", user, teamsMemberCount);
//                jObject = new JSONObject();
//                jObject.put("leaderArray", jArray);
//                teamsMemberCount.setLeaderArray(jObject.toString());
//                // 删除教练中的用户
//                jArray = delUserForTeamsMemberCount("coachArray", user, teamsMemberCount);
//                jObject = new JSONObject();
//                jObject.put("coachArray", jArray);
//                teamsMemberCount.setCoachArray(jObject.toString());
//                break;
//        }
//        return teamsMemberCount;
//    }

    // 删除球队相应角色中的成员
//    private static JSONArray delUserForTeamsMemberCount(String type, User user, FootballTeamMemberCount teamsMemberCount) throws JSONException {
//        JSONArray jArray = new JSONArray();
//        String memberStr;
//        JSONObject object;
//        JSONArray array = null;
//        // 根据角色类型判断
//        if ("playerArray".equals(type)) {
//            // 删除队员中的user
//            memberStr = teamsMemberCount.getPlayerArray();
//            object = JSON.parseObject(memberStr);
//            array = object.getJSONArray("playerArray");
//        } else if ("cheerArray".equals(type)) {
//            memberStr = teamsMemberCount.getCheerArray();
//            object = JSON.parseObject(memberStr);
//            array = object.getJSONArray("cheerArray");
//            // 删除拉拉队中的user
//        } else if ("manageArray".equals(type)) {
//            memberStr = teamsMemberCount.getManageArray();
//            object = JSON.parseObject(memberStr);
//            array = object.getJSONArray("manageArray");
//        } else if ("leaderArray".equals(type)) {
//            memberStr = teamsMemberCount.getLeaderArray();
//            object = JSON.parseObject(memberStr);
//            array = object.getJSONArray("leaderArray");
//        } else if ("coachArray".equals(type)) {
//            memberStr = teamsMemberCount.getCoachArray();
//            object = JSON.parseObject(memberStr);
//            array = object.getJSONArray("coachArray");
//        } else if ("fansArray".equals(type)) {
//            memberStr = teamsMemberCount.getFansArray();
//            object = JSON.parseObject(memberStr);
//            array = object.getJSONArray("fansArray");
//        }
//        if (array != null && array.size() > 0) {
//            for (Object o : array) {
//                if (!o.equals(ConstantUserManage.getLongTenLetter(user.getId()) + "")) {
//                    jArray.add(o);
//                }
//            }
//        }
//        return jArray;
//    }

    // 将成员加入到数组中去
    private static JSONArray setArrayForTeamsMemberCount(String arrayStr, String type, String[] members, User user) throws JSONException {
        JSONArray jArray;
        if (arrayStr.equals("")) {
            jArray = new JSONArray();
            jArray.add(ConstantUserManage.getLongTenLetter(user.getId()));
            if ((members != null) && members.length > 0) {
                for (String imUserid : members) {
                    if (!imUserid.equals("")) {
                        jArray.add(imUserid);
                    }
                }
            }
        } else {
            JSONObject json = JSON.parseObject(arrayStr);
            jArray = json.getJSONArray(type);
            jArray.add(ConstantUserManage.getLongTenLetter(user.getId()));
            if ((members != null) && members.length > 0) {
                for (String imUserid : members) {
                    if (!imUserid.equals("")) {
                        jArray.add(imUserid);
                    }
                }
            }
        }
        return jArray;
    }

    // 初始化 football_team_member_user_count 表
//    public static FootballTeamMemberUserCount setArrayEmptyInTeamsMemberUserCount(FootballTeamMemberUserCount teamsMemberUserCount) throws JSONException {
//        JSONArray jArray = new JSONArray();
//        JSONObject jObject = new JSONObject();
//        jObject.put("ownerTeamsArray", jArray);
//        teamsMemberUserCount.setOwnerTeamsArray(jObject.toString());
//        jObject = new JSONObject();
//        jObject.put("joinTeamsArray", jArray);
//        teamsMemberUserCount.setJoinTeamsArray(jObject.toString());
//        jObject = new JSONObject();
//        jObject.put("followTeamsArray", jArray);
//        teamsMemberUserCount.setFollowTeamsArray(jObject.toString());
//        jObject = new JSONObject();
//        jObject.put("fansTeamsArray", jArray);
//        teamsMemberUserCount.setFansTeamsArray(jObject.toString());
//        return teamsMemberUserCount;
//    }

    // 将球队加入到用户的球队列表中去
//    public static FootballTeamMemberUserCount setOneArrayInTeamsMemberUserCount(FootballTeamMemberUserCount teamsMemberUserCount, FootballTeam teams, String mode) throws JSONException {
//        // 存储变量
//        JSONArray jArray;
//        JSONObject jObject;
//        String arrayStr;
//        // 中间变量
//        JSONArray jArray2;
//        JSONObject jObject2;
//        String arrayStr2;
//        JSONArray jArray3;
//        JSONObject jObject3;
//        String arrayStr3;
//        // 将球队加入到相应的分类球队列表中去
//        switch (mode) {
//            case "ownerTeamsArray": {
//                arrayStr = teamsMemberUserCount.getOwnerTeamsArray();
//                JSONObject Jsonarray = JSON.parseObject(arrayStr);
//                jArray = Jsonarray.getJSONArray("ownerTeamsArray");
//                jArray.add(Long.toString(teams.getId()));
//                jObject = new JSONObject();
//                jObject.put(mode, jArray);
//                teamsMemberUserCount.setOwnerTeamsArray(jObject.toString());
//                break;
//            }
//            case "joinTeamsArray": {
//                arrayStr = teamsMemberUserCount.getJoinTeamsArray();
//                JSONObject Jsonarray = JSON.parseObject(arrayStr);
//                jArray = Jsonarray.getJSONArray("joinTeamsArray");
//                List<String> teamIdList = new ArrayList<>();
//                for (int i = 0; i < jArray.size(); i++) {
//                    String tmpTeamId = jArray.getString(i);
//                    teamIdList.add(tmpTeamId);
//                }
//                if (!teamIdList.contains(Long.toString(teams.getId()))) {
//                    jArray.add(Long.toString(teams.getId()));
//                }
//                jObject = new JSONObject();
//                jObject.put(mode, jArray);
//                teamsMemberUserCount.setJoinTeamsArray(jObject.toString());
//                // 删除关注队伍中的球队
//                arrayStr2 = teamsMemberUserCount.getFollowTeamsArray();
//                JSONObject Jsonarray2 = JSON.parseObject(arrayStr2);
//                jArray2 = Jsonarray2.getJSONArray("followTeamsArray");
//                JSONArray jArrayTemp2 = new JSONArray();
//                for (int i = 0; i < jArray2.size(); i++) {
//                    if (!jArray2.get(i).equals(Long.toString(teams.getId()))) {
//                        jArrayTemp2.add(jArray2.get(i));
//                    }
//                }
//                jObject2 = new JSONObject();
//                jObject2.put("followTeamsArray", jArrayTemp2);
//                teamsMemberUserCount.setFollowTeamsArray(jObject2.toString());
//                // 删除粉丝队伍中的球队
//                arrayStr3 = teamsMemberUserCount.getFansTeamsArray();
//                JSONObject Jsonarray3 = JSON.parseObject(arrayStr3);
//                jArray3 = Jsonarray3.getJSONArray("fansTeamsArray");
//                JSONArray jArrayTemp3 = new JSONArray();
//                for (int i = 0; i < jArray3.size(); i++) {
//                    if (!jArray3.get(i).equals(Long.toString(teams.getId()))) {
//                        jArrayTemp3.add(jArray3.get(i));
//                    }
//                }
//                jObject3 = new JSONObject();
//                jObject3.put("fansTeamsArray", jArrayTemp3);
//                teamsMemberUserCount.setFansTeamsArray(jObject3.toString());
//                break;
//            }
//            case "followTeamsArray": {
//                arrayStr = teamsMemberUserCount.getFollowTeamsArray();
//                JSONObject Jsonarray = JSON.parseObject(arrayStr);
//                jArray = Jsonarray.getJSONArray("followTeamsArray");
//                jArray.add(Long.toString(teams.getId()));
//                jObject = new JSONObject();
//                jObject.put(mode, jArray);
//                teamsMemberUserCount.setFollowTeamsArray(jObject.toString());
//                // 删除加入队伍中的球队
//                arrayStr2 = teamsMemberUserCount.getJoinTeamsArray();
//                JSONObject Jsonarray2 = JSON.parseObject(arrayStr2);
//                jArray2 = Jsonarray2.getJSONArray("joinTeamsArray");
//                JSONArray jArrayTemp2 = new JSONArray();
//                for (int i = 0; i < jArray2.size(); i++) {
//                    if (!jArray2.get(i).equals(Long.toString(teams.getId()))) {
//                        jArrayTemp2.add(jArray2.get(i));
//                    }
//                }
//                jObject2 = new JSONObject();
//                jObject2.put("joinTeamsArray", jArrayTemp2);
//                teamsMemberUserCount.setJoinTeamsArray(jObject2.toString());
//                // 删除粉丝中的球队
//                arrayStr3 = teamsMemberUserCount.getFansTeamsArray();
//                JSONObject Jsonarray3 = JSON.parseObject(arrayStr3);
//                jArray3 = Jsonarray3.getJSONArray("fansTeamsArray");
//                JSONArray jArrayTemp3 = new JSONArray();
//                for (int i = 0; i < jArray3.size(); i++) {
//                    if (!jArray3.get(i).equals(Long.toString(teams.getId()))) {
//                        jArrayTemp3.add(jArray3.get(i));
//                    }
//                }
//                jObject3 = new JSONObject();
//                jObject3.put("fansTeamsArray", jArrayTemp3);
//                teamsMemberUserCount.setFansTeamsArray(jObject3.toString());
//                break;
//            }
//            default: {
//                arrayStr = teamsMemberUserCount.getFansTeamsArray();
//                JSONObject Jsonarray = JSON.parseObject(arrayStr);
//                jArray = Jsonarray.getJSONArray("fansTeamsArray");
//                jArray.add(Long.toString(teams.getId()));
//                jObject = new JSONObject();
//                jObject.put(mode, jArray);
//                teamsMemberUserCount.setFansTeamsArray(jObject.toString());
//                // 删除加入球队列表中的球队
//                arrayStr2 = teamsMemberUserCount.getJoinTeamsArray();
//                JSONObject Jsonarray2 = JSON.parseObject(arrayStr2);
//                jArray2 = Jsonarray2.getJSONArray("joinTeamsArray");
//                JSONArray jArrayTemp2 = new JSONArray();
//                for (int i = 0; i < jArray2.size(); i++) {
//                    if (!jArray2.get(i).equals(Long.toString(teams.getId()))) {
//                        jArrayTemp2.add(jArray2.get(i));
//                    }
//                }
//                jObject2 = new JSONObject();
//                jObject2.put("joinTeamsArray", jArrayTemp2);
//                teamsMemberUserCount.setJoinTeamsArray(jObject2.toString());
//                // 删除关注球队列表中的球队
//                arrayStr3 = teamsMemberUserCount.getFollowTeamsArray();
//                JSONObject Jsonarray3 = JSON.parseObject(arrayStr3);
//                jArray3 = Jsonarray3.getJSONArray("followTeamsArray");
//                JSONArray jArrayTemp3 = new JSONArray();
//                for (int i = 0; i < jArray3.size(); i++) {
//                    if (!jArray3.get(i).equals(Long.toString(teams.getId()))) {
//                        jArrayTemp3.add(jArray3.get(i));
//                    }
//                }
//                jObject3 = new JSONObject();
//                jObject3.put("followTeamsArray", jArrayTemp3);
//                teamsMemberUserCount.setFollowTeamsArray(jObject3.toString());
//                break;
//            }
//        }
//        return teamsMemberUserCount;
//    }

    /**
     * listMap 需要封装的集合，集合内的key，key_name 封装后的key  i 循环数  num  相加的value值
     */
    public static Map<String, Object> capsulationNotes(List<Map> listMap, String key, String key_name, int i) {
        Map<String, Object> faultInfo = new HashMap<>();
        faultInfo.put("userId", listMap.get(i).get("userId"));
        faultInfo.put("nickName", listMap.get(i).get("nickName"));
        faultInfo.put(key_name, listMap.get(i).get(key));
        return faultInfo;
    }

    public static List<Map<String, Object>> mapSort(List<Map<String, Object>> map, final String key) {
        map.sort((o1, o2) -> ((Long) o2.get(key)).compareTo((Long) o1.get(key)));
        return map;
    }

    // 球队比赛统计
    public static Map<String, Object> getTeamGameStatistics(Map<String, Object> map, FootballTeam team, List<FootballTeamGame> gameList) {
        // 主客队分数
        int winCount = 0;
        int loseCount = 0;
        int equalCount = 0;
        if (gameList != null && gameList.size() > 0) {
            for (FootballTeamGame game : gameList) {
                String teamId = team.getId() + "";
                FootballTeam tempTeam = game.getFootballTeam();
                // 查看是主队还是客队
                if (tempTeam != null) {
                    String tempTeamId = tempTeam.getId() + "";
                    if (teamId.equals(tempTeamId)) {
                        // 作为主队
                        if (game.getHostScore() != null
                                && game.getGuestScore() != null) {
                            if (game.getHostScore() > game.getGuestScore()) {
                                winCount++;
                            } else if (game.getHostScore().equals(game.getGuestScore())) {
                                equalCount++;
                            } else if (game.getHostScore() < game.getGuestScore()) {
                                loseCount++;
                            }
                        }
                    } else {
                        // 作为客队
                        if (game.getHostScore() != null
                                && game.getGuestScore() != null) {
                            if (game.getHostScore() < game.getGuestScore()) {
                                winCount++;
                            } else if (game.getHostScore().equals(game.getGuestScore())) {
                                equalCount++;
                            } else if (game.getHostScore() > game.getGuestScore()) {
                                loseCount++;
                            }
                        }
                    }
                }// tempTeam!=null
            }
        }
        if (winCount + loseCount <= 0) {
            map.put("winRate", "0%");
        } else {
            float winRate = (float) winCount / (float) (winCount + loseCount + equalCount);
            BigDecimal b = new BigDecimal(winRate);
            // 保存两位小数
            float f1 = b.setScale(2, RoundingMode.HALF_UP).floatValue();
            map.put("winRate", (Math.round(f1 * 100)) + "%");
        }
        map.put("winCount", winCount);
        map.put("loseCount", loseCount);
        map.put("equalCount", equalCount);
        return map;
    }

    public static Map<String, Object> fengzhuang(List<UserHardwareData> userHardwareDataList, User user) throws JSONException {
        Map<String, Object> map = new HashMap<>();
        if (userHardwareDataList != null && userHardwareDataList.size() > 0) {
            // 查看是左脚还是右脚
            UserHardware tempUserHardware = userHardwareDataList.get(0).getUserHardware();
            long rightFootStartTime = 0L;
            long leftFootStartTime = 0L;
            String rightHighSpeedMoveDataString = "";
            String rightMidSpeedMoveDataString = "";
            String rightLowSpeedMoveDataString = "";
            String rightNormalSpeedMoveDataString = "";
            String rightFootData = "";
            String leftHighSpeedMoveDataString = "";
            String leftMidSpeedMoveDataString = "";
            String leftLowSpeedMoveDataString = "";
            String leftNormalSpeedMoveDataString = "";
            String leftFootData = "";
            if (tempUserHardware.getHardwareType() == 1) {
                // 表示是左脚
                if (userHardwareDataList.get(0).getKickBallStartTime() != null) {
                    leftFootStartTime = userHardwareDataList.get(0).getKickBallStartTime().getTime();
                }
                if (userHardwareDataList.get(0).getHighSpeedMoveData() != null) {
                    leftHighSpeedMoveDataString = userHardwareDataList.get(0).getHighSpeedMoveData();
                }
                if (userHardwareDataList.get(0).getMidSpeedMoveData() != null) {
                    leftMidSpeedMoveDataString = userHardwareDataList.get(0).getMidSpeedMoveData();
                }
                if (userHardwareDataList.get(0).getLowSpeedMoveData() != null) {
                    leftLowSpeedMoveDataString = userHardwareDataList.get(0).getLowSpeedMoveData();
                }
                if (userHardwareDataList.get(0).getNormalSpeedMoveData() != null) {
                    leftNormalSpeedMoveDataString = userHardwareDataList.get(0).getNormalSpeedMoveData();
                }
                if (userHardwareDataList.size() > 1) {
                    if (userHardwareDataList.get(1).getKickBallStartTime() != null) {
                        rightFootStartTime = userHardwareDataList.get(1).getKickBallStartTime().getTime();
                    }
                    rightHighSpeedMoveDataString = userHardwareDataList.get(1).getHighSpeedMoveData();
                    rightMidSpeedMoveDataString = userHardwareDataList.get(1).getMidSpeedMoveData();
                    rightLowSpeedMoveDataString = userHardwareDataList.get(1).getLowSpeedMoveData();
                    rightNormalSpeedMoveDataString = userHardwareDataList.get(1).getNormalSpeedMoveData();
                    rightFootData = userHardwareDataList.get(1).getKickBallData();
                }
            } else {
                // 表示是右脚
                if (userHardwareDataList.get(0).getKickBallStartTime() != null) {
                    rightFootStartTime = userHardwareDataList.get(0).getKickBallStartTime().getTime();
                }
                if (userHardwareDataList.get(0).getHighSpeedMoveData() != null) {
                    rightHighSpeedMoveDataString = userHardwareDataList.get(0).getHighSpeedMoveData();
                }
                if (userHardwareDataList.get(0).getMidSpeedMoveData() != null) {
                    rightMidSpeedMoveDataString = userHardwareDataList.get(0).getMidSpeedMoveData();
                }
                if (userHardwareDataList.get(0).getLowSpeedMoveData() != null) {
                    rightLowSpeedMoveDataString = userHardwareDataList.get(0).getLowSpeedMoveData();
                }
                if (userHardwareDataList.get(0).getNormalSpeedMoveData() != null) {
                    rightNormalSpeedMoveDataString = userHardwareDataList.get(0).getNormalSpeedMoveData();
                }
                if (userHardwareDataList.get(0).getKickBallData() != null) {
                    rightFootData = userHardwareDataList.get(0).getKickBallData();
                }
                if (userHardwareDataList.size() > 1) {
                    if (userHardwareDataList.get(1).getKickBallStartTime() != null) {
                        leftFootStartTime = userHardwareDataList.get(1).getKickBallStartTime().getTime();
                    }
                    leftHighSpeedMoveDataString = userHardwareDataList.get(1).getHighSpeedMoveData();
                    leftMidSpeedMoveDataString = userHardwareDataList.get(1).getMidSpeedMoveData();
                    leftLowSpeedMoveDataString = userHardwareDataList.get(1).getLowSpeedMoveData();
                    leftNormalSpeedMoveDataString = userHardwareDataList.get(1).getNormalSpeedMoveData();
                    leftFootData = userHardwareDataList.get(1).getKickBallData();
                }
            }
            List<Integer> leftList = new ArrayList<>();
            List<Integer> rightList = new ArrayList<>();
            footDataStringToList(leftFootData, leftList);
            footDataStringToList(rightFootData, rightList);
            List<Integer> sortedDataArray = FootballTeamUtils.sortArray(leftFootStartTime, rightFootStartTime, leftList, rightList);
            double stepWidth;
            Short weight = user.getWeight();
            if (user.getHeight() != null && user.getHeight() != 0) {
                stepWidth = (user.getHeight() * 0.45 / 100.0);
            } else {
                // 没有身高，默认为175
                stepWidth = (175 * 0.45 / 100.0);
            }
            //高速数据
            JSONArray highMoveCalorieArray = new JSONArray();
            moveData(highMoveCalorieArray, leftHighSpeedMoveDataString, stepWidth, weight);
            moveData(highMoveCalorieArray, rightHighSpeedMoveDataString, stepWidth, weight);
            //中速数据
            JSONArray midMoveCalorieArray = new JSONArray();
            moveData(midMoveCalorieArray, leftMidSpeedMoveDataString, stepWidth, weight);
            moveData(midMoveCalorieArray, rightMidSpeedMoveDataString, stepWidth, weight);
            //低速数据
            JSONArray lowMoveCalorieArray = new JSONArray();
            moveData(lowMoveCalorieArray, leftLowSpeedMoveDataString, stepWidth, weight);
            moveData(lowMoveCalorieArray, rightLowSpeedMoveDataString, stepWidth, weight);
            //步行数据
            JSONArray normalMoveCalorieArray = new JSONArray();
            moveData(normalMoveCalorieArray, leftNormalSpeedMoveDataString, stepWidth, weight);
            moveData(normalMoveCalorieArray, rightNormalSpeedMoveDataString, stepWidth, weight);
            map.put("sortedDataArray", sortedDataArray);
            map.put("highMoveCalorieArray", highMoveCalorieArray);
            map.put("midMoveCalorieArray", midMoveCalorieArray);
            map.put("lowMoveCalorieArray", lowMoveCalorieArray);
            map.put("normalMoveCalorieArray", normalMoveCalorieArray);
            long kickballTime;
            if (leftFootStartTime >= rightFootStartTime) {
                if (rightFootStartTime > 0) {
                    kickballTime = rightFootStartTime;
                } else {
                    kickballTime = leftFootStartTime;
                }
            } else {
                if (leftFootStartTime > 0) {
                    kickballTime = leftFootStartTime;
                } else {
                    kickballTime = rightFootStartTime;
                }
            }
            map.put("kickballTime", kickballTime);
        }
        return map;
    }

    private static void footDataStringToList(String footData, List<Integer> list) throws JSONException {
        if (!"".equals(footData) && footData != null) {
            JSONArray leftArray = JSON.parseArray(footData);
            if (leftArray.size() > 0) {
                for (int i = 0; i < leftArray.size(); i++) {
                    list.add(leftArray.getInteger(i));
                }
            }
        }
    }


    // 球队数据排名排序
    public static JSONArray sortArrayByLongValue(JSONArray jsonArray, String sortKey) {
        jsonArray.sort((o1, o2) -> {
            Long joinTime1 = JSONObject.parseObject(JSON.toJSONString(o1)).getLong(sortKey);
            Long joinTime2 = JSONObject.parseObject(JSON.toJSONString(o2)).getLong(sortKey);
            return joinTime2.compareTo(joinTime1);
        });
        return jsonArray;
    }

    //排序JSONArray
    public static JSONArray sortJSONArray(JSONArray jArray) {
        jArray.sort((o1, o2) -> {
            JSONArray teampArray_i = (JSONArray) o1;
            JSONArray teampArray_j = (JSONArray) o2;
            return ((JSONArray) o1).size() - ((JSONArray) o2).size();
        });
        return jArray;
    }


    //根据时间对JSONArray排序
    public static JSONArray sortArrayByJoinTime(JSONArray jArray) {
        jArray.sort((o1, o2) -> {
            Long joinTime1 = ((JSONObject) o1).getLong("joinTime");
            Long joinTime2 = ((JSONObject) o2).getLong("joinTime");
            return joinTime2.compareTo(joinTime1);
        });
        return jArray;
    }


    public static JSONArray sortJSONArray(JSONArray array, String map_key) throws JSONException {
        return sortJSONArray(array, map_key, 0);
    }

    //给JSONArray 从大到小排序
    public static JSONArray sortJSONArray(JSONArray array, String map_key, int sort) throws JSONException {
        if (array.size() > 1) {
            array.sort((o1, o2) -> {
                if (((Map) o1).get(map_key) instanceof Integer) {
                    Integer num1 = (Integer) ((Map) o1).get(map_key);
                    Integer num2 = (Integer) ((Map) o2).get(map_key);
                    if (sort == 0) {
                        return num1.compareTo(num2);
                    } else {
                        return num2.compareTo(num1);
                    }
                } else if (((Map) o1).get(map_key) instanceof Long) {
                    Long num1 = (Long) ((Map) o1).get(map_key);
                    Long num2 = (Long) ((Map) o2).get(map_key);
                    if (sort == 0) {
                        return num1.compareTo(num2);
                    } else {
                        return num2.compareTo(num1);
                    }
                } else {
                    return 0;
                }
            });
        }
        return array;
    }

    private static void moveData(JSONArray moveCalorieArray, String moveDataString, double stepWidth, Short weight) throws JSONException {
        //高速数据
        if (!"".equals(moveDataString) && moveDataString != null) {
            JSONArray temp = JSON.parseArray(moveDataString);
            if (temp.size() > 0) {
                for (int i = 0; i < temp.size(); i++) {
                    JSONObject tempObject = temp.getJSONObject(i);
                    int stepCount = tempObject.getInteger("stepCount");
                    long tempMoveCalarie;
                    int intervalTime = tempObject.getInteger("intervalTime");
                    Map<String, Object> tempMap = new HashMap<>();
                    if (weight != null && weight != 0) {
                        tempMoveCalarie = (long) (weight * (stepCount * stepWidth) / 1000 * 1.036);
                    } else {
                        // 没有体重，默认为60
                        tempMoveCalarie = (long) (60 * (stepCount * stepWidth) / 1000 * 1.036);
                    }
                    tempMap.put("intervalTime", intervalTime);
                    tempMap.put("stepCount", stepCount);
                    tempMap.put("moveCalarie", tempMoveCalarie);
                    tempMap.put("startTime", tempObject.getLong("startTime"));
                    tempMap.put("endTime", tempObject.getLong("endTime"));
                    moveCalorieArray.add(tempMap);
                }// for
            }// if
        }
    }

    public void setErrorMessage(MtJavaServerResponseBodyPojo responseBodyPojo, String code, String message) {
        MtJavaServerResponseBodyResultPojo responseBodyResultPojo = new MtJavaServerResponseBodyResultPojo();
        if (code != null && !"".equals(code)) {
            responseBodyResultPojo.setCode(code);
        }
        responseBodyResultPojo.setMessage(message);
        String resultString = CommonUtil.changeResultToString(responseBodyResultPojo);
        responseBodyPojo.setResult(resultString);
    }

    // 球队显示清单列表的内容；
    public JSONArray teamsInformationArray(List<FootballTeam> list, JSONArray jArrayTemp, User user) throws JSONException {
        // 增加非空判断
        if (list != null) {
            for (FootballTeam team : list) {
                Map<String, Object> map = new HashMap<>();
                List<FootballTeamGame> gameList = footballTeamsGameDaoService.findByTeamId(team.getId());
                JSONArray gameArray = new JSONArray();
                if (gameList != null && gameList.size() > 0) {
                    for (FootballTeamGame game : gameList) {
                        Map<String, Object> gameObject = new HashMap<>();
                        String gameName = "";
                        gameName += game.getFootballTeam().getTeamName();
                        gameName += " VS ";
                        gameName += game.getOpponent();
                        gameObject.put("teamsContest", gameName);
                        gameObject.put("contestTime", game.getCompetitionTime().toString().split(" ")[0]);
                        gameObject.put("contestLocation", game.getLocation());
                        gameObject.put("fieldLocation", game.getFieldLocation());
                        gameArray.add(gameObject);
                    }
                } else {
                    // 没有比赛传空
                    Map<String, Object> gameObject = new HashMap<>();
                    gameObject.put("teamsContest", "");
                    gameObject.put("contestTime", "");
                    gameObject.put("contestLocation", "");
                    gameObject.put("fieldLocation", "");
                    gameArray.add(gameObject);
                }
                map.put("teamGameList", gameArray);
                map.put("userid", user.getId()); // 增加userid
                map.put("teamsid", team.getId()); // 球队id；
                map.put("imGroupid", team.getGroups().getImGroupId()); // 球队imgroupid；
                map.put("teamName", team.getTeamName()); // 球队名称；
                map.put("teamsType", "footballTeams"); // 球队种类
                map.put("teams_credits", team.getCredits()); // 球队积分；
                map.put("teams_creditsRank", team.getCreditsRank());
                map.put("teams_winrateRank", team.getWinRateRank());
                map.put("selfEvaluation", team.getSelfEvaluation());
                map.put("description", team.getDescription());
                map.put("isConscribe", team.getIsConscribe());
                map.put("slogan", team.getSlogan());
                map.put("teamHistory", team.getIsHistory());
                map.put("isCampusTeam", team.getIsCampusTeam());
                map.put("teamsNatureType", team.getFootballTeamNatureType().getTypeName());
                // 队徽
                map.put("badgeFilename", team.getTeamBadgeName());
                map.put("badgeimgNeturl", team.getTeamBadgeNetUrl());
                map.put("badgeLength", team.getTeamBadgeLength());
                // 队旗
                map.put("pennantFilename", team.getTeamPennantName());
                map.put("teampennantimgNeturl", team.getTeamPennantNetUrl());
                map.put("pennantLength", team.getTeamPennantLength());
                // 头像
                map.put("teamHeadfilename", team.getTeamHeadImgName());
                map.put("teamHeadimgNeturl", team.getTeamHeadImgNetUrl());
                map.put("teamHeadlength", team.getTeamHeadImgLength());
                // 微信2微码
                map.put("teamWeixinQrcodefilename", team.getTeamWeixinQrcodeName());
                map.put("teamWeixinQrcodeimgNeturl", team.getTeamWeixinQrcodeNetUrl());
                map.put("teamWeixinQrcodelength", team.getTeamWeixinQrcodeLength());
                map.put("favorRules", team.getFavorRules());
                map.put("playStar", team.getPlayStar());
                map.put("teamRecord", team.getTeamRecord());
                map.put("teamHonour", team.getTeamHonour());
                map.put("playerList", team.getPlayerList());
                map.put("playGround", team.getPlayGround());
                map.put("leader", team.getLeader());
                map.put("coach", team.getCoach());
                map.put("weixinGroup", team.getWechatGroup());
                map.put("teamCreateTime", team.getTeamCreateTime().getTime());
                map.put("schoolName", team.getSchoolName());
                // 区县
                map.put("countryCode", team.getCountryCode());
                map.put("provinceCode", team.getProvinceCode());
                map.put("cityCode", team.getCityCode());
                map.put("countyCode", team.getCountyCode());
                // 球队统计
                JSONArray memberHeadImgArray = new JSONArray();
                int memberCount = 0;
                JSONObject json;
                JSONArray jsonArray;
                // 球队创建者
                UserHeadimg userCreatorHeadimg = userHeadimgDaoService.findLastByUserId(team.getUser().getId());
                if (userCreatorHeadimg != null) {
                    Map<String, Object> mapTemp = new HashMap<>();
                    mapTemp.put("userRole", "teamOwner");
                    mapTemp.put("userId", team.getUser().getId());
                    mapTemp.put("nickName", team.getUser().getNickName());
                    mapTemp.put("headimgNeturl", userCreatorHeadimg.getHeadImgNetUrl());
                    mapTemp.put("headimgfilename", userCreatorHeadimg.getFileName());
                    mapTemp.put("headimglength", userCreatorHeadimg.getLength());
                    memberCount += 1;
                    memberHeadImgArray.add(mapTemp);
                }
                // 管理员处理
                jsonArray = new JSONArray();
                List<FootballTeamUser> managerList = footballTeamUserDaoService.findByTeamIdAndTeamMemberRoleId(team.getId(), TeamMemberRoleEnum.TEAMMANAGER.getValue());
                for (FootballTeamUser teamUser : managerList) {
                    jsonArray.add(teamUser.getUser().getId());
                }
                if (jsonArray.size() > 0) {
                    for (int j = 0; j < jsonArray.size(); j++) {
                        Long userId = jsonArray.getLong(j);
                        UserHeadimg userHeadimg = userHeadimgDaoService.findLastByUserId(userId);
                        if (userHeadimg != null) {
                            Map<String, Object> mapTemp = new HashMap<>();
                            mapTemp.put("userRole", "teamManage");
                            mapTemp.put("userId", userId);
                            mapTemp.put("nickName", team.getUser().getNickName());
                            mapTemp.put("headimgNeturl", userHeadimg.getHeadImgNetUrl());
                            mapTemp.put("headimgfilename", userHeadimg.getFileName());
                            mapTemp.put("headimglength", userHeadimg.getLength());
                            // 管理员如果是创建者本人则不计数
                            if (!userId.equals(team.getUser().getId())) {
                                memberCount += 1;
                            }
                            memberHeadImgArray.add(mapTemp);
                        }
                    }
                }
                // 球队队员处理
                jsonArray = new JSONArray();
                List<FootballTeamUser> playerList = footballTeamUserDaoService.findByTeamIdAndTeamMemberRoleId(team.getId(), TeamMemberRoleEnum.TEAMPLAYER.getValue());
                for (FootballTeamUser teamUser : playerList) {
                    jsonArray.add(teamUser.getUser().getId());
                }
                if (jsonArray.size() > 0) {
                    for (int j = 0; j < jsonArray.size(); j++) {
                        Long userId = jsonArray.getLong(j);
                        UserHeadimg userHeadimg = userHeadimgDaoService.findLastByUserId(userId);
                        if (userHeadimg != null) {
                            Map<String, Object> mapTemp = new HashMap<>();
                            mapTemp.put("userRole", "teamPlayer");
                            mapTemp.put("userId", userId);
                            mapTemp.put("nickName", team.getUser().getNickName());
                            mapTemp.put("headimgNeturl", userHeadimg.getHeadImgNetUrl());
                            mapTemp.put("headimgfilename", userHeadimg.getFileName());
                            mapTemp.put("headimglength", userHeadimg.getLength());
                            if (!userId.equals(team.getUser().getId())) {
                                memberCount += 1;
                            }
                            memberHeadImgArray.add(mapTemp);
                        }
                    }
                }
                // 粉丝处理
                jsonArray = new JSONArray();
                List<FootballTeamUser> fansList = footballTeamUserDaoService.findByTeamIdAndTeamMemberRoleId(team.getId(), TeamMemberRoleEnum.TEAMFANS.getValue());
                for (FootballTeamUser teamUser : fansList) {
                    jsonArray.add(teamUser.getUser().getId());
                }
                if (jsonArray.size() > 0) {
                    map.put("fansCount", jsonArray.size());
                    for (int j = 0; j < jsonArray.size(); j++) {
                        Long userId = jsonArray.getLong(j);
                        UserHeadimg userHeadimg = userHeadimgDaoService.findLastByUserId(userId);
                        if (userHeadimg != null) {
                            Map<String, Object> mapTemp = new HashMap<>();
                            mapTemp.put("userRole", "teamFans");
                            mapTemp.put("userId", userId);
                            mapTemp.put("nickName", team.getUser().getNickName());
                            mapTemp.put("headimgNeturl", userHeadimg.getHeadImgNetUrl());
                            mapTemp.put("headimgfilename", userHeadimg.getFileName());
                            mapTemp.put("headimglength", userHeadimg.getLength());
                            memberHeadImgArray.add(mapTemp);
                        }
                    }
                } else {
                    map.put("fansCount", 0);
                }
                // 拉拉队处理
                jsonArray = new JSONArray();
                List<FootballTeamUser> cheerList = footballTeamUserDaoService.findByTeamIdAndTeamMemberRoleId(team.getId(), TeamMemberRoleEnum.CHEERTEAM.getValue());
                for (FootballTeamUser teamUser : cheerList) {
                    jsonArray.add(teamUser.getUser().getId());
                }
                if (jsonArray.size() > 0) {
                    for (int j = 0; j < jsonArray.size(); j++) {
                        Long userId = jsonArray.getLong(j);
                        UserHeadimg userHeadimg = userHeadimgDaoService.findLastByUserId(userId);
                        if (userHeadimg != null) {
                            Map<String, Object> mapTemp = new HashMap<>();
                            mapTemp.put("userRole", "cheerTeam");
                            mapTemp.put("userId", userId);
                            mapTemp.put("nickName", team.getUser().getNickName());
                            mapTemp.put("headimgNeturl", userHeadimg.getHeadImgNetUrl());
                            mapTemp.put("headimgfilename", userHeadimg.getFileName());
                            mapTemp.put("headimglength", userHeadimg.getLength());
                            if (!userId.equals(team.getUser().getId())) {
                                memberCount += 1;
                            }
                            memberHeadImgArray.add(mapTemp);
                        }
                    }
                }
                // 队长处理
                jsonArray = new JSONArray();
                List<FootballTeamUser> leaderList = footballTeamUserDaoService.findByTeamIdAndTeamMemberRoleId(team.getId(), TeamMemberRoleEnum.TEAMLEADER.getValue());
                for (FootballTeamUser teamUser : leaderList) {
                    jsonArray.add(teamUser.getUser().getId());
                }
                if (jsonArray.size() > 0) {
                    for (int j = 0; j < jsonArray.size(); j++) {
                        Long userId = jsonArray.getLong(j);
                        UserHeadimg userHeadimg = userHeadimgDaoService.findLastByUserId(userId);
                        if (userHeadimg != null) {
                            Map<String, Object> mapTemp = new HashMap<>();
                            mapTemp.put("userRole", "teamLeader");
                            mapTemp.put("userId", userId);
                            mapTemp.put("nickName", team.getUser().getNickName());
                            mapTemp.put("headimgNeturl", userHeadimg.getHeadImgNetUrl());
                            mapTemp.put("headimgfilename", userHeadimg.getFileName());
                            mapTemp.put("headimglength", userHeadimg.getLength());
                            if (!userId.equals(team.getUser().getId())) {
                                memberCount += 1;
                            }
                            memberHeadImgArray.add(mapTemp);
                        }
                    }
                }
                // 教练处理
                jsonArray = new JSONArray();
                List<FootballTeamUser> coachList = footballTeamUserDaoService.findByTeamIdAndTeamMemberRoleId(team.getId(), TeamMemberRoleEnum.TEAMCOACH.getValue());
                for (FootballTeamUser teamUser : coachList) {
                    jsonArray.add(teamUser.getUser().getId());
                }
                if (jsonArray.size() > 0) {
                    for (int j = 0; j < jsonArray.size(); j++) {
                        Long userId = jsonArray.getLong(j);
                        UserHeadimg userHeadimg = userHeadimgDaoService.findLastByUserId(userId);
                        if (userHeadimg != null) {
                            Map<String, Object> mapTemp = new HashMap<>();
                            mapTemp.put("userRole", "teamCoach");
                            mapTemp.put("userId", userId);
                            mapTemp.put("nickName", team.getUser().getNickName());
                            mapTemp.put("headimgNeturl", userHeadimg.getHeadImgNetUrl());
                            mapTemp.put("headimgfilename", userHeadimg.getFileName());
                            mapTemp.put("headimglength", userHeadimg.getLength());
                            if (!userId.equals(team.getUser().getId())) {
                                memberCount += 1;
                            }
                            memberHeadImgArray.add(mapTemp);
                        }
                    }
                }
                map.put("memberHeadImg", memberHeadImgArray); // 球队总成员数
                // 因为创建者自己也算一个人
                map.put("teamMemberCount", memberCount);
                jArrayTemp.add(map);
            }
        }
        return jArrayTemp;
    }

    //保存透传扩展信息到数据库
    public TransparentMessageList addTransparentTextExtMessage(Integer messageType, String fromUser, String toUser, String sendMessage, Date createTime, Date updateTime, boolean deleted) {
        TransparentMessageList transparentMessageList = new TransparentMessageList();
        transparentMessageList.setMessageType(messageType);
        transparentMessageList.setFromUser(fromUser);
        transparentMessageList.setToUser(toUser);
        transparentMessageList.setSendMessage(sendMessage);
        transparentMessageList.setCreateTime(createTime);
//        transparentMessageList.setUpdateTime(updateTime);
        transparentMessageList.setDeleted(deleted);
        return transparentMessageListDaoService.save(transparentMessageList);
    }

//    public List<String> userCountToTeamNameList(FootballTeamMemberUserCount userCount) throws JSONException {
//        List<FootballTeam> teamList = new ArrayList<>();
//        List<String> teamNameList = new ArrayList<>();
//        if (userCount != null) {
//            JSONObject ownerObject = JSON.parseObject(userCount.getOwnerTeamsArray());
//            JSONObject joinObject = JSON.parseObject(userCount.getJoinTeamsArray());
//            JSONObject fansObject = JSON.parseObject(userCount.getFansTeamsArray());
//            JSONArray ownerjArray = ownerObject.getJSONArray("ownerTeamsArray");
//            JSONArray joinjArray = joinObject.getJSONArray("joinTeamsArray");
//            JSONArray fansJArray = fansObject.getJSONArray("fansTeamsArray");
//            List<Long> teamIdList = new ArrayList<>();
//            if (ownerjArray != null && ownerjArray.size() > 0) {
//                for (int i = 0; i < ownerjArray.size(); i++) {
//                    teamIdList.add(Long.parseLong(ownerjArray.getString(i)));
//                }
//            }
//            if (joinjArray != null && joinjArray.size() > 0) {
//                for (int j = 0; j < joinjArray.size(); j++) {
//                    teamIdList.add(Long.parseLong(joinjArray.getString(j)));
//                }
//            }
//            if (fansJArray != null && fansJArray.size() > 0) {
//                for (int i = 0; i < fansJArray.size(); i++) {
//                    teamIdList.add(Long.parseLong(fansJArray.getString(i)));
//                }
//            }
//            if (teamIdList.size() > 0) {
//                teamList = footballTeamDaoService.findTeamsByTeamIdList(teamIdList);
//            }
//            if (teamList != null && teamList.size() > 0) {
//                for (FootballTeam team : teamList) {
//                    teamNameList.add(team.getTeamName());
//                }
//            }
//        }
//        return teamNameList;
//    }

    //封装比赛中主客队的权限和主客队实体类和比赛对应的赛事
    public Map<String, Object> getTeamGamePri(List<FootballTeamGame> teamGameList, User user) {
        Map<String, Object> resultMap = new HashMap<>();
        if (teamGameList != null && teamGameList.size() > 0) {
            List<Long> hostIdList = new ArrayList<>();
            List<Long> guestIdList = new ArrayList<>();
            List<FootballTeamMatch> matchList;
            List<Long> matchIdList = new ArrayList<>();
            Map<Long, FootballTeamMatch> matchMap = new HashMap<>();
            for (FootballTeamGame teamGame : teamGameList) {
                hostIdList.add(teamGame.getFootballTeam().getId());
                guestIdList.add(teamGame.getGuestTeamId());
                if (teamGame.getMatchId() != null) {
                    matchIdList.add(teamGame.getMatchId());
                }
            }
            matchList = footballTeamMatchDaoService.findByMatchIdList(matchIdList);
            if (matchList != null && matchList.size() > 0) {
                for (FootballTeamMatch match : matchList) {
                    matchMap.put(match.getId(), match);
                }
            }
            resultMap.put("match", matchMap);
            List<FootballTeam> hostTeamList = footballTeamDaoService.findTeamsByTeamIdList(hostIdList);
            List<FootballTeam> guestTeamList = footballTeamDaoService.findTeamsByTeamIdList(guestIdList);
            if (hostTeamList != null && hostTeamList.size() > 0) {
                resultMap.put("hostTeam", FootballTeamUtils.getFootballTeamMap(hostTeamList));
            }
            if (guestTeamList != null && guestTeamList.size() > 0) {
                resultMap.put("guestTeam", FootballTeamUtils.getFootballTeamMap(guestTeamList));
            }
            //主队权限
            List<Long> hostTeamIdList = TranslatorUtil.getIdList(hostTeamList, FootballTeam.class);
            List<FootballTeamUser> hostPriList = footballTeamUserDaoService.findByUserIdAndTeamIdList(user.getId(), hostTeamIdList);
            Map<String, String> hostPriMap = FootballTeamUtils.getPrimap(hostPriList);
            //客队权限
            List<Long> guestTeamIdList = TranslatorUtil.getIdList(guestTeamList, FootballTeam.class);
            List<FootballTeamUser> guestPriList = footballTeamUserDaoService.findByUserIdAndTeamIdList(user.getId(), guestTeamIdList);
            Map<String, String> guestPriMap = FootballTeamUtils.getPrimap(guestPriList);
            resultMap.put("hostRole", hostPriMap);
            resultMap.put("guestRole", guestPriMap);
        }
        return resultMap;
    }

    public DataRankingPojo rankingValue(List<FootballTeamGameStatisticsPlayer> statisticsList, String typeName) throws NoSuchMethodException, InvocationTargetException, IllegalAccessException {
        DataRankingPojo dataRankingPojo = new DataRankingPojo();
        Double sum = (double) 0;
        List<RankingPojo> rankingPojoList = new ArrayList<>();
        for (FootballTeamGameStatisticsPlayer statistics : statisticsList) {
            Method method = FootballTeamGameStatisticsPlayer.class.getMethod("get" + typeName.substring(0, 1).toUpperCase() + typeName.substring(1));
            Long value;
            if (method.invoke(statistics) instanceof Integer) {
                value = ((Integer) method.invoke(statistics)).longValue();
            } else if (method.invoke(statistics) instanceof Double) {
                value = ((Double) method.invoke(statistics)).longValue();
            } else {
                value = (Long) method.invoke(statistics);
            }

            if (value == null) {
                value = 0L;
            }
            Double doubleValue = value.doubleValue();
//            if ("wholeMoveDistance".equals(typeName)) {
//                doubleValue /= 1000.0;
//            }
            User user = userDaoService.findById(statistics.getUser().getId());
            UserHeadimg userHeadimg = userHeadimgDaoService.findLastByUserId(user.getId());
            String headImgNetUrl;
            if (userHeadimg == null || userHeadimg.getHeadImgNetUrl() == null) {
                headImgNetUrl = "";
            } else {
                headImgNetUrl = userHeadimg.getHeadImgNetUrl();
            }
            String nickName = user.getNickName();
            sum += doubleValue;
            RankingPojo rankingPojo = new RankingPojo();
            rankingPojo.setValue(doubleValue);
            rankingPojo.setNickName(nickName);
            rankingPojo.setHeadImgNetUrl(headImgNetUrl);
            rankingPojo.setUserId(user.getId());
            rankingPojoList.add(rankingPojo);
        }
        rankingPojoList.sort((o1, o2) -> {
            if (o1.getValue() > o2.getValue()) {
                return -1;
            }
            if (o1.getValue().equals(o2.getValue())) {
                return 0;
            }
            return 1;
        });
        dataRankingPojo.setSum(sum);
        dataRankingPojo.setRankingList(rankingPojoList);
        return dataRankingPojo;
    }

    //封装赛后统计关键球员
    public static JSONArray pickKeyPlayer(List<FootballTeamTakeNode> list, Map<Long, UserHeadimg> userImgMap) throws Exception {
        JSONArray array = new JSONArray();
        //射手王(进球)
        array = findMaxNode(list, userImgMap, "goalsfor", array);
        //助攻王
        array = findMaxNode(list, userImgMap, "assist", array);
        //过人王
        array = findMaxNode(list, userImgMap, "excel", array);
        //威胁王
        array = findMaxNode(list, userImgMap, "menace", array);
        //抢断王
        array = findMaxNode(list, userImgMap, "holdUp", array);
        //解围王
        array = findMaxNode(list, userImgMap, "save", array);
        return array;
    }

    public static JSONArray pickKeyPlayer(List<FootballTeamTakeNode> list, List<FootballTeamGameStatisticsPlayer> list1, Map<Long, UserHeadimg> userImgMap) throws Exception {
        JSONArray array = new JSONArray();
        //射手王(进球)
        array = findMaxNode(list, userImgMap, "goalsfor", array);
        //助攻王
        array = findMaxNode(list, userImgMap, "assist", array);
        //传球王
        array = findMaxPlayer(list1, userImgMap, "passBallCounts", array);
        //跑动王
        array = findMaxPlayer(list1, userImgMap, "wholeMoveDistance", array);
        //威胁王
        array = findMaxNode(list, userImgMap, "menace", array);
        //过人王
        array = findMaxNode(list, userImgMap, "excel", array);
        /*//抢断王
        array = findMaxNode(list, userImgMap, "holdUp", array);
        //解围王
        array = findMaxNode(list, userImgMap, "save", array);*/
        return array;
    }

    private static JSONArray findMaxNode(List<FootballTeamTakeNode> list, Map<Long, UserHeadimg> userImgMap, String propertyName, JSONArray array) throws Exception {
        FootballTeamTakeNode node = null;
        Map<String, Object> map = new HashMap<>();
        if (list != null && list.size() > 0) {
            String getName = "get" + TranslatorUtil.getGetter(propertyName);
            Method getter = list.get(0).getClass().getMethod(getName);
            for (FootballTeamTakeNode footballTeamTakeNode : list) {
                if (node == null) {
                    node = footballTeamTakeNode;
                } else {
                    Long count = getter.invoke(footballTeamTakeNode) == null ? Long.valueOf(0) : (Long) getter.invoke(footballTeamTakeNode);     //所有录入数据都是Long型
                    Long count2 = getter.invoke(node) == null ? Long.valueOf(0) : (Long) getter.invoke(node);

                    if (count > count2) {
                        node = footballTeamTakeNode;
                    } else if (count.equals(count2)) {    //如果相等取录入时间靠后的记录
                        if (footballTeamTakeNode.getCreateTime().getTime() >= node.getCreateTime().getTime()) {
                            node = footballTeamTakeNode;
                        }
                    }
                }
            }

            if (getter.invoke(node) != null && (Long) getter.invoke(node) > 0) {
                map.put("count", getter.invoke(node));
                map.put("nickName", node.getUser().getNickName());
                map.put("dataType", propertyName);
                if (userImgMap.containsKey(node.getUser().getId())) {
                    if (userImgMap.get(node.getUser().getId()).getHeadImgNetUrl() == null || userImgMap.get(node.getUser().getId()).getHeadImgNetUrl().equals("")) {
                        map.put("userImg", "https://www.microteam.cn/vsteam/upload/images/users/randomHeadImg/azhaer.png");
                    } else {
                        map.put("userImg", userImgMap.get(node.getUser().getId()).getHeadImgNetUrl());
                    }
                } else {
                    map.put("userImg", "https://www.microteam.cn/vsteam/upload/images/users/randomHeadImg/azhaer.png");
                }
                if (map.size() > 0) {
                    array.set(array.size(), map);
                }
            }
        }
        return array;
    }

    private static JSONArray findMaxPlayer(List<FootballTeamGameStatisticsPlayer> list, Map<Long, UserHeadimg> userImgMap, String propertyName, JSONArray array) throws Exception {
        FootballTeamGameStatisticsPlayer player = null;
        Map<String, Object> map = new HashMap<>();
        if (list != null && list.size() > 0) {
            String getName = "get" + TranslatorUtil.getGetter(propertyName);
            Method getter = list.get(0).getClass().getMethod(getName);
            Long count = 0L;
            for (FootballTeamGameStatisticsPlayer footballTeamGameStatisticsPlayer : list) {
                Long count1;
                if (player == null) {
                    if (getter.invoke(footballTeamGameStatisticsPlayer) instanceof Integer) {
                        count1 = ((Integer) getter.invoke(footballTeamGameStatisticsPlayer)).longValue();
                    } else {
                        count1 = (Long) getter.invoke(footballTeamGameStatisticsPlayer);
                    }
                    count = count1;
                    player = footballTeamGameStatisticsPlayer;
                } else {
                    if (getter.invoke(footballTeamGameStatisticsPlayer) instanceof Integer) {
                        count1 = ((Integer) getter.invoke(footballTeamGameStatisticsPlayer)).longValue();
                    } else {
                        count1 = (Long) getter.invoke(footballTeamGameStatisticsPlayer);
                    }

                    if (count1 > count) {
                        count = count1;
                        player = footballTeamGameStatisticsPlayer;
                    } else if (count1.equals(count)) {
                        if (footballTeamGameStatisticsPlayer.getCreateTime().getTime() <= player.getCreateTime().getTime()) {
                            count = count1;
                            player = footballTeamGameStatisticsPlayer;
                        }
                    }
                }
            }
            if (count > 0) {
                map.put("count", count);
                map.put("nickName", player.getUser().getNickName());
                map.put("dataType", propertyName);
                if (userImgMap.containsKey(player.getUser().getId())) {
                    map.put("userImg", userImgMap.get(player.getUser().getId()).getHeadImgNetUrl());
                } else {
                    map.put("userImg", "");
                }
                if (map.size() > 0) {
                    array.add(array.size(), map);
                }
            }
        }
        return array;
    }

    //user：登陆用户  team：所属球队  teamGame：比赛
    public PassBallOfUserPojo getPassBallOfUser(User user, FootballTeam team, FootballTeamGame teamGame, int size) {
        PassBallOfUserPojo pojo = new PassBallOfUserPojo();
        List<UserPojo> passList = new ArrayList<>();
        List<UserPojo> byPassList = new ArrayList<>();
        Map<Long, Integer> passMap = new LinkedHashMap<>();    //传球 key:userId  value:passCount
        Map<Long, Integer> byPassMap = new LinkedHashMap<>();  //被传球 key:userId  value:passCount
        pojo.setNickName(user.getNickName());
        UserHeadimg userHeadimg = userHeadimgDaoService.findLastByUserId(user.getId());
        String userImg = userHeadimg == null ? "" : userHeadimg.getHeadImgNetUrl();
        pojo.setUserImg(userImg);
        long userId = user.getId();
        try {
            JSONArray touchBallArray = userHardwareDataUtil.getTouchBallTime(team, teamGame); //拿到触球时间
            if (touchBallArray.size() > 0) {
                for (int i = 0; i < touchBallArray.size(); i++) {
                    JSONObject object = touchBallArray.getJSONObject(i);  // object中有三个字段：touchTime：触球时间 userId：用户id hardwareType：左右脚（1 left 2 right
                    if (object.getLong("userId") == userId) {
                        //计算主动传球
                        if (i < touchBallArray.size() - 1) {
                            long userId_next = touchBallArray.getJSONObject(i + 1).getLong("userId");
                            long touchTime_next = touchBallArray.getJSONObject(i + 1).getLong("touchTime");
                            long interval = Math.abs(object.getLong("touchTime") - touchTime_next);
                            if (userId_next != userId && interval <= FootballContants.PASSBALLCUTOFF) {     //发生了传球
                                if (!passMap.containsKey(userId_next)) {
                                    passMap.put(userId_next, 1);
                                } else {
                                    Integer pass = passMap.get(userId_next);
                                    pass++;
                                    passMap.put(userId_next, pass);
                                }
                            }
                        }
                        //计算被动传球
                        if (i > 0) {
                            long userId_befor = touchBallArray.getJSONObject(i - 1).getLong("userId");
                            long touchTime_befor = touchBallArray.getJSONObject(i - 1).getLong("touchTime");
                            long interval_by = Math.abs(object.getLong("touchTime") - touchTime_befor);
                            if (userId_befor != userId && interval_by <= FootballContants.PASSBALLCUTOFF) {       //发生了传球
                                if (!byPassMap.containsKey(userId_befor)) {
                                    byPassMap.put(userId_befor, 1);
                                } else {
                                    Integer byPass = byPassMap.get(userId_befor);
                                    byPass++;
                                    byPassMap.put(userId_befor, byPass);
                                }
                            }
                        }
                    }
                }
            }
            //将passMap和byPassMap从大到小排序 取前5个
            getUserPojoList(passMap, passList, size);
            getUserPojoList(byPassMap, byPassList, size);
            pojo.setPassList(passList);
            pojo.setByPassList(byPassList);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return pojo;
    }

    private void getUserPojoList(Map<Long, Integer> map, List<UserPojo> list, int size) {
        Map<Long, Integer> sortedMap = new TreeMap(new ValueComparator(map));
        for (Map.Entry<Long, Integer> entry : map.entrySet()) {
            sortedMap.put(entry.getKey(), entry.getValue());
        }

        for (Map.Entry<Long, Integer> entry : sortedMap.entrySet()) {
            UserPojo tempPojo = new UserPojo();
            User tempUser = userDaoService.findById(entry.getKey());
            UserHeadimg userHeadimg = userHeadimgDaoService.findLastByUserId(entry.getKey());
            String tempUserImg = "";
            if (userHeadimg != null) {
                tempUserImg = userHeadimg.getHeadImgNetUrl();
            }
            tempPojo.setNickName(tempUser.getNickName());
            tempPojo.setUserImg(tempUserImg);
            tempPojo.setPassCount(entry.getValue());
            list.add(tempPojo);
            if (list.size() >= size) {
                break;
            }
        }
    }

    public Map<String, Object> countKickBallAgility(List<UserHardwareData> userHardwareDataList, Map<String, Object> map, User user) {
        // ----统计完后保存到人比赛统计表,查看想双鞋子是否都同步了
        try {
            //触球曲线
            CurvePojo kickBallCurve = new CurvePojo();

            // 判断两个硬件是否都上传数据了
            if (userHardwareDataList != null && userHardwareDataList.size() > 0) {
                long leftFootStartTime = 0L; //左脚启动硬件时间
                long rightFootStartTime = 0L; //右脚启动硬件时间

                long leftFootEndTime = 0L;  //左脚上传数据结束时间
                long rightFootEndTime = 0L;  //右脚上传数据结束时间

                long leftFoot_kick_ball_startTime = 0L;  //左脚触球开始时间
                long rightFoot_kick_ball_startTime = 0L; //右脚触球开始时间

                long x_axis_startTime; //X轴开始时间
                long x_axis_endTime;   //X轴结束时间
                long x_kick_ball_startTime = 0L;   //X轴触球开始时间
                double play_game_time;  //用户比赛时间(分钟)

                Date tempKickDate;
                //获取硬件启动时间、数据上传时间和触球开始时间
                for (UserHardwareData hardwareData : userHardwareDataList) {
                    UserHardware tempUserHardware = hardwareData.getUserHardware();
                    if (tempUserHardware.getHardwareType() == 1) {  //1、left，2、right
                        leftFootStartTime = hardwareData.getCreateTime().getTime();
                        leftFootEndTime = hardwareData.getUpdateTime().getTime();

                        tempKickDate = hardwareData.getKickBallStartTime();
                        if (tempKickDate != null) {
                            leftFoot_kick_ball_startTime = tempKickDate.getTime();
                        }

                    } else {
                        rightFootStartTime = hardwareData.getCreateTime().getTime();
                        rightFootEndTime = hardwareData.getUpdateTime().getTime();

                        tempKickDate = hardwareData.getKickBallStartTime();
                        if (tempKickDate != null) {
                            rightFoot_kick_ball_startTime = tempKickDate.getTime();
                        }
                    }
                }


                long nTempStartTimeValue;
                long nTempEndTimeValue;
                double dTempValue;

                DecimalFormat intFormat = new java.text.DecimalFormat("#"); //#取整
                intFormat.setRoundingMode(RoundingMode.HALF_UP); //四舍五入

                //启动时间
                //硬件启动时间，取小的时间(单位：秒秒)
                if (leftFootStartTime > 0 && rightFootStartTime > 0) {
                    nTempStartTimeValue = Math.min(leftFootStartTime, rightFootStartTime); //取小的值（两个都不为0）
                } else {
                    nTempStartTimeValue = Math.max(leftFootStartTime, rightFootStartTime); //取大的值（其中有一个为0）
                }
                dTempValue = nTempStartTimeValue / 1000.0;
                x_axis_startTime = Long.parseLong(intFormat.format(dTempValue));

                //结束时间
                nTempEndTimeValue = Math.max(leftFootEndTime, rightFootEndTime);
//                if (leftFootEndTime > 0 && rightFootEndTime > 0) {
//                    nTempEndTimeValue = leftFootEndTime > rightFootEndTime ? leftFootEndTime : rightFootEndTime; //取大的值（两个都不为0）
//                } else {
//                    nTempEndTimeValue = leftFootEndTime > rightFootEndTime ? leftFootEndTime : rightFootEndTime; //取大的值（其中有一个为0）
//                }

                //数据上传结束时间，取大的时间(单位：秒)
                dTempValue = nTempEndTimeValue / 1000.0;
                x_axis_endTime = Long.parseLong(intFormat.format(dTempValue));

                //左脚触球时间(单位：秒)
                if (leftFoot_kick_ball_startTime > 0) {
                    dTempValue = leftFoot_kick_ball_startTime / 1000.0;
                    leftFoot_kick_ball_startTime = Long.parseLong(intFormat.format(dTempValue));
                }

                //右脚触球时间(单位：秒)
                if (rightFoot_kick_ball_startTime > 0) {
                    //右脚启动时间(单位：秒)
                    dTempValue = rightFoot_kick_ball_startTime / 1000.0;
                    rightFoot_kick_ball_startTime = Long.parseLong(intFormat.format(dTempValue));
                }

                //用户比赛的时间(单位：分钟)
                dTempValue = (x_axis_endTime - x_axis_startTime) / 60.0;
                /*DecimalFormat textFmt = new java.text.DecimalFormat("#.0");
                textFmt.setRoundingMode(RoundingMode.HALF_UP);
                play_game_time = Double.valueOf(textFmt.format(dTempValue));*/  //运动实际时间，四舍五入，#取整，  #.0取整一位小数
                play_game_time = dTempValue;

                List<Integer> leftKickBallList = new ArrayList<>();  //左脚的触球时间序列(秒)
                List<Integer> rightKickBallList = new ArrayList<>(); //左脚的触球时间序列(秒)

                //计算总触球次数(包含左脚和右脚)，和总的触球时间序列(包含左脚和右脚)
                for (UserHardwareData userHardwareData : userHardwareDataList) {
                    String kickBallData = userHardwareData.getKickBallData();

                    if (!"".equals(kickBallData) && kickBallData != null) {

                        // 查看是左脚还是右脚
                        UserHardware tempUserHardware = userHardwareData.getUserHardware();
                        if (tempUserHardware.getHardwareType() == 1) {
                            // 表示是左脚
                            JSONArray tempArray = JSONArray.parseArray(kickBallData);
                            //将所有触球的时间数据放到总的触球时间序列中
                            for (Object o : tempArray) {
                                leftKickBallList.add((Integer) o);
                            }
                        } else {
                            // 表示是右脚
                            JSONArray tempArray = JSONArray.parseArray(kickBallData);
                            //将所有触球的时间数据放到总的触球时间序列中
                            for (Object o : tempArray) {
                                rightKickBallList.add((Integer) o);
                            }

                        }
                    }
                }
                //左脚：将触球时间序列由小到大排列
                leftKickBallList.sort(Comparator.comparingInt(o -> o));

                //右脚：将触球时间序列由小到大排列
                rightKickBallList.sort(Comparator.comparingInt(o -> o));

                //计算触球曲线
                //左脚：计算时间区间的触球次数
                //Y坐标最大值样本数组
                int CLICK_BALL_COUNT = 600;  //触球数最大值数列
                int[] y_axis_max_value_array = new int[CLICK_BALL_COUNT];

                //Y轴最大值需要是3的倍数，因为图表的Y轴分为3段
                for (int i = 1; i <= CLICK_BALL_COUNT; i++) {
                    y_axis_max_value_array[i - 1] = 3 * i;
                }

                double x_interval_time;  //轴间隔时间

                //计算触球柱状图的数量
                if (play_game_time <= 10) { //小于10分钟
                    //1.用户比赛小于10分钟，则时间间隔按1分钟计算

                    DecimalFormat textFormat = new java.text.DecimalFormat("#"); //#取整
                    textFormat.setRoundingMode(RoundingMode.HALF_UP); //四舍五入
                    int sectionCount;
                    if (play_game_time > 1.0) {
                        //比赛的时间大于、等于1分钟
                        sectionCount = Integer.parseInt(textFormat.format(play_game_time)); //计算段数,四舍五入，#取整，  #.0取整一位小数
                    } else {
                        //比赛的时间小于1分钟，则按1分种算
                        sectionCount = 1;
                        play_game_time = 1; //如果小于1分，按一分钟来算
                    }
                    int[] totalKickBallCountArray1 = new int[sectionCount]; //触球数量，输出参数
                    double[] x_axis_value_array1 = new double[sectionCount];  //X轴坐标
                    //计算时间X轴上的刻度坐标，刻度区域数小于10个，以1分钟的单位作间隔
                    x_interval_time = 60; //时间间隔(秒)
                    for (int i = 0; i < sectionCount; i++) {
                        double tempValue = x_interval_time * (i + 1) / 60.0; //单位：分钟
                        x_axis_value_array1[i] = Double.parseDouble(textFormat.format(tempValue)); //X轴坐标取整, 四舍五入，#取整，  #.0取整一位小数
                    }
                    //输入参数：leftKickBallList, rightKickBallList, leftFoot_kick_ball_startTime, rightFoot_kick_ball_startTime
                    //输出参数：kickBallCount
                    calculateKickBallCount(leftKickBallList, rightKickBallList, leftFoot_kick_ball_startTime, rightFoot_kick_ball_startTime,
                            x_axis_startTime, x_axis_value_array1, totalKickBallCountArray1);
                    //设置对柱状图对象的参数
                    //输入参数： max_X, totalKickBallCountArray, y_axis_max_value_array, x_axis_value_array
                    //输入参数：kickBallCurve
                    double maxX = play_game_time;
                    makeKickBallCurve(play_game_time, totalKickBallCountArray1, y_axis_max_value_array, x_axis_value_array1, kickBallCurve);
                } else { //大于10分钟
                    //2.用户比赛大于10分钟，则时间需要计算，展示数据分为10段

                    //计算时间X轴上的刻度坐标，固定10区域个刻度
                    //int x_axis_value_array[] = new int [10];

                    int sectionCount = 10; //段数
                    int[] totalKickBallCountArray2 = new int[sectionCount];  //输出参数
                    double[] x_axis_value_array2 = new double[sectionCount];

                    x_interval_time = (x_axis_endTime - x_axis_startTime) / (double) 10; //时间间隔(秒)

                    DecimalFormat txtFormat = new java.text.DecimalFormat("#.0"); //取一位小数
                    txtFormat.setRoundingMode(RoundingMode.HALF_UP);

                    for (int i = 0; i < sectionCount; i++) {
                        double tempValue = x_interval_time * (i + 1) / 60.0; //单位：分钟
                        x_axis_value_array2[i] = Double.parseDouble(txtFormat.format(tempValue)); //X轴坐标取整, 四舍五入，#取整，  #.0取整一位小数
                    }

                    //输入参数：leftKickBallList, rightKickBallList, leftFoot_kick_ball_startTime, rightFoot_kick_ball_startTime
                    //输出参数：kickBallCountxg
                    calculateKickBallCount(leftKickBallList, rightKickBallList, leftFoot_kick_ball_startTime, rightFoot_kick_ball_startTime,
                            x_axis_startTime, x_axis_value_array2, totalKickBallCountArray2);
                    //设置对柱状图对象的参数
                    //输入参数： max_X, totalKickBallCountArray, y_axis_max_value_array, x_axis_value_array
                    //输入参数：kickBallCurve
                    makeKickBallCurve(play_game_time, totalKickBallCountArray2, y_axis_max_value_array, x_axis_value_array2, kickBallCurve);
                    //System.out.println(totalKickBallCountArray2);
                }
            }
            com.alibaba.fastjson.JSONObject json_data = JSONObject.parseObject(JSON.toJSONString(kickBallCurve, SerializerFeature.WriteMapNullValue));  //java对象转json
            map.put("kickBallAnalysisCurve", json_data); //触球分析曲线

        } catch (Exception e) {
            System.out.println(e.getMessage());
            // TODO Auto-generated catch block
            e.printStackTrace();
        }
        return map;
    }

    //功能：计算触球次数
    //输入能参数：
    // leftKickBallList   左脚触球时间数列
    // rightKickBallList  左脚触球时间数列
    // leftFoot_kick_ball_startTime  左脚触球开始时间
    // rightFoot_kick_ball_startTime 右脚触球开始时间
    // x_axis_startTime   坐标开始时间数组
    // x_axis_value_array 坐标课刻度时间数组
    //输出参数：
    // out_kickBallCount
    private void calculateKickBallCount(List<Integer> leftKickBallList, List<Integer> rightKickBallList, long leftFoot_kick_ball_startTime, long rightFoot_kick_ball_startTime,
                                        long x_axis_startTime, double[] x_axis_value_array, int[] out_kickBallCount) {
//        int leftKickBallCount = leftKickBallList.size();
//        int rightKickBallCount = rightKickBallList.size();

        for (int kickBallTime_second : leftKickBallList) {
            double dKickTime = ((leftFoot_kick_ball_startTime + kickBallTime_second) - x_axis_startTime) / 60.0; //要X轴的时间位置，单位：分钟
            double kickBallTime = Double.valueOf(new DecimalFormat("#.0").format(dKickTime)).doubleValue();

            if (kickBallTime <= x_axis_value_array[0]) {
                out_kickBallCount[0] += 1;
            } else if (kickBallTime <= x_axis_value_array[1]) {
                out_kickBallCount[1] += 1;
            } else if (kickBallTime <= x_axis_value_array[2]) {
                out_kickBallCount[2] += 1;
            } else if (kickBallTime <= x_axis_value_array[3]) {
                out_kickBallCount[3] += 1;
            } else if (kickBallTime <= x_axis_value_array[4]) {
                out_kickBallCount[4] += 1;
            } else if (kickBallTime <= x_axis_value_array[5]) {
                out_kickBallCount[5] += 1;
            } else if (kickBallTime <= x_axis_value_array[6]) {
                out_kickBallCount[6] += 1;
            } else if (kickBallTime <= x_axis_value_array[7]) {
                out_kickBallCount[7] += 1;
            } else if (kickBallTime <= x_axis_value_array[8]) {
                out_kickBallCount[8] += 1;
            } else if (kickBallTime <= x_axis_value_array[9]) {
                out_kickBallCount[9] += 1;
            }
        }

        //右脚：计算时间区间的触球次数
        for (int kickBallTime_second : rightKickBallList) {
            double dKickTime = ((rightFoot_kick_ball_startTime + kickBallTime_second) - x_axis_startTime) / 60.0; //要X轴的时间位置，单位：分钟
            double kickBallTime = Double.valueOf(new DecimalFormat("#.0").format(dKickTime)).doubleValue();

            if (kickBallTime <= x_axis_value_array[0]) {
                out_kickBallCount[0] += 1;
            } else if (kickBallTime <= x_axis_value_array[1]) {
                out_kickBallCount[1] += 1;
            } else if (kickBallTime <= x_axis_value_array[2]) {
                out_kickBallCount[2] += 1;
            } else if (kickBallTime <= x_axis_value_array[3]) {
                out_kickBallCount[3] += 1;
            } else if (kickBallTime <= x_axis_value_array[4]) {
                out_kickBallCount[4] += 1;
            } else if (kickBallTime <= x_axis_value_array[5]) {
                out_kickBallCount[5] += 1;
            } else if (kickBallTime <= x_axis_value_array[6]) {
                out_kickBallCount[6] += 1;
            } else if (kickBallTime <= x_axis_value_array[7]) {
                out_kickBallCount[7] += 1;
            } else if (kickBallTime <= x_axis_value_array[8]) {
                out_kickBallCount[8] += 1;
            } else if (kickBallTime <= x_axis_value_array[9]) {
                out_kickBallCount[9] += 1;
            }
        }
    }


    //设置曲线对象参数
    //输入参数：
    //max_X
    //totalKickBallCountArray
    //y_axis_max_value_array
    //x_axis_value_array

    //输入参数：
    //kickBallCurve
    private void makeKickBallCurve(double play_game_time, int[] totalKickBallCountArray, int[] y_axis_max_value_array, double[] x_axis_value_array, CurvePojo kickBallCurve) {
        //设置对柱状图对象的参数
        double maxX = 0.0;
        //Double[] tempArray2 = Arrays.stream( x_axis_value_array ).boxed().toArray(Double[]::new); // int[] 转 Integer[]
        //maxX = (double) Collections.max(Arrays.asList( tempArray2 )); //获取最大时间次数
        //kickBallCurve.setMaxX(maxX);

        DecimalFormat txtFormat = new java.text.DecimalFormat("#"); //时长,四舍五入取整数,
        txtFormat.setRoundingMode(RoundingMode.HALF_UP);
        double play_game_time_half_up = Double.parseDouble(txtFormat.format(play_game_time)); //X轴坐标取整, 四舍五入，#取整，  #.0取整一位小数

        kickBallCurve.setRealMin(play_game_time_half_up); //时长取整数，实际比赛时间

        kickBallCurve.setMaxX(play_game_time_half_up);  //设置X轴的最大从标

        // int[] arr2 = Arrays.stream(integers1).mapToInt(Integer::valueOf).toArray(); // Integer[] 转 int[]

        int maxY = 0;
        Integer[] tempArray = Arrays.stream(totalKickBallCountArray).boxed().toArray(Integer[]::new); // int[] 转 Integer[]
        int maxKickBallCount = Collections.max(Arrays.asList(tempArray)); //获取最大触球次数

        if (maxKickBallCount > 0) {
            for (int refer_y : y_axis_max_value_array) {
                if (maxKickBallCount < refer_y) {
                    maxY = refer_y;
                    break;
                }
            }
        }

        kickBallCurve.setMaxY(maxY);  //设置Y轴最大坐标

        List<Double> axisXList = new ArrayList<>(); //X轴(保留一位小数)

        // int[] 转 List<Integer>
        List<Integer> axisYlist = Arrays.stream(totalKickBallCountArray).boxed().collect(Collectors.toList());
        kickBallCurve.setAxisY(axisYlist);  //设置Y轴上的区域坐标列表

        int segment;
        if (maxX > 10.0) {
            //大于10分钟图表的段数为10
            segment = 10;

        } else {
            //计算小于10分钟图表的段数
            segment = x_axis_value_array.length;
        }

        if (segment > 0) {
            x_axis_value_array[segment - 1] = play_game_time_half_up;  //修整最后一个坐标的值为整数，与时长相同
        }

        for (int i = 0; i < segment; i++) {
            double x = x_axis_value_array[i];
            axisXList.add(x);
        }

        kickBallCurve.setAxisX(axisXList);  //X坐标

    }

}
