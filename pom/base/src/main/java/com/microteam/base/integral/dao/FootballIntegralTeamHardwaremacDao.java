package com.microteam.base.integral.dao;

import com.microteam.base.entity.integral.FootballIntegralTeamHardwaremac;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

public interface FootballIntegralTeamHardwaremacDao extends JpaRepository<FootballIntegralTeamHardwaremac, Long>, JpaSpecificationExecutor<FootballIntegralTeamHardwaremac> {

    @Query("from FootballIntegralTeamHardwaremac as integralTeamMac " +
            "where integralTeamMac.teamId = (:teamId) " +
            "and integralTeamMac.deleted = false " +
            "order by integralTeamMac.updateTime desc")
    List<FootballIntegralTeamHardwaremac> findByTeamId(@Param("teamId") Long teamId);

    @Query("from FootballIntegralTeamHardwaremac as integralTeamMac " +
            "where integralTeamMac.teamId = (:teamId) " +
            "and integralTeamMac.hardwareMac = (:mac) " +
            "and integralTeamMac.deleted = false " +
            "order by integralTeamMac.updateTime desc")
    List<FootballIntegralTeamHardwaremac> findByTeamIdAndMac(@Param("teamId") Long teamId, @Param("mac") String mac);
}
