//package com.microteam.base.common.config;
//
//import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
//import org.springframework.cloud.netflix.eureka.EnableEurekaClient;
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.annotation.ComponentScan;
//import org.springframework.context.annotation.Configuration;
//import org.springframework.transaction.annotation.EnableTransactionManagement;
//import org.springframework.web.client.RestTemplate;
//
//@Configuration
//@ComponentScan(value = "com.microteam")
//@EnableEurekaClient
//@EnableDiscoveryClient
//@EnableTransactionManagement
//public class DefaultConfiguration {
//
//
//}
