package com.microteam.base.common.pojo;

import javax.mail.Authenticator;

public class MyAuthenticator extends Authenticator implements java.io.Serializable {

    private String userName;
    private String password;

    public MyAuthenticator() {
    }

    public MyAuthenticator(String userName, String password) {
        super();
        this.userName = userName;
        this.password = password;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

}
