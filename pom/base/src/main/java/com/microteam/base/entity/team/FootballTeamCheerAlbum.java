package com.microteam.base.entity.team;


import com.microteam.base.common.base.BaseEntity;
import com.microteam.base.entity.user.User;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;
import java.io.Serializable;


@Entity
@Table(name = "football_team_cheer_album")
@Getter
@Setter
public class FootballTeamCheerAlbum extends BaseEntity implements Serializable {

    @ManyToOne
    @JoinColumn(name = "teamsId")
    private FootballTeam footballTeam;
    @ManyToOne
    @JoinColumn(name = "cheerId")
    private User user;
    @Column(name = "albumName", length = 100)
    private String albumName;
    @Column(name = "albumDesc", length = 200)
    private String albumDesc;

}
