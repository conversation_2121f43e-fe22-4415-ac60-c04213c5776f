
package com.microteam.base.lesson.service.impl;

import com.microteam.base.entity.lesson.FootballTeamLesson;
import com.microteam.base.lesson.dao.FootballTeamLessonDao;
import com.microteam.base.lesson.service.FootballTeamLessonDaoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service("footballTeamLessonDaoService")
public class FootballTeamLessonDaoServiceImpl implements FootballTeamLessonDaoService {


    @Autowired
    private FootballTeamLessonDao dao;

    public FootballTeamLessonDaoServiceImpl() {
        super();
    }

    @Override
    public List<FootballTeamLesson> findByLessonId(Long lessonId) {
        List<FootballTeamLesson> list = dao.findByLessonId(lessonId);
        if (list == null) {
            return new ArrayList<>();
        }
        return list;
    }

    @Override
    public List<FootballTeamLesson> findByTeamIdList(List<Long> teamIdList) {
        if (teamIdList != null && teamIdList.size() > 0) {
            return dao.findByTeamIdList(teamIdList);
        }
        return new ArrayList<>();
    }

    @Override
    public int deleteByLessonIdListAndTeamId(List<Long> lessonIdList, Long teamId) {
        if (lessonIdList != null && lessonIdList.size() > 0) {
            return dao.deleteByLessonIdListAndTeamId(lessonIdList, teamId);
        }
        return 0;
    }

    @Override
    public List<FootballTeamLesson> findByTeamId(Long teamId) {
        return dao.findByTeamId(teamId);
    }

    @Override
    public int deleteByTeamId(Long teamId) {
        return dao.deleteByTeamId(teamId);
    }

    @Override
    public List<Long> findAloneLessonIdListByTeamId(Long teamId) {
        return dao.findAloneLessonIdListByTeamId(teamId);
    }

    @Override
    public List<FootballTeamLesson> saveAll(List<FootballTeamLesson> list) {
        return dao.saveAll(list);
    }
}
