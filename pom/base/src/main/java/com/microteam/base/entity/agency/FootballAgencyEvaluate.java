package com.microteam.base.entity.agency;


import com.microteam.base.common.base.BaseEntity;
import com.microteam.base.entity.user.User;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;
import java.io.Serializable;

@Entity
@Table(name = "football_agency_evaluate")
@Getter
@Setter
public class FootballAgencyEvaluate extends BaseEntity implements Serializable {

    @ManyToOne
    @JoinColumn(name = "agencyId", nullable = false)
    private FootballAgency footballAgency;
    @ManyToOne
    @JoinColumn(name = "userId", nullable = false)
    private User user;
    @Column(name = "content", length = 300)
    private String content;
    @Column(name = "allowAnonymous")
    private Boolean allowAnonymous;
    @Column(name = "starLevel")
    private Short starLevel;

}
