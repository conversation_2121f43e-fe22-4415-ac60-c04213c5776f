package com.microteam.base.entity.match;

import com.microteam.base.common.base.BaseEntity;
import com.microteam.base.entity.user.User;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;
import java.io.Serializable;

@Entity
@Table(name = "football_team_match_new_comment")
@Getter
@Setter
public class FootballTeamMatchNewComment extends BaseEntity implements Serializable {

    @ManyToOne
    @JoinColumn(name = "userId", nullable = false)
    private User user;
    @ManyToOne
    @JoinColumn(name = "matchNewId", nullable = false)
    private FootballTeamMatchNew teamMatchNew;
    @Column(name = "comment", nullable = false, length = 65535)
    private String comment;

}
