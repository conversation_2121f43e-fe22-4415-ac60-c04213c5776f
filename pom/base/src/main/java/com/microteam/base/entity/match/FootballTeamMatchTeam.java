package com.microteam.base.entity.match;


import com.microteam.base.common.base.BaseEntity;
import com.microteam.base.entity.team.FootballTeam;
import com.microteam.base.entity.user.User;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;
import java.io.Serializable;

@Entity
@Table(name = "football_team_match_team")
@Getter
@Setter
public class FootballTeamMatchTeam extends BaseEntity implements Serializable {

    @ManyToOne
    @JoinColumn(name = "matchId", nullable = false)
    private FootballTeamMatch footballTeamMatch;
    @ManyToOne
    @JoinColumn(name = "creator", nullable = false)
    private User user;
    @ManyToOne
    @JoinColumn(name = "teamId", nullable = false)
    private FootballTeam footballTeam;
    @Column(name = "contestGroup")
    private Short contestGroup;

}
