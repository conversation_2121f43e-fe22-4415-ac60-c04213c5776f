package com.microteam.base.agency.service;

import com.microteam.base.entity.agency.FootballAgencyClassAbsence;

import java.util.List;

public interface FootballAgencyClassAbsenceDaoService {
    FootballAgencyClassAbsence findAbsenceById(long id);

    //分页查询请假表
    List<FootballAgencyClassAbsence> findByAgencyIdAndAgencyClassIdForPage(Long agencyId, Long agencyClassId, int pageSize, int page);

    //查询班级课程请假名单
    List<FootballAgencyClassAbsence> findByAgencyIdAndAgencyClassIdAndAgencyClassCourseId(Long agencyId, Long agencyClassId, Long agencyClassCourseId);
}
