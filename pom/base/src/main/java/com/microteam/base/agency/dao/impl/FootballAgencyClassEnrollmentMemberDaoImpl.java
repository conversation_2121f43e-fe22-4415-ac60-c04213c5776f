//package com.microteam.base.agency.dao.impl;
//
//import com.microteam.base.agency.dao.FootballAgencyClassEnrollmentMemberDao;
//import com.microteam.base.entity.agency.FootballAgencyClassEnrollmentMember;
//import com.microteam.base.common.base.AbstractHibernateDao;
//import org.apache.log4j.Logger;
//import org.hibernate.query.Query;
//import org.springframework.stereotype.Repository;
//
//import java.util.List;
//
//@Repository("footballAgencyClassEnrollmentMemberDao")
//public class FootballAgencyClassEnrollmentMemberDaoImpl extends AbstractHibernateDao<FootballAgencyClassEnrollmentMember> implements FootballAgencyClassEnrollmentMemberDao {
//    static Logger logger = Logger.getLogger(FootballAgencyClassEnrollmentMemberDaoImpl.class.getName());
//
//    public FootballAgencyClassEnrollmentMemberDaoImpl() {
//        super();
//        setClazz(FootballAgencyClassEnrollmentMember.class);
//    }
//
//    //查询用户报名列表
//    @Override
//    public List<FootballAgencyClassEnrollmentMember> findByEnrollmentIdAndRoleAndNickNameForPage(Long enrollmentId, short userRole, int page, int pageSize, String search) {
//        Query query = null;
//        String hql = "from FootballAgencyClassEnrollmentMember as agencyClassEnrollmentMember " +
//                "left join fetch agencyClassEnrollmentMember.user as user " +
//                "where agencyClassEnrollmentMember.footballAgencyClassEnrollment.id = (:enrollmentId) " +
//                "and agencyClassEnrollmentMember.userRole = (:userRole) ";
//        if (!"".equals(search)) {
//            hql += " and user.nickName like (:search) " +
//                    "and agencyClassEnrollmentMember.deleted=false";
//            query = getCurrentSession().createQuery(hql)
//                    .setParameter("enrollmentId", enrollmentId)
//                    .setParameter("userRole", userRole)
//                    .setParameter("search", "%" + search + "%");
//        } else {
//            hql += " and agencyClassEnrollmentMember.deleted=false";
//            query = getCurrentSession().createQuery(hql)
//                    .setParameter("enrollmentId", enrollmentId)
//                    .setParameter("userRole", userRole);
//        }
//        query.setFirstResult((page - 1) * pageSize);
//        query.setMaxResults(pageSize);
//        return (List<FootballAgencyClassEnrollmentMember>) query.list();
//    }
//
//    @Override
//    public FootballAgencyClassEnrollmentMember findById(long id) {
//        String hql = "from FootballAgencyClassEnrollmentMember as agencyClassEnrollmentMember " +
//                "left join fetch agencyClassEnrollmentMember.footballAgencyClassEnrollment as footballAgencyClassEnrollment " +
//                "left join fetch agencyClassEnrollmentMember.user as user " +
//                "where agencyClassEnrollmentMember.id=?";
//        Query query = getCurrentSession().createQuery(hql).setParameter(0, id);
//        List<FootballAgencyClassEnrollmentMember> list = query.list();
//        if (list != null && list.size() > 0) {
//            return list.get(0);
//        }
//        return null;
//    }
//
//    @Override
//    public List<FootballAgencyClassEnrollmentMember> findByEnrollmentIdAndRoleForPage(Long enrollmentId, short userRole, int page, int pageSize) {
//        Query query = null;
//        String hql = "from FootballAgencyClassEnrollmentMember as agencyClassEnrollmentMember " +
//                "left join fetch agencyClassEnrollmentMember.user as user " +
//                "where agencyClassEnrollmentMember.footballAgencyClassEnrollment.id = (:enrollmentId) " +
//                "and agencyClassEnrollmentMember.userRole = (:userRole) " +
//                "and agencyClassEnrollmentMember.deleted=false";
//        query = getCurrentSession().createQuery(hql)
//                .setParameter("enrollmentId", enrollmentId)
//                .setParameter("userRole", userRole);
//        query.setFirstResult((page - 1) * pageSize);
//        query.setMaxResults(pageSize);
//        List<FootballAgencyClassEnrollmentMember> list = query.list();
//
//        return list;
//    }
//
//    //查询报名表下的用户报名申请
//    @Override
//    public List<FootballAgencyClassEnrollmentMember> findByEnrollmentId(Long enrollmentId) {
//        String hql = "from FootballAgencyClassEnrollmentMember as agencyClassEnrollmentMember " +
//                "left join fetch agencyClassEnrollmentMember.user as user " +
//                "where agencyClassEnrollmentMember.footballAgencyClassEnrollment.id = (:enrollmentId) " +
//                "and agencyClassEnrollmentMember.userRole=1 " +
//                "and agencyClassEnrollmentMember.deleted=false ";
//        Query query = getCurrentSession().createQuery(hql)
//                .setParameter("enrollmentId", enrollmentId);
//        List<FootballAgencyClassEnrollmentMember> list = query.list();
//        return list;
//    }
//
//
//}
