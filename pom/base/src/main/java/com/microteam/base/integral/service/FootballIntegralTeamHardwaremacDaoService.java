package com.microteam.base.integral.service;


import com.microteam.base.entity.integral.FootballIntegralTeamHardwaremac;

import java.util.List;

public interface FootballIntegralTeamHardwaremacDaoService {

    List<FootballIntegralTeamHardwaremac> findByTeamId(Long teamId);

    List<FootballIntegralTeamHardwaremac> findByTeamIdAndMac(Long teamId, String mac);

    FootballIntegralTeamHardwaremac save(FootballIntegralTeamHardwaremac footballIntegralTeamHardwaremac);
}
