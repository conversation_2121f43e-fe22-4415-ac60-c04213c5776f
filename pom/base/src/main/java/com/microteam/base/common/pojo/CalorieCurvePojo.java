package com.microteam.base.common.pojo;


import com.alibaba.fastjson.JSONException;
import com.alibaba.fastjson.JSONObject;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class CalorieCurvePojo {
    private Integer one;
    private Integer two;
    private Integer three;
    private Integer four;
    private Integer five;
    private Integer six;
    private Integer seven;
    private Integer eight;

    public CalorieCurvePojo() {
        this.one = 0;
        this.two = 0;
        this.three = 0;
        this.four = 0;
        this.five = 0;
        this.six = 0;
        this.seven = 0;
        this.eight = 0;
    }

    public void toKcal() {
        this.one /= 1000;
        this.two /= 1000;
        this.three /= 1000;
        this.four /= 1000;
        this.five /= 1000;
        this.six /= 1000;
        this.seven /= 1000;
        this.eight /= 1000;
    }

    public void add(JSONObject jsonObject) throws JSONException {
        if (jsonObject.containsKey("one")) {
            this.one += Integer.parseInt(jsonObject.get("one").toString());
        }
        if (jsonObject.containsKey("two")) {
            this.two += Integer.parseInt(jsonObject.get("two").toString());
        }
        if (jsonObject.containsKey("three")) {
            this.three += Integer.parseInt(jsonObject.get("three").toString());
        }
        if (jsonObject.containsKey("four")) {
            this.four += Integer.parseInt(jsonObject.get("four").toString());
        }
        if (jsonObject.containsKey("five")) {
            this.five += Integer.parseInt(jsonObject.get("five").toString());
        }
        if (jsonObject.containsKey("six")) {
            this.six += Integer.parseInt(jsonObject.get("six").toString());
        }
        if (jsonObject.containsKey("seven")) {
            this.seven += Integer.parseInt(jsonObject.get("seven").toString());
        }
        if (jsonObject.containsKey("eight")) {
            this.eight += Integer.parseInt(jsonObject.get("eight").toString());
        }

    }

}
