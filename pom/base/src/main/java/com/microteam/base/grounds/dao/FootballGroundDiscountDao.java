package com.microteam.base.grounds.dao;

import com.microteam.base.entity.grounds.FootballGroundDiscount;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

public interface FootballGroundDiscountDao extends JpaRepository<FootballGroundDiscount, Long>, JpaSpecificationExecutor<FootballGroundDiscount> {
    // 查询特价，免费场地
//    List<FootballGroundDiscount> findByDiscountAndCodeForPage(String discount, int page, int pageSize, String cityCode, String countryCode);

    // 查询具体打折场地
    @Query("from FootballGroundDiscount as grounds " +
            "left join fetch grounds.footballGround as footballGrounds " +
            "left join fetch footballGrounds.user as user " +
            "left join fetch footballGrounds.footballGroundType as footballGroundsType " +
            "where grounds.id=(:id) " +
            "and grounds.deleted=false ")
    FootballGroundDiscount findById(@Param("id") long id);

    // 模糊查询特价场地
//    List<FootballGroundDiscount> findListDiscountGroundsBySearchContentAndCityOrCounty(String discountType, String cityCode, String countryCode, int page, int pageSize, String search);
}
