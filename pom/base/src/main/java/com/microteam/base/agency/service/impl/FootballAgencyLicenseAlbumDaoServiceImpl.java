package com.microteam.base.agency.service.impl;

import com.microteam.base.agency.dao.FootballAgencyLicenseAlbumDao;
import com.microteam.base.agency.service.FootballAgencyLicenseAlbumDaoService;
import com.microteam.base.entity.agency.FootballAgency;
import com.microteam.base.entity.agency.FootballAgencyLicenseAlbum;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service("footballAgencyLicenseAlbumDaoService")
public class FootballAgencyLicenseAlbumDaoServiceImpl implements FootballAgencyLicenseAlbumDaoService {
    static Logger logger = Logger.getLogger(FootballAgencyLicenseAlbumDaoServiceImpl.class.getName());
    @Autowired
    private FootballAgencyLicenseAlbumDao dao;

    public FootballAgencyLicenseAlbumDaoServiceImpl() {
        super();

    }

    //查询机构的营业执照
    @Override
    public List<FootballAgencyLicenseAlbum> findLicenseByAgency(
            FootballAgency footballAgency) {

        return dao.findLicenseByAgency(footballAgency);
    }

}
