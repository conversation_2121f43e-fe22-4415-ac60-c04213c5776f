package com.microteam.base.common.util.imServerManage.service.huanxinService;


import com.alibaba.fastjson.JSONArray;
import com.microteam.base.common.util.imServerManage.pojo.ImGroupRegisterRequestBodyPojo;
import com.microteam.base.common.util.imServerManage.pojo.ImResponseDatas;
import com.microteam.base.common.util.imServerManage.pojo.ImResponseEntities;
import com.microteam.base.entity.user.User;

import java.util.List;

/**
 * 环信的IM SERVER 接口service；；
 *
 * @param
 * @param
 * @return
 */
public interface ImServerHuanxinService {
    //注册环信用户，返回Im用户response
    ImResponseEntities[] registerUserImServer(User user);

    //给用户发文本消息
//    boolean sendTextMessageToMmbers(String mode, String message, List<String> memberArray, String fromImUsername);

    //发送透传消息
//    boolean sendTransparentTextMessageToMembers(String mode, String message, List<String> memberArray, String fromImUsername);

    //发送透传扩展消息
//    boolean sendTransparentTextExtMessageToMmbers(String mode, String message, List<String> memberArray, String fromImUsername);

    //注册环信群组
    ImResponseDatas registerGroupImServer(ImGroupRegisterRequestBodyPojo imGroupRegisterRequestBodyPojo);

    //添加一个 群组成员
    boolean addGroupMemberOneToImServer(String imGroupid, Long userid);

    //删除一个 群组成员
    boolean delGroupMemberOneToImServer(String imGroupid, Long userid);

    //转让群组
    boolean transferGroupOwner(String imGroupid, Long userid);

    void batchDelImUser(List<User> list);

    boolean sendUMengMessage(String message, List<String> memberArray);
}

