package com.microteam.base.entity.agency;


import com.microteam.base.common.base.BaseEntity;
import com.microteam.base.entity.user.Groups;
import com.microteam.base.entity.user.User;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

@Entity
@Table(name = "football_agency_class")
@Getter
@Setter
public class FootballAgencyClass extends BaseEntity implements Serializable {

    @ManyToOne
    @JoinColumn(name = "agencyId", nullable = false)
    private FootballAgency footballAgency;
    @ManyToOne
    @JoinColumn(name = "creator", nullable = false)
    private User user;
    @ManyToOne
    @JoinColumn(name = "groupId", nullable = false)
    private Groups groups;
    @Column(name = "name", nullable = false, length = 200)
    private String name;
    @Column(name = "logoImgNetUrl", length = 250)
    private String logoImgNetUrl;
    @Column(name = "logoImgUrl", length = 250)
    private String logoImgUrl;
    @Column(name = "logoImgName", length = 50)
    private String logoImgName;
    @Column(name = "logoImgLength")
    private Long logoImgLength;
    @Column(name = "stuAgeRange", length = 50)
    private String stuAgeRange;
    @Column(name = "trainContent", length = 65535)
    private String trainContent;
    @Column(name = "memberCount")
    private Integer memberCount;
    @Column(name = "honor", length = 65535)
    private String honor;
    @Column(name = "isOver")
    private Boolean isOver;
    @Column(name = "introduction", length = 65535)
    private String introduction;
    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "trainStartTime", length = 19)
    private Date trainStartTime;
    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "trainEndTime", length = 19)
    private Date trainEndTime;

    public Date getTrainStartTime() {
        if (this.trainStartTime == null) {
            return null;
        }
        return (Date) this.trainStartTime.clone();
    }

    public void setTrainStartTime(Date trainStartTime) {
        this.trainStartTime = (Date) trainStartTime.clone();
    }


    public Date getTrainEndTime() {
        if (this.trainEndTime == null) {
            return null;
        }
        return (Date) this.trainEndTime.clone();
    }

    public void setTrainEndTime(Date trainEndTime) {
        this.trainEndTime = (Date) trainEndTime.clone();
    }


}
