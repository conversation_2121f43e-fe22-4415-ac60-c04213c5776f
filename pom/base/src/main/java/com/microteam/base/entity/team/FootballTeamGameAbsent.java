package com.microteam.base.entity.team;


import com.microteam.base.common.base.BaseEntity;
import com.microteam.base.entity.user.User;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;
import java.io.Serializable;

@Entity
@Table(name = "football_team_game_absent")
@Getter
@Setter
public class FootballTeamGameAbsent extends BaseEntity implements Serializable {
    @ManyToOne
    @JoinColumn(name = "teamId", nullable = false)
    private FootballTeam footballTeam;
    @ManyToOne
    @JoinColumn(name = "teamGameId", nullable = false)
    private FootballTeamGame footballTeamGame;
    @ManyToOne
    @JoinColumn(name = "userRoleId", nullable = false)
    private FootballTeamMemberRole footballTeamMemberRole;
    @ManyToOne
    @JoinColumn(name = "userId", nullable = false)
    private User user;
    @Column(name = "absentReason", nullable = false, length = 200)
    private String absentReason;
    @Column(name = "absentDuration", length = 300)
    private String absentDuration;

}
