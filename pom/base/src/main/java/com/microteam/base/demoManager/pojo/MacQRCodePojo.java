package com.microteam.base.demoManager.pojo;

public class MacQ<PERSON><PERSON>Pojo {
    private String mac;
    private String qrCode;
    private String userName;

    public MacQRCodePojo() {

    }


    public MacQRCodePojo(String mac, String qrCode, String userName) {
        super();
        this.mac = mac;
        this.qrCode = qrCode;
        this.userName = userName;
    }


    public String getMac() {
        return mac;
    }


    public void setMac(String mac) {
        this.mac = mac;
    }


    public String getQrCode() {
        return qrCode;
    }


    public void setQrCode(String code) {
        this.qrCode = code;
    }


    public String getUserName() {
        return userName;
    }


    public void setUserName(String userName) {
        this.userName = userName;
    }


}
