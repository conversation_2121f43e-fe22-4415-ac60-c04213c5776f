package com.microteam.base.common.util.user;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONException;
import com.alibaba.fastjson.JSONObject;
import com.microteam.base.common.constant.FootballContants;
import com.microteam.base.common.pojo.CalorieCurveSumPojo;
import com.microteam.base.common.pojo.DataPojo;
import com.microteam.base.common.pojo.UserHardWarePojo;
import com.microteam.base.common.pojo.UserHardWareStepPojo;
import com.microteam.base.common.pojo.team.CurvePojo;
import com.microteam.base.common.pojo.team.PassBallOfUserPojo;
import com.microteam.base.common.pojo.team.UserPojo;
import com.microteam.base.common.util.TeamLostAndFreeUtil;
import com.microteam.base.common.util.common.ArithmeticUtil;
import com.microteam.base.common.util.team.FootballTeamGameUtils;
import com.microteam.base.common.util.team.FootballTeamUtils;
import com.microteam.base.entity.team.*;
import com.microteam.base.entity.user.*;
import com.microteam.base.team.service.*;
import com.microteam.base.user.service.UserHardwareDaoService;
import com.microteam.base.user.service.UserHardwareDataDaoService;
import com.microteam.base.user.service.UserHardwarePracticeDaoService;
import com.microteam.base.user.service.UserHeadimgDaoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.*;

@Component("userHardwareDataUtil")
public class UserHardwareDataUtil {
    @Autowired
    private FootballTeamDaoService footballTeamDaoService;
    @Autowired
    private PoloShirtDaoService poloShirtDaoService;
    @Autowired
    private FootballTeamGameStatisticsPlayerDaoService footballTeamGameStatisticsPlayerDaoService;
    @Autowired
    private FootballTeamGameTakeNodeDaoService teamGameTakeNodeDaoService;
    @Autowired
    private UserHardwareDaoService userHardwareDaoService;
    @Autowired
    private FootballTeamGameEnrollDaoService footballTeamGameEnrollDaoService;
    @Autowired
    private ArithmeticUtil arithmeticUtil;
    @Autowired
    private TeamLostAndFreeUtil teamLostAndFreeUtil;
    @Autowired
    private UserHardwareDataDaoService userHardwareDataDaoService;
    @Autowired
    private DataUtil dataUtil;
    @Autowired
    private FootballTeamUtils footballTeamUtils;
    @Autowired
    private UserHardwarePracticeDaoService userHardwarePracticeDaoService;
    @Autowired
    private UserHeadimgDaoService userHeadimgDaoService;

    public Map<String, Object> teamGameData(List<UserHardwareData> userHardwareDataList, User user, FootballTeamGame teamGame, FootballTeam team) throws JSONException {
        Map<String, Object> gameMap = new HashMap<>();
        UserDataUtil.packFileds(gameMap,userHardwareDataList);
        //个人卡路里 团队平均卡路里  个人跑动 带球跑动曲线
        List<CurvePojo> curvePojo = arithmeticUtil.getCurve(userHardwareDataList, teamGame, null, 10, user, "person");
        gameMap.put("moveCurve", FootballTeamGameUtils.packCurve(curvePojo.get(0)));
        gameMap.put("ballMoveCurve", FootballTeamGameUtils.packCurve(curvePojo.get(1)));
        gameMap.put("kalCurve", FootballTeamGameUtils.packCurve(curvePojo.get(2)));
        List<FootballTeamGameEnroll> enrollList = footballTeamGameEnrollDaoService.findByTeamIdAndGameId(team.getId(), teamGame.getId());
        CurvePojo teamAvgPojo;
        teamAvgPojo = curvePojo.get(3);
        int size = 0;
        for (FootballTeamGameEnroll footballTeamGameEnroll : enrollList) {
            size++;
            if (!footballTeamGameEnroll.getUser().getId().equals(user.getId())) {
                List<UserHardwareData> dataList = userHardwareDataDaoService.isStartupGame(teamGame.getId(), footballTeamGameEnroll.getUser().getId());
                if (dataList != null && dataList.size() > 0) {
                    CurvePojo kalCurve = arithmeticUtil.getCurve(dataList, teamGame, null, 10, footballTeamGameEnroll.getUser(), "person").get(3);
                    teamAvgPojo = FootballTeamGameUtils.mergeDiffGameCurvePojo(teamAvgPojo, kalCurve);
                }
            }
        }
        FootballTeamGameUtils.addUp(teamAvgPojo);
        teamAvgPojo = FootballTeamGameUtils.avgAxisY(teamAvgPojo, size);
        gameMap.put("teamAvgKalCurve", FootballTeamGameUtils.packCurve(teamAvgPojo));
        //No.1个人传球
        PassBallOfUserPojo passBallOfUserPojo = dataUtil.getPassBallOfUser(user, team, teamGame, 1);
        List<UserPojo> passList = passBallOfUserPojo.getPassList();        //主动传球人
        List<UserPojo> byPassList = passBallOfUserPojo.getByPassList();  //被动传球人
        gameMap.put("passList", JSON.parseArray(JSON.toJSONString(passList)));
        gameMap.put("byPassList", JSON.parseArray(JSON.toJSONString(byPassList)));
        //触球分析曲线数据
        gameMap = dataUtil.countKickBallAgility(userHardwareDataList, gameMap, user);
        //体能数据
        gameMap = arithmeticUtil.countPhysicalAgility(userHardwareDataList, gameMap, user);
        Map<String, Object> speedMap = DataUtil.fengzhuang(userHardwareDataList, user);
        List<Integer> array = (List<Integer>) speedMap.get("sortedDataArray");
        JSONArray highSpeedMoveDataArray = (JSONArray) speedMap.get("highMoveCalorieArray");
        JSONArray midSpeedMoveDataArray = (JSONArray) speedMap.get("midMoveCalorieArray");
        JSONArray lowSpeedMoveDataArray = (JSONArray) speedMap.get("lowMoveCalorieArray");
        JSONArray normalSpeedMoveDataArray = (JSONArray) speedMap.get("normalMoveCalorieArray");
        long kickballTime;
        kickballTime = (long) speedMap.get("kickballTime");
        JSONObject speedCurve = arithmeticUtil.countDribbleSpeed(array, 3, highSpeedMoveDataArray, midSpeedMoveDataArray, lowSpeedMoveDataArray, normalSpeedMoveDataArray, kickballTime, user.getHeight());
        gameMap.put("ballSpeed", speedCurve);   //球速数据分析
        //脚部触球数据
        JSONArray jsonarray = teamLostAndFreeUtil.packagingFootData(userHardwareDataList);
        gameMap.put("footData", jsonarray);
        int touchCount = 0;
        if (jsonarray != null && jsonarray.size() > 0) {
            for (int i = 0; i < jsonarray.size(); i++) {
                JSONObject object = jsonarray.getJSONObject(i);
                touchCount += (object.getInteger("exteriorData") + object.getInteger("instepKickingData") + object.getInteger("archData") +
                        object.getInteger("tiptoeData") + object.getInteger("heelData") + object.getInteger("soleFootData"));
            }
        }
        //比赛状态
        long gameStartTime = System.currentTimeMillis();
        long teamGameCompeteTime = teamGame.getCompetitionTime().getTime();
        long gameEndTime = teamGame.getFinishTime().getTime();
        if (gameEndTime < gameStartTime) {
            gameMap.put("gameStatus", 1);
        } else if (teamGameCompeteTime <= gameStartTime && gameStartTime <= gameEndTime) {
            gameMap.put("gameStatus", 2);
        } else if (teamGameCompeteTime > gameStartTime) {
            gameMap.put("gameStatus", 3);
        }
        //球衣编号
        FootballTeamGameEnroll enroll = footballTeamGameEnrollDaoService.findByTeamIdAndGameIdAndUserId(team.getId(), teamGame.getId(), user.getId());
        if (enroll != null) {
            gameMap.put("poloNumber", enroll.getNumber());
        } else {
            gameMap.put("poloNumber", 0);
        }
//        PoloShirt poloShirt = poloShirtDaoService.findPoloShirtByUserAndTeam(user, team);
//        if (poloShirt != null) {
//            gameMap.put("poloShirt", poloShirt.getId());
//            gameMap.put("poloNumber", poloShirt.getNumber()); //  2017/5/27 添加字段：球衣号码
//        }
        gameMap.put("hometeam_id", teamGame.getFootballTeam().getId());
        FootballTeam opponetTeam = footballTeamDaoService.findTeamsById(teamGame.getGuestTeamId());
        if (opponetTeam != null) {
            gameMap.put("opponet_id", opponetTeam.getId());
        } else {
            gameMap.put("opponet_id", 0);
        }
        gameMap.put("teamId", team.getId());
        gameMap.put("teamGameId", teamGame.getId());
        gameMap.put("competitionTime", teamGame.getCompetitionTime().getTime());
        gameMap.put("fieldLocation", teamGame.getFieldLocation());
        gameMap.put("location", teamGame.getLocation());
        gameMap.put("provinceCode", teamGame.getProvinceCode());
        gameMap.put("countryCode", teamGame.getCountryCode());
        gameMap.put("cityCode", teamGame.getCityCode());
        FootballTeam hostTeam = teamGame.getFootballTeam();
        gameMap.put("hostTeamName", hostTeam.getTeamName());
        gameMap.put("hostAbbreviation", hostTeam.getTeamAbbreviation());
        if (hostTeam.getTeamBadgeNetUrl() == null) {
            gameMap.put("hostTeamBadgeNetUrl", "");
        } else {
            gameMap.put("hostTeamBadgeNetUrl", hostTeam.getTeamBadgeNetUrl());
        }

        gameMap.put("hostTeamHeadImgNetUrl", hostTeam.getTeamHeadImgNetUrl());
        if (hostTeam.getTeamPennantNetUrl() == null) {
            gameMap.put("hostTeamPennantNetUrl", "");
        } else {
            gameMap.put("hostTeamPennantNetUrl", hostTeam.getTeamPennantNetUrl());
        }
        gameMap.put("isPractice", 2);
        gameMap.put("hostScore", teamGame.getHostScore() == null ? Integer.valueOf(-1) : teamGame.getHostScore());
        gameMap.put("guestScore", teamGame.getGuestScore() == null ? Integer.valueOf(-1) : teamGame.getGuestScore());
        FootballTeam guestTeam = footballTeamDaoService.findTeamsById(teamGame.getGuestTeamId());
        if (guestTeam != null) {
            gameMap.put("guestTeamName", guestTeam.getTeamName());
            gameMap.put("guestAbbreviation", guestTeam.getTeamAbbreviation());
            if (guestTeam.getTeamBadgeNetUrl() == null) {
                gameMap.put("guestTeamBadgeNetUrl", "");
            } else {
                gameMap.put("guestTeamBadgeNetUrl", guestTeam.getTeamBadgeNetUrl());
            }
            gameMap.put("guestTeamHeadImgNetUrl", guestTeam.getTeamHeadImgNetUrl());
            if (guestTeam.getTeamPennantNetUrl() == null) {
                gameMap.put("guestTeamPennantNetUrl", "");
            } else {
                gameMap.put("guestTeamPennantNetUrl", guestTeam.getTeamPennantNetUrl());
            }
        } else {
            gameMap.put("guestTeamName", teamGame.getOpponent());
            gameMap.put("guestAbbreviation", teamGame.getOpponent());
            gameMap.put("guestTeamBadgeNetUrl", "");
            gameMap.put("guestTeamHeadImgNetUrl", "");
            gameMap.put("guestTeamPennantNetUrl", "");
        }
        // 是否上传了数据
        FootballTeamGameStatisticsPlayer teamGameStatisticsPlayer = footballTeamGameStatisticsPlayerDaoService.findByTeamIdAndGameIdAndUserId(team.getId(), teamGame.getId(), user.getId());
        Map<String, Object> velocityMap = teamLostAndFreeUtil.maxVelocity(team, teamGame, user);
        gameMap.put("velocity", velocityMap.get("velocity"));
        gameMap.put("velocitySpeedUpOrDown", velocityMap.get("velocitySpeedUpOrDown"));
        gameMap.put("velocitySpeedRank", velocityMap.get("velocitySpeedRank"));
        if (teamGameStatisticsPlayer != null) {
            List<FootballTeamGameStatisticsPlayer> teamGameStatisticsPlayerList = footballTeamGameStatisticsPlayerDaoService.findByTeamIdAndGameId(team.getId(), teamGame.getId());
            if (teamGameStatisticsPlayerList != null && teamGameStatisticsPlayerList.size() > 0) {
                long wholeDistance = 0;
                int kickBallCount = 0;
                for (FootballTeamGameStatisticsPlayer teamGameStatisticsPlayerTemp : teamGameStatisticsPlayerList) {
                    if (teamGameStatisticsPlayerTemp != null) {
                        if (teamGameStatisticsPlayerTemp.getWholeMoveDistance() != null) {
                            wholeDistance += teamGameStatisticsPlayerTemp.getWholeMoveDistance();
                        }
                        if (teamGameStatisticsPlayerTemp.getTouchCounts() != null) {
                            kickBallCount += teamGameStatisticsPlayerTemp.getTouchCounts();
                        }
                    }// if
                    // teamGameStatisticsPlayerTemp!=null
                }
                /*
                 * DATA:2017-5-8
                 *获取跑动距离，带球距离球员集合
                 *START
                 */
                List<FootballTeamGameStatisticsPlayer> teamGameStatisticsPlayerListByMove;
                teamGameStatisticsPlayerListByMove = teamGameStatisticsPlayerList;
                /*
                 * int compare(Student o1, Student o2) 返回一个基本类型的整型，
                 * 返回负数表示：o1 小于o2，
                 * 返回0 表示：o1和o2相等，
                 * 返回正数表示：o1大于o2。
                 */
                teamGameStatisticsPlayerListByMove.sort((o1, o2) -> {
                    if (o1 != null && o2 != null && o1.getWholeMoveDistance() != null && o2.getWholeMoveDistance() != null) {
                        if (o1.getWholeMoveDistance() < o2.getWholeMoveDistance()) {
                            return 1;
                        }
                        if (Objects.equals(o1.getWholeMoveDistance(), o2.getWholeMoveDistance())) {
                            return 0;
                        }
                    }
                    return -1;
                });
                //转化为map，根据userid获取排名
                Map<String, String> RankMap = new HashMap<>();
                for (int l = 0; l < teamGameStatisticsPlayerListByMove.size(); l++) {
                    RankMap.put(teamGameStatisticsPlayerListByMove.get(l).getUser().getId().toString(), String.valueOf(l + 1));
                }
                List<FootballTeamGameStatisticsPlayer> teamGameStatisticsPlayerListByCarry;
                teamGameStatisticsPlayerListByCarry = teamGameStatisticsPlayerList;
                teamGameStatisticsPlayerListByCarry.sort((o1, o2) -> {
                    if (o1 != null && o2 != null && o1.getCarryDistance() != null && o2.getCarryDistance() != null) {
                        if (o1.getCarryDistance() < o2.getCarryDistance()) {
                            return 1;
                        }
                        if (Objects.equals(o1.getCarryDistance(), o2.getCarryDistance())) {
                            return 0;
                        }
                    }
                    return -1;
                });
                //转化为map，根据userid获取排名
                Map<String, String> RankMap1 = new HashMap<>();
                for (int l = 0; l < teamGameStatisticsPlayerListByCarry.size(); l++) {
                    RankMap1.put(teamGameStatisticsPlayerListByCarry.get(l).getUser().getId().toString(), String.valueOf(l + 1));
                }
                List<FootballTeamGameStatisticsPlayer> teamGameStatisticsPlayerListByPass;
                teamGameStatisticsPlayerListByPass = teamGameStatisticsPlayerList;
                teamGameStatisticsPlayerListByPass.sort((o1, o2) -> {
                    if (o1 != null && o2 != null && o1.getPassBallCounts() != null && o2.getPassBallCounts() != null) {
                        if (o1.getPassBallCounts() < o2.getPassBallCounts()) {
                            return 1;
                        }
                        if (Objects.equals(o1.getPassBallCounts(), o2.getPassBallCounts())) {
                            return 0;
                        }
                    }
                    return -1;
                });
                //转化为map，根据userid获取排名
                Map<String, String> RankMap2 = new HashMap<>();
                for (int l = 0; l < teamGameStatisticsPlayerListByPass.size(); l++) {
                    RankMap2.put(teamGameStatisticsPlayerListByPass.get(l).getUser().getId().toString(), String.valueOf(l + 1));
                }
                //上一场比赛
                List<FootballTeamGameStatisticsPlayer> footballTeamGameStatisticsPlayer;
                footballTeamGameStatisticsPlayer = footballTeamGameStatisticsPlayerDaoService.findBeforeIdByTeamIdAndUserId(team.getId(), user.getId(), teamGameStatisticsPlayer.getId());
//                FootballTeamTakeNode footballTeamTakeNode = new FootballTeamTakeNode();
//                footballTeamTakeNode.setFootballTeam(team);
//                footballTeamTakeNode.setFootballTeamGame(teamGame);
//                footballTeamTakeNode.setUser(user);
                //查询数据库，判断数据是否存在，不存在则创建新数据，存在则修改
//                List<FootballTeamTakeNode> FootballTeamTakeNodeList;
//                FootballTeamTakeNodeList = teamGameTakeNodeDaoService.findPersonTakeNode_New(user.getId(), teamGame.getId(), team.getId());
                FootballTeamTakeNode footballTeamTakeNode = teamGameTakeNodeDaoService.findPersonTakeNode_New(user.getId(), teamGame.getId(), team.getId());
                long goalsforSum = 0; //进球数
                long assistSum = 0;  //助攻
                long shootSum = 0;//射正
                long shootAsideSum = 0;//射偏
                long waveShotSum = 0;//浪射
                if (footballTeamTakeNode != null) {
                    goalsforSum = goalsforSum + footballTeamTakeNode.getGoalsfor();
                    assistSum = assistSum + footballTeamTakeNode.getAssist();
                    shootSum = shootSum + footballTeamTakeNode.getShoot();
                    waveShotSum = waveShotSum + footballTeamTakeNode.getWaveShot();
                    shootAsideSum = shootAsideSum + footballTeamTakeNode.getShootAside();
                }
                gameMap.put("shoots", (waveShotSum + shootSum + shootAsideSum) == 0 ? -1 : (waveShotSum + shootSum + shootAsideSum));//射门
                gameMap.put("goalsfor", goalsforSum == 0 ? -1 : goalsforSum);//进球
                gameMap.put("assist", assistSum == 0 ? -1 : assistSum);//助攻
                gameMap.put("allWholeMoveDistance", teamGameStatisticsPlayer.getWholeMoveDistance() == 0 ? Long.valueOf(-1) : teamGameStatisticsPlayer.getWholeMoveDistance());
                gameMap.put("allCarryDistance", teamGameStatisticsPlayer.getCarryDistance() == 0 ? Integer.valueOf(-1) : teamGameStatisticsPlayer.getCarryDistance());
                if (footballTeamGameStatisticsPlayer != null && footballTeamGameStatisticsPlayer.size() > 0 && null != footballTeamGameStatisticsPlayer.get(0).getWholeMoveDistance()) {
                    if (footballTeamGameStatisticsPlayer.get(0).getWholeMoveDistance() > teamGameStatisticsPlayer.getWholeMoveDistance()) {
                        //ranking 0 ：下降  1：上升    2：上场比赛没有数据
                        gameMap.put("allWholeMoveDistanceState", 0);
                    } else if (Objects.equals(footballTeamGameStatisticsPlayer.get(0).getWholeMoveDistance(), teamGameStatisticsPlayer.getWholeMoveDistance()) && teamGameStatisticsPlayer.getWholeMoveDistance() == 0) {
                        gameMap.put("allWholeMoveDistanceState", 2);
                    } else {
                        gameMap.put("allWholeMoveDistanceState", 1);
                    }
                } else {
                    gameMap.put("allWholeMoveDistanceState", 2);
                }
                //排名
                gameMap.put("allWholeMoveDistanceRank", RankMap.get(teamGameStatisticsPlayer.getUser().getId().toString()));

                if (footballTeamGameStatisticsPlayer != null && footballTeamGameStatisticsPlayer.size() > 0 && null != footballTeamGameStatisticsPlayer.get(0).getCarryDistance()) {
                    if (footballTeamGameStatisticsPlayer.get(0).getCarryDistance() > teamGameStatisticsPlayer.getCarryDistance()) {
                        //ranking 0 ：下降  1：上升    2：上场比赛没有数据
                        gameMap.put("allCarryDistanceState", 0);
                    } else if (Objects.equals(footballTeamGameStatisticsPlayer.get(0).getCarryDistance(), teamGameStatisticsPlayer.getCarryDistance()) && teamGameStatisticsPlayer.getCarryDistance() == 0) {
                        gameMap.put("allCarryDistanceState", 2);
                    } else {
                        gameMap.put("allCarryDistanceState", 1);
                    }
                } else {
                    gameMap.put("allCarryDistanceState", 2);
                }
                //排名
                gameMap.put("allCarryDistanceRank", RankMap1.get(teamGameStatisticsPlayer.getUser().getId().toString()));

                if (footballTeamGameStatisticsPlayer != null && footballTeamGameStatisticsPlayer.size() > 0 && null != footballTeamGameStatisticsPlayer.get(0).getPassBallCounts()) {
                    if (footballTeamGameStatisticsPlayer.get(0).getPassBallCounts() > teamGameStatisticsPlayer.getPassBallCounts()) {
                        //ranking 0 ：下降  1：上升    2：上场比赛没有数据
                        gameMap.put("passBallCountsState", 0);
                    } else if (Objects.equals(footballTeamGameStatisticsPlayer.get(0).getPassBallCounts(), teamGameStatisticsPlayer.getPassBallCounts()) && teamGameStatisticsPlayer.getPassBallCounts() == 0) {
                        gameMap.put("passBallCountsState", 2);
                    } else {
                        gameMap.put("passBallCountsState", 1);
                    }
                } else {
                    gameMap.put("passBallCountsState", 2);
                }
                //排名
                gameMap.put("passBallCountsRank", RankMap2.get(teamGameStatisticsPlayer.getUser().getId().toString()));
                //是否上传硬件数据，是否录入手记数据
                Map<String, Object> InputAndEnterMap;
                InputAndEnterMap = teamLostAndFreeUtil.whetherInput(team, teamGame);
                gameMap.put("upload", InputAndEnterMap.get("upload"));
                gameMap.put("enter", InputAndEnterMap.get("enter"));
                //队员在球队中的球衣编号
                PoloShirt poloShirt = poloShirtDaoService.findByUserIdAndTeamId(user.getId(), team.getId());
                if (poloShirt != null) {
                    gameMap.put("poloShirt", poloShirt.getNumber());
                }

                gameMap.put("teamAverageDistance", (long) Math.ceil(wholeDistance * 1.0 / teamGameStatisticsPlayerList.size()));
                gameMap.put("teamAverageKick", (int) Math.ceil(kickBallCount * 1.0 / teamGameStatisticsPlayerList.size()));
            } else {
                gameMap.put("teamAverageDistance", 0);
                gameMap.put("teamAverageKick", 0);
            }
            // 计算卡路里消耗时间
            String highMoveString = teamGameStatisticsPlayer.getHighMoveCalorie();
            String midMoveString = teamGameStatisticsPlayer.getMidMoveCalorie();
            String lowMoveString = teamGameStatisticsPlayer.getLowMoveCalorie();
            String normalMoveString = teamGameStatisticsPlayer.getNormalMoveCalorie();
            long maxAppearTime = 0;
            long minAppearTime = 0;
            // 遍历高中低走数据，找出上场最后时间,和最早上场时间
            List<Long> tempList3 = new ArrayList<>();
            List<Long> tempList2 = new ArrayList<>();
            JSONArray highMoveArray = null;
            if (!"".equals(highMoveString) && highMoveString != null) {
                highMoveArray = JSONArray.parseArray(highMoveString);
            }
            if (highMoveArray != null && highMoveArray.size() > 0) {
                for (int k = 0; k < highMoveArray.size(); k++) {
                    JSONObject tempObject = highMoveArray.getJSONObject(k);
                    long endTime = tempObject.getLong("endTime");
                    long startTime = tempObject.getLong("startTime");
                    tempList3.add(endTime);
                    tempList2.add(startTime);
                }
            }
            JSONArray midMoveArray = null;
            if (!"".equals(midMoveString) && midMoveString != null) {
                midMoveArray = JSONArray.parseArray(midMoveString);
            }
            if (midMoveArray != null && midMoveArray.size() > 0) {
                for (int k = 0; k < midMoveArray.size(); k++) {
                    JSONObject tempObject = midMoveArray.getJSONObject(k);
                    long endTime = tempObject.getLong("endTime");
                    long startTime = tempObject.getLong("startTime");
                    tempList3.add(endTime);
                    tempList2.add(startTime);
                }
            }
            JSONArray lowMoveArray = null;
            if (!"".equals(lowMoveString) && lowMoveString != null) {
                lowMoveArray = JSONArray.parseArray(lowMoveString);
            }
            if (lowMoveArray != null && lowMoveArray.size() > 0) {
                for (int k = 0; k < lowMoveArray.size(); k++) {
                    JSONObject tempObject = lowMoveArray.getJSONObject(k);
                    long endTime = tempObject.getLong("endTime");
                    long startTime = tempObject.getLong("startTime");
                    tempList3.add(endTime);
                    tempList2.add(startTime);
                }
            }
            JSONArray normalMoveArray = null;
            if (!"".equals(normalMoveString) && normalMoveString != null) {
                normalMoveArray = JSONArray.parseArray(normalMoveString);
            }
            if (normalMoveArray != null && normalMoveArray.size() > 0) {
                for (int k = 0; k < normalMoveArray.size(); k++) {
                    JSONObject tempObject = normalMoveArray.getJSONObject(k);
                    long endTime = tempObject.getLong("endTime");
                    long startTime = tempObject.getLong("startTime");
                    tempList3.add(endTime);
                    tempList2.add(startTime);
                }
            }
            // 遍历 取出最大的时间
            if (tempList3.size() > 0) {
                for (Long aTempList3 : tempList3) {
                    if (aTempList3 >= maxAppearTime) {
                        maxAppearTime = aTempList3;
                    }
                }// for
            }// if tempList.size()>0
            if (tempList2.size() > 0) {
                minAppearTime = tempList2.get(0);
                for (int k = 1; k < tempList2.size(); k++) {
                    if (tempList2.get(k) <= minAppearTime) {
                        minAppearTime = tempList2.get(k);
                    }
                }// for
            }// if tempList.size()>0
            // 遍历最早时间
            if (maxAppearTime != 0 && minAppearTime != 0) {
                gameMap.put("calorieConsumeTime", ((maxAppearTime - minAppearTime) > 60000) ? (maxAppearTime - minAppearTime) / 1000 / 60 : 1);
            } else {
                gameMap.put("calorieConsumeTime", 90);
            }
            // 计算卡路里消耗时间
            gameMap.put("carryTime", teamGameStatisticsPlayer.getCarryTime());
            gameMap.put("isUploadData", 1);// 上传数据
            gameMap.put("teamGameStatisticsPlayerId", teamGameStatisticsPlayer.getId());
            gameMap.put("passBallCounts", teamGameStatisticsPlayer.getPassBallCounts() == null ? Integer.valueOf(-1) : teamGameStatisticsPlayer.getPassBallCounts());
            gameMap.put("leftPassBallCounts", teamGameStatisticsPlayer.getLeftPassBallCounts() == null ? Integer.valueOf(-1) : teamGameStatisticsPlayer.getLeftPassBallCounts());
            gameMap.put("rightPassBallCounts", teamGameStatisticsPlayer.getRightPassBallCounts() == null ? Integer.valueOf(-1) : teamGameStatisticsPlayer.getRightPassBallCounts());
            gameMap.put("carryDistance", teamGameStatisticsPlayer.getCarryDistance());
            gameMap.put("touchCounts", touchCount);
            gameMap.put("carryCounts", teamGameStatisticsPlayer.getCarryCount());
            gameMap.put("oneFootPassCounts", teamGameStatisticsPlayer.getOneFootPassCount() == null ? Integer.valueOf(-1) : teamGameStatisticsPlayer.getOneFootPassCount());
            gameMap.put("singleShoots", teamGameStatisticsPlayer.getShoots() == null ? Integer.valueOf(0) : teamGameStatisticsPlayer.getShoots());
            gameMap.put("singleAssists", teamGameStatisticsPlayer.getAssists() == null ? Integer.valueOf(0) : teamGameStatisticsPlayer.getAssists());
            gameMap.put("wholeMoveDistance", teamGameStatisticsPlayer.getWholeMoveDistance());
            int highMoveCount = teamGameStatisticsPlayer.getHighMoveCount();
            if (teamGameStatisticsPlayer.getHighMoveDistance() == 0L) {
                highMoveCount = 0;
            }
            gameMap.put("highMoveCount", highMoveCount);
            gameMap.put("highMoveDistance", teamGameStatisticsPlayer.getHighMoveDistance());
            int midMoveCount = teamGameStatisticsPlayer.getMidMoveCount();
            if (teamGameStatisticsPlayer.getMidMoveDistance() == 0L) {
                midMoveCount = 0;
            }
            gameMap.put("midMoveCount", midMoveCount);
            gameMap.put("midMoveDistance", teamGameStatisticsPlayer.getMidMoveDistance());
            int lowMoveCount = teamGameStatisticsPlayer.getLowMoveCount();
            if (teamGameStatisticsPlayer.getLowMoveDistance() == 0L) {
                lowMoveCount = 0;
            }
            gameMap.put("lowMoveCount", lowMoveCount);
            gameMap.put("lowMoveDistance", teamGameStatisticsPlayer.getLowMoveDistance());
            int normalMoveCount = teamGameStatisticsPlayer.getNormalMoveCount();
            if (teamGameStatisticsPlayer.getNormalMoveDistance() == 0L) {
                normalMoveCount = 0;
            }
            gameMap.put("normalMoveCount", normalMoveCount);
            gameMap.put("normalMoveDistance", teamGameStatisticsPlayer.getNormalMoveDistance());
            if (teamGameStatisticsPlayer.getHighMoveCalorie() != null) {
                gameMap.put("highMoveCalorie", JSONArray.parseArray(teamGameStatisticsPlayer.getHighMoveCalorie()));
            } else {
                gameMap.put("highMoveCalorie", new JSONArray());
            }
            if (teamGameStatisticsPlayer.getMidMoveCalorie() != null) {
                gameMap.put("midMoveCalorie", JSONArray.parseArray(teamGameStatisticsPlayer.getMidMoveCalorie()));
            } else {
                gameMap.put("midMoveCalorie", new JSONArray());
            }
            if (teamGameStatisticsPlayer.getLowMoveCalorie() != null) {
                gameMap.put("lowMoveCalorie", JSONArray.parseArray(teamGameStatisticsPlayer.getLowMoveCalorie()));
            } else {
                gameMap.put("lowMoveCalorie", new JSONArray());
            }
            if (teamGameStatisticsPlayer.getNormalMoveCalorie() != null) {
                gameMap.put("normalMoveCalorie", JSONArray.parseArray(teamGameStatisticsPlayer.getNormalMoveCalorie()));
            } else {
                gameMap.put("normalMoveCalorie", new JSONArray());
            }
            if (teamGameStatisticsPlayer.getCalorieCurveData() != null) {
                gameMap.put("calorieCurveData", JSONObject.parseObject(teamGameStatisticsPlayer.getCalorieCurveData()));
                CalorieCurveSumPojo userCalorieCurveData = footballTeamUtils.userCalorieCurveData(teamGameStatisticsPlayer);
                gameMap.put("userCalorieCurveData", JSON.parseObject(JSON.toJSONString(userCalorieCurveData)));
                CalorieCurveSumPojo teamGameCalorieCurveData = footballTeamUtils.teamGameCalorieCurveData(teamGame.getId(), team.getId());
                gameMap.put("teamGameCalorieCurveData", JSON.parseObject(JSON.toJSONString(teamGameCalorieCurveData)));

            } else {
                gameMap.put("calorieCurveData", new JSONObject());
                CalorieCurveSumPojo userCalorieCurveData = footballTeamUtils.userCalorieCurveData(teamGameStatisticsPlayer);
                gameMap.put("userCalorieCurveData", JSON.parseObject(JSON.toJSONString(userCalorieCurveData)));
                gameMap.put("userTeamAverCalorieCurveData", new JSONObject());
            }
            gameMap.put("moveCalorie", teamGameStatisticsPlayer.getMoveCalorie());
            gameMap.put("uploadHardWareTime", teamGameStatisticsPlayer.getUpdateTime().getTime());
            gameMap.put("maxSprintSpeed", teamGameStatisticsPlayer.getMaxSprintSpeed());
            gameMap.put("highCarryCount", teamGameStatisticsPlayer.getHighCarryCount());
            gameMap.put("midCarryCount", teamGameStatisticsPlayer.getMidCarryCount());
            gameMap.put("lowCarryCount", teamGameStatisticsPlayer.getLowCarryCount());
            gameMap.put("normalCarryCount", teamGameStatisticsPlayer.getNormalCarryCount());
            Integer passBallError = teamGameStatisticsPlayer.getPassBallError();
            Integer passBallCount = teamGameStatisticsPlayer.getPassBallCounts();
            gameMap.put("passBallError", passBallError);
            if (passBallCount + passBallError != 0) {
                Double passBallErrorRate = passBallError.doubleValue() / (passBallError.doubleValue() + passBallCount.doubleValue());
                passBallErrorRate *= 100.0;
                gameMap.put("lostAndFreeRate", (double) Math.round(passBallErrorRate * 10) / 10);

                Double passBallRate = passBallCount.doubleValue() / (passBallError.doubleValue() + passBallCount.doubleValue());
                passBallRate *= 100.0;
                gameMap.put("passBallRate", (double) Math.round(passBallRate * 10) / 10);
            } else {
                gameMap.put("lostAndFreeRate", -1.0);
                gameMap.put("passBallRate", -1.0);
            }
            JSONArray hardwareArray = new JSONArray();
            for (UserHardwareData userHardwareData : userHardwareDataList) {
                Map<String, Object> map = new HashMap<>();
                map.put("userHardwareId", userHardwareData.getUserHardware().getId());
                map.put("hardwareType", userHardwareData.getUserHardware().getHardwareType());
                map.put("userHardwareDataId", userHardwareData.getId());
                map.put("hardwareMac", userHardwareData.getUserHardware().getHardwareMac());
                hardwareArray.add(map);
            }
            gameMap.put("hardwareArray", hardwareArray);
        } else {
            gameMap.put("isUploadData", 2);// 没有上传数据
            int isUploadData = 1;
            if (userHardwareDataList != null && userHardwareDataList.size() > 0) {
                for (UserHardwareData userHardwareData : userHardwareDataList) {
                    if (userHardwareData.getIsStartUp() != 3) {
                        isUploadData = 2;
                        break;
                    }
                }
                gameMap.put("isUploadData", isUploadData);
                CalorieCurveSumPojo userCalorieCurveData = footballTeamUtils.userCalorieCurveData(teamGameStatisticsPlayer);
                gameMap.put("userCalorieCurveData", JSON.parseObject(JSON.toJSONString(userCalorieCurveData)));
                gameMap.put("allWholeMoveDistanceRank", -1);
                Map<String, Object> InputAndEnterMap;
                InputAndEnterMap = teamLostAndFreeUtil.whetherInput(team, teamGame);
                gameMap.put("upload", InputAndEnterMap.get("upload"));
                gameMap.put("enter", InputAndEnterMap.get("enter"));

            }
            CalorieCurveSumPojo teamGameCalorieCurveData = footballTeamUtils.teamGameCalorieCurveData(teamGame.getId(), team.getId());
            gameMap.put("teamGameCalorieCurveData", JSON.parseObject(JSON.toJSONString(teamGameCalorieCurveData)));
            JSONArray hardwareArray = new JSONArray();
            // 封装硬件信息
            for (UserHardwareData userHardwareData : userHardwareDataList) {
                UserHardware userHardware = userHardwareData.getUserHardware();
                if (userHardware != null) {
                    /*
                     * 20170406
                     * 增加限制条件 只返回用户当前绑定的硬件信息
                     * start
                     */
                    List<UserHardware> userHardware_list = userHardwareDaoService.findByUserId(user.getId());
                    for (UserHardware anUserHardware_list : userHardware_list) {
                        long nowUserHardwareId = anUserHardware_list.getId();
                        long tempUserHardwareId = userHardware.getId();
                        if (nowUserHardwareId == tempUserHardwareId) {
                            /*
                             * 20170406
                             * 增加限制条件 只返回用户当前绑定的硬件信息
                             * end
                             */
                            Map<String, Object> map = new HashMap<>();
                            map.put("userHardwareId", userHardware.getId());
                            map.put("hardwareType", userHardware.getHardwareType());
                            map.put("userHardwareDataId", userHardwareData.getId());
                            map.put("hardwareMac", userHardware.getHardwareMac());
                            hardwareArray.add(map);
                        }
                    }
                }//
            }// for
            List<FootballTeamGameStatisticsPlayer> teamGameStatisticsPlayerList = footballTeamGameStatisticsPlayerDaoService.findByTeamIdAndGameId(team.getId(), teamGame.getId());
            if (teamGameStatisticsPlayerList != null && teamGameStatisticsPlayerList.size() > 0) {
                long wholeDistance = 0;
                int kickBallCount = 0;
                for (FootballTeamGameStatisticsPlayer teamGameStatisticsPlayerTemp : teamGameStatisticsPlayerList) {
                    if (teamGameStatisticsPlayerTemp != null) {
                        if (teamGameStatisticsPlayerTemp.getWholeMoveDistance() != null) {
                            wholeDistance += teamGameStatisticsPlayerTemp.getWholeMoveDistance();
                        }
                        if (teamGameStatisticsPlayerTemp.getTouchCounts() != null) {
                            kickBallCount += teamGameStatisticsPlayerTemp.getTouchCounts();
                        }
                    }// if
                    // teamGameStatisticsPlayerTemp!=null
                }
                gameMap.put("teamAverageDistance", (long) Math.ceil(wholeDistance * 1.0 / teamGameStatisticsPlayerList.size()));
                gameMap.put("teamAverageKick", (int) Math.ceil(kickBallCount * 1.0 / teamGameStatisticsPlayerList.size()));
            } else {
                gameMap.put("teamAverageDistance", 0);
                gameMap.put("teamAverageKick", 0);
            }
            // 计算卡路里消耗时间
            gameMap.put("calorieConsumeTime", 0);
            // 计算卡路里消耗时间
            gameMap.put("teamGameStatisticsPlayerId", 0);
            gameMap.put("carryTime", 0);
            gameMap.put("hardwareArray", hardwareArray);
            gameMap.put("passBallCounts", -1);
            gameMap.put("leftPassBallCounts", -1);
            gameMap.put("rightPassBallCounts", -1);
            gameMap.put("touchCounts", 0);
            gameMap.put("carryCounts", 0);
            gameMap.put("oneFootPassCounts", -1);
            gameMap.put("singleShoots", 0);
            gameMap.put("singleAssists", 0);
            gameMap.put("wholeMoveDistance", 0L);
            gameMap.put("highMoveCount", 0);
            gameMap.put("highMoveDistance", 0);
            gameMap.put("midMoveCount", 0);
            gameMap.put("midMoveDistance", 0);
            gameMap.put("lowMoveCount", 0);
            gameMap.put("lowMoveDistance", 0);
            gameMap.put("normalMoveCount", 0);
            gameMap.put("normalMoveDistance", 0);
            gameMap.put("highMoveCalorie", new JSONArray());
            gameMap.put("midMoveCalorie", new JSONArray());
            gameMap.put("lowMoveCalorie", new JSONArray());
            gameMap.put("normalMoveCalorie", new JSONArray());
            gameMap.put("calorieCurveData", new JSONObject());
            /*gameMap.put("userCalorieCurveData", new JSONObject());*/
            gameMap.put("userTeamAverCalorieCurveData", new JSONObject());
            gameMap.put("moveCalorie", 0);
            gameMap.put("uploadHardWareTime", 0);
            gameMap.put("carryDistance", 0);
            gameMap.put("shoots", -1);//射门
            gameMap.put("goalsfor", -1);//进球
            gameMap.put("assist", -1);//助攻
            gameMap.put("allWholeMoveDistance", -1);
            gameMap.put("allCarryDistance", -1);
            gameMap.put("allWholeMoveDistanceState", 2);
            gameMap.put("allCarryDistanceState", 2);
            gameMap.put("allCarryDistanceRank", -1);
            gameMap.put("passBallCountsState", 2);
            gameMap.put("passBallCountsRank", -1);
            /*gameMap.put("velocityState", 2);
            gameMap.put("velocity", -1);
            gameMap.put("velocityTime", -1);*/
//            gameMap.put("lostAndFreeRank", -1);
//            gameMap.put("lostAndFreeRate", -1);
//            gameMap.put("lostAndFreeState", 2);
            gameMap.put("footData", new JSONArray());
            gameMap.put("maxSprintSpeed", 0.0);
            gameMap.put("highCarryCount", 0);
            gameMap.put("midCarryCount", 0);
            gameMap.put("lowCarryCount", 0);
            gameMap.put("normalCarryCount", 0);
            gameMap.put("passBallError", -1);
            gameMap.put("lostAndFreeRate", -1.0);
            gameMap.put("passBallRate", -1.0);
        }
        //计算用户有球无球的数据
        if (teamGameStatisticsPlayer != null) {
            //放入有球数据
            gameMap.put("haveballDistance", teamGameStatisticsPlayer.getCarryDistance());
            gameMap.put("highHaveballDistance", teamGameStatisticsPlayer.getHighCarryDistance());
            gameMap.put("midHaveballDistance", teamGameStatisticsPlayer.getMidCarryDistance());
            gameMap.put("lowHaveballDistance", teamGameStatisticsPlayer.getLowCarryDistance());
            gameMap.put("normalHaveballDistance", teamGameStatisticsPlayer.getNormalCarryDistance());

            //放入无球数据
            gameMap.put("NotballDistance", teamGameStatisticsPlayer.getWholeMoveDistance() - teamGameStatisticsPlayer.getCarryDistance());
            gameMap.put("highNotballDistance", teamGameStatisticsPlayer.getHighMoveDistance() - teamGameStatisticsPlayer.getHighCarryDistance());
            gameMap.put("midNotballDistance", teamGameStatisticsPlayer.getMidMoveDistance() - teamGameStatisticsPlayer.getMidCarryDistance());
            gameMap.put("lowNotballDistance", teamGameStatisticsPlayer.getLowMoveDistance() - teamGameStatisticsPlayer.getLowCarryDistance());
            gameMap.put("normalNotballDistance", teamGameStatisticsPlayer.getNormalMoveDistance() - teamGameStatisticsPlayer.getNormalCarryDistance());
        } else {
            //放入有球数据
            gameMap.put("haveballDistance", 0);
            gameMap.put("highHaveballDistance", 0);
            gameMap.put("midHaveballDistance", 0);
            gameMap.put("lowHaveballDistance", 0);
            gameMap.put("normalHaveballDistance", 0);

            //放入无球数据
            gameMap.put("NotballDistance", 0);
            gameMap.put("highNotballDistance", 0);
            gameMap.put("midNotballDistance", 0);
            gameMap.put("lowNotballDistance", 0);
            gameMap.put("normalNotballDistance", 0);
        }

        Integer stepCount;
        stepCount = FootballTeamGameUtils.getStepCount(teamGameStatisticsPlayer);
        gameMap.put("stepCount", stepCount);
        List<FootballTeamGameStatisticsPlayer> statisticsPlayerList = footballTeamGameStatisticsPlayerDaoService.findGameByUserId(user.getId());
        Long historyWholeMoveDistance = 0L;
        Integer historyPassBallCounts = 0;
        Integer historyCarryDistance = 0;
        Double historyMaxSprintSpeed = 0.0;
        Integer historyStepCount = 0;
        Integer passBallSum = 0;
        Integer passBallErrorSum = 0;
        Double passBallErrorRate = (Double) gameMap.get("lostAndFreeRate");
        Double passBallRate = (Double) gameMap.get("passBallRate");
        Integer lostAndFreeRank = 1;
        Integer maxSprintSpeedRank = 1;
        Integer passBallRateRank = 1;
        Integer stepCountRank = 1;

        Long wholeMoveDistance = (Long) gameMap.get("wholeMoveDistance");
        Integer passBallCounts = (Integer) gameMap.get("passBallCounts");
        Integer carryDistance = (Integer) gameMap.get("carryDistance");
        Double maxSprintSpeed = (Double) gameMap.get("maxSprintSpeed");
        for (FootballTeamGameStatisticsPlayer statisticsPlayer : statisticsPlayerList) {
            historyWholeMoveDistance += statisticsPlayer.getWholeMoveDistance();
            historyPassBallCounts += statisticsPlayer.getPassBallCounts();
            historyCarryDistance += statisticsPlayer.getCarryDistance();
            historyMaxSprintSpeed += statisticsPlayer.getMaxSprintSpeed() == null ? Double.valueOf(0) : statisticsPlayer.getMaxSprintSpeed();
            Integer tmpPassBallCounts = statisticsPlayer.getPassBallCounts();
            Integer tmpPassBallError = statisticsPlayer.getPassBallError() == null ? Integer.valueOf(0) : statisticsPlayer.getPassBallError();
            passBallSum += tmpPassBallCounts;
            passBallErrorSum += tmpPassBallError;
            historyStepCount += FootballTeamGameUtils.getStepCount(statisticsPlayer);
        }

        List<FootballTeamGameStatisticsPlayer> teamGameStatisticsPlayerList = footballTeamGameStatisticsPlayerDaoService.findByTeamIdAndGameId(team.getId(), teamGame.getId());
        for (FootballTeamGameStatisticsPlayer statisticsPlayer : teamGameStatisticsPlayerList) {
            Integer tmpPassBallCounts = statisticsPlayer.getPassBallCounts();
            Integer tmpPassBallError = statisticsPlayer.getPassBallError();
            if (tmpPassBallCounts + tmpPassBallError != 0) {
                Double tmpPassBallErrorRate = tmpPassBallError.doubleValue() / (tmpPassBallError.doubleValue() + tmpPassBallCounts.doubleValue());
                tmpPassBallErrorRate *= 100.0;
                tmpPassBallErrorRate = (double) Math.round(tmpPassBallErrorRate * 10) / 10;
                if (tmpPassBallErrorRate > passBallErrorRate) {
                    lostAndFreeRank++;
                }

                Double tmpPassBallRate = tmpPassBallCounts.doubleValue() / (tmpPassBallError.doubleValue() + tmpPassBallCounts.doubleValue());
                tmpPassBallRate *= 100.0;
                tmpPassBallRate = (double) Math.round(tmpPassBallRate * 10) / 10;
                if (tmpPassBallRate > passBallRate) {
                    passBallRateRank++;
                }
            } else {
                lostAndFreeRank++;
                passBallRateRank++;
            }
            Double tmpMaxSprintSpeed = statisticsPlayer.getMaxSprintSpeed();
            if (tmpMaxSprintSpeed > maxSprintSpeed) {
                maxSprintSpeedRank++;
            }

            Integer tmpStepCount = FootballTeamGameUtils.getStepCount(statisticsPlayer);
            if (tmpStepCount > stepCount) {
                stepCountRank++;
            }
        }
        if (passBallErrorRate.equals(-1.0)) {
            gameMap.put("lostAndFreeRank", -1);
            gameMap.put("passBallRateRank", -1);
        } else {
            gameMap.put("lostAndFreeRank", lostAndFreeRank);
            gameMap.put("passBallRateRank", passBallRateRank);
        }
        gameMap.put("maxSprintSpeedRank", maxSprintSpeedRank);
        gameMap.put("stepCountRank", stepCountRank);
        Integer length = statisticsPlayerList.size();
        if (length > 0) {
            historyWholeMoveDistance /= length;
            historyPassBallCounts /= length;
            historyCarryDistance /= length;
            historyMaxSprintSpeed /= length;
            historyStepCount /= length;
        }
        gameMap.put("wholeMoveDistanceUpOrDown", wholeMoveDistance.compareTo(historyWholeMoveDistance) + 1);
        gameMap.put("passBallCountsUpOrDown", passBallCounts.compareTo(historyPassBallCounts) + 1);
        gameMap.put("carryDistanceUpOrDown", carryDistance.compareTo(historyCarryDistance) + 1);
        gameMap.put("maxSprintSpeedUpOrDown", maxSprintSpeed.compareTo(historyMaxSprintSpeed) + 1);
        gameMap.put("stepCountUpOrDown", stepCount.compareTo(historyStepCount) + 1);
        if (passBallSum + passBallErrorSum != 0 && passBallErrorRate != -1) {
            Double historyPassBallErrorRate = passBallErrorSum.doubleValue() / (passBallErrorSum.doubleValue() + passBallSum.doubleValue());
            historyPassBallErrorRate *= 100.0;
            historyPassBallErrorRate = (double) Math.round(historyPassBallErrorRate * 10) / 10;
            gameMap.put("passBallErrorRateUpOrDown", passBallErrorRate.compareTo(historyPassBallErrorRate) + 1);

            Double historyPassBallRate = passBallSum.doubleValue() / (passBallErrorSum.doubleValue() + passBallSum.doubleValue());
            historyPassBallRate *= 100.0;
            historyPassBallRate = (double) Math.round(historyPassBallRate * 10) / 10;
            gameMap.put("passBallRateUpOrDown", passBallErrorRate.compareTo(historyPassBallRate) + 1);
        } else {
            gameMap.put("passBallErrorRateUpOrDown", 1);
            gameMap.put("passBallRateUpOrDown", 1);
        }

        return gameMap;
    }


    public Map<String, Object> practiceData(List<UserHardwareData> userHardwareDataList, UserHardwarePractice userHardwarePractice, User user) throws JSONException {
        // 练习赛
        Map<String, Object> gameMap = new HashMap<>();
        //个人卡路里 个人历史消耗  团队平均卡路里 个人跑动 带球跑动曲线
        List<CurvePojo> curvePojo = arithmeticUtil.getCurve(userHardwareDataList, null, userHardwarePractice, 10, user, "person");
        gameMap.put("moveCurve", FootballTeamGameUtils.packCurve(curvePojo.get(0)));
        gameMap.put("ballMoveCurve", FootballTeamGameUtils.packCurve(curvePojo.get(1)));
        gameMap.put("kalCurve", FootballTeamGameUtils.packCurve(curvePojo.get(2)));
        CurvePojo historyCurvePojo;
        historyCurvePojo = curvePojo.get(3);
        List<UserHardwarePractice> historyPractices = userHardwarePracticeDaoService.findByUserIdBeforePracticeId(user.getId(), userHardwarePractice.getId());
        for (UserHardwarePractice historyPractice : historyPractices) {
            if (!historyPractice.getId().equals(userHardwarePractice.getId())) {
                List<UserHardwareData> dataList = userHardwareDataDaoService.isStartupPractice(historyPractice.getId(), user.getId());
                if (dataList != null && dataList.size() > 0) {
                    CurvePojo kalCurve = arithmeticUtil.getCurve(dataList, null, historyPractice, 10, user, "pserson").get(3);
                    historyCurvePojo = FootballTeamGameUtils.mergeDiffGameCurvePojo(historyCurvePojo, kalCurve);
                }
            }
        }
        FootballTeamGameUtils.addUp(historyCurvePojo);
        historyCurvePojo = FootballTeamGameUtils.avgAxisY(historyCurvePojo, historyPractices.size());
        gameMap.put("historyKalCurve", FootballTeamGameUtils.packCurve(historyCurvePojo));

        gameMap.put("nickName", user.getNickName() == null ? "" : user.getNickName());
        gameMap.put("height", user.getHeight());
        gameMap.put("weight", user.getWeight());
        gameMap.put("age", user.getAges());
        List<UserHardware> hardwareList = userHardwareDaoService.findByUserId(user.getId());
        gameMap.put("hardwareCount", hardwareList.size());
        UserHeadimg img = userHeadimgDaoService.findLastByUserId(user.getId());
        gameMap.put("headImg", img == null ? "" : img.getHeadImgNetUrl());

        //在练习赛中调用触球分析曲线函数，加入触球分析曲线的返回数据
        gameMap = dataUtil.countKickBallAgility(userHardwareDataList, gameMap, user);

        //体能数据
        gameMap = arithmeticUtil.countPhysicalAgility(userHardwareDataList, gameMap, user);
        Map<String, Object> speedMap = DataUtil.fengzhuang(userHardwareDataList, user);
        List<Integer> array = (List<Integer>) speedMap.get("sortedDataArray");
        JSONArray highSpeedMoveDataArray = (JSONArray) speedMap.get("highMoveCalorieArray");
        JSONArray midSpeedMoveDataArray = (JSONArray) speedMap.get("midMoveCalorieArray");
        JSONArray lowSpeedMoveDataArray = (JSONArray) speedMap.get("lowMoveCalorieArray");
        JSONArray normalSpeedMoveDataArray = (JSONArray) speedMap.get("normalMoveCalorieArray");
        long kickballTime = (long) speedMap.get("kickballTime");
        JSONObject speedCurve = arithmeticUtil.countDribbleSpeed(array, 3, highSpeedMoveDataArray, midSpeedMoveDataArray, lowSpeedMoveDataArray, normalSpeedMoveDataArray, kickballTime, user.getHeight());
        gameMap.put("ballSpeed", speedCurve);   //球速数据分析
        if (null != userHardwareDataList.get(0).getFootBallData()) {
            JSONArray jsonarray = JSONArray.parseArray(userHardwareDataList.get(0).getFootBallData());
            gameMap.put("footData", jsonarray);
        } else if (userHardwareDataList.size() > 1) {
            if (null != userHardwareDataList.get(1).getFootBallData()) {
                JSONArray jsonarrayInfo = JSONArray.parseArray(userHardwareDataList.get(1).getFootBallData());
                gameMap.put("footData", jsonarrayInfo);
            }
        } else {
            gameMap.put("footData", new JSONArray());
        }
        int touchCount = 0;
        JSONArray jsonarray = (JSONArray) gameMap.get("footData");
        if (jsonarray != null && jsonarray.size() > 0) {
            for (int i = 0; i < jsonarray.size(); i++) {
                JSONObject object = jsonarray.getJSONObject(i);
                touchCount += (object.getInteger("exteriorData") + object.getInteger("instepKickingData") + object.getInteger("archData") +
                        object.getInteger("tiptoeData") + object.getInteger("heelData") + object.getInteger("soleFootData"));
            }
        }
        gameMap.put("isPractice", 1);
        gameMap.put("userHardwarePracticeId", userHardwarePractice.getId());
        gameMap.put("userHardwarePracticeName", userHardwarePractice.getPracticeName());
        gameMap.put("hardwarePracticeTime", userHardwarePractice.getCreateTime().getTime());
        gameMap.put("hardwareIdentification", userHardwarePractice.getHardwareIdentification());
        FootballTeamGameStatisticsPlayer teamGameStatisticsPlayer = footballTeamGameStatisticsPlayerDaoService.findByPracticeIdAndUserId(userHardwarePractice.getId(), user.getId());
        if (teamGameStatisticsPlayer != null) {
            gameMap.put("gameStatus", 1);
            gameMap.put("isUploadData", 1);// 上传数据
            List<FootballTeamGameStatisticsPlayer> teamGameStatisticsPlayerList = footballTeamGameStatisticsPlayerDaoService.findPracticeByUserId(user.getId());
            if (teamGameStatisticsPlayerList != null && teamGameStatisticsPlayerList.size() > 0) {
                long wholeDistance = 0;
                int kickBallCount = 0;
                for (FootballTeamGameStatisticsPlayer teamGameStatisticsPlayerTemp : teamGameStatisticsPlayerList) {
                    if (teamGameStatisticsPlayerTemp != null) {
                        if (teamGameStatisticsPlayerTemp.getWholeMoveDistance() != null) {
                            wholeDistance += teamGameStatisticsPlayerTemp.getWholeMoveDistance();
                        }
                        if (teamGameStatisticsPlayerTemp.getTouchCounts() != null) {
                            kickBallCount += teamGameStatisticsPlayerTemp.getTouchCounts();
                        }
                    }// if
                    // teamGameStatisticsPlayerTemp!=null
                }
                gameMap.put("teamAverageDistance", (long) Math.ceil(wholeDistance * 1.0 / teamGameStatisticsPlayerList.size()));
                gameMap.put("teamAverageKick", (int) Math.ceil(kickBallCount * 1.0 / teamGameStatisticsPlayerList.size()));
            } else {
                gameMap.put("teamAverageDistance", 0);
                gameMap.put("teamAverageKick", 0);
            }
            // 计算卡路里消耗时间
            String highMoveString = teamGameStatisticsPlayer.getHighMoveCalorie();
            String midMoveString = teamGameStatisticsPlayer.getMidMoveCalorie();
            String lowMoveString = teamGameStatisticsPlayer.getLowMoveCalorie();
            String normalMoveString = teamGameStatisticsPlayer.getNormalMoveCalorie();
            long maxAppearTime = 0;
            long minAppearTime = 0;
            // 遍历高中低走数据，找出上场最后时间,和最早上场时间
            List<Long> tempList3 = new ArrayList<>();
            List<Long> tempList2 = new ArrayList<>();
            JSONArray highMoveArray = null;
            if (!"".equals(highMoveString) && highMoveString != null) {
                highMoveArray = JSONArray.parseArray(highMoveString);
            }
            if (highMoveArray != null && highMoveArray.size() > 0) {
                for (int k = 0; k < highMoveArray.size(); k++) {
                    JSONObject tempObject = highMoveArray.getJSONObject(k);
                    long endTime = tempObject.getLong("endTime");
                    long startTime = tempObject.getLong("startTime");
                    tempList3.add(endTime);
                    tempList2.add(startTime);
                }
            }
            JSONArray midMoveArray = null;
            if (!"".equals(midMoveString) && midMoveString != null) {
                midMoveArray = JSONArray.parseArray(midMoveString);
            }
            if (midMoveArray != null && midMoveArray.size() > 0) {
                for (int k = 0; k < midMoveArray.size(); k++) {
                    JSONObject tempObject = midMoveArray.getJSONObject(k);
                    long endTime = tempObject.getLong("endTime");
                    long startTime = tempObject.getLong("startTime");
                    tempList3.add(endTime);
                    tempList2.add(startTime);
                }
            }
            JSONArray lowMoveArray = null;
            if (!"".equals(lowMoveString) && lowMoveString != null) {
                lowMoveArray = JSONArray.parseArray(lowMoveString);
            }
            if (lowMoveArray != null && lowMoveArray.size() > 0) {
                for (int k = 0; k < lowMoveArray.size(); k++) {
                    JSONObject tempObject = lowMoveArray.getJSONObject(k);
                    long endTime = tempObject.getLong("endTime");
                    long startTime = tempObject.getLong("startTime");
                    tempList3.add(endTime);
                    tempList2.add(startTime);
                }
            }
            JSONArray normalMoveArray = null;
            if (!"".equals(normalMoveString) && normalMoveString != null) {
                normalMoveArray = JSONArray.parseArray(normalMoveString);
            }
            if (normalMoveArray != null && normalMoveArray.size() > 0) {
                for (int k = 0; k < normalMoveArray.size(); k++) {
                    JSONObject tempObject = normalMoveArray.getJSONObject(k);
                    long endTime = tempObject.getLong("endTime");
                    long startTime = tempObject.getLong("startTime");
                    tempList3.add(endTime);
                    tempList2.add(startTime);
                }
            }
            // 遍历 取出最大的时间
            if (tempList3.size() > 0) {
                for (Long aTempList3 : tempList3) {
                    if (aTempList3 >= maxAppearTime) {
                        maxAppearTime = aTempList3;
                    }
                }// for
            }// if tempList.size()>0
            if (tempList2.size() > 0) {
                minAppearTime = tempList2.get(0);
                for (int k = 1; k < tempList2.size(); k++) {
                    if (tempList2.get(k) <= minAppearTime) {
                        minAppearTime = tempList2.get(k);
                    }
                }// for
            }// if tempList.size()>0
            // 遍历最早时间
            if (maxAppearTime != 0 && minAppearTime != 0) {
                gameMap.put("calorieConsumeTime", ((maxAppearTime - minAppearTime) > 60000) ? (maxAppearTime - minAppearTime) / 1000 / 60 : 1);
            } else {
                gameMap.put("calorieConsumeTime", 90);
            }
            // 计算卡路里消耗时间
            gameMap.put("teamGameStatisticsPlayerId", teamGameStatisticsPlayer.getId());
            //比赛自动结束时间
            long tryEndhardWareDate = teamGameStatisticsPlayer.getCreateTime().getTime() + FootballContants.GAMEDURATION;
            //正常结束时间
            long endhardwareDate = teamGameStatisticsPlayer.getUpdateTime().getTime();
            gameMap.put("endLoadhardWareTime", Math.min(tryEndhardWareDate, endhardwareDate));
            gameMap.put("carryTime", teamGameStatisticsPlayer.getCarryTime());
            gameMap.put("passBallCounts", teamGameStatisticsPlayer.getPassBallCounts() == null ? Integer.valueOf(-1) : teamGameStatisticsPlayer.getPassBallCounts());
            gameMap.put("touchCounts", touchCount);
            gameMap.put("carryCounts", teamGameStatisticsPlayer.getCarryCount());
            gameMap.put("oneFootPassCounts", teamGameStatisticsPlayer.getOneFootPassCount() == null ? Integer.valueOf(-1) : teamGameStatisticsPlayer.getOneFootPassCount());
            gameMap.put("singleShoots", teamGameStatisticsPlayer.getShoots() == null ? Integer.valueOf(0) : teamGameStatisticsPlayer.getShoots());
            gameMap.put("singleAssists", teamGameStatisticsPlayer.getAssists() == null ? Integer.valueOf(0) : teamGameStatisticsPlayer.getAssists());
            gameMap.put("wholeMoveDistance", teamGameStatisticsPlayer.getWholeMoveDistance());
            int highMoveCount = teamGameStatisticsPlayer.getHighMoveCount();
            if (teamGameStatisticsPlayer.getHighMoveCount() == 0L) {
                highMoveCount = 0;
            }
            gameMap.put("highMoveCount", highMoveCount);
            gameMap.put("highMoveDistance", teamGameStatisticsPlayer.getHighMoveDistance());
            int midMoveCount = teamGameStatisticsPlayer.getMidMoveCount();
            if (teamGameStatisticsPlayer.getMidMoveDistance() == 0L) {
                midMoveCount = 0;
            }
            gameMap.put("midMoveCount", midMoveCount);
            gameMap.put("midMoveDistance", teamGameStatisticsPlayer.getMidMoveDistance());
            int lowMoveCount = teamGameStatisticsPlayer.getLowMoveCount();
            if (teamGameStatisticsPlayer.getLowMoveDistance() == 0L) {
                lowMoveCount = 0;
            }
            gameMap.put("lowMoveCount", lowMoveCount);
            gameMap.put("lowMoveDistance", teamGameStatisticsPlayer.getLowMoveDistance());
            int normalMoveCount = teamGameStatisticsPlayer.getNormalMoveCount();
            if (teamGameStatisticsPlayer.getNormalMoveDistance() == 0L) {
                normalMoveCount = 0;
            }
            gameMap.put("normalMoveCount", normalMoveCount);
            gameMap.put("normalMoveDistance", teamGameStatisticsPlayer.getNormalMoveDistance());
            if (teamGameStatisticsPlayer.getHighMoveCalorie() != null) {
                gameMap.put("highMoveCalorie", JSONArray.parseArray(teamGameStatisticsPlayer.getHighMoveCalorie()));
            } else {
                gameMap.put("highMoveCalorie", new JSONArray());
            }
            if (teamGameStatisticsPlayer.getMidMoveCalorie() != null) {
                gameMap.put("midMoveCalorie", JSONArray.parseArray(teamGameStatisticsPlayer.getMidMoveCalorie()));
            } else {
                gameMap.put("midMoveCalorie", new JSONArray());
            }
            if (teamGameStatisticsPlayer.getLowMoveCalorie() != null) {
                gameMap.put("lowMoveCalorie", JSONArray.parseArray(teamGameStatisticsPlayer.getLowMoveCalorie()));
            } else {
                gameMap.put("lowMoveCalorie", new JSONArray());
            }
            if (teamGameStatisticsPlayer.getNormalMoveCalorie() != null) {
                gameMap.put("normalMoveCalorie", JSONArray.parseArray(teamGameStatisticsPlayer.getNormalMoveCalorie()));
            } else {
                gameMap.put("normalMoveCalorie", new JSONArray());
            }
            if (teamGameStatisticsPlayer.getCalorieCurveData() != null) {
                gameMap.put("calorieCurveData", JSONObject.parseObject(teamGameStatisticsPlayer.getCalorieCurveData()));
                CalorieCurveSumPojo userCalorieCurveData = footballTeamUtils.userCalorieCurveData(teamGameStatisticsPlayer);
                CalorieCurveSumPojo userPracticeAverCalorieCurveData = footballTeamUtils.userPracticeAverCalorieCurveData(user);
                gameMap.put("userCalorieCurveData", JSONObject.parseObject(JSON.toJSONString(userCalorieCurveData)));
                gameMap.put("userPracticeAverCalorieCurveData", JSONObject.parseObject(JSON.toJSONString(userPracticeAverCalorieCurveData)));
            } else {
                gameMap.put("calorieCurveData", new JSONObject());
                gameMap.put("userCalorieCurveData", new JSONObject());
                gameMap.put("userPracticeAverCalorieCurveData", new JSONObject());
            }
            gameMap.put("moveCalorie", teamGameStatisticsPlayer.getMoveCalorie());
            gameMap.put("uploadHardWareTime", teamGameStatisticsPlayer.getUpdateTime().getTime());
            gameMap.put("carryDistance", teamGameStatisticsPlayer.getCarryDistance());
        } else {
            // 比赛默认时间不超过90分钟
            long gameStartTime = System.currentTimeMillis();
            long teamGameCompeteTime = userHardwarePractice.getCreateTime().getTime();
            long gameEndTime = teamGameCompeteTime + FootballContants.GAMEDURATION;
            if (gameEndTime < gameStartTime) {
                gameMap.put("gameStatus", 1);
            } else if (teamGameCompeteTime <= gameStartTime
                    && gameStartTime <= gameEndTime) {
                gameMap.put("gameStatus", 2);
            } else if (teamGameCompeteTime > gameStartTime) {
                gameMap.put("gameStatus", 3);
            }
            //比赛自动结束时间
            long tryEndhardWareDate = userHardwarePractice.getCreateTime().getTime() + FootballContants.GAMEDURATION;
            gameMap.put("endLoadhardWareTime", tryEndhardWareDate);
            gameMap.put("isUploadData", 2);// 没有上传数据
            JSONArray hardwareArray = new JSONArray();
            // 封装硬件信息
            for (UserHardwareData userHardwareData : userHardwareDataList) {
                UserHardware userHardware = userHardwareData.getUserHardware();
                if (userHardware != null) {
                    List<UserHardware> userHardware_list = userHardwareDaoService.findByUserId(user.getId());
                    for (UserHardware anUserHardware_list : userHardware_list) {
                        long nowUserHardwareId = anUserHardware_list.getId();
                        long tempUserHardwareId = userHardware.getId();
                        if (nowUserHardwareId == tempUserHardwareId) {
                            Map<String, Object> map = new HashMap<>();
                            map.put("userHardwareId", userHardware.getId());
                            map.put("hardwareType", userHardware.getHardwareType());
                            map.put("userHardwareDataId", userHardwareData.getId());
                            map.put("hardwareMac", userHardware.getHardwareMac());
                            hardwareArray.add(map);
                        }
                    }
                }//
            }// for
            List<FootballTeamGameStatisticsPlayer> teamGameStatisticsPlayerList = footballTeamGameStatisticsPlayerDaoService.findPracticeByUserId(user.getId());
            if (teamGameStatisticsPlayerList != null && teamGameStatisticsPlayerList.size() > 0) {
                long wholeDistance = 0;
                int kickBallCount = 0;
                for (FootballTeamGameStatisticsPlayer teamGameStatisticsPlayerTemp : teamGameStatisticsPlayerList) {
                    if (teamGameStatisticsPlayerTemp != null) {
                        if (teamGameStatisticsPlayerTemp.getWholeMoveDistance() != null) {
                            wholeDistance += teamGameStatisticsPlayerTemp.getWholeMoveDistance();
                        }
                        if (teamGameStatisticsPlayerTemp.getTouchCounts() != null) {
                            kickBallCount += teamGameStatisticsPlayerTemp.getTouchCounts();
                        }
                    }// if
                }
                gameMap.put("teamAverageDistance", (long) Math.ceil(wholeDistance * 1.0 / teamGameStatisticsPlayerList.size()));
                gameMap.put("teamAverageKick", (int) Math.ceil(kickBallCount * 1.0 / teamGameStatisticsPlayerList.size()));
            } else {
                gameMap.put("teamAverageDistance", 0);
                gameMap.put("teamAverageKick", 0);
            }
            // 计算卡路里消耗时间
            gameMap.put("teamGameStatisticsPlayerId", 0);
            gameMap.put("calorieConsumeTime", 0);
            // 计算卡路里消耗时间
            gameMap.put("carryTime", 0);
            gameMap.put("hardwareArray", hardwareArray);
            gameMap.put("passBallCounts", -1);
            gameMap.put("touchCounts", 0);
            gameMap.put("carryCounts", 0);
            gameMap.put("oneFootPassCounts", -1);
            gameMap.put("singleShoots", 0);
            gameMap.put("singleAssists", 0);
            gameMap.put("wholeMoveDistance", 0);
            gameMap.put("highMoveCount", 0);
            gameMap.put("highMoveDistance", 0);
            gameMap.put("midMoveCount", 0);
            gameMap.put("midMoveDistance", 0);
            gameMap.put("lowMoveCount", 0);
            gameMap.put("lowMoveDistance", 0);
            gameMap.put("normalMoveCount", 0);
            gameMap.put("normalMoveDistance", 0);
            gameMap.put("highMoveCalorie", new JSONArray());
            gameMap.put("midMoveCalorie", new JSONArray());
            gameMap.put("lowMoveCalorie", new JSONArray());
            gameMap.put("normalMoveCalorie", new JSONArray());
            gameMap.put("calorieCurveData", new JSONObject());
            gameMap.put("userCalorieCurveData", new JSONObject());
            gameMap.put("userPracticeAverCalorieCurveData", new JSONObject());
            gameMap.put("moveCalorie", 0);
            gameMap.put("uploadHardWareTime", 0);
            gameMap.put("carryDistance", 0);
            gameMap.put("footData", new JSONArray());
        }
        if (teamGameStatisticsPlayer != null) {
            //放入有球数据
            gameMap.put("haveballDistance", teamGameStatisticsPlayer.getCarryDistance());
            gameMap.put("highHaveballDistance", teamGameStatisticsPlayer.getHighCarryDistance());
            gameMap.put("midHaveballDistance", teamGameStatisticsPlayer.getMidCarryDistance());
            gameMap.put("lowHaveballDistance", teamGameStatisticsPlayer.getLowCarryDistance());
            gameMap.put("normalHaveballDistance", teamGameStatisticsPlayer.getNormalCarryDistance());

            //放入无球数据
            gameMap.put("NotballDistance", teamGameStatisticsPlayer.getWholeMoveDistance() - teamGameStatisticsPlayer.getCarryDistance());
            gameMap.put("highNotballDistance", teamGameStatisticsPlayer.getHighMoveDistance() - teamGameStatisticsPlayer.getHighCarryDistance());
            gameMap.put("midNotballDistance", teamGameStatisticsPlayer.getMidMoveDistance() - teamGameStatisticsPlayer.getMidCarryDistance());
            gameMap.put("lowNotballDistance", teamGameStatisticsPlayer.getLowMoveDistance() - teamGameStatisticsPlayer.getLowCarryDistance());
            gameMap.put("normalNotballDistance", teamGameStatisticsPlayer.getNormalMoveDistance() - teamGameStatisticsPlayer.getNormalCarryDistance());

            gameMap.put("highCarryCount", teamGameStatisticsPlayer.getHighCarryCount() == null ? Integer.valueOf(0) : teamGameStatisticsPlayer.getHighCarryCount());
            gameMap.put("midCarryCount", teamGameStatisticsPlayer.getMidCarryCount() == null ? Integer.valueOf(0) : teamGameStatisticsPlayer.getMidCarryCount());
            gameMap.put("lowCarryCount", teamGameStatisticsPlayer.getLowCarryCount() == null ? Integer.valueOf(0) : teamGameStatisticsPlayer.getLowCarryCount());
            gameMap.put("normalCarryCount", teamGameStatisticsPlayer.getNormalCarryCount() == null ? Integer.valueOf(0) : teamGameStatisticsPlayer.getNormalCarryCount());
        } else {
            //放入有球数据
            gameMap.put("haveballDistance", 0);
            gameMap.put("highHaveballDistance", 0);
            gameMap.put("midHaveballDistance", 0);
            gameMap.put("lowHaveballDistance", 0);
            gameMap.put("normalHaveballDistance", 0);

            //放入无球数据
            gameMap.put("NotballDistance", 0);
            gameMap.put("highNotballDistance", 0);
            gameMap.put("midNotballDistance", 0);
            gameMap.put("lowNotballDistance", 0);
            gameMap.put("normalNotballDistance", 0);

            gameMap.put("highCarryCount", 0);
            gameMap.put("midCarryCount", 0);
            gameMap.put("lowCarryCount", 0);
            gameMap.put("normalCarryCount", 0);
        }
        return gameMap;
    }

    //保存硬件数据到UserHardwareData表
    public static UserHardwareData saveUserHardwareData(UserHardwareData userHardwareData, UserHardWarePojo userHardWarePojo) {
        int[] userHardwareDateList = userHardWarePojo.getKickBallData();
        if (userHardwareDateList != null && userHardwareDateList.length > 0) {
            // 获取硬件数据数组进行封装
            JSONArray kickBallArray = new JSONArray();
            for (int i : userHardwareDateList) {
                kickBallArray.add(i);
            }
            userHardwareData.setKickBallData(kickBallArray.toString());
        }
        // 保存高速跑数据
        List<UserHardWareStepPojo> highMoveData = userHardWarePojo
                .getHighMoveData();
        if (highMoveData != null && highMoveData.size() > 0) {
            JSONArray highMoveArray = new JSONArray();
            putStepMap(highMoveData, highMoveArray);
            userHardwareData.setHighSpeedMoveData(highMoveArray.toString());
        }
        // 保存中速跑数据
        List<UserHardWareStepPojo> midMoveData = userHardWarePojo.getMidMoveData();
        if (midMoveData != null && midMoveData.size() > 0) {
            JSONArray midMoveArray = new JSONArray();
            putStepMap(midMoveData, midMoveArray);
            userHardwareData.setMidSpeedMoveData(midMoveArray.toString());
        }
        // 保存低速跑数据
        List<UserHardWareStepPojo> lowMoveData = userHardWarePojo.getLowMoveData();
        if (lowMoveData != null && lowMoveData.size() > 0) {
            JSONArray lowMoveArray = new JSONArray();
            putStepMap(lowMoveData, lowMoveArray);
            userHardwareData.setLowSpeedMoveData(lowMoveArray.toString());
        }
        // 保存步行数据
        List<UserHardWareStepPojo> normalMoveData = userHardWarePojo.getNormalMoveData();
        if (normalMoveData != null && normalMoveData.size() > 0) {
            JSONArray normalMoveArray = new JSONArray();
            putStepMap(normalMoveData, normalMoveArray);
            userHardwareData.setNormalSpeedMoveData(normalMoveArray.toString());
        }
        JSONArray json = new JSONArray();
        Map<String, Object> map = new HashMap<>();
        map.put("exteriorData", userHardWarePojo.getExteriorData());
        map.put("instepKickingData", userHardWarePojo.getInstepKickingData());
        map.put("archData", userHardWarePojo.getArchData());
        map.put("tiptoeData", userHardWarePojo.getTiptoeData());
        map.put("heelData", userHardWarePojo.getHeelData());
        map.put("soleFootData", userHardWarePojo.getSoleFootData());
        json.add(map);
        userHardwareData.setFootBallData(json.toString());
        return userHardwareData;
    }

    //保存硬件数据到UserHardwareData表
    public static UserHardwareData saveUserHardwareData(UserHardwareData userHardwareData, DataPojo dataPojo) {
        int[] kickBallData = dataPojo.getKickBallData();
        if (kickBallData != null && kickBallData.length > 0) {
            // 获取硬件数据数组进行封装
            JSONArray kickBallArray = new JSONArray();
            for (int i : kickBallData) {
                kickBallArray.add(i);
            }
            userHardwareData.setKickBallData(kickBallArray.toString());
        }
        // 保存高速跑数据
        List<UserHardWareStepPojo> highMoveData = dataPojo.getHighMoveData();
        if (highMoveData != null && highMoveData.size() > 0) {
            JSONArray highMoveArray = new JSONArray();
            putStepMap(highMoveData, highMoveArray);
            userHardwareData.setHighSpeedMoveData(highMoveArray.toString());
        }
        // 保存中速跑数据
        List<UserHardWareStepPojo> midMoveData = dataPojo.getMidMoveData();
        if (midMoveData != null && midMoveData.size() > 0) {
            JSONArray midMoveArray = new JSONArray();
            putStepMap(midMoveData, midMoveArray);
            userHardwareData.setMidSpeedMoveData(midMoveArray.toString());
        }
        // 保存低速跑数据
        List<UserHardWareStepPojo> lowMoveData = dataPojo.getLowMoveData();
        if (lowMoveData != null && lowMoveData.size() > 0) {
            JSONArray lowMoveArray = new JSONArray();
            putStepMap(lowMoveData, lowMoveArray);
            userHardwareData.setLowSpeedMoveData(lowMoveArray.toString());
        }
        // 保存步行数据
        List<UserHardWareStepPojo> normalMoveData = dataPojo.getNormalMoveData();
        if (normalMoveData != null && normalMoveData.size() > 0) {
            JSONArray normalMoveArray = new JSONArray();
            putStepMap(normalMoveData, normalMoveArray);
            userHardwareData.setNormalSpeedMoveData(normalMoveArray.toString());
        }
        JSONArray json = new JSONArray();
        Map<String, Object> map = new HashMap<>();
        map.put("exteriorData", dataPojo.getExteriorData());
        map.put("instepKickingData", dataPojo.getInstepKickingData());
        map.put("archData", dataPojo.getArchData());
        map.put("tiptoeData", dataPojo.getTiptoeData());
        map.put("heelData", dataPojo.getHeelData());
        map.put("soleFootData", dataPojo.getSoleFootData());
        json.add(map);
        userHardwareData.setFootBallData(json.toString());
        return userHardwareData;
    }

    private static void putStepMap(List<UserHardWareStepPojo> highMoveData, JSONArray highMoveArray) {
        for (UserHardWareStepPojo tempPojo : highMoveData) {
            Map<String, Object> tempMap = new HashMap<>();
            tempMap.put("intervalTime", tempPojo.getIntervalTime());
            tempMap.put("stepCount", tempPojo.getStepCount());
            tempMap.put("startTime", tempPojo.getStartTime());
            tempMap.put("endTime", tempPojo.getEndTime());
            tempMap.put("isHaveBall", tempPojo.getIsHaveBall());
            highMoveArray.add(tempMap);
        }
    }

    //保存硬件数据的统计数据到FootballTeamGameStatisticsPlayer表
    public Map<String, Object> saveFootballTeamGameStatisticsPlayer(UserHardware userHardware, Map<String, Object> map, User user, FootballTeamGame teamGame, FootballTeam team, Timestamp tempTime) {
        // ----统计完后保存到人比赛统计表,查看想双鞋子是否都同步了
        try {
            List<UserHardware> userHardwareList = userHardwareDaoService.findByIdentification(userHardware.getIdentification());
            if (userHardwareList != null && userHardwareList.size() > 0) {
                // 获取另一个硬件的数据
                List<UserHardwareData> userHardwareDataList = userHardwareDataDaoService.findByGameIdAndUserId(teamGame.getId(), user.getId());
                // 判断两个硬件是否都上传数据了
                if (userHardwareDataList != null && userHardwareDataList.size() > 0) {
                    // 查看是左脚还是右脚
                    UserHardware tempUserHardware = userHardwareDataList.get(0).getUserHardware();
                    long rightFootStartTime = 0L;
                    long leftFootStartTime = 0L;
                    String rightHighSpeedMoveDataString = "";
                    String rightMidSpeedMoveDataString = "";
                    String rightLowSpeedMoveDataString = "";
                    String rightNormalSpeedMoveDataString = "";
                    String rightFootData = "";
                    String leftHighSpeedMoveDataString = "";
                    String leftMidSpeedMoveDataString = "";
                    String leftLowSpeedMoveDataString = "";
                    String leftNormalSpeedMoveDataString = "";
                    String leftFootData = "";
                    if (tempUserHardware.getHardwareType() == 1) {
                        // 表示是左脚
                        if (userHardwareDataList.get(0)
                                .getKickBallStartTime() != null) {
                            leftFootStartTime = userHardwareDataList.get(0)
                                    .getKickBallStartTime().getTime();
                        }
                        if (userHardwareDataList
                                .get(0).getHighSpeedMoveData() != null) {
                            leftHighSpeedMoveDataString = userHardwareDataList
                                    .get(0).getHighSpeedMoveData();
                        }
                        if (userHardwareDataList
                                .get(0).getMidSpeedMoveData() != null) {
                            leftMidSpeedMoveDataString = userHardwareDataList
                                    .get(0).getMidSpeedMoveData();
                        }
                        if (userHardwareDataList
                                .get(0).getLowSpeedMoveData() != null) {
                            leftLowSpeedMoveDataString = userHardwareDataList
                                    .get(0).getLowSpeedMoveData();
                        }
                        if (userHardwareDataList
                                .get(0).getNormalSpeedMoveData() != null) {
                            leftNormalSpeedMoveDataString = userHardwareDataList
                                    .get(0).getNormalSpeedMoveData();
                        }
                        if (userHardwareDataList
                                .get(0).getKickBallData() != null) {
                            leftFootData = userHardwareDataList.get(0)
                                    .getKickBallData();
                        }

                        if (userHardwareDataList.size() > 1) {
                            if (userHardwareDataList.get(1).getKickBallStartTime() != null) {
                                rightFootStartTime = userHardwareDataList
                                        .get(1).getKickBallStartTime().getTime();
                            }

                            rightHighSpeedMoveDataString = userHardwareDataList
                                    .get(1).getHighSpeedMoveData();
                            rightMidSpeedMoveDataString = userHardwareDataList
                                    .get(1).getMidSpeedMoveData();
                            rightLowSpeedMoveDataString = userHardwareDataList
                                    .get(1).getLowSpeedMoveData();
                            rightNormalSpeedMoveDataString = userHardwareDataList
                                    .get(1).getNormalSpeedMoveData();
                            rightFootData = userHardwareDataList.get(1)
                                    .getKickBallData();
                        }
                    } else {
                        // 表示是右脚
                        if (userHardwareDataList.get(0)
                                .getKickBallStartTime() != null) {
                            rightFootStartTime = userHardwareDataList.get(0)
                                    .getKickBallStartTime().getTime();
                        }
                        if (userHardwareDataList
                                .get(0).getHighSpeedMoveData() != null) {
                            rightHighSpeedMoveDataString = userHardwareDataList
                                    .get(0).getHighSpeedMoveData();
                        }
                        if (userHardwareDataList
                                .get(0).getMidSpeedMoveData() != null) {
                            rightMidSpeedMoveDataString = userHardwareDataList
                                    .get(0).getMidSpeedMoveData();
                        }
                        if (userHardwareDataList
                                .get(0).getLowSpeedMoveData() != null) {
                            rightLowSpeedMoveDataString = userHardwareDataList
                                    .get(0).getLowSpeedMoveData();
                        }
                        if (userHardwareDataList
                                .get(0).getNormalSpeedMoveData() != null) {
                            rightNormalSpeedMoveDataString = userHardwareDataList
                                    .get(0).getNormalSpeedMoveData();
                        }
                        if (userHardwareDataList
                                .get(0).getKickBallData() != null) {
                            rightFootData = userHardwareDataList
                                    .get(0).getKickBallData();
                        }

                        if (userHardwareDataList.size() > 1) {
                            if (userHardwareDataList.get(1).getKickBallStartTime() != null) {
                                leftFootStartTime = userHardwareDataList.get(1)
                                        .getKickBallStartTime().getTime();
                            }
                            leftHighSpeedMoveDataString = userHardwareDataList
                                    .get(1).getHighSpeedMoveData();
                            leftMidSpeedMoveDataString = userHardwareDataList
                                    .get(1).getMidSpeedMoveData();
                            leftLowSpeedMoveDataString = userHardwareDataList
                                    .get(1).getLowSpeedMoveData();
                            leftNormalSpeedMoveDataString = userHardwareDataList
                                    .get(1).getNormalSpeedMoveData();
                            leftFootData = userHardwareDataList.get(1)
                                    .getKickBallData();
                        }
                    }
                    // 计算跑动数据
                    // 步长计算公式：步长=身高*0.45
                    double stepWidth;

                    if (user.getHeight() != null && user.getHeight() != 0) {
                        stepWidth = (user.getHeight() * 0.45 / 100.0);
                    } else {
                        // 没有身高，默认为175
                        stepWidth = (175 * 0.45 / 100.0);
                    }
                    int highSpeedMoveCount = 0;//步数
                    int highMoveCount = 0;//次数
                    int highIntervalTime = 0;
                    // 计算高速跑次数和高速跑距离
                    // 计算卡路里，计算公式：跑步热量=体重（kg）*距离（公里）*1.036
                    JSONArray highMoveCalorieArray = new JSONArray();
                    if (!"".equals(leftHighSpeedMoveDataString)
                            && leftHighSpeedMoveDataString != null) {
                        JSONArray temp = JSON.parseArray(leftHighSpeedMoveDataString);
                        if (temp != null && temp.size() > 0) {
                            for (int i = 0; i < temp.size(); i++) {
                                JSONObject tempObject = temp.getJSONObject(i);
                                int stepCount = tempObject.getInteger("stepCount");
                                highSpeedMoveCount += stepCount;
                                long tempMoveCalarie;
                                int intervalTime = tempObject.getInteger("intervalTime");
                                highIntervalTime += intervalTime;
                                Map<String, Object> tempMap = new HashMap<String, Object>();
                                if (user.getWeight() != null && user.getWeight() != 0) {
                                    tempMoveCalarie = (long) (user.getWeight() * (stepCount * stepWidth) / 1000 * 1.036);
                                } else {
                                    // 没有体重，默认为60
                                    tempMoveCalarie = (long) (60 * (stepCount * stepWidth) / 1000 * 1.036);
                                }
                                tempMap.put("intervalTime", intervalTime);
                                tempMap.put("stepCount", stepCount);
                                tempMap.put("moveCalarie", tempMoveCalarie);
                                tempMap.put("startTime", tempObject.getLong("startTime"));
                                tempMap.put("endTime", tempObject.getLong("endTime"));
                                tempMap.put("isHaveBall", tempObject.getInteger("isHaveBall"));
                                highMoveCalorieArray.add(tempMap);
                                if (intervalTime > 2000) {
                                    highMoveCount += 1;
                                }
                            }// for
                            /*highMoveCount += temp.length();*/
                        }// if
                    }
                    if (!"".equals(rightHighSpeedMoveDataString) && rightHighSpeedMoveDataString != null) {
                        JSONArray temp = JSON.parseArray(rightHighSpeedMoveDataString);
                        if (temp != null && temp.size() > 0) {
                            for (int i = 0; i < temp.size(); i++) {
                                JSONObject tempObject = temp.getJSONObject(i);
                                int stepCount = tempObject.getInteger("stepCount");
                                highSpeedMoveCount += stepCount;
                                long tempMoveCalarie;
                                int intervalTime = tempObject.getInteger("intervalTime");
                                highIntervalTime += intervalTime;
                                Map<String, Object> tempMap = new HashMap<>();
                                if (user.getWeight() != null && user.getWeight() != 0) {
                                    tempMoveCalarie = (long) (user.getWeight() * (stepCount * stepWidth) / 1000 * 1.036);
                                } else {
                                    // 没有体重，默认为60
                                    tempMoveCalarie = (long) (60 * (stepCount * stepWidth) / 1000 * 1.036);
                                }
                                tempMap.put("intervalTime", intervalTime);
                                tempMap.put("stepCount", stepCount);
                                tempMap.put("moveCalarie", tempMoveCalarie);
                                tempMap.put("startTime", tempObject.getLong("startTime"));
                                tempMap.put("endTime", tempObject.getLong("endTime"));
                                tempMap.put("isHaveBall", tempObject.getInteger("isHaveBall"));
                                highMoveCalorieArray.add(tempMap);
                                if (intervalTime > 2000) {
                                    highMoveCount += 1;
                                }
                            }// for
                        }// if
                    }
                    // 计算中速跑次数
                    int midSpeedMoveCount = 0;
                    int midMoveCount = 0;
                    int midIntervalTime = 0;
                    JSONArray midMoveCalorieArray = new JSONArray();
                    // 计算高速跑次数和高速跑距离
                    if (!"".equals(leftMidSpeedMoveDataString) && leftMidSpeedMoveDataString != null) {
                        JSONArray temp = JSON.parseArray(leftMidSpeedMoveDataString);
                        if (temp != null && temp.size() > 0) {
                            for (int i = 0; i < temp.size(); i++) {
                                JSONObject tempObject = temp.getJSONObject(i);
                                int stepCount = tempObject.getInteger("stepCount");
                                midSpeedMoveCount += stepCount;
                                long tempMoveCalarie;
                                int intervalTime = tempObject.getInteger("intervalTime");
                                midIntervalTime += intervalTime;
                                Map<String, Object> tempMap = new HashMap<>();
                                if (user.getWeight() != null && user.getWeight() != 0) {
                                    tempMoveCalarie = (long) (user.getWeight() * (stepCount * stepWidth) / 1000 * 1.036);
                                } else {
                                    // 没有体重，默认为60
                                    tempMoveCalarie = (long) (60 * (stepCount * stepWidth) / 1000 * 1.036);
                                }
                                tempMap.put("intervalTime", intervalTime);
                                tempMap.put("stepCount", stepCount);
                                tempMap.put("moveCalarie", tempMoveCalarie);
                                tempMap.put("startTime", tempObject.getLong("startTime"));
                                tempMap.put("endTime", tempObject.getLong("endTime"));
                                tempMap.put("isHaveBall", tempObject.getInteger("isHaveBall"));
                                midMoveCalorieArray.add(tempMap);
                                if (intervalTime > 2000) {
                                    midMoveCount += 1;
                                }
                            }// for
                        }// if
                    }
                    if (!"".equals(rightMidSpeedMoveDataString) && rightMidSpeedMoveDataString != null) {
                        JSONArray temp = JSON.parseArray(rightMidSpeedMoveDataString);
                        if (temp != null && temp.size() > 0) {
                            for (int i = 0; i < temp.size(); i++) {
                                JSONObject tempObject = temp.getJSONObject(i);
                                int stepCount = tempObject.getInteger("stepCount");
                                midSpeedMoveCount += stepCount;
                                long tempMoveCalarie;
                                int intervalTime = tempObject.getInteger("intervalTime");
                                midIntervalTime += intervalTime;
                                Map<String, Object> tempMap = new HashMap<>();
                                if (user.getWeight() != null && user.getWeight() != 0) {
                                    tempMoveCalarie = (long) (user.getWeight() * (stepCount * stepWidth) / 1000 * 1.036);
                                } else {
                                    // 没有体重，默认为60
                                    tempMoveCalarie = (long) (60 * (stepCount * stepWidth) / 1000 * 1.036);
                                }
                                tempMap.put("intervalTime", intervalTime);
                                tempMap.put("stepCount", stepCount);
                                tempMap.put("moveCalarie", tempMoveCalarie);
                                tempMap.put("startTime", tempObject.getLong("startTime"));
                                tempMap.put("endTime", tempObject.getLong("endTime"));
                                tempMap.put("isHaveBall", tempObject.getInteger("isHaveBall"));
                                midMoveCalorieArray.add(tempMap);
                                if (intervalTime > 2000) {
                                    midMoveCount += 1;
                                }
                            }// for
                            midMoveCount += temp.size();
                        }// if
                    }
                    // 计算地速跑
                    int lowSpeedMoveCount = 0;
                    int lowMoveCount = 0;
                    int lowIntervalTime = 0;
                    JSONArray lowMoveCalorieArray = new JSONArray();
                    // 计算高速跑次数和高速跑距离
                    if (!"".equals(leftLowSpeedMoveDataString) && leftLowSpeedMoveDataString != null) {
                        JSONArray temp = JSON.parseArray(leftLowSpeedMoveDataString);
                        if (temp != null && temp.size() > 0) {
                            for (int i = 0; i < temp.size(); i++) {
                                JSONObject tempObject = temp.getJSONObject(i);
                                int stepCount = tempObject.getInteger("stepCount");
                                lowSpeedMoveCount += stepCount;
                                long tempMoveCalarie;
                                int intervalTime = tempObject.getInteger("intervalTime");
                                lowIntervalTime += intervalTime;
                                Map<String, Object> tempMap = new HashMap<>();
                                if (user.getWeight() != null && user.getWeight() != 0) {
                                    tempMoveCalarie = (long) (user.getWeight() * (stepCount * stepWidth) / 1000 * 1.036);
                                } else {
                                    // 没有体重，默认为60
                                    tempMoveCalarie = (long) (60 * (stepCount * stepWidth) / 1000 * 1.036);
                                }
                                tempMap.put("intervalTime", intervalTime);
                                tempMap.put("stepCount", stepCount);
                                tempMap.put("moveCalarie", tempMoveCalarie);
                                tempMap.put("startTime", tempObject.getLong("startTime"));
                                tempMap.put("endTime", tempObject.getLong("endTime"));
                                lowMoveCalorieArray.add(tempMap);
                                tempMap.put("isHaveBall", tempObject.getInteger("isHaveBall"));
                                if (intervalTime > 2000) {
                                    lowMoveCount += 1;
                                }
                            }// for
                            lowMoveCount += temp.size();
                        }// if
                    }
                    if (!"".equals(rightLowSpeedMoveDataString) && rightLowSpeedMoveDataString != null) {
                        JSONArray temp = JSON.parseArray(rightLowSpeedMoveDataString);
                        if (temp != null && temp.size() > 0) {
                            for (int i = 0; i < temp.size(); i++) {
                                JSONObject tempObject = temp.getJSONObject(i);
                                int stepCount = tempObject.getInteger("stepCount");
                                lowSpeedMoveCount += stepCount;
                                long tempMoveCalarie;
                                int intervalTime = tempObject.getInteger("intervalTime");
                                lowIntervalTime += intervalTime;
                                Map<String, Object> tempMap = new HashMap<>();
                                if (user.getWeight() != null && user.getWeight() != 0) {
                                    tempMoveCalarie = (long) (user.getWeight() * (stepCount * stepWidth) / 1000 * 1.036);
                                } else {
                                    // 没有体重，默认为60
                                    tempMoveCalarie = (long) (60 * (stepCount * stepWidth) / 1000 * 1.036);
                                }
                                tempMap.put("intervalTime", intervalTime);
                                tempMap.put("stepCount", stepCount);
                                tempMap.put("moveCalarie", tempMoveCalarie);
                                tempMap.put("startTime", tempObject.getLong("startTime"));
                                tempMap.put("endTime", tempObject.getLong("endTime"));
                                tempMap.put("isHaveBall", tempObject.getInteger("isHaveBall"));
                                lowMoveCalorieArray.add(tempMap);
                                if (intervalTime > 2000) {
                                    lowMoveCount += 1;
                                }
                            }// for
                        }// if
                    }
                    // 计算走动次数
                    int normalSpeedMoveCount = 0;
                    int normalMoveCount = 0;
                    int norIntervalTime = 0;
                    // 计算高速跑次数和高速跑距离
                    JSONArray normalMoveCalorieArray = new JSONArray();
                    if (!"".equals(leftNormalSpeedMoveDataString) && leftNormalSpeedMoveDataString != null) {
                        JSONArray temp = JSON.parseArray(leftNormalSpeedMoveDataString);
                        if (temp != null && temp.size() > 0) {
                            for (int i = 0; i < temp.size(); i++) {
                                JSONObject tempObject = temp.getJSONObject(i);
                                int stepCount = tempObject.getInteger("stepCount");
                                normalSpeedMoveCount += stepCount;
                                long tempMoveCalarie;
                                int intervalTime = tempObject.getInteger("intervalTime");
                                norIntervalTime += intervalTime;
                                Map<String, Object> tempMap = new HashMap<String, Object>();
                                if (user.getWeight() != null && user.getWeight() != 0) {
                                    tempMoveCalarie = (long) (user.getWeight() * (stepCount * stepWidth) / 1000 * 1.036);
                                } else {
                                    // 没有体重，默认为60
                                    tempMoveCalarie = (long) (60 * (stepCount * stepWidth) / 1000 * 1.036);
                                }
                                tempMap.put("intervalTime", intervalTime);
                                tempMap.put("stepCount", stepCount);
                                tempMap.put("moveCalarie", tempMoveCalarie);
                                tempMap.put("startTime", tempObject.getLong("startTime"));
                                tempMap.put("endTime", tempObject.getLong("endTime"));
                                tempMap.put("isHaveBall", tempObject.getInteger("isHaveBall"));
                                normalMoveCalorieArray.add(tempMap);
                                if (intervalTime > 2000) {
                                    normalMoveCount += 1;
                                }
                            }// for
                        }// if
                    }
                    if (!"".equals(rightNormalSpeedMoveDataString) && rightNormalSpeedMoveDataString != null) {
                        JSONArray temp = JSON.parseArray(rightNormalSpeedMoveDataString);
                        if (temp != null && temp.size() > 0) {
                            for (int i = 0; i < temp.size(); i++) {
                                JSONObject tempObject = temp.getJSONObject(i);
                                int stepCount = tempObject.getInteger("stepCount");
                                normalSpeedMoveCount += stepCount;
                                long tempMoveCalarie;
                                int intervalTime = tempObject.getInteger("intervalTime");
                                norIntervalTime += intervalTime;
                                Map<String, Object> tempMap = new HashMap<String, Object>();
                                if (user.getWeight() != null && user.getWeight() != 0) {
                                    tempMoveCalarie = (long) (user.getWeight() * (stepCount * stepWidth) / 1000 * 1.036);
                                } else {
                                    // 没有体重，默认为60
                                    tempMoveCalarie = (long) (60 * (stepCount * stepWidth) / 1000 * 1.036);
                                }
                                tempMap.put("intervalTime", intervalTime);
                                tempMap.put("stepCount", stepCount);
                                tempMap.put("moveCalarie", tempMoveCalarie);
                                normalMoveCalorieArray.add(tempMap);
                                tempMap.put("startTime", tempObject.getLong("startTime"));
                                tempMap.put("endTime", tempObject.getLong("endTime"));
                                tempMap.put("isHaveBall", tempObject.getInteger("isHaveBall"));
                                normalMoveCalorieArray.add(tempMap);
                                if (intervalTime > 2000) {
                                    normalMoveCount += 1;
                                }
                            }// for
                        }// if
                    }

                    long highMoveDistance = 0;
                    if (highSpeedMoveCount != 0 && highIntervalTime != 0) {
                        highMoveDistance = UserDataUtil.calculateMoveDistanceNew(highSpeedMoveCount, highIntervalTime, user.getHeight(), 1);
                        if (highMoveDistance <= 0) {
                            highMoveCount = 0;
                        }
                    }
                    long midMoveDistance = 0;
                    if (midSpeedMoveCount != 0 && midIntervalTime != 0) {
                        midMoveDistance = UserDataUtil.calculateMoveDistanceNew(midSpeedMoveCount, midIntervalTime, user.getHeight(), 2);
                        if (midMoveDistance <= 0) {
                            midMoveCount = 0;
                        }
                    }
                    long lowMoveDistance = 0;
                    if (lowSpeedMoveCount != 0 && lowIntervalTime != 0) {
                        lowMoveDistance = UserDataUtil.calculateMoveDistanceNew(lowSpeedMoveCount, lowIntervalTime, user.getHeight(), 3);
                        if (lowMoveDistance <= 0) {
                            lowMoveDistance = 0;
                        }
                    }
                    long normalMoveDistance = 0;
                    if (normalSpeedMoveCount != 0 && norIntervalTime != 0) {
                        normalMoveDistance = UserDataUtil.calculateMoveDistanceNew(normalSpeedMoveCount, norIntervalTime, user.getHeight(), 4);
                        if (normalMoveDistance <= 0) {
                            normalMoveCount = 0;
                        }
                    }
                    // 计算卡路里，计算公式：跑步热量=体重（kg）*距离（公里）*1.036
                    long moveCalarie;
                    if (user.getWeight() != null && user.getWeight() != 0) {
                        moveCalarie = (long) ((double) user.getWeight()
                                * ((double) highMoveDistance + (double) midMoveDistance
                                + (double) lowMoveDistance + (double) normalMoveDistance)
                                / 1000 * 1.036);
                    } else {
                        // 没有体重，默认为60
                        moveCalarie = (long) (60.0 * (highMoveDistance + midMoveDistance + lowMoveDistance + normalMoveDistance) / 1000.0 * 1.036);
                    }
                    // 计算跑动数据
                    List<Integer> leftList = new ArrayList<>();
                    List<Integer> rightList = new ArrayList<>();
                    if (!"".equals(leftFootData) && leftFootData != null) {
                        JSONArray leftArray = JSON.parseArray(leftFootData);
                        if (leftArray != null && leftArray.size() > 0) {
                            for (int i = 0; i < leftArray.size(); i++) {
                                leftList.add(leftArray.getInteger(i));
                            }// for
                        }// if
                    }
                    if (!"".equals(rightFootData) && rightFootData != null) {
                        JSONArray rightArray = JSON.parseArray(rightFootData);
                        if (rightArray != null && rightArray.size() > 0) {
                            for (int i = 0; i < rightArray.size(); i++) {
                                rightList.add(rightArray.getInteger(i));
                            }// for
                        }// if
                    }
                    // 左右脚数组排序
                    List<Integer> sortedDataArray = FootballTeamUtils.sortArray(leftFootStartTime, rightFootStartTime, leftList, rightList);
                    // 触球次数：
                    int kickCount = 0;
                    if (sortedDataArray != null && sortedDataArray.size() > 0) {
                        kickCount = sortedDataArray.size();
                    }
                    // 计算带球次数
                    int carryCount = FootballTeamUtils.calculateCarryCount(sortedDataArray, 3);
                    // 计算传球次数
//					int passCount = FootballTeamUtils.calculatePassCount(sortedDataArray, 3);

                    int carryTime = FootballTeamUtils.calculateCarryTime(sortedDataArray, 3);
                    Map<String, Integer> resultMap = getPassballCount(team, teamGame, user);
                    int passCount = resultMap.get("passballCount");
                    int oneFootCount = resultMap.get("oneFootPassCount");  // 2017/6/9 修改一脚传球的算法
                    int twoFootballPassCount = resultMap.get("twoFootballPassCount");
                    int longPassCount = resultMap.get("longPassCount");
                    int shortPassCount = resultMap.get("shortPassCount");
                    int passError = resultMap.get("passError");
                    int leftPassBallCounts = resultMap.get("leftPassBallCounts");
                    int rightPassBallCounts = resultMap.get("rightPassBallCounts");
                    // 跑动卡路里排序FootballTeamUtils
                    // 修改 卡路里曲线，将各种跑动运动数据叠加在一起
                    JSONArray calorieCurveArray = new JSONArray();
                    if (highMoveCalorieArray != null && highMoveCalorieArray.size() > 0) {
                        for (int j = 0; j < highMoveCalorieArray.size(); j++) {
                            calorieCurveArray.add(highMoveCalorieArray.getJSONObject(j));
                        }
                        highMoveCalorieArray = DataUtil.sortArrayByLongValue(highMoveCalorieArray, "startTime");
                    }
                    if (midMoveCalorieArray != null && midMoveCalorieArray.size() > 0) {
                        for (int j = 0; j < midMoveCalorieArray.size(); j++) {
                            calorieCurveArray.add(midMoveCalorieArray.getJSONObject(j));
                        }
                        midMoveCalorieArray = DataUtil.sortArrayByLongValue(midMoveCalorieArray, "startTime");
                    }
                    if (lowMoveCalorieArray != null && lowMoveCalorieArray.size() > 0) {
                        for (int j = 0; j < lowMoveCalorieArray.size(); j++) {
                            calorieCurveArray.add(lowMoveCalorieArray.getJSONObject(j));
                        }
                        lowMoveCalorieArray = DataUtil.sortArrayByLongValue(lowMoveCalorieArray, "startTime");
                    }
                    if (normalMoveCalorieArray != null && normalMoveCalorieArray.size() > 0) {
                        for (int j = 0; j < normalMoveCalorieArray.size(); j++) {
                            calorieCurveArray.add(normalMoveCalorieArray.getJSONObject(j));
                        }
                        normalMoveCalorieArray = DataUtil.sortArrayByLongValue(normalMoveCalorieArray, "startTime");
                    }
                    // 进行重新排序
                    JSONObject calorieCurveObject = new JSONObject();
                    if (calorieCurveArray != null && calorieCurveArray.size() > 0) {
                        calorieCurveArray = DataUtil.sortArrayByLongValue(calorieCurveArray, "startTime");
                        // 卡路里曲线对象
                        calorieCurveObject = FootballTeamUtils.buildCalorieCurveData(calorieCurveArray);
                    }
                    // 计算一脚传球次数
                    FootballTeamGameStatisticsPlayer teamGameStatisticsPlayer = footballTeamGameStatisticsPlayerDaoService.findByTeamIdAndGameIdAndUserId(team.getId(), teamGame.getId(), user.getId());
                    //计算带球距离
                    int carryBallDistance;
                    int highCarryDistance;
                    int midCarryDistance;
                    int lowCarryDistance;
                    int normalCarryDistance;

                    int highCarryCount;
                    int midCarryCount;
                    int lowCarryCount;
                    int normalCarryCount;

                    double highMaxSpeed;
                    double maxCarrySpeed = 0;
                    JSONObject speedCurve;
                    Map<String, Object> haveBallMap;
                    JSONArray rightHighJarray = JSON.parseArray(rightHighSpeedMoveDataString);
                    JSONArray rightMidJarray = JSON.parseArray(rightMidSpeedMoveDataString);
                    JSONArray rightLowJarray = JSON.parseArray(rightLowSpeedMoveDataString);
                    JSONArray rightNormalJarray = JSON.parseArray(rightNormalSpeedMoveDataString);

                    JSONArray leftHighJarray = JSON.parseArray(leftHighSpeedMoveDataString);
                    JSONArray leftMidJarray = JSON.parseArray(leftMidSpeedMoveDataString);
                    JSONArray leftLowJarray = JSON.parseArray(rightLowSpeedMoveDataString);
                    JSONArray leftNormalJarray = JSON.parseArray(leftNormalSpeedMoveDataString);
                    if (leftFootStartTime >= rightFootStartTime) {
                        if (rightFootStartTime > 0) {
                            /*haveBallMap = FootballTeamUtils.calculateBallDistance(sortedDataArray, 3, highMoveCalorieArray, midMoveCalorieArray, lowMoveCalorieArray, normalMoveCalorieArray, rightFootStartTime, user.getHeight());*/
                            haveBallMap = FootballTeamUtils.calculateCarryBallDistance(rightHighJarray, rightMidJarray, rightLowJarray, rightNormalJarray, user.getHeight());
                        } else {
                            /* haveBallMap = FootballTeamUtils.calculateBallDistance(sortedDataArray, 3, highMoveCalorieArray, midMoveCalorieArray, lowMoveCalorieArray, normalMoveCalorieArray, leftFootStartTime, user.getHeight());*/
                            haveBallMap = FootballTeamUtils.calculateCarryBallDistance(leftHighJarray, leftMidJarray, leftLowJarray, leftNormalJarray, user.getHeight());
                        }
                    } else {
                        if (leftFootStartTime > 0) {
                            /*haveBallMap = FootballTeamUtils.calculateBallDistance(sortedDataArray, 3, highMoveCalorieArray, midMoveCalorieArray, lowMoveCalorieArray, normalMoveCalorieArray, leftFootStartTime, user.getHeight());*/
                            haveBallMap = FootballTeamUtils.calculateCarryBallDistance(leftHighJarray, leftMidJarray, leftLowJarray, leftNormalJarray, user.getHeight());
                        } else {
                            /*haveBallMap = FootballTeamUtils.calculateBallDistance(sortedDataArray, 3, highMoveCalorieArray, midMoveCalorieArray, lowMoveCalorieArray, normalMoveCalorieArray, rightFootStartTime, user.getHeight());*/
                            haveBallMap = FootballTeamUtils.calculateCarryBallDistance(rightHighJarray, rightMidJarray, rightLowJarray, rightNormalJarray, user.getHeight());
                        }
                    }
                    //增加体能数据
                    speedCurve = arithmeticUtil.countDribbleSpeed(sortedDataArray, 3,
                            highMoveCalorieArray, midMoveCalorieArray, lowMoveCalorieArray, normalMoveCalorieArray, rightFootStartTime, user.getHeight());

                    //高速带球
                    int temphighCarryDistance = (int) haveBallMap.get("highCarryDistance");
                    highCarryDistance = Math.min(temphighCarryDistance, (int) highMoveDistance);
                    //中速带球
                    int tempmidCarryDistance = (int) haveBallMap.get("midCarryDistance");
                    midCarryDistance = Math.min(tempmidCarryDistance, (int) midMoveDistance);
                    //低速带球
                    int templowCarryDistance = (int) haveBallMap.get("lowCarryDistance");
                    lowCarryDistance = Math.min(templowCarryDistance, (int) lowMoveDistance);
                    //步行带球
                    int tempnormalCarryDistance = (int) haveBallMap.get("normalCarryDistance");
                    normalCarryDistance = Math.min(tempnormalCarryDistance, (int) normalMoveDistance);
                    carryBallDistance = (highCarryDistance + midCarryDistance + lowCarryDistance + normalCarryDistance);
                    //带球次数
                    highCarryCount = (int) haveBallMap.get("highCarryCount");
                    midCarryCount = (int) haveBallMap.get("midCarryCount");
                    lowCarryCount = (int) haveBallMap.get("lowCarryCount");
                    normalCarryCount = (int) haveBallMap.get("normalCarryCount");
                    highMaxSpeed = (double) haveBallMap.get("highMaxSpeed");
                    maxCarrySpeed = ((double) haveBallMap.get("maxCarrySpeed") + maxCarrySpeed) / 2;
                    /*
                     * 2017.9.26 对带球距离做限制  如果带球距离超过跑动距离  那么  带球距离=跑动距离
                     */
                    if (carryBallDistance > (highMoveDistance + midMoveDistance + lowMoveDistance + normalMoveDistance)) {
                        carryBallDistance = (int) (highMoveDistance + midMoveDistance + lowMoveDistance + normalMoveDistance);
                    }
                    /*
                     * end
                     */
                    /*Map<String, Integer> moveCountMap = UserDataUtil.moveCount(userHardwareDataList);*/
                    if (teamGameStatisticsPlayer == null) {
                        // 空才统计，防止重复统计
                        teamGameStatisticsPlayer = new FootballTeamGameStatisticsPlayer();
                        teamGameStatisticsPlayer.setFootballTeam(team);
                        teamGameStatisticsPlayer.setFootballTeamGame(teamGame);
                        teamGameStatisticsPlayer.setCreateTime(tempTime);
                        teamGameStatisticsPlayer.setDeleted(false);
                        teamGameStatisticsPlayer.setIdentification(userHardware.getIdentification());
                        teamGameStatisticsPlayer.setCarryDistance(carryBallDistance);
                        teamGameStatisticsPlayer.setHighCarryDistance(highCarryDistance);
                        teamGameStatisticsPlayer.setMidCarryDistance(midCarryDistance);
                        teamGameStatisticsPlayer.setLowCarryDistance(lowCarryDistance);
                        teamGameStatisticsPlayer.setNormalCarryDistance(normalCarryDistance);
                        teamGameStatisticsPlayer.setHighCarryCount(highCarryCount);
                        teamGameStatisticsPlayer.setMidCarryCount(midCarryCount);
                        teamGameStatisticsPlayer.setLowCarryCount(lowCarryCount);
                        teamGameStatisticsPlayer.setNormalCarryCount(normalCarryCount);
                        teamGameStatisticsPlayer.setPassBallCounts(passCount);
                        teamGameStatisticsPlayer.setTouchCounts(kickCount);
                        teamGameStatisticsPlayer.setCarryCount(carryCount);
                        teamGameStatisticsPlayer.setOneFootPassCount(oneFootCount);
//                        teamGameStatisticsPlayer.setHighMoveCount(moveCountMap.get("highMoveCount"));
                        teamGameStatisticsPlayer.setHighMoveCount(highMoveCount);
                        teamGameStatisticsPlayer.setHighMoveDistance(highMoveDistance);
                        teamGameStatisticsPlayer.setHighMoveCalorie(highMoveCalorieArray.toString());
                        teamGameStatisticsPlayer.setCarryTime(carryTime);
//                        teamGameStatisticsPlayer.setMidMoveCount(moveCountMap.get("midMoveCount"));
                        teamGameStatisticsPlayer.setMidMoveCount(midMoveCount);
                        teamGameStatisticsPlayer.setMidMoveDistance(midMoveDistance);
                        teamGameStatisticsPlayer.setMidMoveCalorie(midMoveCalorieArray.toString());
//                        teamGameStatisticsPlayer.setLowMoveCount(moveCountMap.get("lowMoveCount"));
                        teamGameStatisticsPlayer.setLowMoveCount(lowMoveCount);
                        teamGameStatisticsPlayer.setLowMoveDistance(lowMoveDistance);
                        teamGameStatisticsPlayer.setLowMoveCalorie(lowMoveCalorieArray.toString());
                        teamGameStatisticsPlayer.setCalorieCurveData(calorieCurveObject.toString());
                        teamGameStatisticsPlayer.setMoveCalorie(moveCalarie);
//                        teamGameStatisticsPlayer.setNormalMoveCount(moveCountMap.get("normalMoveCount"));
                        teamGameStatisticsPlayer.setNormalMoveCount(normalMoveCount);
                        teamGameStatisticsPlayer.setNormalMoveDistance(normalMoveDistance);
                        teamGameStatisticsPlayer.setNormalMoveCalorie(normalMoveCalorieArray.toString());
                        teamGameStatisticsPlayer.setWholeMoveDistance(highMoveDistance + midMoveDistance + lowMoveDistance + normalMoveDistance);
                        teamGameStatisticsPlayer.setUpdateTime(tempTime);
                        teamGameStatisticsPlayer.setUser(user);
                        teamGameStatisticsPlayer.setMaxSprintSpeed(highMaxSpeed);
                        teamGameStatisticsPlayer.setMaxCarrySpeed(maxCarrySpeed);
                        teamGameStatisticsPlayer.setLeftPassBallCounts(leftPassBallCounts);
                        teamGameStatisticsPlayer.setRightPassBallCounts(rightPassBallCounts);
                        teamGameStatisticsPlayer.setTwoFootPassCount(twoFootballPassCount);
                        teamGameStatisticsPlayer.setLongPass(longPassCount);
                        teamGameStatisticsPlayer.setShortPass(shortPassCount);
                        teamGameStatisticsPlayer.setPassBallError(passError);
                    } else {
                        teamGameStatisticsPlayer.setIdentification(userHardware.getIdentification());
                        teamGameStatisticsPlayer.setPassBallCounts(passCount);
                        teamGameStatisticsPlayer.setTouchCounts(kickCount);
                        teamGameStatisticsPlayer.setCarryCount(carryCount);
                        teamGameStatisticsPlayer.setOneFootPassCount(oneFootCount);
//                        teamGameStatisticsPlayer.setHighMoveCount(moveCountMap.get("highMoveCount"));
                        teamGameStatisticsPlayer.setHighMoveCount(highMoveCount);
                        teamGameStatisticsPlayer.setHighMoveDistance(highMoveDistance);
                        teamGameStatisticsPlayer.setHighMoveCalorie(highMoveCalorieArray.toString());
//                        teamGameStatisticsPlayer.setMidMoveCount(moveCountMap.get("midMoveCount"));
                        teamGameStatisticsPlayer.setMidMoveCount(midMoveCount);
                        teamGameStatisticsPlayer.setMidMoveDistance(midMoveDistance);
                        teamGameStatisticsPlayer.setMidMoveCalorie(midMoveCalorieArray.toString());
//                        teamGameStatisticsPlayer.setLowMoveCount(moveCountMap.get("lowMoveCount"));
                        teamGameStatisticsPlayer.setLowMoveCount(lowMoveCount);
                        teamGameStatisticsPlayer.setLowMoveDistance(lowMoveDistance);
                        teamGameStatisticsPlayer.setCarryDistance(carryBallDistance);
                        teamGameStatisticsPlayer.setHighCarryDistance(highCarryDistance);
                        teamGameStatisticsPlayer.setMidCarryDistance(midCarryDistance);
                        teamGameStatisticsPlayer.setLowCarryDistance(lowCarryDistance);
                        teamGameStatisticsPlayer.setNormalCarryDistance(normalCarryDistance);
                        teamGameStatisticsPlayer.setHighCarryCount(highCarryCount);
                        teamGameStatisticsPlayer.setMidCarryCount(midCarryCount);
                        teamGameStatisticsPlayer.setLowCarryCount(lowCarryCount);
                        teamGameStatisticsPlayer.setNormalCarryCount(normalCarryCount);
                        teamGameStatisticsPlayer.setLowMoveCalorie(lowMoveCalorieArray.toString());
                        teamGameStatisticsPlayer.setCarryTime(carryTime);
                        teamGameStatisticsPlayer.setCalorieCurveData(calorieCurveObject.toString());
                        teamGameStatisticsPlayer.setMoveCalorie(moveCalarie);
//                        teamGameStatisticsPlayer.setNormalMoveCount(moveCountMap.get("normalMoveCount"));
                        teamGameStatisticsPlayer.setNormalMoveCount(normalMoveCount);
                        teamGameStatisticsPlayer.setNormalMoveDistance(normalMoveDistance);
                        teamGameStatisticsPlayer.setNormalMoveCalorie(normalMoveCalorieArray.toString());
                        teamGameStatisticsPlayer.setWholeMoveDistance(highMoveDistance + midMoveDistance + lowMoveDistance + normalMoveDistance);
                        teamGameStatisticsPlayer.setUpdateTime(tempTime);
                        teamGameStatisticsPlayer.setMaxSprintSpeed(highMaxSpeed);
                        teamGameStatisticsPlayer.setMaxCarrySpeed(maxCarrySpeed);
                        teamGameStatisticsPlayer.setLeftPassBallCounts(leftPassBallCounts);
                        teamGameStatisticsPlayer.setRightPassBallCounts(rightPassBallCounts);
                        teamGameStatisticsPlayer.setTwoFootPassCount(twoFootballPassCount);
                        teamGameStatisticsPlayer.setLongPass(longPassCount);
                        teamGameStatisticsPlayer.setShortPass(shortPassCount);
                        teamGameStatisticsPlayer.setPassBallError(passError);
                    }
                    footballTeamGameStatisticsPlayerDaoService.save(teamGameStatisticsPlayer);
                    map.put("teamGameStatisticsPlayerId", teamGameStatisticsPlayer.getId());
                    map.put("leftPassBallCounts", teamGameStatisticsPlayer.getLeftPassBallCounts() == null ? Integer.valueOf(-1) : teamGameStatisticsPlayer.getLeftPassBallCounts());
                    map.put("rightPassBallCounts", teamGameStatisticsPlayer.getRightPassBallCounts() == null ? Integer.valueOf(-1) : teamGameStatisticsPlayer.getRightPassBallCounts());
                    map.put("passBallCounts", teamGameStatisticsPlayer.getPassBallCounts());
                    map.put("touchCounts", teamGameStatisticsPlayer.getTouchCounts());
                    map.put("carryCounts", teamGameStatisticsPlayer.getCarryCount());
                    map.put("oneFootPassCounts", teamGameStatisticsPlayer.getOneFootPassCount());
                    map.put("hostScore", teamGame.getHostScore() == null ? Integer.valueOf(-1) : teamGame.getHostScore());
                    map.put("guestScore", teamGame.getGuestScore() == null ? Integer.valueOf(-1) : teamGame.getGuestScore());
                    map.put("singleShoots", teamGameStatisticsPlayer.getShoots());
                    map.put("carryDistance", teamGameStatisticsPlayer.getCarryDistance());
                    map.put("singleAssists", teamGameStatisticsPlayer.getAssists());
                    map.put("carryTime", teamGameStatisticsPlayer.getCarryTime());
                    map.put("wholeMoveDistance", teamGameStatisticsPlayer.getWholeMoveDistance());
                    map.put("highMoveCount", teamGameStatisticsPlayer.getHighMoveCount());
                    map.put("highMoveDistance", teamGameStatisticsPlayer.getHighMoveDistance());
                    map.put("midMoveCount", teamGameStatisticsPlayer.getMidMoveCount());
                    map.put("midMoveDistance", teamGameStatisticsPlayer.getMidMoveDistance());
                    map.put("lowMoveCount", teamGameStatisticsPlayer.getLowMoveCount());
                    map.put("lowMoveDistance", teamGameStatisticsPlayer.getLowMoveDistance());
                    map.put("normalMoveCount", teamGameStatisticsPlayer.getNormalMoveCount());
                    map.put("highCarryDistance", teamGameStatisticsPlayer.getHighCarryCount());
                    map.put("midCarryCount", teamGameStatisticsPlayer.getMidCarryCount());
                    map.put("lowCarryCount", teamGameStatisticsPlayer.getLowCarryCount());
                    map.put("normalCarryCount", teamGameStatisticsPlayer.getNormalCarryCount());
                    map.put("normalMoveDistance", teamGameStatisticsPlayer.getNormalMoveDistance());
                    if (teamGameStatisticsPlayer.getHighMoveCalorie() != null) {
                        map.put("highMoveCalorie", JSON.parseArray(teamGameStatisticsPlayer.getHighMoveCalorie()));
                    } else {
                        map.put("highMoveCalorie", new JSONArray());
                    }
                    if (teamGameStatisticsPlayer.getMidMoveCalorie() != null) {
                        map.put("midMoveCalorie", JSON.parseArray(teamGameStatisticsPlayer.getMidMoveCalorie()));
                    } else {
                        map.put("midMoveCalorie", new JSONArray());
                    }
                    if (teamGameStatisticsPlayer.getLowMoveCalorie() != null) {
                        map.put("lowMoveCalorie", JSON.parseArray(teamGameStatisticsPlayer.getLowMoveCalorie()));
                    } else {
                        map.put("lowMoveCalorie", new JSONArray());
                    }
                    if (teamGameStatisticsPlayer.getNormalMoveCalorie() != null) {
                        map.put("normalMoveCalorie", JSON.parseArray(teamGameStatisticsPlayer.getNormalMoveCalorie()));
                    } else {
                        map.put("normalMoveCalorie", new JSONArray());
                    }
                    if (teamGameStatisticsPlayer.getCalorieCurveData() != null) {
                        map.put("calorieCurveData", JSON.parseObject(teamGameStatisticsPlayer.getCalorieCurveData()));
                    } else {
                        map.put("calorieCurveData", new JSONObject());
                    }
                    map.put("moveCalorie", teamGameStatisticsPlayer.getMoveCalorie());
                    map.put("uploadHardWareTime", teamGameStatisticsPlayer.getUpdateTime().getTime());
                    List<FootballTeamGameStatisticsPlayer> teamGameStatisticsPlayerList = footballTeamGameStatisticsPlayerDaoService.findByTeamIdAndGameId(team.getId(), teamGame.getId());
                    if (teamGameStatisticsPlayerList != null && teamGameStatisticsPlayerList.size() > 0) {
                        long wholeDistance = 0;
                        int kickBallCount = 0;
                        for (FootballTeamGameStatisticsPlayer teamGameStatisticsPlayerTemp : teamGameStatisticsPlayerList) {
                            if (teamGameStatisticsPlayerTemp != null) {
                                if (teamGameStatisticsPlayerTemp.getWholeMoveDistance() != null) {
                                    wholeDistance += teamGameStatisticsPlayerTemp.getWholeMoveDistance();
                                }
                                if (teamGameStatisticsPlayerTemp.getTouchCounts() != null) {
                                    kickBallCount += teamGameStatisticsPlayerTemp.getTouchCounts();
                                }
                            }
                        }
                        map.put("teamAverageDistance", (long) Math.ceil(wholeDistance * 1.0 / teamGameStatisticsPlayerList.size()));
                        map.put("teamAverageKick", (int) Math.ceil(kickBallCount * 1.0 / teamGameStatisticsPlayerList.size()));
                    } else {
                        map.put("teamAverageDistance", 0);
                        map.put("teamAverageKick", 0);
                    }
                    map.put("ballSpeed", speedCurve);   // 2017-8-8 增加体能数据  带球速度
                }
            }
        } catch (JSONException e) {
            System.out.println(e.getMessage());
            // TODO Auto-generated catch block
            e.printStackTrace();
        }
        return map;
    }

    public Map<String, Integer> getPassballCount(FootballTeam team, FootballTeamGame teamGame, User user) {
        //查出比赛的报名队员
        List<FootballTeamGameEnroll> footballteamEnroll_list = null;
        if (team != null && teamGame != null) {
            footballteamEnroll_list = footballTeamGameEnrollDaoService.findByTeamIdAndGameId(team.getId(), teamGame.getId());
        }
        Map<String, Integer> map = new HashMap<>();
        try {
            JSONArray teamtouch_timeArray = getTouchBallTime(team, teamGame);
            //计算该用户的传球数据
            map = UserDataUtil.getPassBallData(teamtouch_timeArray, user);
            //修改这场球赛其他队员的传球数据
            if (footballteamEnroll_list != null && footballteamEnroll_list.size() > 0) {
                for (FootballTeamGameEnroll enroll : footballteamEnroll_list) {
                    Map<String, Integer> tempMap = UserDataUtil.getPassBallData(teamtouch_timeArray, enroll.getUser());
                    FootballTeamGameStatisticsPlayer player = footballTeamGameStatisticsPlayerDaoService.findByTeamIdAndGameIdAndUserId(team.getId(), teamGame.getId(), enroll.getUser().getId());
                    if (player != null) {
                        player.setPassBallCounts(tempMap.get("passballCount"));
                        player.setOneFootPassCount(tempMap.get("oneFootPassCount"));
                        player.setTwoFootPassCount(tempMap.get("twoFootballPassCount"));
                        player.setLongPass(tempMap.get("longPassCount"));
                        player.setShortPass(tempMap.get("shortPassCount"));
                        player.setPassBallError(tempMap.get("passError"));
                        player.setLeftPassBallCounts(tempMap.get("leftPassBallCounts"));
                        player.setRightPassBallCounts(tempMap.get("rightPassBallCounts"));
                        footballTeamGameStatisticsPlayerDaoService.save(player);
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return map;
    }

    public JSONArray getTouchBallTime(FootballTeam team, FootballTeamGame teamGame) {
        if (team == null || teamGame == null) {
            return new JSONArray();
        }
        List<FootballTeamGameEnroll> footballteamEnroll_list = footballTeamGameEnrollDaoService.findByTeamIdAndGameId(team.getId(), teamGame.getId());
        JSONArray touchball_Array = new JSONArray(); //触球时间和对应的用户id(个人)
        JSONArray teamtouch_timeArray = new JSONArray(); //触球时间和对应的用户id(全队)
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd  HH:mm:ss");
        try {
            //查询队员在比赛中的硬件数据(触球时间)
            if (footballteamEnroll_list != null && footballteamEnroll_list.size() > 1) {
                for (FootballTeamGameEnroll footballTeamGameEnroll : footballteamEnroll_list) {
                    List<UserHardwareData> userHardwaredate_list_user = userHardwareDataDaoService.findByGameIdAndUserId(teamGame.getId(), footballTeamGameEnroll.getUser().getId());        //获取触球记录
                    if (userHardwaredate_list_user != null && userHardwaredate_list_user.size() > 0) {
                        for (UserHardwareData userhardwaredata_one : userHardwaredate_list_user) {
                            if (userhardwaredata_one.getIsStartUp() == 3) {
                                if (userhardwaredata_one.getKickBallData() != null && !userhardwaredata_one.getKickBallData().equals("")) {
                                    Date kickBalldataStartTime = userhardwaredata_one.getKickBallStartTime();
                                    long kickBalldataStartTime_long = kickBalldataStartTime.getTime();
                                    JSONArray kickBalldata_array;
                                    kickBalldata_array = JSON.parseArray(userhardwaredata_one.getKickBallData());
                                    for (int y = 0; y < kickBalldata_array.size(); y++) {
                                        long touchball_time = kickBalldataStartTime_long + kickBalldata_array.getInteger(y) * 1000;
                                        Map<String, Object> touchball_time_user_map = new HashMap<String, Object>();
                                        touchball_time_user_map.put("timeFormat", sdf.format(new Date(touchball_time)));
                                        touchball_time_user_map.put("touchTime", touchball_time);       //触球时间
                                        touchball_time_user_map.put("userId", userhardwaredata_one.getUser().getId());     //用户id
                                        touchball_time_user_map.put("hardwareType", userhardwaredata_one.getUserHardware().getHardwareType());     //左右脚 1、left，2、right
                                        touchball_Array.add(touchball_time_user_map);
                                    }
                                }  // if  userhardwaredata_one
                            } // if userhardwaredata_one.getIsStartUp()==3
                        } // if userHardwaredate_list_user
                    } // if
                } // for
            }// if


            //把全队每个人的的触球时间按时间排序合并到一起
            if (touchball_Array != null && touchball_Array.size() > 1) {
                for (int u = 0; u < touchball_Array.size(); u++) {
                    teamtouch_timeArray.add(touchball_Array.getJSONObject(u));
                }
            }

            //排序
            if (teamtouch_timeArray != null && teamtouch_timeArray.size() > 0) {
                for (int v = 0; v < teamtouch_timeArray.size(); v++) {
                    for (int g = v + 1; g < teamtouch_timeArray.size(); g++) {
                        JSONObject tocuchTimev = teamtouch_timeArray.getJSONObject(v);
                        JSONObject tocuchTimeg = teamtouch_timeArray.getJSONObject(g);
                        if (tocuchTimev.getLong("touchTime") > tocuchTimeg.getLong("touchTime")) {
                            teamtouch_timeArray.set(v, tocuchTimeg);
                            teamtouch_timeArray.set(g, tocuchTimev);
                        }
                    }
                }
            } // if
        } catch (Exception e) {
            e.printStackTrace();
        }
        return teamtouch_timeArray;
    }

    public List<Integer> getPassSeries(JSONArray teamtouch_timeArray) throws JSONException {
        List<Integer> list = new ArrayList<>();
        Integer count = 0;
        for (int k = 0; k < teamtouch_timeArray.size() - 1; k++) {
            JSONObject Object = teamtouch_timeArray.getJSONObject(k);
            long userId_next = teamtouch_timeArray.getJSONObject(k + 1).getLong("userId");
            long touchTime_next = teamtouch_timeArray.getJSONObject(k + 1).getLong("touchTime");

            long userId = teamtouch_timeArray.getJSONObject(k).getLong("userId");

            long interval = Math.abs(Object.getLong("touchTime") - touchTime_next);
            if (userId != userId_next) {
                if (interval <= FootballContants.PASSBALLCUTOFF) {
                    count++;
                } else {
                    list.add(count);
                    count = 0;
                }
            }
            if (k == teamtouch_timeArray.size() - 2) {
                list.add(count);
            }
        }
        return list;
    }
}
