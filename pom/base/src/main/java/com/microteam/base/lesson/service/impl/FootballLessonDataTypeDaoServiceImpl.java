package com.microteam.base.lesson.service.impl;

import com.microteam.base.common.util.lesson.FootballLessonUtils;
import com.microteam.base.entity.lesson.FootballLesson;
import com.microteam.base.entity.lesson.FootballLessonDataType;
import com.microteam.base.lesson.dao.FootballLessonDataTypeDao;
import com.microteam.base.lesson.service.FootballLessonDataTypeDaoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service("footballLessonDataTypeDaoService")
public class FootballLessonDataTypeDaoServiceImpl implements FootballLessonDataTypeDaoService {

    @Autowired
    private FootballLessonDataTypeDao dao;

    @Override
    public List<FootballLessonDataType> findByLessonId(Long lessonId) {
        return dao.findByLessonId(lessonId);
    }

    @Override
    public List<FootballLessonDataType> findByLessonIdList(List<FootballLesson> lessonList) throws Exception {
        if (lessonList != null && lessonList.size() > 0) {
            List<Long> idList = (List<Long>) FootballLessonUtils.getFileldList("id", lessonList);
            return dao.findByLessonIdList(idList);
        } else {
            return new ArrayList<>();
        }
    }

    @Override
    public List<FootballLessonDataType> saveAll(List<FootballLessonDataType> list) {
        return dao.saveAll(list);
    }

    @Override
    public List<FootballLessonDataType> findByCourwareId(Long courwareId) {
        return dao.findByCourwareId(courwareId);
    }
}
