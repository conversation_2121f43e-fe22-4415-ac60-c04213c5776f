package com.microteam.base.entity.team;


import com.microteam.base.common.base.BaseEntity;
import com.microteam.base.entity.user.User;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;
import java.io.Serializable;

@Entity
@Table(name = "football_team_invitation")
@Getter
@Setter
public class FootballTeamInvitation extends BaseEntity implements Serializable {

    @ManyToOne
    @JoinColumn(name = "teamId", nullable = false)
    private FootballTeam footballTeam;
    @ManyToOne
    @JoinColumn(name = "creatorId", nullable = false)
    private User user;
    @Column(name = "invitee")
    private Long invitee;
    @Column(name = "inviteMessage", length = 200)
    private String inviteMessage;
    @Column(name = "audit", nullable = false)
    private Short audit;
    @ManyToOne
    @JoinColumn(name = "teamMemberRoleId", nullable = false)
    private FootballTeamMemberRole footballTeamMemberRole;
    @Column(name = "teamNumber")
    private Integer teamNumber;

}
