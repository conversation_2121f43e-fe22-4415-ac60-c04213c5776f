package com.microteam.base.entity.team;


import com.microteam.base.common.base.BaseEntity;
import com.microteam.base.entity.user.User;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;
import java.io.Serializable;

@Entity
@Table(name = "football_team_new_comment")
@Getter
@Setter
public class FootballTeamNewComment extends BaseEntity implements Serializable {

    @ManyToOne
    @JoinColumn(name = "teamNewId", nullable = false)
    private FootballTeamNew footballTeamNew;
    @ManyToOne
    @JoinColumn(name = "userId", nullable = false)
    private User user;
    @Column(name = "comment", length = 65535)
    private String comment;

}
