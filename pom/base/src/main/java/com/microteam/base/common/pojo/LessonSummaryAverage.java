package com.microteam.base.common.pojo;

import lombok.Getter;
import lombok.Setter;

import javax.persistence.Tuple;

@Getter
@Setter
//@SqlResultSetMapping(
//        name = "getLessonSummaryAverage",  // 如果@Query 不指定name 会默认使用方法名
//        classes = {
//                @ConstructorResult(
//                        targetClass = LessonSummaryAverage.class,
//                        columns = {
//                                @ColumnResult(name = "avgWholeMoveDistance", type = double.class),
//                                @ColumnResult(name = "avgCarryDistance", type = double.class),
//                                @ColumnResult(name = "avgPassBallCounts", type = double.class),
//                                @ColumnResult(name = "avgMaxSprintSpeed", type = double.class),
//                                @ColumnResult(name = "avgPassBallError", type = double.class),
//                                @ColumnResult(name = "avgSuccessRate", type = double.class)
//                        }
//                )
//        }
//)
public class LessonSummaryAverage {
    private double avgWholeMoveDistance;  //跑动距离,平均值
    private double avgCarryDistance;      //带球距离,平均值
    private double avgMaxSprintSpeed;     //最大冲刺速度,平均值
    private double avgPassBallCounts;     //传球次数,平均值
    private double avgPassBallError;      //传球失误,平均值
    private double avgSuccessRate;        //传球成功率,平均值

    public LessonSummaryAverage(Tuple tuple) {
        this.avgWholeMoveDistance = Double.parseDouble(tuple.get("avgWholeMoveDistance").toString());
        this.avgCarryDistance = Double.parseDouble(tuple.get("avgCarryDistance").toString());
        this.avgMaxSprintSpeed = Double.parseDouble(tuple.get("avgMaxSprintSpeed").toString());
        this.avgPassBallCounts = Double.parseDouble(tuple.get("avgPassBallCounts").toString());
        this.avgPassBallError = Double.parseDouble(tuple.get("avgPassBallError").toString());
        this.avgSuccessRate = Double.parseDouble(tuple.get("avgSuccessRate").toString());

    }

}
