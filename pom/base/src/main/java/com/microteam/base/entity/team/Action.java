package com.microteam.base.entity.team;

import com.microteam.base.common.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.io.Serializable;

@Entity
@Table(name = "action")
@Getter
@Setter
public class Action extends BaseEntity implements Serializable {

    @Column(name = "actionName")
    private String actionName;
    @Column(name = "description")
    private String description;

}
