package com.microteam.base.agency.service.impl;

import com.microteam.base.agency.dao.FootballAgencyCoachCommentPraiseDao;
import com.microteam.base.entity.agency.FootballAgencyCoachCommentPraise;
import com.microteam.base.agency.service.FootballAgencyCoachCommentPraiseDaoService;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service("footballAgencyCoachCommentPraiseDaoService")
public class FootballAgencyCoachCommentPraiseDaoServiceImpl implements FootballAgencyCoachCommentPraiseDaoService {
    static Logger logger = Logger.getLogger(FootballAgencyCoachCommentPraiseDaoServiceImpl.class.getName());
    @Autowired
    private FootballAgencyCoachCommentPraiseDao dao;

    @Override
    public List<FootballAgencyCoachCommentPraise> findByCoachCommentId(Long coachCommentId) {
        return dao.findByCoachCommentId(coachCommentId);
    }

    @Override
    public FootballAgencyCoachCommentPraise findByCoachCommentIdAndUserId(Long coachCommentId, Long userId) {
        return dao.findByCoachCommentIdAndUserId(coachCommentId, userId);
    }

}
