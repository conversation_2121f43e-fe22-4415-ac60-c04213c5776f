package com.microteam.base.agency.service.impl;

import com.microteam.base.agency.dao.FootballAgencyClassCourseDao;
import com.microteam.base.agency.service.FootballAgencyClassCourseDaoService;
import com.microteam.base.entity.agency.FootballAgencyClassCourse;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.List;

@Service("footballAgencyClassCourseDaoService")
public class FootballAgencyClassCourseDaoServiceImpl implements FootballAgencyClassCourseDaoService {
    static Logger logger = Logger.getLogger(FootballAgencyClassCourseDaoServiceImpl.class.getName());
    @Autowired
    private FootballAgencyClassCourseDao dao;

    //查询指定机构班级下的课程列表
    @Override
    public List<FootballAgencyClassCourse> findByAgencyClassIdForPage(Long agencyClassId, int page, int pageSize) {
        if (page > 0) {
            page--;
        }
        Pageable pageable = PageRequest.of(page, pageSize);
        return dao.findByAgencyClassIdForPage(agencyClassId, pageable);
    }

    @Override
    public FootballAgencyClassCourse findById(long id) {
        return dao.findById(id);
    }


}
