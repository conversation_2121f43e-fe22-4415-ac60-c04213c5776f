package com.microteam.base.entity.user;

import com.microteam.base.common.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.io.Serializable;

@Entity
@Table(name = "user_headimg_random")
@Getter
@Setter
public class UserHeadimgRandom extends BaseEntity implements Serializable {

    @Column(name = "headImgNetUrl", length = 250)
    private String headImgNetUrl;
    @Column(name = "headImgUrl", length = 250)
    private String headImgUrl;
    @Column(name = "fileName", length = 250)
    private String fileName;
    @Column(name = "length")
    private Long length;
    @Column(name = "enabled", nullable = false)
    private boolean enabled;

}
