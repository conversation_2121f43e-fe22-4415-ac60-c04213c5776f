package com.microteam.base.agency.dao;

import com.microteam.base.entity.agency.FootballAgencyApplicant;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

public interface FootballAgencyApplicantDao extends JpaRepository<FootballAgencyApplicant, Long>, JpaSpecificationExecutor<FootballAgencyApplicant> {

    @Query("from FootballAgencyApplicant as agencyApplicant " +
            " left join fetch agencyApplicant.footballAgency as agency " +
            " left join fetch agencyApplicant.user as user " +
            " where agency.id = (:agencyId) " +
            " and user.id = (:applicantId) " +
            " and agencyApplicant.deleted = false " +
            " and agency.deleted = false " +
            " and user.deleted = false ")
    FootballAgencyApplicant findByAgencyIdAndApplicantId(@Param("agencyId") Long agencyId, @Param("applicantId") Long applicantId);

    @Query("from FootballAgencyApplicant as agencyApplicant " +
            " left join fetch agencyApplicant.footballAgency as agency " +
            " left join fetch agencyApplicant.user as user " +
            " where agencyApplicant.id = (:id) " +
            " and agencyApplicant.deleted = false ")
    FootballAgencyApplicant findById(@Param("id") long id);

    @Query("from FootballAgencyApplicant as agencyApplicant " +
            "left join fetch agencyApplicant.footballAgency as agency " +
            "left join fetch agencyApplicant.user as user " +
            "where agency.id = (:agencyId) " +
            "and agency.deleted = false " +
            "and agencyApplicant.deleted = false " +
            "order by INSTR('312',agencyApplicant.audit) ")
    List<FootballAgencyApplicant> findByAgencyIdForPageOrderByCreateTimeAndAudit(@Param("agencyId") Long agencyId, Pageable pageable);
}
