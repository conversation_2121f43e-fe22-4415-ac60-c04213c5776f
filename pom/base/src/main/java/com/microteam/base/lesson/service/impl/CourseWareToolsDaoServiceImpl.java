package com.microteam.base.lesson.service.impl;

import com.microteam.base.entity.courseware.CourseWareTools;
import com.microteam.base.lesson.dao.CourseWareToolsDao;
import com.microteam.base.lesson.service.CourseWareToolsDaoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class CourseWareToolsDaoServiceImpl implements CourseWareToolsDaoService {

    @Autowired
    private CourseWareToolsDao dao;

    @Override
    public CourseWareTools findByCoursewareIdAndToolsId(Long coursewareId, Long toolsId) {
        return dao.findByCoursewareIdAndToolsId(coursewareId, toolsId);
    }

    @Override
    public CourseWareTools save(CourseWareTools courseWareTools) {
        return dao.save(courseWareTools);
    }
}
