package com.microteam.base.entity.match;


import com.microteam.base.common.base.BaseEntity;
import com.microteam.base.entity.user.User;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;
import java.io.Serializable;

@Entity
@Table(name = "football_team_match_album")
@Getter
@Setter
public class FootballTeamMatchAlbum extends BaseEntity implements Serializable {

    @ManyToOne
    @JoinColumn(name = "userId")
    private User user;
    @ManyToOne
    @JoinColumn(name = "matchId", nullable = false)
    private FootballTeamMatch footballTeamMatch;
    @Column(name = "albumName", length = 100)
    private String albumName;
    @Column(name = "albumDesc", length = 200)
    private String albumDesc;

}
