package com.microteam.base.agency.dao;

import com.microteam.base.entity.agency.FootballAgencyCoach;
import com.microteam.base.entity.agency.FootballAgencyCoachResumeAlbum;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

public interface FootballAgencyCoachResumeAlbumDao extends JpaRepository<FootballAgencyCoachResumeAlbum, Long>, JpaSpecificationExecutor<FootballAgencyCoachResumeAlbum> {

    @Query("from  FootballAgencyCoachResumeAlbum as agencyCoachResumeAlbum " +
            "where agencyCoachResumeAlbum.footballAgencyCoach=?1 " +
            "and agencyCoachResumeAlbum.deleted=false ")
    List<FootballAgencyCoachResumeAlbum> findCoachResumeAlbumList(FootballAgencyCoach agencyCoach);
}
