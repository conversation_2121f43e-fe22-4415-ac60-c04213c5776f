package com.microteam.base.agency.service.impl;

import com.microteam.base.agency.dao.FootballAgencyClassAlbumPraiseDao;
import com.microteam.base.entity.agency.FootballAgencyClassAlbumPraise;
import com.microteam.base.agency.service.FootballAgencyClassAlbumPraiseDaoService;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service("footballAgencyClassAlbumPraiseDaoService")
public class FootballAgencyClassAlbumPraiseDaoServiceImpl implements FootballAgencyClassAlbumPraiseDaoService {
    static Logger logger = Logger.getLogger(FootballAgencyClassAlbumPraiseDaoServiceImpl.class.getName());
    @Autowired
    private FootballAgencyClassAlbumPraiseDao dao;

    //查询班级相册点赞
    @Override
    public List<FootballAgencyClassAlbumPraise> findByAgencyClassAlbumId(Long agencyClassAlbumId) {
        return dao.findByAgencyClassAlbumId(agencyClassAlbumId);
    }

    //查询用户对相册的点赞
    @Override
    public FootballAgencyClassAlbumPraise findByAgencyClassAlbumIdAndUserId(Long agencyClassAlbumId, Long userId) {
        return dao.findByAgencyClassAlbumIdAndUserId(agencyClassAlbumId, userId);
    }


}
