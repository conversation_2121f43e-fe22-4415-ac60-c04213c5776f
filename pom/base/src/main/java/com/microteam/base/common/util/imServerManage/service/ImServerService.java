package com.microteam.base.common.util.imServerManage.service;


import com.microteam.base.common.util.imServerManage.pojo.ImGroupRegisterRequestBodyPojo;
import com.microteam.base.entity.user.Groups;
import com.microteam.base.entity.user.User;

//因为是操作rest client for IM SERVER,因此没有DAO处理；
public interface ImServerService {

    //注册环信用户账号
    boolean registerUserImServer(User user);

    //注册环信群组
    Groups registerGroupImServer(Groups groups, ImGroupRegisterRequestBodyPojo imGroupRegisterRequestBodyPojo);


}


