package com.microteam.base.agency.service.impl;

import com.microteam.base.agency.dao.FootballAgencyStudentScoreDao;
import com.microteam.base.entity.agency.FootballAgencyClassCourse;
import com.microteam.base.entity.agency.FootballAgencyStudentScore;
import com.microteam.base.agency.service.FootballAgencyStudentScoreDaoService;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service("footballAgencyStudentScoreDaoService")
public class FootballAgencyStudentScoreDaoServiceImpl implements FootballAgencyStudentScoreDaoService {
    static Logger logger = Logger.getLogger(FootballAgencyStudentScoreDaoServiceImpl.class.getName());
    @Autowired
    private FootballAgencyStudentScoreDao dao;

    public FootballAgencyStudentScoreDaoServiceImpl() {
        super();

    }

    //查询课程下所有学员的分数
    @Override
    public List<FootballAgencyStudentScore> findScoreByCourse(
            FootballAgencyClassCourse agencyClassCourse) {

        return dao.findScoreByCourse(agencyClassCourse);
    }

    @Override
    public FootballAgencyStudentScore findScoreById(long id) {

        return dao.findScoreById(id);
    }

}
