package com.microteam.base.agency.dao;

import com.microteam.base.entity.agency.FootballAgencyAlbumPraise;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

public interface FootballAgencyAlbumPraiseDao extends JpaRepository<FootballAgencyAlbumPraise, Long>, JpaSpecificationExecutor<FootballAgencyAlbumPraise> {
    //查询机构相册点赞
    @Query("from FootballAgencyAlbumPraise as footballAgencyAlbumPraise " +
            "where footballAgencyAlbumPraise.footballAgencyAlbum.id = (:agencyAlbumId) " +
            "and footballAgencyAlbumPraise.isPraised=true " +
            "and footballAgencyAlbumPraise.deleted=false ")
    List<FootballAgencyAlbumPraise> findByAgencyAlbumId(@Param("agencyAlbumId") Long agencyAlbumId);

    //查询用户对机构的点赞
    @Query("from FootballAgencyAlbumPraise as footballAgencyAlbumPraise " +
            "where footballAgencyAlbumPraise.footballAgencyAlbum.id = (:agencyAlbumId) " +
            "and footballAgencyAlbumPraise.user.id = (:userId) " +
            "and footballAgencyAlbumPraise.deleted=false ")
    FootballAgencyAlbumPraise findByAgencyAlbumIdAndUserId(@Param("agencyAlbumId") Long agencyAlbumId, @Param("userId") Long userId);
}
