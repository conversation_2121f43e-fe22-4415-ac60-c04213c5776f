package com.microteam.base.lesson.service.impl;

import com.microteam.base.common.util.common.RedisUtil;
import com.microteam.base.entity.lesson.FootballLessonHardwareData;
import com.microteam.base.lesson.dao.FootballLessonHardwareDataDao;
import com.microteam.base.lesson.service.FootballLessonHardwareDataDaoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service("footballLessonHardwareDataDaoService")
public class FootballLessonHardwareDataDaoServiceImpl implements FootballLessonHardwareDataDaoService {

    @Autowired
    private FootballLessonHardwareDataDao dao;
    @Autowired
    private RedisUtil redisUtil;

    @Override
    public int shutDownByUserIdAndHardwareId(Long userId, Long hardwareId) {
        redisUtil.hDel("football_lesson_hardware_data");
        return dao.shutDownByUserIdAndHardwareId(userId, hardwareId);
    }

    @Override
    public int shutDownOtherLessonByUserIdAndLessonId(Long userId, Long lessonId) {
        redisUtil.hDel("football_lesson_hardware_data");
        return dao.shutDownOtherLessonByUserIdAndLessonId(userId, lessonId);
    }

    @Override
    public FootballLessonHardwareData findByHardwareIdAndLessonIdAndUserId(Long hardwareId, Long lessonId, Long userId) {
        return dao.findByHardwareIdAndLessonIdAndUserId(hardwareId, lessonId, userId);
    }

    @Override
    public List<FootballLessonHardwareData> findStartedByLessonIdAndUserId(Long lessonId, Long userId) {
        return dao.findStartedByLessonIdAndUserId(lessonId, userId);
    }


    @Override
    public List<FootballLessonHardwareData> findUploadedByLessonIdAndUserId(Long lessonId, Long userId) {
        return dao.findUploadedByLessonIdAndUserId(lessonId, userId);
    }

    @Override
    public List<FootballLessonHardwareData> findByLessonIdListAndUserId(List<Long> lessonIdList, Long userId) {
//        String type = "football_lesson_hardware_data";
//        String name = "findByUserIdAndLessonId_" + userId + "_";
//        List<FootballLessonHardwareData> redis = new ArrayList<>();
//        List<FootballLessonHardwareData> lessonHardwareData;
//        for (Long lessonId : lessonIdList) {
//            lessonHardwareData = (List<FootballLessonHardwareData>) redisUtil.hGet(type, name + lessonId);
//            if (lessonHardwareData != null) {
//                redis.addAll(lessonHardwareData);
//            }
//        }
//        if (redis.isEmpty()) {
//            redis = dao.findByLessonIdListAndUserId(lessonIdList, userId);
//            Map<Long, List<FootballLessonHardwareData>> map = (Map<Long, List<FootballLessonHardwareData>>) TranslatorUtil.listToMap("lessonId", redis, 2);
//            for (Map.Entry<Long, List<FootballLessonHardwareData>> entry : map.entrySet()) {
//                List<FootballLessonHardwareData> dataList = entry.getValue();
//                redisUtil.hSet(type, name + entry.getKey(), dataList);
//            }
//        }
//        return redis;
        return dao.findByLessonIdListAndUserId(lessonIdList, userId);
    }

    @Override
    public void deleteByLessonIdAndUserId(Long lessonId, Long userId) {
        redisUtil.hDel("football_lesson_hardware_data");
        dao.deleteByLessonIdAndUserId(lessonId, userId);
    }

    @Override
    public int delByUserId(Long userId) {
        redisUtil.hDel("football_lesson_hardware_data");
        return dao.delByUserId(userId);
    }

    @Override
    public List<FootballLessonHardwareData> findStartedByHardwareIdAndLessonId(Long hardwareId, Long lessonId) {
        return dao.findStartedByHardwareIdAndLessonId(hardwareId, lessonId);
    }

    @Override
    public List<FootballLessonHardwareData> findByLessonIdAndUserId(Long lessonId, Long userId) {
        return dao.findByLessonIdAndUserId(lessonId, userId);
    }

    @Override
    public List<FootballLessonHardwareData> findStartedByLessonIdAndUserIdOrderByHardwareId(Long lessonId, Long userId) {
        return dao.findStartedByLessonIdAndUserIdOrderByHardwareId(lessonId, userId);
    }

    @Override
    public List<FootballLessonHardwareData> findStartedByUserId(Long userId) {
        return dao.findStartedByUserId(userId);
    }

    @Override
    public List<FootballLessonHardwareData> findUploadedByLessonIdAndUserIdList(Long lessonId, List<Long> userIdList) {
        if (userIdList != null && userIdList.size() > 0) {
            return dao.findUploadedByLessonIdAndUserIdList(lessonId, userIdList);
        }
        return new ArrayList<>();
    }

    //获取用户的硬件课程训练数据
    @Override
    public List<FootballLessonHardwareData> findStartedByLessonIdAndUserIdOrderByKickBallStartTime(Long lessonId, Long userId) {
        return dao.findStartedByLessonIdAndUserIdOrderByKickBallStartTime(lessonId, userId);
    }

    //获取课程训练报名的所有用户的硬件数据
    public List<FootballLessonHardwareData> findUploadedByLessonIdAndUserIdListOrderByKickBallStartTime(Long lessonId, List<Long> userIdList) {
        return dao.findUploadedByLessonIdAndUserIdListOrderByKickBallStartTime(lessonId, userIdList);
    }

    @Override
    public int deleteByLessonIdList(List<Long> lessonIdList) {
        if (lessonIdList != null && lessonIdList.size() > 0) {
            return dao.deleteByLessonIdList(lessonIdList);
        }
        return 0;
    }
}
