package com.microteam.base.common.pojo;


import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import org.hibernate.annotations.Proxy;


@JsonIgnoreProperties(ignoreUnknown = true)
public class MtJavaServerResponseBodyResultPojo implements java.io.Serializable{

	

		/**
		 * 
		 */
		
		private String code;
		
		private String message; //增加；
		
		
		public MtJavaServerResponseBodyResultPojo() {
		}

		public MtJavaServerResponseBodyResultPojo(String code, String message) {
			
			this.code = code;
			
			this.message = message;
        
		}

		
		public String getCode() {
			return this.code;
		}

		public void setCode(String code) {
			this.code = code;
		}


		public String getMessage() {
			return this.message;
		}

		public void setMessage(String message) {
			this.message = message;
		}
	
}