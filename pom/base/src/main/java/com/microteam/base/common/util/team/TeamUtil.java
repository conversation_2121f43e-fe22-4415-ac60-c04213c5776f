package com.microteam.base.common.util.team;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.microteam.base.common.pojo.MtJavaServerResponseBodyPojo;
import com.microteam.base.common.pojo.MtJavaServerResponseBodyResultPojo;
import com.microteam.base.common.pojo.team.TeamsCreateInformation;
import com.microteam.base.common.util.system.ErrorUtil;
import com.microteam.base.entity.team.FootballTeam;
import com.microteam.base.entity.team.FootballTeamNatureType;
import com.microteam.base.entity.user.Groups;
import com.microteam.base.entity.user.User;
import com.microteam.base.team.service.FootballTeamDaoService;
import com.microteam.base.team.service.FootballTeamNatureTypeDaoService;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

@Component
public class TeamUtil {
    static Logger logger = Logger.getLogger(TeamUtil.class.getName());
    @Autowired
    private ErrorUtil errorUtil;
    @Autowired
    private FootballTeamNatureTypeDaoService footballTeamNatureTypeDaoService;
    @Autowired
    private FootballTeamDaoService footballTeamDaoService;

    public MtJavaServerResponseBodyPojo saveTeams(MtJavaServerResponseBodyPojo responseBodyPojoId, TeamsCreateInformation teamsCreateInformation, User user, Groups groups) {
        MtJavaServerResponseBodyResultPojo responseBodyResultPojo = new MtJavaServerResponseBodyResultPojo();
        try {
            // 给球队对象赋值
            FootballTeam teams = new FootballTeam();
            teams.setAudit((short) 1);
            if (teamsCreateInformation.getCountry() != null && !teamsCreateInformation.getCountry().equals("")) {
                teams.setCountryCode(teamsCreateInformation.getCountry());
            } else {
                teams.setCountryCode("1000000");
            }
            if (teamsCreateInformation.getProvince() != null && !teamsCreateInformation.getProvince().equals("")) {
                teams.setProvinceCode(teamsCreateInformation.getProvince());
            }
            if (teamsCreateInformation.getCity() != null && !teamsCreateInformation.getCity().equals("")) {
                teams.setCityCode(teamsCreateInformation.getCity());
            }
            if (teamsCreateInformation.getCounty() != null && !teamsCreateInformation.getCounty().equals("")) {
                teams.setCountyCode(teamsCreateInformation.getCounty());
            }
            if (teamsCreateInformation.getHostColor() != 0) {
                teams.setHostColor(teamsCreateInformation.getHostColor());
            }
            if (teamsCreateInformation.getGuestColor() != 0) {
                teams.setGuestColor(teamsCreateInformation.getGuestColor());
            }
            Timestamp datetime = new Timestamp(System.currentTimeMillis());
            if (teamsCreateInformation.getTeamTime() != null && !"".equals(teamsCreateInformation.getTeamTime())) {
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                Date date = sdf.parse(teamsCreateInformation.getTeamTime());
                teams.setTeamTime(date);
            } else {
                teams.setTeamTime(new Date());
            }
            teams.setCreateTime(datetime);
            teams.setUser(user);
            teams.setDeleted(false);
            teams.setEnabled(true);
            teams.setGroups(groups);
            teams.setTeamCreateTime(datetime);
            teams.setTeamName(teamsCreateInformation.getTeamName());
            if (teamsCreateInformation.getIsConscribe() != 0) {
                teams.setIsConscribe((short) teamsCreateInformation.getIsConscribe());
            }
            if (teamsCreateInformation.getIsHistory() != 0) {
                teams.setIsHistory((short) teamsCreateInformation.getIsHistory());
            }
            teams.setIsCampusTeam(teamsCreateInformation.isCampusTeam());
            // gerald 新增联系人和联系方式
            if (teamsCreateInformation.getTelephone() != null && !"".equals(teamsCreateInformation.getTelephone())) {
                teams.setTelephone(teamsCreateInformation.getTelephone());
            }
            if (teamsCreateInformation.getContact() != null && !"".equals(teamsCreateInformation.getContact())) {
                teams.setContact(teamsCreateInformation.getContact());
            }
            // gerald 新增联系人和联系方式
            String teamNatureType;
            if (teamsCreateInformation.getAgencyId() != null && teamsCreateInformation.getAgencyId() != -1L) {
                teamNatureType = "youthTeam";
            } else {
                teamNatureType = teamsCreateInformation.getTeamNatureType();
            }
            if (teamNatureType != null && !teamNatureType.equals("")) {
                FootballTeamNatureType teamsNatureType = footballTeamNatureTypeDaoService.findByTypeName(teamsCreateInformation.getTeamNatureType());
                if (teamsNatureType == null) {// 无teamsNatureType；
                    errorUtil.setError(responseBodyPojoId, responseBodyResultPojo, "2022", "footballTeamNatureType is null in dataBase.");
                    return responseBodyPojoId;
                }
                teams.setFootballTeamNatureType(teamsNatureType);
            } else {
                errorUtil.setError(responseBodyPojoId, responseBodyResultPojo, "2023", "footballTeamNatureType request parameter is null.");
                return responseBodyPojoId;
            }
            if (!"".equals(teamsCreateInformation.getTeamAbbreviation()) && teamsCreateInformation.getTeamAbbreviation() != null) {
                teams.setTeamAbbreviation(teamsCreateInformation.getTeamAbbreviation());
            }
            if (teamsCreateInformation.getFavorRules() != 0) {
                teams.setFavorRules((short) teamsCreateInformation.getFavorRules());
            }
            teams.setVsteamNumber(teamsCreateInformation.getVsteamNumber());
            teams.setSelfEvaluation((short) 2);
            teams.setDescription(teamsCreateInformation.getDescription());
            teams.setSlogan(teamsCreateInformation.getSlogan());
            teams.setTeamRecord(teamsCreateInformation.getTeamRecord());
            teams.setTeamHonour(teamsCreateInformation.getTeamHonour());
            teams.setPlayerList(teamsCreateInformation.getPlayerList());
            teams.setPlayGround(teamsCreateInformation.getPlayGround());
            teams.setPlayStar(teamsCreateInformation.getPlayStar());
            teams.setLeader(teamsCreateInformation.getLeader());
            teams.setCoach(teamsCreateInformation.getCoach());
            teams.setWechatGroup(teamsCreateInformation.getWechatGroup());
            teams.setSchoolName(teamsCreateInformation.getSchoolName());
            FootballTeam team = footballTeamDaoService.save(teams);
            if (team == null) {
                errorUtil.setError(responseBodyPojoId, responseBodyResultPojo, "2024", "save Teams table Error.");
                return responseBodyPojoId;
            }
            Map<String, Object> map = new HashMap<>();
            map.put("groupsid", groups.getId());
            map.put("groupRole", "footballTeams");
            map.put("imGroupid", groups.getImGroupId());
            map.put("imGroupname", groups.getGroupName());
            map.put("teamsid", teams.getId());
            map.put("teamName", teams.getTeamName());
            map.put("vsteamNumber", teams.getVsteamNumber());
            map.put("teamsMemberRole", "teamOwner");
            map.put("teamHistory", teams.getIsHistory());
            map.put("userToken", user.getTokens().getAccessToken());
            map.put("selfEvaluation", teams.getSelfEvaluation());
            map.put("description", teams.getDescription());
            map.put("slogan", teams.getSlogan());
            map.put("teamHeadImgNetUrl", teams.getTeamHeadImgNetUrl());
            map.put("teamPennantNetUrl", teams.getTeamPennantNetUrl());
            map.put("teamBadgeNetUrl", teams.getTeamBadgeNetUrl());
            map.put("teamWeixinQrcodeNetUrl", teams.getTeamWeixinQrcodeNetUrl());
            map.put("favorRules", teams.getFavorRules());
            map.put("teamRecord", teams.getTeamRecord());
            map.put("teamHonour", teams.getTeamHonour());
            map.put("playerList", teams.getPlayerList());
            map.put("playGround", teams.getPlayGround());
            map.put("playStar", teams.getPlayStar());
            map.put("leader", teams.getLeader());
            map.put("coach", teams.getCoach());
            map.put("contact", teams.getContact());
            map.put("telephone", teams.getTelephone());
            map.put("wechatGroup", teams.getWechatGroup());
            JSONObject json = new JSONObject(0);
            json.put("teams", map);
            JSONArray jArray = new JSONArray();
            jArray.add(0, json);
            responseBodyPojoId.setData(jArray.toString());
            errorUtil.setSuccess(responseBodyPojoId, responseBodyResultPojo);
        } catch (Exception e) {
            e.printStackTrace();
            logger.debug("Teams Create Exception Error." + e.getMessage());
            errorUtil.setError(responseBodyPojoId, responseBodyResultPojo, "2024", "Teams Create Exception Error.");
        }
        return responseBodyPojoId;

    }
}
