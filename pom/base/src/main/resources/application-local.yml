custom:
  ip: **************
  server-ip: mtdev.microteam.cn
  database-ip: mtdev.microteam.cn
  port: 25032
  name: team-getpersonteam

server:
  port: ${custom.port}
spring:
  application:
    name: ${custom.name}
  datasource:
    url: jdbc:mysql://${custom.database-ip}:3306/vsteam_db?useunicode=true&characterEncoding=utf8&useSSL=false
    username: vsteam
    password: vsteam2015
    driver-class-name: com.mysql.cj.jdbc.Driver
    dbcp2:
      default-catalog: vsteam_db
    hikari:
      idle-timeout: 60000
      connection-timeout: 60000
      validation-timeout: 3000
      login-timeout: 5
      max-lifetime: 60000
  redis:
    host: ${custom.database-ip}
    port: 6379
    timeout: 3000ms
    database: 0
    password: vsteam
  jpa:
    hibernate:
      ddl-auto: none
      naming:
        physical-strategy: org.hibernate.boot.model.naming.PhysicalNamingStrategyStandardImpl
    show-sql: true
    database-platform: org.hibernate.dialect.MySQL5Dialect
    properties:
      hibernate:
        current_session_context_class: org.springframework.orm.hibernate5.SpringSessionContext

eureka:
  client:
    healthcheck:
      enabled: true
    instance-info-replication-interval-seconds: 10
    serviceUrl:
      defaultZone: http://${custom.server-ip}:1111/eureka
  instance:
    prefer-ip-address: true
    instance-id: ${spring.application.name}:${custom.ip}:${spring.application.instance_id:${server.port}}
    #    instance-id: ${spring.application.name}:${docker.ipAddress}:${spring.application.instance_id:${server.port}}
    lease-renewal-interval-in-seconds: 5
    lease-expiration-duration-in-seconds: 15
    ip-address: ${custom.ip}
#设置最大容错超时时间
hystrix.command.default.execution.isolation.thread.timeoutInMilliseconds: 20000
#设置最大容错超时时间
hystrix:
  command:
    default:
      execution:
        timeout:
          enabled: true    #设置熔断的超时时间开启 false关闭熔断功能，true开启熔断功能
        isolation:
          thread:
            timeoutInMilliseconds: 20000
management:
  endpoints:
    web:
      exposure:
        include: "*"
  endpoint:
    health:
      show-details: always
logging:
  config: classpath:logback-boot.xml