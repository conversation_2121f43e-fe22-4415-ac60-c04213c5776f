import lombok.Data;
import lombok.Getter;
import lombok.Setter;

/**
 * 测试Lombok是否正常工作
 */
@Data
public class LombokTest {
    
    @Getter
    @Setter
    private String name;
    
    private int age;
    
    private String email;
    
    public static void main(String[] args) {
        LombokTest test = new LombokTest();
        test.setName("测试用户");
        test.setAge(25);
        test.setEmail("<EMAIL>");
        
        System.out.println("姓名: " + test.getName());
        System.out.println("年龄: " + test.getAge());
        System.out.println("邮箱: " + test.getEmail());
        System.out.println("toString: " + test.toString());
    }
}
