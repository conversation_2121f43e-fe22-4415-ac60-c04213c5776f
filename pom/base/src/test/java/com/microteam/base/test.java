package com.microteam.base;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

import java.text.SimpleDateFormat;
import java.util.Date;

public class test {

    public static void main(String[] args) {
        /*String s = "[{\"sec\":151,\"touchTime\":1568206211060,\"userId\":1,\"speed\":4},{\"sec\":152,\"touchTime\":1568206212060,\"userId\":1,\"speed\":24},{\"sec\":153,\"touchTime\":1568206213060,\"userId\":1,\"speed\":4},{\"sec\":158,\"touchTime\":1568206218060,\"userId\":1,\"speed\":24},{\"sec\":159,\"touchTime\":1568206219060,\"userId\":1,\"speed\":2},{\"sec\":161,\"touchTime\":1568206221060,\"userId\":1,\"speed\":3},{\"sec\":167,\"touchTime\":1568206227060,\"userId\":1,\"speed\":13},{\"sec\":168,\"touchTime\":1568206228060,\"userId\":1,\"speed\":4},{\"sec\":169,\"touchTime\":1568206229060,\"userId\":1,\"speed\":9},{\"sec\":171,\"touchTime\":1568206231060,\"userId\":1,\"speed\":2},{\"sec\":172,\"touchTime\":1568206232060,\"userId\":1,\"speed\":5},{\"sec\":175,\"touchTime\":1568206235060,\"userId\":1,\"speed\":13}]";
        JSONArray array = JSON.parseArray(s);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy年MM月dd日 HH时mm分ss秒");
        for (int i = 0; i < array.size(); i++) {
            JSONObject object = array.getJSONObject(i);
            long touchTime = object.getLongValue("touchTime");
            array.getJSONObject(i).put("date", sdf.format(new Date(touchTime)));
        }
        System.out.println(array);
        String s2 = "{\"axisX\":[1.0,2.0,3.0,4.0],\"axisXLong\":[1568206120000,1568206180000,1568206240000,1568206300000],\"axisY\":[2147483647,2147483647,40,0],\"maxX\":4.0,\"maxY\":2147483647,\"realMin\":4.0}";
        JSONObject object = JSON.parseObject(s2);
        JSONArray array1 = object.getJSONArray("axisXLong");
        for (int i = 0; i < array1.size(); i++) {
            System.out.println(sdf.format(new Date(array1.getLongValue(i))));
        }*/

    }
}
