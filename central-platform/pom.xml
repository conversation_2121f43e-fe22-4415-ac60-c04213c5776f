<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>com.central</groupId>
    <artifactId>central-platform</artifactId>
    <version>0.0.1-SNAPSHOT</version>
    <packaging>pom</packaging>


    <properties>

        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <maven.compiler.source>1.8</maven.compiler.source>
        <maven.compiler.target>1.8</maven.compiler.target>
        <java.version>1.8</java.version>
        <sharding-jdbc>2.0.3</sharding-jdbc>
        <swagger.version>2.8.0</swagger.version>
        <core.version>0.0.1-SNAPSHOT</core.version>
        <swagger.m.version>1.0.4</swagger.m.version>
        <docker.image.prefix>owen</docker.image.prefix>
        <spring-boot.version>2.0.1.RELEASE</spring-boot.version>
        <security-oauth2.version>2.3.3.RELEASE</security-oauth2.version>
        <hibernate-validator.verion>5.0.2.Final</hibernate-validator.verion>
        <commons-collections4.version>4.1</commons-collections4.version>
        <fastjson.version>1.2.47</fastjson.version>
        <spring-boot-dependencies.version>2.0.1.RELEASE</spring-boot-dependencies.version>
        <spring-cloud-dependencies.version>Finchley.SR1</spring-cloud-dependencies.version>
    </properties>


    <dependencies>

        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>1.18.30</version>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-collections4</artifactId>
            <version>${commons-collections4.version}</version>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
            <version>${fastjson.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>

        <!-- jaxb模块引用 - start -->
        <dependency>
            <groupId>javax.xml.bind</groupId>
            <artifactId>jaxb-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sun.xml.bind</groupId>
            <artifactId>jaxb-impl</artifactId>
            <version>2.3.0</version>
        </dependency>
        <dependency>
            <groupId>org.glassfish.jaxb</groupId>
            <artifactId>jaxb-runtime</artifactId>
            <version>2.3.0</version>
        </dependency>
        <dependency>
            <groupId>javax.activation</groupId>
            <artifactId>activation</artifactId>
            <version>1.1.1</version>
        </dependency>
        <!-- jaxb模块引用 - end -->
    </dependencies>
    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring-boot-dependencies.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring-cloud-dependencies.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

        </dependencies>
    </dependencyManagement>

    <build>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-maven-plugin</artifactId>
                </plugin>
                <plugin>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <configuration>
                        <target>${java.version}</target>
                        <source>${java.version}</source>
                        <encoding>UTF-8</encoding>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-jar-plugin</artifactId>
                    <configuration>
                        <archive>
                            <addMavenDescriptor>false</addMavenDescriptor>
                        </archive>
                    </configuration>
                </plugin>

                <!--<plugin>    -->
                    <!--<groupId>org.apache.maven.plugins</groupId>-->
                    <!--<artifactId>maven-compiler-plugin</artifactId>-->
                    <!--<version>${maven-compiler-plugin.version}</version>    -->
                    <!--<configuration>        -->
                        <!--<annotationProcessorPaths>            -->
                            <!--<path>-->
                                <!--<groupId>org.mapstruct</groupId>-->
                                <!--<artifactId>mapstruct-processor</artifactId>-->
                                <!--&lt;!&ndash;<version>${mapstruct.version}</version>            &ndash;&gt;-->
                            <!--</path>       -->
                            <!--<path>-->
                                <!--<groupId>org.projectlombok</groupId>-->
                                <!--<artifactId>lombok</artifactId>       -->
                            <!--</path>        -->
                        <!--</annotationProcessorPaths>-->
                    <!--</configuration>-->
                <!--</plugin>-->

            </plugins>
        </pluginManagement>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <includes>
                    <include>**/*.properties</include>
                    <include>**/*.yml</include>
                    <include>**/*.xml</include>
                    <include>**/*.tld</include>
                    <include>**/*.p12</include>
                    <include>**/*.conf</include>
                    <include>**/*.txt</include>
                    <include>**/*.wsdl</include>
                    <include>**/*.xsd</include>
                    <include>**/*.ftl</include>
                </includes>
                <filtering>false</filtering>
            </resource>
            <resource>
                <directory>src/main/java</directory>
                <includes>
                    <include>**/*.properties</include>
                    <include>**/*.xml</include>
                    <include>**/*.tld</include>
                </includes>
                <filtering>false</filtering>
            </resource>
            <resource>
                <directory>src/main/view</directory>
                <includes>
                    <include>**/*.*</include>
                </includes>
                <filtering>false</filtering>
            </resource>
        </resources>
    </build>

    <modules>
        <!--数据核心访问模块-->
        <module>db-core</module>
        <!-- 公共模块 -->
        <module>api-commons</module>
        <!--注册中心-->
        <module>register-center</module>
        <!-- 配置中心 -->
        <module>config-center</module>
        <!--认证中心-->
        <module>oauth-center</module>
        <!-- 业务网关 -->
        <module>api-gateway</module>
        <!-- 监控中心 -->
        <module>monitor-center</module>
        <!--任务中心-->
        <module>job-center</module>
        <!-- 业务中心 -->
        <module>business-center</module>
        <!-- 压测
        <module>tuning-center</module>
         -->
    </modules>
</project>